{"name": "lizenzero-admin-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prisma:migrate": "npx prisma migrate dev --name", "generate": "npx prisma generate", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write .", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:cov": "jest --coverage", "test:watch": "jest --watch", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "cross-env NODE_ENV=test jest --config ./jest.config.e2e.js", "check-types": "tsc --pretty --noEmit", "check-format": "prettier --check .", "check-lint": "eslint . --ext ts --ext tsx --ext js", "check-all": " pnpm run format && pnpm run check-format && pnpm run check-lint && pnpm run generate && pnpm run check-types && pnpm run build", "prepare": "husky install"}, "dependencies": {"@azure/storage-blob": "^12.17.0", "@customerio/cdp-analytics-node": "^0.2.0", "@nestjs-modules/mailer": "^1.11.2", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^8.0.0", "@nestjs/config": "^3.2.0", "@nestjs/core": "^8.0.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^10.3.3", "@nestjs/swagger": "^7.2.0", "@prisma/client": "^5.10.2", "@sendgrid/mail": "^8.1.1", "async-retry": "^1.3.3", "axios": "^1.7.4", "blob-polyfill": "^9.0.20240710", "bottleneck": "^2.19.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "customerio-node": "^4.1.1", "dotenv": "^16.4.5", "html-to-pdfmake": "^2.5.13", "jsdom": "^25.0.0", "p-limit": "^3.1.0", "pdfmake": "^0.2.12", "pdfmake-unicode": "^0.0.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "uuidv4": "^6.2.13", "xlsx": "^0.18.5"}, "resolutions": {"wrap-ansi": "7.0.0", "string-width": "4.1.0"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/preset-env": "^7.26.7", "@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/async-retry": "^1.4.9", "@types/express": "^4.17.13", "@types/html-to-pdfmake": "^2.4.4", "@types/jest": "27.4.1", "@types/jsdom": "^21.1.7", "@types/multer": "^1.4.11", "@types/node": "^16.0.0", "@types/nodemailer": "^6.4.15", "@types/pdfmake": "^0.2.9", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.0", "jest": "^27.2.5", "prettier": "^2.3.2", "prisma": "^5.19.1", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^27.1.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^4.3.5"}}