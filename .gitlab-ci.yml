# ----------------- GLOBAL CONFIGURATIONS ----------------- #
default:
  tags: ["aws-build"]

image: nexus.interzero.de:5000/node:${NODE_VERSION}-alpine

include:
  - project: "devops-templates/ci-templates"
    ref: main
    file: "/teams/notifications.yml"

stages:
  - test_code
  - build_docker_image
  - deploy
  - teams_notifications
# Global variables used across multiple jobs.
variables:
  PROJECT_NAME: lizenzero
  COMPONENT: customer-api
  NEXUS_PROD: 6000
  NEXUS_ENTW_QAT: 5000
  BRANCH: ${CI_COMMIT_REF_NAME}

# ----------------- TEMPLATES ----------------- #
.docker_build_template: &docker_build_template
  image:
    name: gcr.io/kaniko-project/executor:v1.14.0-debug
    entrypoint: [""]
  script:
    - echo $DOCKER_AUTH_CONFIG > /kaniko/.docker/config.json
    - /kaniko/executor
      --context $CI_PROJECT_DIR
      --dockerfile $CI_PROJECT_DIR/Dockerfile${DOCKERFILE_EXTENSION}
      --build-arg NODE_VERSION=$NODE_VERSION
      --build-arg PROJECT_NAME=$PROJECT_NAME
      --build-arg COMPONENT=$COMPONENT
      --build-arg BRANCH=$BRANCH
      --build-arg SYSTEM_API_KEY=$SYSTEM_API_KEY
      --build-arg DATABASE_URL=$DATABASE_URL
      --build-arg AUTH_API_URL=$AUTH_API_URL
      --build-arg CRM_API_URL=$CRM_API_URL
      --build-arg CORE_API_URL=$CORE_API_URL
      --build-arg PAYMENT_API_URL=$PAYMENT_API_URL
      --build-arg ADMIN_API_URL=$ADMIN_API_URL
      --build-arg CUSTOMER_IO_SITE_ID=$CUSTOMER_IO_SITE_ID
      --build-arg CUSTOMER_IO_API_KEY=$CUSTOMER_IO_API_KEY
      --build-arg CUSTOMER_IO_KEY=$CUSTOMER_IO_KEY
      --insecure
      --insecure-pull
      --destination nexus.interzero.de:${NEXUS_PORT}/${PROJECT_NAME}-${COMPONENT_NAME}:${CI_COMMIT_REF_NAME}
  environment:
    name: $CI_COMMIT_REF_NAME

.deployment_template: &deployment_template
  stage: deploy
  image: nexus.interzero.de:5000/dtzar/helm-kubectl:3.12.3
  script:
    - echo "Deploy to ${DEPLOY_ENV} server"
    - echo "Delete old service"
    - kubectl delete -n ${NAMESPACE} deployment ${PROJECT_NAME}-${COMPONENT} --ignore-not-found
    - echo "Add new service"
    - kubectl config view
    - sed -i "s/<BRANCH>/${CI_COMMIT_REF_NAME}/g" deployment.yml
    - sed -i "s/<NAMESPACE>/${NAMESPACE}/g" deployment.yml
    - sed -i "s/<COMPONENT>/${COMPONENT}/g" deployment.yml
    - sed -i "s/<PROJECT_NAME>/${PROJECT_NAME}/g" deployment.yml
    - sed -i "s/<NEXUS_PORT>/${NEXUS_PORT}/g" deployment.yml
    - sed -i "s/<HOSTNAME>/${HOSTNAME}/g" deployment.yml
    - sed -i "s/<ALB_SCHEME>/${ALB_SCHEME}/g" deployment.yml
    - sed -i "s/<ALB_GROUP>/${ALB_GROUP}/g" deployment.yml
    - sed -i "s|<PORT>|${PORT}|g" deployment.yml
    - sed -i "s|<NODE_ENV>|${NODE_ENV}|g" deployment.yml
    - sed -i "s|<DATABASE_URL>|${DATABASE_URL}|g" deployment.yml
    - sed -i "s|<AUTH_API_URL>|${AUTH_API_URL}|g" deployment.yml
    - sed -i "s|<CRM_API_URL>|${CRM_API_URL}|g" deployment.yml
    - sed -i "s|<CORE_API_URL>|${CORE_API_URL}|g" deployment.yml
    - sed -i "s|<PAYMENT_API_URL>|${PAYMENT_API_URL}|g" deployment.yml
    - sed -i "s|<ADMIN_API_URL>|${ADMIN_API_URL}|g" deployment.yml
    - sed -i "s|<CUSTOMER_IO_SITE_ID>|${CUSTOMER_IO_SITE_ID}|g" deployment.yml
    - sed -i "s|<CUSTOMER_IO_API_KEY>|${CUSTOMER_IO_API_KEY}|g" deployment.yml
    - sed -i "s|<CUSTOMER_IO_KEY>|${CUSTOMER_IO_KEY}|g" deployment.yml
    - cat deployment.yml
    - kubectl apply -f deployment.yml
  environment:
    name: $CI_COMMIT_REF_NAME

# ----------------- DOCKER IMAGE BUILD ----------------- #
docker_build_entw:
  stage: build_docker_image
  <<: *docker_build_template
  tags: ["aws-build"]
  only:
    - develop
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT}"
    PORT: ${NEXUS_PORT}
    DATABASE_URL: $ENTW_DATABASE_URL
    AUTH_API_URL: $ENTW_AUTH_API_URL
    CRM_API_URL: $ENTW_CRM_API_URL
    CORE_API_URL: $ENTW_CORE_API_URL
    PAYMENT_API_URL: $ENTW_PAYMENT_API_URL
    ADMIN_API_URL: $ENTW_ADMIN_API_URL
    CUSTOMER_IO_SITE_ID: $ENTW_CUSTOMER_IO_SITE_ID
    CUSTOMER_IO_API_KEY: $ENTW_CUSTOMER_IO_API_KEY
    CUSTOMER_IO_KEY: $ENTW_CUSTOMER_IO_KEY
    SYSTEM_API_KEY: $SYSTEM_API_KEY

docker_build_qat:
  stage: build_docker_image
  <<: *docker_build_template
  tags: ["aws-build"]
  only:
    - staging
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT}"
    PORT: ${NEXUS_PORT}
    DATABASE_URL: $QAT_DATABASE_URL
    AUTH_API_URL: $QAT_AUTH_API_URL
    CRM_API_URL: $QAT_CRM_API_URL
    CORE_API_URL: $QAT_CORE_API_URL
    PAYMENT_API_URL: $QAT_PAYMENT_API_URL
    ADMIN_API_URL: $QAT_ADMIN_API_URL
    CUSTOMER_IO_SITE_ID: $QAT_CUSTOMER_IO_SITE_ID
    CUSTOMER_IO_API_KEY: $QAT_CUSTOMER_IO_API_KEY
    CUSTOMER_IO_KEY: $QAT_CUSTOMER_IO_KEY
    SYSTEM_API_KEY: $SYSTEM_API_KEY

docker_build_prod:
  stage: build_docker_image
  <<: *docker_build_template
  tags: ["aws-build"]
  only:
    - main
  variables:
    NEXUS_PORT: "${NEXUS_PROD}"
    COMPONENT_NAME: "${COMPONENT}"
    PORT: ${NEXUS_PORT}
    DATABASE_URL: $PROD_DATABASE_URL
    AUTH_API_URL: $PROD_AUTH_API_URL
    CRM_API_URL: $PROD_CRM_API_URL
    CORE_API_URL: $PROD_CORE_API_URL
    PAYMENT_API_URL: $PROD_PAYMENT_API_URL
    ADMIN_API_URL: $PROD_ADMIN_API_URL
    CUSTOMER_IO_SITE_ID: $PROD_CUSTOMER_IO_SITE_ID
    CUSTOMER_IO_API_KEY: $PROD_CUSTOMER_IO_API_KEY
    CUSTOMER_IO_KEY: $PROD_CUSTOMER_IO_KEY
    SYSTEM_API_KEY: $SYSTEM_API_KEY

# ----------------- DEPLOYMENT ----------------- #
deploy_to_develop:
  <<: *deployment_template
  only:
    - develop
  tags: ["eks-entw-qat"]
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "entw-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    HOSTNAME: "${PROJECT_NAME}-${COMPONENT}.entw.portale.interzero.de"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-entw-qat-vpn"
    PORT: 3000
    NODE_ENV: production
    DATABASE_URL: $ENTW_DATABASE_URL
    AUTH_API_URL: $ENTW_AUTH_API_URL
    CRM_API_URL: $ENTW_CRM_API_URL
    CORE_API_URL: $ENTW_CORE_API_URL
    PAYMENT_API_URL: $ENTW_PAYMENT_API_URL
    ADMIN_API_URL: $ENTW_ADMIN_API_URL
    CUSTOMER_IO_SITE_ID: $ENTW_CUSTOMER_IO_SITE_ID
    CUSTOMER_IO_API_KEY: $ENTW_CUSTOMER_IO_API_KEY
    CUSTOMER_IO_KEY: $ENTW_CUSTOMER_IO_KEY
    SYSTEM_API_KEY: $SYSTEM_API_KEY

deploy_to_staging:
  <<: *deployment_template
  only:
    - staging
  tags: ["eks-prod"]
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "qat-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    HOSTNAME: "${PROJECT_NAME}-${COMPONENT}-qat.vpn.portale.interzero.de"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-prod-vpn"
    PORT: 3000
    NODE_ENV: production
    DATABASE_URL: $QAT_DATABASE_URL
    AUTH_API_URL: $QAT_AUTH_API_URL
    CRM_API_URL: $QAT_CRM_API_URL
    CORE_API_URL: $QAT_CORE_API_URL
    PAYMENT_API_URL: $QAT_PAYMENT_API_URL
    ADMIN_API_URL: $QAT_ADMIN_API_URL
    CUSTOMER_IO_SITE_ID: $QAT_CUSTOMER_IO_SITE_ID
    CUSTOMER_IO_API_KEY: $QAT_CUSTOMER_IO_API_KEY
    CUSTOMER_IO_KEY: $QAT_CUSTOMER_IO_KEY
    SYSTEM_API_KEY: $SYSTEM_API_KEY
deploy_to_prod:
  <<: *deployment_template
  only:
    - main
  tags: ["eks-prod"]
  when: manual
  variables:
    DEPLOY_ENV: "PROD"
    NAMESPACE: "prod-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_PROD}"
    HOSTNAME: "${PROJECT_NAME}-${COMPONENT}.vpn.portale.interzero.de"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-prod-vpn"
    PORT: 3000
    NODE_ENV: production
    DATABASE_URL: $PROD_DATABASE_URL
    AUTH_API_URL: $PROD_AUTH_API_URL
    CRM_API_URL: $PROD_CRM_API_URL
    CORE_API_URL: $PROD_CORE_API_URL
    PAYMENT_API_URL: $PROD_PAYMENT_API_URL
    ADMIN_API_URL: $PROD_ADMIN_API_URL
    CUSTOMER_IO_SITE_ID: $PROD_CUSTOMER_IO_SITE_ID
    CUSTOMER_IO_API_KEY: $PROD_CUSTOMER_IO_API_KEY
    CUSTOMER_IO_KEY: $PROD_CUSTOMER_IO_KEY
    SYSTEM_API_KEY: $SYSTEM_API_KEY

# ----------------- NOTIFY ----------------- #
teams_notifications_failed:
  stage: teams_notifications
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: on_failure
    - if: '$CI_COMMIT_BRANCH == "staging"'
      when: on_failure
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: on_failure
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: on_failure
    - if: '$CI_COMMIT_BRANCH == "devops/pipeline_message"'
      when: on_failure
  variables:
    STATUS: "failed"
    ICON: "❌"
  extends:
    - .pipeline_failed_aws

teams_notifications_success:
  stage: teams_notifications
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  variables:
    STATUS: "success"
    ICON: "✅"
  extends:
    - .pipeline_succeed_aws

# ----------------- TESTING ----------------- #
unit_tests:
  stage: test_code
  image: nexus.interzero.de:5000/node:18.17.1-alpine
  allow_failure: true
  only:
    - develop
    - staging
    - main
    - merge_requests
  script:
    - corepack enable
    - pnpm install
    - pnpm run check-all
  tags: ["aws-ec2"]
  environment:
    name: $CI_COMMIT_REF_NAME

# ----------------- SONAR ----------------- #
sonarqube-check:
  stage: test_code
  image:
    name: nexus.interzero.de:5000/sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  tags: ["aws-ec2"]
  only:
    - develop
