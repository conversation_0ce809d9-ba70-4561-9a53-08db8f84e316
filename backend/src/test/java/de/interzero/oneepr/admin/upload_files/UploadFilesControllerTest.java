package de.interzero.oneepr.admin.upload_files;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit5.WireMockTest;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.upload_files.dto.CreateFileDto;
import de.interzero.oneepr.admin.upload_files.dto.LambdaPresignedResponseDto;
import de.interzero.oneepr.admin.upload_files.dto.RequestPresignedUrlDto;
import de.interzero.oneepr.common.string.TestRole;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static de.interzero.oneepr.common.string.Api.UPLOAD_FILES;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@WireMockTest
@Transactional
class UploadFilesControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private FilesRepository filesRepository;

    private static WireMockServer wireMockServer;
    private Country testCountry;
    private RequestPresignedUrlDto requestPresignedUrlDto;
    private MockMultipartFile mockFile;

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        wireMockServer = new WireMockServer(8181);
        wireMockServer.start();

        // Override the Lambda URL to point to our WireMock server
        registry.add("file.lambda.request-presigned-url", () -> "http://localhost:8181/lambda");
        registry.add("file.lambda.download-file-url", () -> "http://localhost:8181/download");
    }

    @BeforeEach
    void setUp() {
        // Clean up repositories
        filesRepository.deleteAll();

        // Create a test country
        testCountry = countryRepository.findByCode("DE").orElseThrow();

        // Setup test DTOs
        requestPresignedUrlDto = new RequestPresignedUrlDto();
        requestPresignedUrlDto.setFilename("test-document.pdf");
        requestPresignedUrlDto.setFileType("application/pdf");


        // Create mock file
        mockFile = new MockMultipartFile(
                "file",
                "test-document.pdf",
                "application/pdf",
                "test file content".getBytes()
        );
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void requestUrl_shouldReturnPresignedUrl_whenValidRequest() throws Exception {
        // Given
        Map<String, Object> fields = new HashMap<>();
        fields.put("key", "test-key");
        fields.put("policy", "test-policy");
        fields.put("x-amz-algorithm", "AWS4-HMAC-SHA256");

        LambdaPresignedResponseDto expectedResponse = new LambdaPresignedResponseDto();
        expectedResponse.setUploadUrl("https://test-bucket.s3.amazonaws.com/upload");
        expectedResponse.setFields(fields);

        // Mock the Lambda function call
        wireMockServer.stubFor(WireMock.post(urlMatching("/lambda"))
                .withHeader("Content-Type", equalTo("application/json"))
                .withRequestBody(containing("test-document.pdf"))
                .withRequestBody(containing("application/pdf"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody(objectMapper.writeValueAsString(expectedResponse))));

        // When & Then
        mockMvc.perform(post(UPLOAD_FILES + "/request-presigned-url")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestPresignedUrlDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.uploadUrl", is("https://test-bucket.s3.amazonaws.com/upload")))
                .andExpect(jsonPath("$.fields.key", is("test-key")))
                .andExpect(jsonPath("$.fields.policy", is("test-policy")))
                .andExpect(jsonPath("$.fields['x-amz-algorithm']", is("AWS4-HMAC-SHA256")));

        // Verify the call was made to WireMock
        wireMockServer.verify(postRequestedFor(urlEqualTo("/lambda"))
                .withHeader("Content-Type", equalTo("application/json")));
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void requestUrl_shouldReturnBadRequest_whenLambdaReturnsNullBody() throws Exception {
        // Given - Mock Lambda function to return null body
        wireMockServer.stubFor(WireMock.post(urlEqualTo("/lambda"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("null")));

        // When & Then
        mockMvc.perform(post(UPLOAD_FILES + "/request-presigned-url")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestPresignedUrlDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void uploadFile_shouldCreateFileRecord_whenValidRequest() throws Exception {
        // Given
        Map<String, Object> fields = new HashMap<>();
        fields.put("key", "test-key");
        fields.put("policy", "test-policy");
        fields.put("x-amz-algorithm", "AWS4-HMAC-SHA256");
        fields.put("x-amz-credential", "test-credential");
        fields.put("x-amz-date", "20240101T000000Z");
        fields.put("x-amz-signature", "test-signature");

        LambdaPresignedResponseDto presignedResponse = new LambdaPresignedResponseDto();
        presignedResponse.setUploadUrl("http://localhost:8181/s3-upload");
        presignedResponse.setFields(fields);

        // Mock Lambda function call for presigned URL
        wireMockServer.stubFor(WireMock.post(urlEqualTo("/lambda"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody(objectMapper.writeValueAsString(presignedResponse))));

        // Mock S3 upload
        wireMockServer.stubFor(WireMock.post(urlMatching("/s3-upload"))
                .willReturn(aResponse()
                        .withStatus(204)));

        // When & Then
        mockMvc.perform(multipart(UPLOAD_FILES)
                        .file(mockFile).param("document_type", String.valueOf(CreateFileDto.DocumentType.CONTRACT))
                        .param("country_id", testCountry.getId().toString())
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .header("x-user-id", "test-user-123")
                        .header("x-user-role", "ADMIN"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", notNullValue()))
                .andExpect(jsonPath("$.original_name", is("test-document.pdf")))
                .andExpect(jsonPath("$.extension", is("application/pdf")))
                .andExpect(jsonPath("$.size", is("17"))) // "test file content".length()
                .andExpect(jsonPath("$.creator_type", is("ADMIN")))
                .andExpect(jsonPath("$.document_type", is(String.valueOf(CreateFileDto.DocumentType.CONTRACT))))
                .andExpect(jsonPath("$.user_id", is("test-user-123")))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())));

        // Verify both calls were made
        wireMockServer.verify(postRequestedFor(urlEqualTo("/lambda")));
        wireMockServer.verify(postRequestedFor(urlEqualTo("/s3-upload")));

        // Verify file was saved to database
        assert filesRepository.count() == 1;
    }


    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void uploadFile_shouldReturnBadRequest_whenNullCountryId() throws Exception {
        // When & Then
        mockMvc.perform(multipart(UPLOAD_FILES)
                        .file(mockFile)
                        .param("document_type", "CONTRACT")
                        // Missing country_id parameter
                        .header("x-user-id", "test-user-123")
                        .header("x-user-role", "ADMIN"))
                .andExpect(status().isBadRequest());
    }


    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void uploadFile_shouldHandleDifferentDocumentTypes() throws Exception {
        // Given
        Map<String, Object> fields = new HashMap<>();
        fields.put("key", "test-key");
        fields.put("policy", "test-policy");

        LambdaPresignedResponseDto presignedResponse = new LambdaPresignedResponseDto();
        presignedResponse.setUploadUrl("http://localhost:8181/s3-upload");
        presignedResponse.setFields(fields);

        // Mock Lambda function call
        wireMockServer.stubFor(WireMock.post(urlEqualTo("/lambda"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody(objectMapper.writeValueAsString(presignedResponse))));

        // Mock S3 upload
        wireMockServer.stubFor(WireMock.post(urlEqualTo("/s3-upload"))
                .willReturn(aResponse()
                        .withStatus(204)));

        // Test with INVOICE document type
        MockMultipartFile invoiceFile = new MockMultipartFile(
                "file",
                "invoice.pdf",
                "application/pdf",
                "invoice content".getBytes()
        );

        // When & Then
        mockMvc.perform(multipart(UPLOAD_FILES)
                        .file(invoiceFile)
                        .param("document_type", "INVOICE")
                        .param("country_id", testCountry.getId().toString())
                        .header("x-user-id", "test-user-456")
                        .header("x-user-role", "CUSTOMER"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.document_type", is("INVOICE")))
                .andExpect(jsonPath("$.creator_type", is("CUSTOMER")))
                .andExpect(jsonPath("$.user_id", is("test-user-456")));
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void uploadFile_shouldHandleDifferentFileTypes() throws Exception {
        // Given
        Map<String, Object> fields = new HashMap<>();
        fields.put("key", "test-key");
        fields.put("policy", "test-policy");

        LambdaPresignedResponseDto presignedResponse = new LambdaPresignedResponseDto();
        presignedResponse.setUploadUrl("http://localhost:8181/s3-upload");
        presignedResponse.setFields(fields);

        // Mock Lambda function call
        wireMockServer.stubFor(WireMock.post(urlEqualTo("/lambda"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody(objectMapper.writeValueAsString(presignedResponse))));

        // Mock S3 upload
        wireMockServer.stubFor(WireMock.post(urlEqualTo("/s3-upload"))
                .willReturn(aResponse()
                        .withStatus(204)));

        // Test with Excel file
        MockMultipartFile excelFile = new MockMultipartFile(
                "file",
                "data.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "excel content".getBytes()
        );

        // When & Then
        mockMvc.perform(multipart(UPLOAD_FILES)
                        .file(excelFile)
                        .param("document_type", "GENERAL_DOCUMENT")
                        .param("country_id", testCountry.getId().toString())
                        .header("x-user-id", "test-user-789")
                        .header("x-user-role", "ADMIN"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.original_name", is("data.xlsx")))
                .andExpect(jsonPath("$.extension", is("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")));
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void getFile_shouldReturnFileContent_whenValidFileIdAndHeaders() throws Exception {
        // Given - Create a test file in the database
        Files testFile = createTestFileInDatabase();

        // Mock the Lambda download service
        byte[] mockFileContent = "Mock PDF file content".getBytes();
        wireMockServer.stubFor(get(urlMatching("/download\\?fileName=.*"))
                                       .willReturn(aResponse()
                                                           .withStatus(200)
                                                           .withHeader("Content-Type", "application/octet-stream")
                                                           .withBody(mockFileContent)));

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(UPLOAD_FILES + "/{id}", testFile.getId())
                                .header("x-user-id", "test-user-123")
                                .header("x-user-role", "ADMIN"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/pdf"))
                .andExpect(header().string("Content-Disposition",
                                           "attachment; filename=contract-" + testFile.getOriginalName() + ".pdf"))
                .andExpect(content().bytes(mockFileContent));
    }

    /**
     * Helper method to create a test file in the database
     */
    private Files createTestFileInDatabase() {
        Files testFile = new Files();
        testFile.setId("test-file-id-" + System.currentTimeMillis());
        testFile.setName("CONTRACT/2024/01/15/ABC12345-test-document.pdf");
        testFile.setOriginalName("test-document.pdf");
        testFile.setExtension("application/pdf");
        testFile.setSize("1024");
        testFile.setCreatorType("ADMIN");
        testFile.setDocumentType("CONTRACT");
        testFile.setUserId("test-user-123");
        testFile.setCountryId(testCountry.getId());
        testFile.setCreatedAt(Instant.now());

        return filesRepository.save(testFile);
    }

}
