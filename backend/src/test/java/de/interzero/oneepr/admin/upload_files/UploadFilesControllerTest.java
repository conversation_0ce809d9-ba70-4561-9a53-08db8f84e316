package de.interzero.oneepr.admin.upload_files;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.upload_files.dto.CreateFileDto;
import de.interzero.oneepr.admin.upload_files.dto.RequestPresignedUrlDto;
import de.interzero.oneepr.common.string.TestRole;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.io.ByteArrayInputStream;
import java.time.Instant;

import static de.interzero.oneepr.common.string.Api.UPLOAD_FILES;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@Transactional
class UploadFilesControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private FilesRepository filesRepository;

    @MockBean
    private S3Client s3Client;

    @MockBean
    private S3Presigner s3Presigner;

    private Country testCountry;
    private RequestPresignedUrlDto requestPresignedUrlDto;
    private MockMultipartFile mockFile;

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // Override AWS S3 configuration for tests
        registry.add("aws.region", () -> "eu-west-2");
        registry.add("aws.s3.bucket-name", () -> "test-bucket");
    }

    @BeforeEach
    void setUp() {
        // Clean up repositories
        filesRepository.deleteAll();

        // Create a test country
        testCountry = countryRepository.findByCode("DE").orElseThrow();

        // Setup test DTOs
        requestPresignedUrlDto = new RequestPresignedUrlDto();
        requestPresignedUrlDto.setFilename("test-document.pdf");
        requestPresignedUrlDto.setFileType("application/pdf");


        // Create mock file
        mockFile = new MockMultipartFile(
                "file",
                "test-document.pdf",
                "application/pdf",
                "test file content".getBytes()
        );
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void requestUrl_shouldReturnPresignedUrl_whenValidRequest() throws Exception {
        // Given
        PresignedPutObjectRequest mockPresignedRequest = PresignedPutObjectRequest.builder()
                .expiration(Instant.now().plusSeconds(30))
                .build();

        // Mock S3Presigner
        when(s3Presigner.presignPutObject(any(PutObjectPresignRequest.class)))
                .thenReturn(mockPresignedRequest);

        // When & Then
        mockMvc.perform(post(UPLOAD_FILES + "/request-presigned-url")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestPresignedUrlDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.uploadUrl", is("https://test-bucket.s3.amazonaws.com/upload")))
                .andExpect(jsonPath("$.fields").exists());
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void requestUrl_shouldReturnBadRequest_whenS3PresignerThrowsException() throws Exception {
        // Given - Mock S3Presigner to throw exception
        when(s3Presigner.presignPutObject(any(PutObjectPresignRequest.class)))
                .thenThrow(new RuntimeException("S3 error"));

        // When & Then
        mockMvc.perform(post(UPLOAD_FILES + "/request-presigned-url")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestPresignedUrlDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void uploadFile_shouldCreateFileRecord_whenValidRequest() throws Exception {
        // Given
        // Mock S3Client putObject to succeed
        PutObjectResponse mockPutResponse = PutObjectResponse.builder().build();
        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenReturn(mockPutResponse);

        // When & Then
        mockMvc.perform(multipart(UPLOAD_FILES)
                        .file(mockFile).param("document_type", String.valueOf(CreateFileDto.DocumentType.CONTRACT))
                        .param("country_id", testCountry.getId().toString())
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .header("x-user-id", "test-user-123")
                        .header("x-user-role", "ADMIN"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", notNullValue()))
                .andExpect(jsonPath("$.original_name", is("test-document.pdf")))
                .andExpect(jsonPath("$.extension", is("application/pdf")))
                .andExpect(jsonPath("$.size", is("17"))) // "test file content".length()
                .andExpect(jsonPath("$.creator_type", is("ADMIN")))
                .andExpect(jsonPath("$.document_type", is(String.valueOf(CreateFileDto.DocumentType.CONTRACT))))
                .andExpect(jsonPath("$.user_id", is("test-user-123")))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())));

        // Verify file was saved to database
        assert filesRepository.count() == 1;
    }


    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void uploadFile_shouldReturnBadRequest_whenNullCountryId() throws Exception {
        // When & Then
        mockMvc.perform(multipart(UPLOAD_FILES)
                        .file(mockFile)
                        .param("document_type", "CONTRACT")
                        // Missing country_id parameter
                        .header("x-user-id", "test-user-123")
                        .header("x-user-role", "ADMIN"))
                .andExpect(status().isBadRequest());
    }


    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void uploadFile_shouldHandleDifferentDocumentTypes() throws Exception {
        // Given
        // Mock S3Client putObject to succeed
        PutObjectResponse mockPutResponse = PutObjectResponse.builder().build();
        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenReturn(mockPutResponse);

        // Test with INVOICE document type
        MockMultipartFile invoiceFile = new MockMultipartFile(
                "file",
                "invoice.pdf",
                "application/pdf",
                "invoice content".getBytes()
        );

        // When & Then
        mockMvc.perform(multipart(UPLOAD_FILES)
                        .file(invoiceFile)
                        .param("document_type", "INVOICE")
                        .param("country_id", testCountry.getId().toString())
                        .header("x-user-id", "test-user-456")
                        .header("x-user-role", "CUSTOMER"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.document_type", is("INVOICE")))
                .andExpect(jsonPath("$.creator_type", is("CUSTOMER")))
                .andExpect(jsonPath("$.user_id", is("test-user-456")));
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void uploadFile_shouldHandleDifferentFileTypes() throws Exception {
        // Given
        // Mock S3Client putObject to succeed
        PutObjectResponse mockPutResponse = PutObjectResponse.builder().build();
        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenReturn(mockPutResponse);

        // Test with Excel file
        MockMultipartFile excelFile = new MockMultipartFile(
                "file",
                "data.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "excel content".getBytes()
        );

        // When & Then
        mockMvc.perform(multipart(UPLOAD_FILES)
                        .file(excelFile)
                        .param("document_type", "GENERAL_DOCUMENT")
                        .param("country_id", testCountry.getId().toString())
                        .header("x-user-id", "test-user-789")
                        .header("x-user-role", "ADMIN"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.original_name", is("data.xlsx")))
                .andExpect(jsonPath("$.extension", is("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")));
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void getFile_shouldReturnFileContent_whenValidFileIdAndHeaders() throws Exception {
        // Given - Create a test file in the database
        Files testFile = createTestFileInDatabase();

        // Mock S3Client getObject
        byte[] mockFileContent = "Mock PDF file content".getBytes();
        GetObjectResponse mockGetResponse = GetObjectResponse.builder().build();
        ResponseInputStream<GetObjectResponse> mockInputStream =
                new ResponseInputStream<>(mockGetResponse, new ByteArrayInputStream(mockFileContent));

        when(s3Client.getObject(any(GetObjectRequest.class)))
                .thenReturn(mockInputStream);

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(UPLOAD_FILES + "/{id}", testFile.getId())
                                .header("x-user-id", "test-user-123")
                                .header("x-user-role", "ADMIN"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/pdf"))
                .andExpect(header().string("Content-Disposition",
                                           "attachment; filename=contract-" + testFile.getOriginalName() + ".pdf"))
                .andExpect(content().bytes(mockFileContent));
    }

    /**
     * Helper method to create a test file in the database
     */
    private Files createTestFileInDatabase() {
        Files testFile = new Files();
        testFile.setId("test-file-id-" + System.currentTimeMillis());
        testFile.setName("CONTRACT/2024/01/15/ABC12345-test-document.pdf");
        testFile.setOriginalName("test-document.pdf");
        testFile.setExtension("application/pdf");
        testFile.setSize("1024");
        testFile.setCreatorType("ADMIN");
        testFile.setDocumentType("CONTRACT");
        testFile.setUserId("test-user-123");
        testFile.setCountryId(testCountry.getId());
        testFile.setCreatedAt(Instant.now());

        return filesRepository.save(testFile);
    }

}
