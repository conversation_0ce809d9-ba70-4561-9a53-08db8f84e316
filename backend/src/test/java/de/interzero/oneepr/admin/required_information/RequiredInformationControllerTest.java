package de.interzero.oneepr.admin.required_information;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.criteria.Criteria;
import de.interzero.oneepr.admin.criteria.CriteriaRepository;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.required_information.dto.CreateRequiredInformationDto;
import de.interzero.oneepr.admin.required_information.dto.UpdateRequiredInformationDto;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.UUID;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link RequiredInformationController}.
 * This class validates the full HTTP request-response cycle.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class RequiredInformationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RequiredInformationRepository requiredInformationRepository;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private FilesRepository filesRepository;

    @Autowired
    private CriteriaRepository criteriaRepository;

    private Country testCountry;

    private Files testFile;

    private RequiredInformation testInfo;

    @BeforeEach
    void setUp() {
        criteriaRepository.deleteAll();
        requiredInformationRepository.deleteAll();
        countryRepository.deleteAll();
        filesRepository.deleteAll();

        testCountry = createAndSaveTestCountry();
        testFile = createAndSaveTestFile();
        testInfo = createAndSaveTestRequiredInformation(testCountry);
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldCreateNewInformation() throws Exception {
        CreateRequiredInformationDto createDto = new CreateRequiredInformationDto();
        createDto.setName("Trade Register Extract");
        createDto.setDescription("A recent copy of the trade register extract.");
        createDto.setType(RequiredInformation.Type.DOCUMENT);
        createDto.setCountryId(testCountry.getId());
        createDto.setFileId(testFile.getId());

        mockMvc.perform(post(Api.ADMIN_REQUIRED_INFORMATIONS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name", is("Trade Register Extract")))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())))
                .andExpect(jsonPath("$.file_id", is(testFile.getId())));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_shouldReturnListOfInformation() throws Exception {
        mockMvc.perform(get(Api.ADMIN_REQUIRED_INFORMATIONS))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testInfo.getId())));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnCorrectInformation() throws Exception {
        mockMvc.perform(get(Api.ADMIN_REQUIRED_INFORMATIONS + "/{id}", testInfo.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testInfo.getId())))
                .andExpect(jsonPath("$.name", is("VAT ID")));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void update_shouldModifyExistingInformation() throws Exception {
        UpdateRequiredInformationDto updateDto = new UpdateRequiredInformationDto();
        updateDto.setName("Updated VAT ID Number");
        updateDto.setQuestion("What is your new VAT ID?");

        mockMvc.perform(put(
                        Api.ADMIN_REQUIRED_INFORMATIONS + "/{id}",
                        testInfo.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testInfo.getId())))
                .andExpect(jsonPath("$.name", is("Updated VAT ID Number")))
                .andExpect(jsonPath("$.question", is("What is your new VAT ID?")));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void remove_shouldSoftDeleteInformationAndCriteria() throws Exception {
        // Setup: Create a child criteria linked to the main test info
        Criteria childCriteria = createAndSaveTestCriteria(testInfo);

        // Action: Delete the parent information
        mockMvc.perform(delete(Api.ADMIN_REQUIRED_INFORMATIONS + "/{id}", testInfo.getId())).andExpect(status().isOk());

        // Verification 1: The parent info should now be "not found" via the API
        mockMvc.perform(get(Api.ADMIN_REQUIRED_INFORMATIONS + "/{id}", testInfo.getId()))
                .andExpect(status().isNotFound());

        // Verification 2: Check database directly for soft-delete status
        RequiredInformation deletedInfo = requiredInformationRepository.findById(testInfo.getId()).orElseThrow();
        Criteria deletedCriteria = criteriaRepository.findById(childCriteria.getId()).orElseThrow();

        assertNotNull(deletedInfo.getDeletedAt());
        assertNotNull(deletedCriteria.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    private Country createAndSaveTestCountry() {
        Country country = new Country();
        country.setName("Testland");
        country.setCode("TL");
        country.setFlagUrl("http://example.com/flag.png");
        return countryRepository.saveAndFlush(country);
    }

    private Files createAndSaveTestFile() {
        Files file = new Files();
        file.setId(UUID.randomUUID().toString());
        file.setName("test-document.pdf");
        file.setOriginalName("test-document.pdf");
        file.setExtension("pdf");
        file.setSize("2048");
        file.setCreatorType("USER");
        file.setDocumentType("DOCUMENT");
        file.setCreatedAt(Instant.now());
        return filesRepository.saveAndFlush(file);
    }

    private RequiredInformation createAndSaveTestRequiredInformation(Country country) {
        RequiredInformation info = new RequiredInformation();
        info.setName("VAT ID");
        info.setDescription("Description for " + "VAT ID");
        info.setType(RequiredInformation.Type.DOCUMENT);
        info.setCountry(country);
        return requiredInformationRepository.saveAndFlush(info);
    }

    private Criteria createAndSaveTestCriteria(RequiredInformation requiredInformation) {
        Criteria criteria = new Criteria();
        criteria.setTitle("Sub-criteria for " + requiredInformation.getName());
        criteria.setMode(Criteria.Mode.CALCULATOR);
        criteria.setType(Criteria.Type.PACKAGING_SERVICE);
        criteria.setRequiredInformation(requiredInformation);
        criteria.setCountry(requiredInformation.getCountry());
        return criteriaRepository.saveAndFlush(criteria);
    }
}