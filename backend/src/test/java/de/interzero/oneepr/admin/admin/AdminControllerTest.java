package de.interzero.oneepr.admin.admin;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.admin.admin.dto.CreateAdminDto;
import de.interzero.oneepr.admin.admin.dto.UpdateAdminDto;
import de.interzero.oneepr.auth.role.Role;
import de.interzero.oneepr.auth.role.RoleRepository;
import de.interzero.oneepr.auth.user.User;
import de.interzero.oneepr.auth.user.UserRepository;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class AdminControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        userRepository.deleteAll();
    }

    /**
     * Integration test for {@link AdminController#create(CreateAdminDto)}.
     * Verifies that a valid admin user is created successfully.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_shouldCreateAdminUser() throws Exception {
        CreateAdminDto dto = new CreateAdminDto();
        dto.setEmail("<EMAIL>");
        dto.setName("New Admin");
        dto.setPassword("securePass123");
        dto.setRoleId(roleRepository.findByName("ADMIN").orElseThrow().getId());

        String jsonRequest = objectMapper.writeValueAsString(dto);

        ResultActions result = mockMvc.perform(post(Api.ADMIN).contentType(MediaType.APPLICATION_JSON)
                                                       .content(jsonRequest)).andExpect(status().isOk()).andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(jsonResponse);

        assertNotNull(jsonNode.get("id"));
        assertEquals(dto.getEmail(), jsonNode.get("email").asText());
        assertEquals(dto.getName(), jsonNode.get("name").asText());
        assertTrue(jsonNode.get("is_active").asBoolean());
        assertEquals(dto.getRoleId().intValue(), jsonNode.get("role").get("id").asInt());
    }

    /**
     * {@link AdminController#create(CreateAdminDto)} should return 401 Unauthorized
     * if request is made without authentication.
     *
     * @throws Exception if request fails
     */
    @Test
    void create_shouldReturnUnauthorizedIfNotAuthenticated() throws Exception {
        CreateAdminDto dto = new CreateAdminDto();
        dto.setEmail("<EMAIL>");
        dto.setName("Unauthorized Admin");
        dto.setPassword("securePass123");
        dto.setRoleId(2);

        String jsonRequest = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.ADMIN).contentType(MediaType.APPLICATION_JSON).content(jsonRequest))
                .andExpect(status().isUnauthorized())
                .andDo(print());
    }

    /**
     * {@link AdminController#update(Integer, UpdateAdminDto)} should update admin user successfully.
     *
     * @throws Exception if request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldUpdateAdminSuccessfully() throws Exception {
        Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("Original Name");
        user.setPassword(passwordEncoder.encode("originalPass"));
        user.setRole(adminRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);

        UpdateAdminDto updateDto = new UpdateAdminDto();
        updateDto.setName("Updated Admin Name");
        updateDto.setEmail("<EMAIL>");
        updateDto.setRoleId(adminRole.getId());

        String jsonRequest = objectMapper.writeValueAsString(updateDto);

        ResultActions result = mockMvc.perform(patch(Api.ADMIN + "/" + user.getId()).contentType(MediaType.APPLICATION_JSON)
                                                       .content(jsonRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Updated Admin Name"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.password").doesNotExist())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        assertFalse(jsonResponse.contains("<EMAIL>"));
        assertFalse(jsonResponse.contains("Original Name"));
    }

    /**
     * {@link AdminController#findAll(String, String, String)} should return list of admin users
     * when requester has valid admin role (SUPER_ADMIN / ADMIN / CLERK).
     *
     * @throws Exception if request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_shouldReturnAdminUsersWithValidRole() throws Exception {
        Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();
        Role clerkRole = roleRepository.findByName("CLERK").orElseThrow();

        User admin = new User();
        admin.setEmail("<EMAIL>");
        admin.setName("Admin One");
        admin.setPassword(passwordEncoder.encode("pass"));
        admin.setRole(adminRole);
        admin.setIsActive(true);
        admin.setStatus(User.Status.COMPLETE);

        User clerk = new User();
        clerk.setEmail("<EMAIL>");
        clerk.setName("Clerk One");
        clerk.setPassword(passwordEncoder.encode("pass"));
        clerk.setRole(clerkRole);
        clerk.setIsActive(true);
        clerk.setStatus(User.Status.COMPLETE);

        userRepository.saveAll(List.of(admin, clerk));

        ResultActions result = mockMvc.perform(get(Api.ADMIN).param("role", "ADMIN,CLERK")
                                                       .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        assertTrue(jsonResponse.contains("<EMAIL>"));
        assertTrue(jsonResponse.contains("<EMAIL>"));
    }

    /**
     * Test for retrieving a single admin user by ID.
     * Verifies that the correct user is returned with all expected fields.
     * Endpoint: GET /admin/{id}
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findOne_shouldReturnAdminById() throws Exception {
        Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();

        User admin = new User();
        admin.setEmail("<EMAIL>");
        admin.setName("Admin One");
        admin.setPassword(passwordEncoder.encode("testpass"));
        admin.setRole(adminRole);
        admin.setIsActive(true);
        admin.setStatus(User.Status.COMPLETE);

        userRepository.save(admin);
        userRepository.flush();

        ResultActions result = mockMvc.perform(get(Api.ADMIN + "/" + admin.getId()).contentType(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(admin.getId()))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.name").value("Admin One"))
                .andExpect(jsonPath("$.is_active").value(true))
                .andExpect(jsonPath("$.role.name").value("ADMIN"));
    }

    /**
     * Test for deleting an admin user by ID.
     * Verifies that the user is removed and endpoint returns 204 No Content.
     * Endpoint: DELETE /admin/{id}
     */
    @Test
    @WithMockUser(roles = TestRole.SUPER_ADMIN)
    void delete_shouldRemoveAdminById() throws Exception {
        Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();

        User admin = new User();
        admin.setEmail("<EMAIL>");
        admin.setName("Delete Admin");
        admin.setPassword(passwordEncoder.encode("pass"));
        admin.setRole(adminRole);
        admin.setIsActive(true);
        admin.setStatus(User.Status.COMPLETE);

        userRepository.save(admin);
        userRepository.flush();

        mockMvc.perform(delete(Api.ADMIN + "/" + admin.getId()).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        assertThat(userRepository.findById(admin.getId())).isEmpty();
    }

}
