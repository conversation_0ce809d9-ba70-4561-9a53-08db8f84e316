package de.interzero.oneepr.admin.report_set_fraction;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.fraction_icon.FractionIcon;
import de.interzero.oneepr.admin.fraction_icon.FractionIconRepository;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.admin.report_set.ReportSet;
import de.interzero.oneepr.admin.report_set.ReportSetRepository;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFractionController;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFractionRepository;
import de.interzero.oneepr.admin.report_set_fractions.dto.CreateReportSetFractionDto;
import de.interzero.oneepr.admin.report_set_fractions.dto.UpdateReportSetFractionDto;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.UUID;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link ReportSetFractionController}.
 * This class validates the full HTTP request-response cycle for the report set fraction module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ReportSetFractionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ReportSetRepository reportSetRepository;

    @Autowired
    private ReportSetFractionRepository reportSetFractionRepository;

    @Autowired
    private FractionIconRepository fractionIconRepository;

    @Autowired
    private FilesRepository filesRepository;

    @Autowired
    private PackagingServiceRepository packagingServiceRepository;

    @Autowired
    private CountryRepository countryRepository;

    private ReportSet testReportSet;

    private FractionIcon testFractionIcon;

    private ReportSetFraction testReportSetFraction;

    /**
     * Sets up a consistent and valid database state before each test method runs.
     */
    @BeforeEach
    void setUp() {
        // Clear repositories in reverse order of dependency to avoid constraint violations
        reportSetFractionRepository.deleteAll();
        fractionIconRepository.deleteAll();
        filesRepository.deleteAll();
        reportSetRepository.deleteAll();
        packagingServiceRepository.deleteAll();
        countryRepository.deleteAll();

        // Create the required chain of entities
        Country country = createAndSaveTestCountry();
        PackagingService packagingService = createAndSaveTestPackagingService(country);
        testReportSet = createAndSaveTestReportSet(packagingService);
        Files file = createAndSaveTestFile();
        testFractionIcon = createAndSaveTestFractionIcon(file);
        testReportSetFraction = createAndSaveTestReportSetFraction(
                "Top Level Fraction",
                testReportSet,
                testFractionIcon,
                null);
    }

    /**
     * Verifies that a POST request successfully creates a new report set fraction.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldCreateNewFraction() throws Exception {
        CreateReportSetFractionDto createDto = new CreateReportSetFractionDto();
        createDto.setName("New Test Fraction");
        createDto.setDescription("A new fraction for testing");
        createDto.setIcon("paper");
        createDto.setReportSetId(testReportSet.getId());
        createDto.setFractionIconId(testFractionIcon.getId());
        createDto.setIsActive(true);

        mockMvc.perform(post(Api.REPORT_SET_FRACTIONS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name", is("New Test Fraction")))
                .andExpect(jsonPath("$.report_set_id", is(testReportSet.getId())));
    }

    /**
     * Verifies that a GET request returns all active report set fractions.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_shouldReturnListOfFractions() throws Exception {
        mockMvc.perform(get(Api.REPORT_SET_FRACTIONS))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testReportSetFraction.getId())));
    }

    /**
     * Verifies that a GET request for a specific ID returns the correct fraction.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnCorrectFraction() throws Exception {
        mockMvc.perform(get(Api.REPORT_SET_FRACTIONS + "/{id}", testReportSetFraction.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testReportSetFraction.getId())))
                .andExpect(jsonPath("$.name", is("Top Level Fraction")));
    }

    /**
     * Verifies that a PUT request successfully updates an existing fraction.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void update_shouldModifyExistingFraction() throws Exception {
        UpdateReportSetFractionDto updateDto = new UpdateReportSetFractionDto();
        updateDto.setName("Updated Fraction Name");
        updateDto.setIsActive(false);

        mockMvc.perform(put(
                        Api.REPORT_SET_FRACTIONS + "/{id}",
                        testReportSetFraction.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testReportSetFraction.getId())))
                .andExpect(jsonPath("$.name", is("Updated Fraction Name")))
                .andExpect(jsonPath("$.is_active", is(false)));
    }

    /**
     * Verifies that a DELETE request correctly soft-deletes a parent fraction and its direct children.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void remove_shouldSoftDeleteFractionAndItsChildren() throws Exception {
        // Setup: Create a child fraction linked to the main test fraction
        ReportSetFraction childFraction = createAndSaveTestReportSetFraction(
                "Child Fraction",
                testReportSet,
                testFractionIcon,
                testReportSetFraction);

        // Action: Delete the parent fraction
        mockMvc.perform(delete(Api.REPORT_SET_FRACTIONS + "/{id}", testReportSetFraction.getId()))
                .andExpect(status().isOk());

        // Verification 1: The parent should now be "not found" via the API
        mockMvc.perform(get(Api.REPORT_SET_FRACTIONS + "/{id}", testReportSetFraction.getId()))
                .andExpect(status().isNotFound());

        // Verification 2: The child should also be "not found" via the API
        mockMvc.perform(get(Api.REPORT_SET_FRACTIONS + "/{id}", childFraction.getId()))
                .andExpect(status().isNotFound());

        // Verification 3: Both should have their deletedAt timestamp set in the database
        ReportSetFraction deletedParent = reportSetFractionRepository.findById(testReportSetFraction.getId())
                .orElseThrow();
        ReportSetFraction deletedChild = reportSetFractionRepository.findById(childFraction.getId()).orElseThrow();

        assertNotNull(deletedParent.getDeletedAt());
        assertNotNull(deletedChild.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    private Country createAndSaveTestCountry() {
        Country country = new Country();
        country.setName("Testland");
        country.setCode("TL");
        country.setFlagUrl("http://example.com/flag.png");
        country.setCreatedAt(Instant.now());
        return countryRepository.saveAndFlush(country);
    }

    private PackagingService createAndSaveTestPackagingService(Country country) {
        PackagingService service = new PackagingService();
        service.setName("Test Service");
        service.setDescription("A test packaging service");
        service.setCountry(country);
        return packagingServiceRepository.saveAndFlush(service);
    }

    private ReportSet createAndSaveTestReportSet(PackagingService packagingService) {
        ReportSet reportSet = new ReportSet();
        reportSet.setName("Annual Test Report Set");
        reportSet.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        reportSet.setType(ReportSet.ReportSetType.FRACTIONS);
        reportSet.setPackagingService(packagingService);
        return reportSetRepository.saveAndFlush(reportSet);
    }

    private Files createAndSaveTestFile() {
        Files file = new Files();
        file.setId(UUID.randomUUID().toString());
        file.setName("test-icon.svg");
        file.setOriginalName("test-icon.svg");
        file.setExtension("svg");
        file.setSize("1024");
        file.setCreatorType("SYSTEM");
        file.setDocumentType("ICON");
        file.setCreatedAt(Instant.now());
        return filesRepository.saveAndFlush(file);
    }

    private FractionIcon createAndSaveTestFractionIcon(Files file) {
        FractionIcon icon = new FractionIcon();
        icon.setImageUrl("http://example.com/icons/icon.svg");
        icon.setFile(file);
        return fractionIconRepository.saveAndFlush(icon);
    }

    private ReportSetFraction createAndSaveTestReportSetFraction(String name,
                                                                 ReportSet reportSet,
                                                                 FractionIcon icon,
                                                                 ReportSetFraction parent) {
        ReportSetFraction fraction = new ReportSetFraction();
        fraction.setName(name);
        fraction.setDescription("Description for " + name);
        fraction.setCode(UUID.randomUUID().toString());
        fraction.setReportSet(reportSet);
        fraction.setFractionIcon(icon);
        if (parent != null) {
            fraction.setParent(parent);
            fraction.setParentId(parent.getId());
        }
        return reportSetFractionRepository.saveAndFlush(fraction);
    }
}