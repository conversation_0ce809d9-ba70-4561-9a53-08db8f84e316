package de.interzero.oneepr.customer.coupon;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.coupon.dto.CreateCouponDto;
import de.interzero.oneepr.customer.coupon.dto.UpdateCouponDto;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import java.time.Instant;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * Integration tests for the {@link CouponController}.
 * <p>
 * This class uses {@link SpringBootTest} to load the full application context
 * and {@link AutoConfigureMockMvc} to configure {@link MockMvc} for sending HTTP requests
 * to the controller endpoints. It verifies the behavior of the coupon-related API,
 * including creation, retrieval (by ID and code), update, and soft deletion of coupon records.
 *
 * <p>
 * Each test method is designed to be independent, with common setup logic handled
 * in the {@link #setup()} method, which runs before each test. This setup includes
 * clearing the relevant database table and initializing a consistent set of coupon data
 * to ensure repeatable and isolated test scenarios.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CouponControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CouponRepository couponRepository;

    /**
     * Jackson ObjectMapper for serializing request DTOs to JSON and
     * deserializing JSON responses to DTOs or entities.
     */
    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        couponRepository.deleteAll();

        Coupon coupon1 = new Coupon();
        coupon1.setCode("DISCOUNT10");
        coupon1.setIsActive(true);
        coupon1.setType(Coupon.Type.SYSTEM);
        coupon1.setStartDate(Instant.now().minusSeconds(7200));
        coupon1.setEndDate(Instant.now().plusSeconds(7200));
        coupon1.setDiscountType(Coupon.DiscountType.PERCENTAGE);
        coupon1.setMode(Coupon.Mode.GENERAL);
        coupon1.setValue(10);
        coupon1.setRedeemableByNewCustomers(true);
        coupon1.setUsedAt(Instant.now().minusSeconds(3500));

        Coupon coupon2 = new Coupon();
        coupon2.setCode("FREESHIP");
        coupon2.setIsActive(true);
        coupon2.setType(Coupon.Type.SYSTEM);
        coupon2.setStartDate(Instant.now().minusSeconds(7200));
        coupon2.setEndDate(Instant.now().plusSeconds(7200));
        coupon2.setDiscountType(Coupon.DiscountType.ABSOLUTE);
        coupon2.setMode(Coupon.Mode.INDIVIDUAL);
        coupon2.setValue(5);
        coupon2.setRedeemableByNewCustomers(true);
        coupon2.setUsedAt(Instant.now().minusSeconds(7000));

        this.couponRepository.saveAll(List.of(coupon1, coupon2));
    }

    /**
     * Helper method to create and persist a {@link Coupon} entity for test purposes.
     *
     * @param code the coupon code
     * @return the persisted {@link Coupon} entity
     */
    private Coupon createAndSaveTestCoupon(String code) {
        Coupon coupon = new Coupon();
        coupon.setCode(code);
        coupon.setIsActive(true);
        coupon.setUsedAt(Instant.now().minusSeconds(3500));
        coupon.setStartDate(Instant.now().minusSeconds(7200));
        coupon.setEndDate(Instant.now().plusSeconds(7200));
        coupon.setDiscountType(Coupon.DiscountType.PERCENTAGE);
        coupon.setMode(Coupon.Mode.GENERAL);
        coupon.setType(Coupon.Type.SYSTEM);
        coupon.setRedeemableByNewCustomers(true);
        coupon.setValue(10);
        coupon.setForCommission(false);
        return couponRepository.save(coupon);
    }

    /**
     * Test for {@link CouponController#create(CreateCouponDto)}
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_shouldCreateNewCoupon() throws Exception {
        CreateCouponDto dto = new CreateCouponDto();
        dto.setCode("NEWYEAR2025");
        dto.setIsActive(true);
        dto.setType(Coupon.Type.SYSTEM);
        dto.setStartDate(Instant.now());
        dto.setEndDate(Instant.now().plusSeconds(86400));
        dto.setDiscountType(Coupon.DiscountType.PERCENTAGE);
        dto.setMode(Coupon.Mode.GENERAL);
        dto.setValue(10);
        dto.setRedeemableByNewCustomers(true);

        String jsonRequest = objectMapper.writeValueAsString(dto);

        ResultActions result = mockMvc.perform(post(Api.COUPON).contentType(MediaType.APPLICATION_JSON)
                                                       .content(jsonRequest))
                .andExpect(status().isCreated())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        Coupon created = objectMapper.readValue(jsonResponse, Coupon.class);

        assertNotNull(created.getId());
        assertEquals(dto.getCode(), created.getCode());
        assertTrue(created.getIsActive());
        assertNotNull(created.getCreatedAt());
        assertNotNull(created.getUpdatedAt());
    }

    /**
     * Test for {@link CouponController#findAll()}
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnAllCoupons() throws Exception {
        mockMvc.perform(get(Api.COUPON).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andDo(print());
    }

    /**
     * Test for {@link CouponController#findOne(Integer)}
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void findOne_shouldReturnSpecificCoupon() throws Exception {
        Coupon savedCoupon = createAndSaveTestCoupon("WELCOME25");

        ResultActions result = mockMvc.perform(get(Api.COUPON + "/" + savedCoupon.getId()).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        Coupon found = objectMapper.readValue(jsonResponse, Coupon.class);

        assertEquals(savedCoupon.getId(), found.getId());
        assertEquals(savedCoupon.getCode(), found.getCode());
        assertTrue(found.getIsActive());
    }

    /**
     * Test for {@link CouponController#findByCode(String)}
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void findByCode_shouldReturnCouponByCode() throws Exception {
        Coupon coupon = createAndSaveTestCoupon("WELCOME25");

        ResultActions result = mockMvc.perform(get(Api.COUPON + "/code/" + coupon.getCode()).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        Coupon found = objectMapper.readValue(jsonResponse, Coupon.class);

        assertEquals(coupon.getId(), found.getId());
        assertEquals(coupon.getCode(), found.getCode());
        assertTrue(found.getIsActive());
    }

    /**
     * Test for {@link CouponController#update(Integer, UpdateCouponDto)}
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldUpdateCoupon() throws Exception {
        Coupon existing = createAndSaveTestCoupon("TO_UPDATE");

        CreateCouponDto updateDto = new CreateCouponDto();
        updateDto.setCode("UPDATED2025");
        updateDto.setIsActive(false);
        updateDto.setType(Coupon.Type.SYSTEM);
        updateDto.setStartDate(Instant.now());
        updateDto.setEndDate(Instant.now().plusSeconds(86400));
        updateDto.setDiscountType(Coupon.DiscountType.ABSOLUTE);
        updateDto.setMode(Coupon.Mode.GENERAL);
        updateDto.setValue(20);
        updateDto.setRedeemableByNewCustomers(false);

        String jsonRequest = objectMapper.writeValueAsString(updateDto);

        ResultActions result = mockMvc.perform(patch(Api.COUPON + "/" + existing.getId()).contentType(MediaType.APPLICATION_JSON)
                                                       .content(jsonRequest)).andExpect(status().isOk()).andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        Coupon updated = objectMapper.readValue(jsonResponse, Coupon.class);

        assertEquals(updateDto.getCode(), updated.getCode());
        assertFalse(updated.getIsActive());
        assertEquals(updateDto.getValue(), updated.getValue());
    }

    /**
     * Test for {@link CouponController#remove(Integer)}
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void delete_shouldSoftDeleteCoupon() throws Exception {
        Coupon coupon = createAndSaveTestCoupon("DELETE_ME");

        ResultActions result = mockMvc.perform(delete(Api.COUPON + "/" + coupon.getId()).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        Coupon deletedCoupon = objectMapper.readValue(jsonResponse, Coupon.class);

        assertEquals(coupon.getId(), deletedCoupon.getId());
        assertNotNull(deletedCoupon.getDeletedAt());

        List<Coupon> remaining = couponRepository.findAll();
        assertTrue(remaining.stream().noneMatch(c -> c.getId().equals(coupon.getId()) && c.getDeletedAt() == null));
    }


}