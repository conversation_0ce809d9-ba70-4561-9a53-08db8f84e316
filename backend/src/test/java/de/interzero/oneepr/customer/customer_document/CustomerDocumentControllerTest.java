package de.interzero.oneepr.customer.customer_document;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_activity.CustomerActivityRepository;
import de.interzero.oneepr.customer.customer_document.dto.CreateCustomerDocumentDto;
import de.interzero.oneepr.customer.customer_document.dto.UpdateCustomerDocumentDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

import static org.hamcrest.Matchers.hasSize;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the {@link CustomerDocumentController}.
 * This class uses {@link SpringBootTest} to load the full application context
 * and {@link AutoConfigureMockMvc} to configure {@link MockMvc} for sending HTTP requests
 * to the controller endpoints. It verifies the behavior of the customer document API.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CustomerDocumentControllerTest {

    private static final String API_CUSTOMER_DOCUMENT_BASE_URL = Api.CUSTOMER_DOCUMENT;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CustomerDocumentRepository customerDocumentRepository;

    @Autowired
    private CustomerActivityRepository customerActivityRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private Customer testCustomer1; // Has userId 101

    private Customer testCustomer2; // Has userId 102

    /**
     * Sets up the test environment before each test method execution.
     */

    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.findAndRegisterModules();
        customerActivityRepository.deleteAllInBatch();
        customerDocumentRepository.deleteAllInBatch();
        customerRepository.deleteAllInBatch();
        // Create customers with known userIds that can be used in @WithMockUser(username=...)
        testCustomer1 = createAndSaveTestCustomer("<EMAIL>", "Alice", "DocOwner", "Ms.", 101);
        testCustomer2 = createAndSaveTestCustomer("<EMAIL>", "Bob", "Viewer", "Mr.", 102);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private Customer createAndSaveTestCustomer(String email,
                                               String firstName,
                                               String lastName,
                                               String salutation,
                                               Integer userId) {
        Customer customer = new Customer();
        customer.setEmail(email);
        customer.setFirstName(firstName);
        customer.setLastName(lastName);
        customer.setSalutation(salutation);
        customer.setUserId(userId);
        customer.setCreatedAt(Instant.now());
        customer.setUpdatedAt(Instant.now());
        customer.setType(Customer.Type.REGULAR);
        return customerRepository.saveAndFlush(customer);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private CustomerDocument createAndSaveTestCustomerDocument(String documentUrl,
                                                               String status,
                                                               Customer customer) {
        CustomerDocument document = new CustomerDocument();
        document.setDocumentUrl(documentUrl);
        document.setStatus(status);
        document.setCustomer(customer);
        return customerDocumentRepository.saveAndFlush(document);
    }

    /**
     * Test for {@link CustomerDocumentController#create(CreateCustomerDocumentDto)}.
     * Verifies successful creation of a customer document.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_shouldCreateCustomerDocument() throws Exception {
        CreateCustomerDocumentDto createDto = new CreateCustomerDocumentDto();
        createDto.setCustomerId(testCustomer1.getId());
        createDto.setDocumentUrl("https://example.com/docs/doc1.pdf");
        createDto.setStatus("UPLOADED");

        ResultActions resultActions = mockMvc.perform(post(API_CUSTOMER_DOCUMENT_BASE_URL).with(csrf())
                                                              .contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(createDto))
                                                              .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.document_url").value("https://example.com/docs/doc1.pdf"))
                .andExpect(jsonPath("$.status").value("UPLOADED"))
                .andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()));

        String responseString = resultActions.andReturn().getResponse().getContentAsString();
        CustomerDocument createdDocumentResponseObject = objectMapper.readValue(responseString, CustomerDocument.class);

        assertNotNull(createdDocumentResponseObject.getId());
        assertEquals("https://example.com/docs/doc1.pdf", createdDocumentResponseObject.getDocumentUrl());
        assertEquals("UPLOADED", createdDocumentResponseObject.getStatus());
        Optional<CustomerDocument> foundInDbOpt = customerDocumentRepository.findById(createdDocumentResponseObject.getId());
        assertTrue(foundInDbOpt.isPresent());
        CustomerDocument foundInDb = foundInDbOpt.get();
        assertNotNull(foundInDb.getCustomer(), "Customer should be associated in DB");
        assertEquals(testCustomer1.getId(), foundInDb.getCustomer().getId(), "Customer ID in DB should match");
        assertEquals("https://example.com/docs/doc1.pdf", foundInDb.getDocumentUrl());
        assertEquals("UPLOADED", foundInDb.getStatus());
    }

    /**
     * Test for {@link CustomerDocumentController#findAll()}.
     * Verifies retrieval of all customer documents.
     */
    @Test
    @WithMockUser(roles = {TestRole.CLERK})
    void findAll_shouldReturnAllCustomerDocuments() throws Exception {
        createAndSaveTestCustomerDocument("https://example.com/docs/report.pdf", "FINAL", testCustomer1);
        createAndSaveTestCustomerDocument("https://example.com/docs/invoice.pdf", "PAID", testCustomer2);

        mockMvc.perform(get(API_CUSTOMER_DOCUMENT_BASE_URL).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(2)));
    }

    /**
     * Test for {@link CustomerDocumentController#findOne(String)}.
     * Verifies retrieval by a CUSTOMER who owns the document.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.CUSTOMER}
    )
    // username "101" is testCustomer1's userId
    void findOne_shouldReturnCustomerDocument_whenCustomerOwnsDocument() throws Exception {
        CustomerDocument document = createAndSaveTestCustomerDocument(
                "https://example.com/docs/owned.pdf",
                "PRIVATE",
                testCustomer1);

        mockMvc.perform(get(
                        API_CUSTOMER_DOCUMENT_BASE_URL + "/{id}",
                        document.getId()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(document.getId()))
                .andExpect(jsonPath("$.document_url").value("https://example.com/docs/owned.pdf"))
                .andExpect(jsonPath("$.status").value("PRIVATE"))
                .andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()));
    }

    /**
     * Test for {@link CustomerDocumentController#findOne(String)}.
     * Verifies that a CUSTOMER cannot access a document they do not own.
     * This tests the permission logic within the service.
     */
    @Test
    @WithMockUser(
            username = "102",
            roles = {TestRole.CUSTOMER}
    )
    void findOne_shouldReturnForbidden_whenCustomerDoesNotOwnDocument() throws Exception {
        // Document belongs to testCustomer1 (userId 101)
        CustomerDocument documentOfCustomer1 = createAndSaveTestCustomerDocument(
                "https://example.com/docs/other.pdf",
                "CONFIDENTIAL",
                testCustomer1);

        // testCustomer2 (userId 102) tries to access it
        mockMvc.perform(get(
                        API_CUSTOMER_DOCUMENT_BASE_URL + "/{id}",
                        documentOfCustomer1.getId()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isForbidden()); // Expecting 403 Forbidden from the service's permission check
    }


    /**
     * Test for {@link CustomerDocumentController#update(String, UpdateCustomerDocumentDto)}.
     * Verifies update by an ADMIN. AuthUtil derives admin user.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void update_shouldUpdateCustomerDocument_whenAdmin() throws Exception {
        CustomerDocument originalDocument = createAndSaveTestCustomerDocument(
                "https://example.com/docs/old_version.pdf",
                "DRAFT",
                testCustomer1);

        UpdateCustomerDocumentDto updateDto = new UpdateCustomerDocumentDto();
        updateDto.setDocumentUrl("https://example.com/docs/new_version.pdf");
        updateDto.setStatus("REVIEWED");

        mockMvc.perform(put(API_CUSTOMER_DOCUMENT_BASE_URL + "/{id}", originalDocument.getId()).with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(originalDocument.getId()))
                .andExpect(jsonPath("$.document_url").value("https://example.com/docs/new_version.pdf"))
                .andExpect(jsonPath("$.status").value("REVIEWED"))
                .andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()));

        CustomerDocument updatedDocumentInDb = customerDocumentRepository.findById(originalDocument.getId())
                .orElseThrow();
        assertEquals("https://example.com/docs/new_version.pdf", updatedDocumentInDb.getDocumentUrl());
        assertEquals("REVIEWED", updatedDocumentInDb.getStatus());
    }

    /**
     * Test for {@link CustomerDocumentController#remove(String)}.
     * Verifies deletion by a SUPER_ADMIN. AuthUtil derives super admin user.
     * This is a hard delete as per service logic.
     */
    @Test
    @WithMockUser(
            username = "9999",
            roles = {TestRole.SUPER_ADMIN}
    )
    void remove_shouldDeleteCustomerDocument_whenSuperAdmin() throws Exception {
        CustomerDocument document = createAndSaveTestCustomerDocument(
                "https://example.com/docs/to_delete.pdf",
                "ARCHIVED",
                testCustomer1);
        assertTrue(customerDocumentRepository.existsById(document.getId()));

        mockMvc.perform(delete(API_CUSTOMER_DOCUMENT_BASE_URL + "/{id}", document.getId()).with(csrf())
                                .accept(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isOk());

        assertFalse(customerDocumentRepository.existsById(document.getId()), "Document should be deleted from DB");
    }

}