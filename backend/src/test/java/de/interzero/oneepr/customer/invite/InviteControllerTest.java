package de.interzero.oneepr.customer.invite;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.invite.dto.CreateInviteDto;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link InviteController}.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class InviteControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CustomerInvitationRepository customerInvitationRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private Customer testCustomer1;

    private Customer testCustomer2;

    private Customer testInvitedCustomer1;

    private CustomerInvitation testCustomerInvitation1;

    private CustomerInvitation testCustomerInvitation2;

    @BeforeEach
    void setup() {
        // create customer 1
        testCustomer1 = new Customer();
        testCustomer1.setFirstName("Customer");
        testCustomer1.setLastName("One");
        testCustomer1.setEmail("<EMAIL>");
        testCustomer1.setUserId(1);
        testCustomer1.setIsActive(true);
        testCustomer1.setCreatedAt(Instant.now());
        testCustomer1.setUpdatedAt(Instant.now());
        testCustomer1 = customerRepository.save(testCustomer1);

        // create customer 2
        testCustomer2 = new Customer();
        testCustomer2.setFirstName("Customer");
        testCustomer2.setLastName("Two");
        testCustomer2.setEmail("<EMAIL>");
        testCustomer2.setUserId(2);
        testCustomer2.setIsActive(true);
        testCustomer2.setCreatedAt(Instant.now());
        testCustomer2.setUpdatedAt(Instant.now());
        testCustomer2 = customerRepository.save(testCustomer2);

        // create invited customer 1
        testInvitedCustomer1 = new Customer();
        testInvitedCustomer1.setFirstName("Invited");
        testInvitedCustomer1.setLastName("One");
        testInvitedCustomer1.setEmail("<EMAIL>");
        testInvitedCustomer1.setUserId(3);
        testInvitedCustomer1.setIsActive(true);
        testInvitedCustomer1.setCreatedAt(Instant.now());
        testInvitedCustomer1.setUpdatedAt(Instant.now());
        testInvitedCustomer1 = customerRepository.save(testInvitedCustomer1);

        // create invited customer 2
        Customer testInvitedCustomer2 = new Customer();
        testInvitedCustomer2.setFirstName("Invited");
        testInvitedCustomer2.setLastName("Two");
        testInvitedCustomer2.setEmail("<EMAIL>");
        testInvitedCustomer2.setUserId(4);
        testInvitedCustomer2.setIsActive(true);
        testInvitedCustomer2.setCreatedAt(Instant.now());
        testInvitedCustomer2.setUpdatedAt(Instant.now());
        testInvitedCustomer2 = customerRepository.save(testInvitedCustomer2);

        // create CustomerInvitation1 linked to customer 1
        testCustomerInvitation1 = new CustomerInvitation();
        testCustomerInvitation1.setComissionDate(Instant.now());
        testCustomerInvitation1.setProduct("Test Product 1");
        testCustomerInvitation1.setComission(new BigDecimal("100.00"));
        testCustomerInvitation1.setOrderNumber("ORD-12345");
        testCustomerInvitation1.setLeadSource("Test Lead Source 1");
        testCustomerInvitation1.setCustomer(testCustomer1);
        testCustomerInvitation1.setInvitedCustomer(testInvitedCustomer1);
        testCustomerInvitation1 = customerInvitationRepository.save(testCustomerInvitation1);

        // create CustomerInvitation2 linked to customer 2
        testCustomerInvitation2 = new CustomerInvitation();
        testCustomerInvitation2.setComissionDate(Instant.now());
        testCustomerInvitation2.setProduct("Test Product 2");
        testCustomerInvitation2.setComission(new BigDecimal("200.00"));
        testCustomerInvitation2.setOrderNumber("ORD-67890");
        testCustomerInvitation2.setLeadSource("Test Lead Source 2");
        testCustomerInvitation2.setCustomer(testCustomer2);
        testCustomerInvitation2.setInvitedCustomer(testInvitedCustomer2);
        testCustomerInvitation2 = customerInvitationRepository.save(testCustomerInvitation2);
    }

    @AfterEach
    void tearDown() {
        customerInvitationRepository.deleteAllInBatch();
        customerRepository.deleteAllInBatch();
    }

    /**
     * Test for {@link InviteController#findAll(Integer)}.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnBasedOnGivenId() throws Exception {
        // confirm there are two invitations in the database
        assertEquals(2, customerInvitationRepository.count());

        // retrieve invitations for testCustomer1 and assert
        ResultActions firstInvite = mockMvc.perform(get(Api.INVITE + "/" + testCustomer1.getId()).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        firstInvite.andExpect(jsonPath("length()").value(1));
        firstInvite.andExpect(jsonPath("$[0].id").value(testCustomerInvitation1.getId()));

        // retrieve invitations for testCustomer2 and assert
        ResultActions secondInvite = mockMvc.perform(get(Api.INVITE + "/" + testCustomer2.getId()).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        secondInvite.andExpect(jsonPath("length()").value(1));
        secondInvite.andExpect(jsonPath("$[0].id").value(testCustomerInvitation2.getId()));
    }


    /**
     * Test for {@link InviteController#create(CreateInviteDto)}.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_shouldCreateCustomerInvitation() throws Exception {
        // delete all invites to start fresh
        customerInvitationRepository.deleteAllInBatch();
        assertEquals(0, customerInvitationRepository.count());

        // create a new invitation
        CreateInviteDto dto = new CreateInviteDto();
        dto.setProduct("New Product");
        dto.setCommission(new BigDecimal("250.00"));
        dto.setOrderNumber("ORD-67890");
        dto.setLeadSource("New Lead Source");
        dto.setCustomerId(testCustomer1.getId());
        dto.setInvitedCustomerId(testInvitedCustomer1.getId());

        ResultActions result = mockMvc.perform(post(Api.INVITE).contentType(MediaType.APPLICATION_JSON)
                                                       .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isCreated());

        result.andExpect(jsonPath("id").exists());
        result.andExpect(jsonPath("$.product").value(dto.getProduct()));
        result.andExpect(jsonPath("$.comission").value(dto.getCommission().doubleValue()));
        result.andExpect(jsonPath("$.order_number").value(dto.getOrderNumber()));
        result.andExpect(jsonPath("$.lead_source").value(dto.getLeadSource()));
        result.andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()));
        result.andExpect(jsonPath("$.invited_customer_id").value(testInvitedCustomer1.getId()));
    }
}
