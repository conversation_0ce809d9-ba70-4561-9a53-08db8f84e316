package de.interzero.oneepr.customer.customer_activity;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.action_guide.ActionGuideRepository;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_activity.dto.CreateCustomerActivityDto;
import de.interzero.oneepr.customer.customer_activity.dto.FindAllCustomerActivityDto;
import de.interzero.oneepr.customer.customer_activity.dto.UpdateCustomerActivityDto;
import de.interzero.oneepr.customer.customer_document.CustomerDocumentRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.not;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link CustomerActivityController}.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CustomerActivityControllerTest {

    private static final String API_CUSTOMER_ACTIVITY_BASE_URL = Api.CUSTOMER_ACTIVITY;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CustomerActivityRepository customerActivityRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CustomerDocumentRepository customerDocumentRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private ActionGuideRepository actionGuideRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private Customer testCustomer1;

    private Customer testCustomer2;

    /**
     * Sets up the test environment before each test method execution.
     */
    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.findAndRegisterModules();
        actionGuideRepository.deleteAllInBatch();
        contractRepository.deleteAllInBatch();
        customerActivityRepository.deleteAllInBatch();
        customerDocumentRepository.deleteAllInBatch();
        customerRepository.deleteAllInBatch();


        testCustomer1 = createAndSaveTestCustomer("<EMAIL>", "Alice", "One", "Ms.", 101);
        testCustomer2 = createAndSaveTestCustomer("<EMAIL>", "Bob", "Two", "Mr.", 102);
    }

    /**
     * Creates and persists a new {@link Customer} entity with specified details for testing purposes.
     * Sets current timestamps for creation and update, and a default customer type.
     *
     * @param email      The email address for the test customer.
     * @param firstName  The first name of the test customer.
     * @param lastName   The last name of the test customer.
     * @param salutation The salutation for the test customer (e.g., Mr., Ms.).
     * @param userId     The user ID to associate with this test customer.
     * @return The persisted {@link Customer} entity, flushed to the database.
     */
    private Customer createAndSaveTestCustomer(String email,
                                               String firstName,
                                               String lastName,
                                               String salutation,
                                               Integer userId) {
        Customer customer = new Customer();
        customer.setEmail(email);
        customer.setFirstName(firstName);
        customer.setLastName(lastName);
        customer.setSalutation(salutation);
        customer.setUserId(userId);
        customer.setCreatedAt(Instant.now());
        customer.setUpdatedAt(Instant.now());
        customer.setType(Customer.Type.REGULAR);
        return customerRepository.saveAndFlush(customer);
    }

    /**
     * Creates and persists a new {@link CustomerActivity} entity with specified details for testing purposes.
     * Associates the activity with a given customer and sets timestamps to one hour in the past.
     *
     * @param type        The {@link CustomerActivity.Type} for the test activity.
     * @param metadataMap A map containing metadata for the test activity. Assumes the entity's metadata field can accept this (e.g., it's Map or converted to JSON String by Hibernate).
     * @param customer    The {@link Customer} to associate with this test activity.
     * @return The persisted {@link CustomerActivity} entity, flushed to the database.
     */
    private CustomerActivity createAndSaveTestCustomerActivity(CustomerActivity.Type type,
                                                               Map<String, Object> metadataMap,
                                                               Customer customer) {
        CustomerActivity activity = new CustomerActivity();
        activity.setType(type);
        activity.setMetadata(metadataMap);
        activity.setCustomer(customer);
        activity.setCreatedAt(Instant.now().minusSeconds(3600));
        activity.setUpdatedAt(Instant.now().minusSeconds(3600));
        return customerActivityRepository.saveAndFlush(activity);
    }

    /**
     * Test for {@link CustomerActivityController#create(CreateCustomerActivityDto)}.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_shouldCreateCustomerActivity() throws Exception {
        CreateCustomerActivityDto createDto = new CreateCustomerActivityDto();
        createDto.setCustomerId(testCustomer1.getId());
        createDto.setType(CustomerActivity.Type.ACCOUNT_LOGIN);
        Map<String, Object> metadataMap = new HashMap<>();
        metadataMap.put("ipAddress", "***********");
        metadataMap.put("userAgent", "Test Browser");
        createDto.setMetadata(metadataMap);

        ResultActions resultActions = mockMvc.perform(post(API_CUSTOMER_ACTIVITY_BASE_URL).with(csrf())
                                                              .contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(createDto))
                                                              .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.type").value(CustomerActivity.Type.ACCOUNT_LOGIN.toString()))
                .andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()))
                .andExpect(jsonPath("$.metadata.ipAddress").value("***********"))
                .andExpect(jsonPath("$.metadata.userAgent").value("Test Browser"));

        String responseString = resultActions.andReturn().getResponse().getContentAsString();
        CustomerActivity createdActivity = objectMapper.readValue(responseString, CustomerActivity.class);

        assertNotNull(createdActivity.getId());
        assertEquals(CustomerActivity.Type.ACCOUNT_LOGIN, createdActivity.getType());
        assertNotNull(createdActivity.getCreatedAt());
        assertNotNull(createdActivity.getUpdatedAt());
        assertNull(createdActivity.getDeletedAt());
        assertEquals(metadataMap, createdActivity.getMetadata());

        Optional<CustomerActivity> foundInDbOpt = customerActivityRepository.findById(createdActivity.getId());
        assertTrue(foundInDbOpt.isPresent());
        CustomerActivity foundInDb = foundInDbOpt.get();
        assertEquals(metadataMap, foundInDb.getMetadata());
        assertEquals(testCustomer1.getId(), foundInDb.getCustomer().getId());
    }

    /**
     * Test for {@link CustomerActivityController#findAll(FindAllCustomerActivityDto)} without filters.
     *
     * @ts-legacy Controller uses @RequestBody for FindAllCustomerActivityDto on GET.
     */
    @Test
    @WithMockUser(roles = {TestRole.CLERK})
    void findAll_shouldReturnAllCustomerActivities_whenNoFilter() throws Exception {
        createAndSaveTestCustomerActivity(
                CustomerActivity.Type.DOCUMENT_DOWNLOAD,
                Collections.singletonMap("doc", "report.pdf"),
                testCustomer1);
        createAndSaveTestCustomerActivity(
                CustomerActivity.Type.ACCOUNT_UPDATE_EMAIL,
                Collections.singletonMap("new_email", "<EMAIL>"),
                testCustomer2);

        FindAllCustomerActivityDto emptyQuery = new FindAllCustomerActivityDto();

        mockMvc.perform(get(API_CUSTOMER_ACTIVITY_BASE_URL).with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(emptyQuery))
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)));
    }

    /**
     * Test for {@link CustomerActivityController#findAll(FindAllCustomerActivityDto)} with customerId filter.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void findAll_shouldReturnFilteredCustomerActivities_byCustomerId() throws Exception {
        createAndSaveTestCustomerActivity(CustomerActivity.Type.ACCOUNT_LOGIN, Collections.emptyMap(), testCustomer1);
        createAndSaveTestCustomerActivity(CustomerActivity.Type.REPORT_ADD, Collections.emptyMap(), testCustomer1);
        createAndSaveTestCustomerActivity(CustomerActivity.Type.ACCOUNT_LOGIN, Collections.emptyMap(), testCustomer2);

        FindAllCustomerActivityDto queryDto = new FindAllCustomerActivityDto();
        queryDto.setCustomerId(testCustomer1.getId());

        mockMvc.perform(get(API_CUSTOMER_ACTIVITY_BASE_URL).with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(queryDto))
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].customer_id").value(testCustomer1.getId()))
                .andExpect(jsonPath("$[1].customer_id").value(testCustomer1.getId()));
    }

    /**
     * Test for {@link CustomerActivityController#findOne(String)}.
     * Relies on AuthUtil deriving AuthenticatedUser from @WithMockUser's username.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.CUSTOMER}
    )
    void findOne_shouldReturnCustomerActivity_whenCustomerOwnsActivity() throws Exception {
        Map<String, Object> metadata = Collections.singletonMap("detail", "password updated by user");
        CustomerActivity activity = createAndSaveTestCustomerActivity(
                CustomerActivity.Type.ACCOUNT_UPDATE_PASSWORD,
                metadata,
                testCustomer1);

        mockMvc.perform(get(
                        API_CUSTOMER_ACTIVITY_BASE_URL + "/{id}",
                        activity.getId()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(activity.getId()))
                .andExpect(jsonPath("$.type").value(CustomerActivity.Type.ACCOUNT_UPDATE_PASSWORD.toString()))
                .andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()))
                .andExpect(jsonPath("$.metadata.detail").value("password updated by user"));
    }

    /**
     * Test for {@link CustomerActivityController#update(String, UpdateCustomerActivityDto)}.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void update_shouldUpdateCustomerActivity_whenAdmin() throws Exception {
        CustomerActivity originalActivity = createAndSaveTestCustomerActivity(
                CustomerActivity.Type.DOCUMENT_UPLOAD,
                Collections.singletonMap(
                        "docName",
                        "old.pdf"),
                testCustomer1);
        Instant initialUpdatedAt = customerActivityRepository.findById(originalActivity.getId())
                .orElseThrow()
                .getUpdatedAt();


        UpdateCustomerActivityDto updateDto = new UpdateCustomerActivityDto();
        updateDto.setType(CustomerActivity.Type.DOCUMENT_ANSWER);
        Map<String, Object> newMetadata = new HashMap<>();
        newMetadata.put("docName", "new_answer.pdf");
        newMetadata.put("status", "answered");
        updateDto.setMetadata(newMetadata);

        mockMvc.perform(put(API_CUSTOMER_ACTIVITY_BASE_URL + "/{id}", originalActivity.getId()).with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(originalActivity.getId()))
                .andExpect(jsonPath("$.type").value(CustomerActivity.Type.DOCUMENT_ANSWER.toString()))
                .andExpect(jsonPath("$.metadata.docName").value("new_answer.pdf"))
                .andExpect(jsonPath("$.metadata.status").value("answered"))
                .andExpect(jsonPath("$.updated_at").value(not(initialUpdatedAt.toString())));

        CustomerActivity updatedActivityInDb = customerActivityRepository.findById(originalActivity.getId())
                .orElseThrow();
        assertEquals(CustomerActivity.Type.DOCUMENT_ANSWER, updatedActivityInDb.getType());
        assertEquals(newMetadata, updatedActivityInDb.getMetadata());
        assertTrue(updatedActivityInDb.getUpdatedAt().isAfter(initialUpdatedAt));
    }

    /**
     * Test for {@link CustomerActivityController#remove(String)}.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.SUPER_ADMIN}
    )
    void remove_shouldSoftDeleteCustomerActivity_whenSuperAdmin() throws Exception {
        CustomerActivity activity = createAndSaveTestCustomerActivity(
                CustomerActivity.Type.CONTRACT_TERMINATION,
                Collections.emptyMap(),
                testCustomer1);
        assertNull(activity.getDeletedAt());

        mockMvc.perform(delete(API_CUSTOMER_ACTIVITY_BASE_URL + "/{id}", activity.getId()).with(csrf())
                                .accept(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isOk());

        CustomerActivity deletedActivityInDb = customerActivityRepository.findById(activity.getId()).orElseThrow();
        assertNotNull(deletedActivityInDb.getDeletedAt(), "deletedAt timestamp should be set after soft delete.");

        mockMvc.perform(get(API_CUSTOMER_ACTIVITY_BASE_URL + "/{id}", activity.getId()).with(csrf())
                                .accept(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isNotFound());
    }
}