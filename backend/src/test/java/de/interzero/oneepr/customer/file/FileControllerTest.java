package de.interzero.oneepr.customer.file;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.file.dto.CreateFileDto;
import de.interzero.oneepr.customer.file.dto.LambdaPresignedResponseDto;
import de.interzero.oneepr.customer.file.dto.RequestPresignedUrlDto;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.mockito.Mockito.when;


@SpringBootTest
@AutoConfigureMockMvc
@TestPropertySource(
        properties = {"file.lambda.request-presigned-url=http://localhost:8089/presigned-url", "file.lambda.download-file-url=http://localhost:8089/download"}
)
@Transactional
class FileControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FileRepository fileRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private WireMockServer wireMockServer;

    private File testFile;

    @BeforeEach
    @Transactional
    void setUp() {
        // Start WireMock server
        wireMockServer = new WireMockServer(8089);
        wireMockServer.start();
        WireMock.configureFor("localhost", 8089);

        // Setup test file
        testFile = new File();
        testFile.setId("test-file-id");
        testFile.setName("CONTRACT/2024/07/07/ABC12345-test-file.pdf");
        testFile.setOriginalName("test-file.pdf");
        testFile.setExtension("application/pdf");
        testFile.setSize("1024");
        testFile.setType(File.Type.CONTRACT);
        testFile.setUserId("test-user-id");
        testFile.setCreatedAt(Instant.now());
        testFile.setUpdatedAt(Instant.now());
    }

    @AfterEach
    void tearDown() {
        if (wireMockServer != null) {
            wireMockServer.stop();
        }
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void getFileUsingRBAC_ShouldReturnFile() throws Exception {
        // Mock file repository to return test file
        when(fileRepository.findById("test-file-id")).thenReturn(Optional.of(testFile));

        // Mock S3 download response
        byte[] fileContent = "test file content".getBytes();
        wireMockServer.stubFor(WireMock.get(urlPathEqualTo("/download"))
                                       .withQueryParam("fileName", equalTo(testFile.getName()))
                                       .willReturn(aResponse().withStatus(200)
                                                           .withHeader("Content-Type", "application/octet-stream")
                                                           .withBody(fileContent)));

        mockMvc.perform(MockMvcRequestBuilders.get("/customer/files/test-file-id"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.header().string("Content-Type", "application/pdf"))
                .andExpect(MockMvcResultMatchers.header()
                                   .string("Content-Disposition", "attachment; filename=\"test-file.pdf\""))
                .andExpect(MockMvcResultMatchers.content().bytes(fileContent));
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void requestUrl_ShouldReturnPresignedUrl() throws Exception {
        RequestPresignedUrlDto requestDto = new RequestPresignedUrlDto();
        requestDto.setFilename("test.pdf");
        requestDto.setFileType("application/pdf");

        // Mock presigned URL response
        LambdaPresignedResponseDto mockResponse = new LambdaPresignedResponseDto();
        mockResponse.setUploadUrl("http://localhost:8089/s3-upload");
        Map<String, Object> fields = new HashMap<>();
        fields.put("key", "test-key");
        fields.put("policy", "test-policy");
        mockResponse.setFields(fields);

        wireMockServer.stubFor(post(urlPathEqualTo("/presigned-url")).withRequestBody(equalToJson(objectMapper.writeValueAsString(
                        requestDto)))
                                       .willReturn(aResponse().withStatus(200)
                                                           .withHeader("Content-Type", "application/json")
                                                           .withBody(objectMapper.writeValueAsString(mockResponse))));

        mockMvc.perform(MockMvcRequestBuilders.post("/customer/files/request-presigned-url")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.uploadUrl").value("http://localhost:8089/s3-upload"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.fields.key").value("test-key"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.fields.policy").value("test-policy"));
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void create_ShouldUploadFile() throws Exception {
        CreateFileDto createDto = new CreateFileDto();
        createDto.setType(File.Type.CONTRACT);
        createDto.setCertificateId(1);
        createDto.setContractId(1);
        createDto.setLicenseId(1);
        createDto.setRequiredInformationId(1);
        createDto.setTerminationId(1);

        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.pdf",
                "application/pdf",
                "test content".getBytes());

        MockMultipartFile data = new MockMultipartFile(
                "data",
                "",
                "application/json",
                objectMapper.writeValueAsString(createDto).getBytes());

        // Mock presigned URL response for upload
        LambdaPresignedResponseDto mockPresignedResponse = new LambdaPresignedResponseDto();
        mockPresignedResponse.setUploadUrl("http://localhost:8089/s3-upload");
        Map<String, Object> fields = new HashMap<>();
        fields.put("key", "CONTRACT/2024/07/07/ABC12345-test.pdf");
        fields.put("policy", "test-policy");
        mockPresignedResponse.setFields(fields);

        wireMockServer.stubFor(post(urlPathEqualTo("/presigned-url")).willReturn(aResponse().withStatus(200)
                                                                                         .withHeader(
                                                                                                 "Content-Type",
                                                                                                 "application/json")
                                                                                         .withBody(objectMapper.writeValueAsString(
                                                                                                 mockPresignedResponse))));

        // Mock S3 upload success
        wireMockServer.stubFor(post(urlPathEqualTo("/s3-upload")).willReturn(aResponse().withStatus(204)));

        // Mock file repository save
        when(fileRepository.save(ArgumentMatchers.any())).thenReturn(testFile);

        mockMvc.perform(MockMvcRequestBuilders.multipart("/customer/files")
                                .file(file)
                                .file(data)
                                .header("x-user-id", "test-user-id")
                                .header("x-user-role", "customer"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.id").value("test-file-id"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.type").value("CONTRACT"));
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void delete_ShouldDeleteFile() throws Exception {
        // Mock file repository to return test file for deletion
        when(fileRepository.findById("test-file-id")).thenReturn(Optional.of(testFile));
        when(fileRepository.save(ArgumentMatchers.any())).thenReturn(testFile);

        mockMvc.perform(MockMvcRequestBuilders.delete("/customer/files/test-file-id"))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void getFileByRelative_ShouldReturnFile_WhenValidRelationAndId() throws Exception {
        // Mock file repository to return test file by relation
        when(fileRepository.findByRelation("contract_id", 123)).thenReturn(Optional.of(testFile));

        // Mock S3 download response
        byte[] fileContent = "test file content".getBytes();
        wireMockServer.stubFor(WireMock.get(urlPathEqualTo("/download"))
                                       .withQueryParam("fileName", equalTo(testFile.getName()))
                                       .willReturn(aResponse().withStatus(200)
                                                           .withHeader("Content-Type", "application/octet-stream")
                                                           .withBody(fileContent)));

        mockMvc.perform(MockMvcRequestBuilders.get("/customer/files/relation")
                                .param("relation", "contract_id")
                                .param("id", "123"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.header().string("Content-Type", "application/pdf"))
                .andExpect(MockMvcResultMatchers.header()
                                   .string("Content-Disposition", "attachment; filename=\"test-file.pdf\""))
                .andExpect(MockMvcResultMatchers.content().bytes(fileContent));
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void getFileByRelative_ShouldTestAllValidRelations() throws Exception {
        String[] validRelations = {
                "required_information_id",
                "contract_id",
                "certificate_id",
                "license_id",
                "termination_id",
                "general_information_id",
                "third_party_invoice_id",
                "marketing_material_id",
                "partner_contract_id",
                "order_id"
        };

        byte[] fileContent = "test file content".getBytes();

        for (String relation : validRelations) {
            // Mock file repository to return test file for each relation
            when(fileRepository.findByRelation(relation, 123)).thenReturn(Optional.of(testFile));

            // Mock S3 download response
            wireMockServer.stubFor(WireMock.get(urlPathEqualTo("/download"))
                                           .withQueryParam("fileName", equalTo(testFile.getName()))
                                           .willReturn(aResponse().withStatus(200)
                                                               .withHeader("Content-Type", "application/octet-stream")
                                                               .withBody(fileContent)));

            mockMvc.perform(MockMvcRequestBuilders.get("/customer/files/relation")
                                    .param("relation", relation)
                                    .param("id", "123"))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andExpect(MockMvcResultMatchers.header().string("Content-Type", "application/pdf"))
                    .andExpect(MockMvcResultMatchers.content().bytes(fileContent));
        }
    }

}
