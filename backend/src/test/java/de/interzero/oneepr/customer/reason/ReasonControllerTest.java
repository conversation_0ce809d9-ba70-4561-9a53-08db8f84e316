package de.interzero.oneepr.customer.reason;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.reason.dto.FindAllReasonsDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for {@link ReasonController}
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ReasonControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ReasonRepository reasonRepository;

    @BeforeEach
    void setup() {
        reasonRepository.deleteAll();
    }

    /**
     * {@link ReasonController#findAll(FindAllReasonsDto)} ()}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_shouldReturnReasonsWithTheRightType() throws Exception {

        // create reason in database
        Reason reason1 = new Reason();
        reason1.setValue("value1");
        reason1.setTitle("name1");
        reason1.setCreatedAt(Instant.now());
        reason1.setUpdatedAt(Instant.now());
        reason1.setType(Reason.Type.LICENSE_INFORMATION);
        this.reasonRepository.save(reason1);

        // create reason in database
        Reason reason2 = new Reason();
        reason2.setValue("value2");
        reason2.setTitle("name2");
        reason2.setCreatedAt(Instant.now());
        reason2.setUpdatedAt(Instant.now());
        reason2.setType(Reason.Type.TERMINATION);
        this.reasonRepository.save(reason2);

        // create request dto with reason type
        FindAllReasonsDto findAllReasonsDto = new FindAllReasonsDto();
        findAllReasonsDto.setType(Reason.Type.LICENSE_INFORMATION);

        // hit endpoint
        ResultActions resultActions = mockMvc.perform(get(Api.REASONS).contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(findAllReasonsDto)))
                .andExpect(status().isOk());

        // assert
        String jsonResponse = resultActions.andReturn().getResponse().getContentAsString();
        List<Reason> reasons = objectMapper.readValue(jsonResponse, new TypeReference<>() {
        });
        assertEquals(1, reasons.size());
        assertEquals(Reason.Type.LICENSE_INFORMATION, reasons.get(0).getType());
    }

    /**
     * {@link ReasonController#findAll(FindAllReasonsDto)} ()}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_shouldReturnExceptionTypeNotSet() throws Exception {

        // create reason in database
        Reason reason1 = new Reason();
        reason1.setValue("value1");
        reason1.setTitle("name1");
        reason1.setCreatedAt(Instant.now());
        reason1.setUpdatedAt(Instant.now());
        reason1.setType(Reason.Type.LICENSE_INFORMATION);

        // create request dto with reason type
        FindAllReasonsDto findAllReasonsDto = new FindAllReasonsDto();
        findAllReasonsDto.setType(null);

        this.reasonRepository.save(reason1);

        // hit endpoint
        mockMvc.perform(get(Api.REASONS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(findAllReasonsDto)))
                .andExpect(status().isBadRequest());
    }
}
