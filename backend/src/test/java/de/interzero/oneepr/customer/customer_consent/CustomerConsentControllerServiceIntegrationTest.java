package de.interzero.oneepr.customer.customer_consent;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.admin.mail.EmailMessage;
import de.interzero.oneepr.admin.mail.EmailOutboxGateway;
import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.consent.Consent;
import de.interzero.oneepr.customer.consent.ConsentRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_consent.dto.CreateCustomerConsentDto;
import de.interzero.oneepr.customer.customer_consent.dto.UpdateCustomerConsentDto;
import de.interzero.oneepr.customer.shared.auth.Role;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the {@link CustomerConsentController}.
 * This class uses {@link SpringBootTest} to load the full application context
 * and {@link AutoConfigureMockMvc} to configure {@link MockMvc} for sending HTTP requests
 * to the controller endpoints. It verifies the behavior of the customer consent API,
 * including creation, retrieval, and update of customer consent records,
 * interacting with the actual service and repository layers and a test database.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CustomerConsentControllerServiceIntegrationTest {

    private static final String API_BASE_URL = Api.CUSTOMER_CONSENT;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ConsentRepository consentRepository;

    @Autowired
    private CustomerConsentRepository customerConsentRepository;

    @MockBean
    private EmailOutboxGateway emailOutboxGateway;

    @Autowired
    private EntityManager entityManager;

    private Customer testCustomer1;

    private Customer testCustomer2;

    private Consent testConsentType1;

    private Consent testConsentType2;

    /**
     * Sets up the test environment before each test method execution.
     * Clears relevant database tables and creates initial customer and consent type data.
     */
    @BeforeEach
    void setUp() {
        objectMapper.registerModule(new JavaTimeModule());
        Customer customer1 = new Customer();
        customer1.setFirstName("John");
        customer1.setLastName("Doe");
        customer1.setEmail("john.doe." + Instant.now().toEpochMilli() + "@example.com"); // Ensure unique email
        customer1.setUserId(101);
        customer1.setCreatedAt(Instant.now());
        customer1.setUpdatedAt(Instant.now());
        customer1.setType(Customer.Type.REGULAR);
        testCustomer1 = customerRepository.saveAndFlush(customer1);

        Customer customer2 = new Customer();
        customer2.setFirstName("Jane");
        customer2.setLastName("Smith");
        customer2.setEmail("jane.smith." + Instant.now().toEpochMilli() + "@example.com"); // Ensure unique email
        customer2.setUserId(102);
        customer2.setCreatedAt(Instant.now());
        customer2.setUpdatedAt(Instant.now());
        customer2.setType(Customer.Type.PREMIUM);
        testCustomer2 = customerRepository.saveAndFlush(customer2);

        Consent consentType1 = new Consent();
        consentType1.setName("Newsletter Subscription");
        consentType1.setCreatedAt(Instant.now());
        consentType1.setUpdatedAt(Instant.now());
        consentType1.setDescription("desc");
        consentType1.setVersion(1);
        consentType1.setType(Consent.Type.ACCOUNT);
        testConsentType1 = consentRepository.saveAndFlush(consentType1);

        Consent consentType2 = new Consent();
        consentType2.setName("Terms of Service Agreement");
        consentType2.setCreatedAt(Instant.now());
        consentType2.setUpdatedAt(Instant.now());
        consentType2.setDescription("desc");
        consentType2.setType(Consent.Type.PURCHASE);
        consentType2.setVersion(1);
        testConsentType2 = consentRepository.saveAndFlush(consentType2);
    }

    /**
     * Creates and persists a {@link CustomerConsent} entity for testing purposes.
     * Sets created/updated timestamps to one hour ago, and given/revoked timestamps based on the 'given' status.
     *
     * @param customer    The {@link Customer} to associate with the consent.
     * @param consentType The {@link Consent} type for this record.
     * @param given       The boolean status indicating if consent is given.
     * @return The persisted {@link CustomerConsent} entity.
     */
    private CustomerConsent createAndSaveTestCustomerConsent(Customer customer,
                                                             Consent consentType,
                                                             boolean given) {
        CustomerConsent customerConsent = new CustomerConsent();
        customerConsent.setCustomer(customer);
        customerConsent.setConsent(consentType);
        customerConsent.setGiven(given);
        Instant now = Instant.now();
        customerConsent.setCreatedAt(now.minusSeconds(3600)); // An hour ago
        customerConsent.setUpdatedAt(now.minusSeconds(3600));
        if (given) {
            customerConsent.setGivenAt(customerConsent.getCreatedAt());
            customerConsent.setRevokedAt(null);
        } else {
            customerConsent.setGivenAt(null);
            customerConsent.setRevokedAt(customerConsent.getCreatedAt());
        }
        return customerConsentRepository.saveAndFlush(customerConsent);
    }

    /**
     * Test for {@link CustomerConsentController#create(CreateCustomerConsentDto)}.
     * Verifies the successful creation of a new customer consent via the POST endpoint.
     * It sends a {@link CreateCustomerConsentDto} and expects an HTTP 201 (Created) status.
     * The response body is verified to be a {@link CustomerConsent} matching the
     * input data, with a generated ID and appropriate given/revoked timestamps.
     * This test runs with SUPER_ADMIN privileges.
     * Note: If CSRF protection is enabled, the mockMvc POST request might require `.with(csrf())`.
     *
     * @throws Exception if any error occurs during the MockMvc performance or assertions.
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void create_shouldCreateNewCustomerConsent() throws Exception {
        CreateCustomerConsentDto createDto = new CreateCustomerConsentDto();
        createDto.setCustomerId(testCustomer1.getId());
        createDto.setConsentId(testConsentType1.getId());
        createDto.setGiven(true);

        ResultActions resultActions = mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.customer_id").value(createDto.getCustomerId()))
                .andExpect(jsonPath("$.consent_id").value(createDto.getConsentId()));

        CustomerConsent responseDto = objectMapper.readValue(
                resultActions.andReturn()
                        .getResponse()
                        .getContentAsString(), CustomerConsent.class);

        assertNotNull(responseDto.getId());
        assertEquals(createDto.getGiven(), responseDto.getGiven());
        assertNotNull(responseDto.getGivenAt());
        assertNull(responseDto.getRevokedAt());
    }

    /**
     * Test for {@link CustomerConsentController#createMany(List)}.
     * Verifies the successful creation of list new customer consent via the POST endpoint.
     * It sends a {@link CreateCustomerConsentDto} and expects an HTTP 201 (Created) status.
     * The response body is verified to be a {@link CustomerConsent} matching the
     * input data, with a generated ID and appropriate given/revoked timestamps.
     *
     * @throws Exception if any error occurs during the MockMvc performance or assertions.
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void createMany_shouldCreateManyCustomerConsents() throws Exception {
        CreateCustomerConsentDto dto1 = new CreateCustomerConsentDto();
        dto1.setCustomerId(testCustomer1.getId());
        dto1.setConsentId(testConsentType1.getId());
        dto1.setGiven(true);

        CreateCustomerConsentDto dto2 = new CreateCustomerConsentDto();
        dto2.setCustomerId(testCustomer2.getId());
        dto2.setConsentId(testConsentType2.getId());
        dto2.setGiven(false);

        List<CreateCustomerConsentDto> requestList = Arrays.asList(dto1, dto2);

        mockMvc.perform(post(API_BASE_URL + "/create-many").with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestList)))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].customer_id").value(testCustomer1.getId()))
                .andExpect(jsonPath("$[0].given").value(true))
                .andExpect(jsonPath("$[1].customer_id").value(testCustomer2.getId()))
                .andExpect(jsonPath("$[1].given").value(false));
    }

    /**
     * Test for {@link CustomerConsentController#findAll()}.
     * Verifies that the GET endpoint retrieves all existing customer consents.
     * It first creates two sample consents and then expects an HTTP 200 (OK) status
     *
     * @throws Exception if any error occurs during the MockMvc performance or assertions.
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_shouldReturnAllCustomerConsents() throws Exception {
        createAndSaveTestCustomerConsent(testCustomer1, testConsentType1, true);
        createAndSaveTestCustomerConsent(testCustomer2, testConsentType2, false);

        mockMvc.perform(get(API_BASE_URL).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(2)));
    }

    /**
     * Test for {@link CustomerConsentController#findOne(Integer)}.
     * Verifies that the GET endpoint retrieves a specific customer consent by its ID.
     * It creates a sample consent, then requests it via its ID, expecting an HTTP 200 (OK) status.
     * The response body is checked to ensure it's a JSON object matching the details
     * of the created consent, including its ID, associated customer ID, and given status.
     *
     * @throws Exception if any error occurs during the MockMvc performance or assertions.
     */
    @Test
    @WithMockUser(
            username = "user101",
            roles = {TestRole.SUPER_ADMIN}
    )
    void findOne_shouldReturnSpecificCustomerConsent() throws Exception {
        CustomerConsent savedConsent = createAndSaveTestCustomerConsent(testCustomer1, testConsentType1, true);
        mockMvc.perform(get(API_BASE_URL + "/{id}", savedConsent.getId()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(savedConsent.getId()))
                .andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()))
                .andExpect(jsonPath("$.given").value(true));
    }

    /**
     * Test for {@link CustomerConsentController#findAllByCustomerId(Integer)}.
     * Verifies that the GET endpoint retrieves all consents associated with a specific customer ID.
     * It creates consents for two different customers, then requests consents for the first customer.
     * Expects an HTTP 200 (OK) status and a JSON array containing only the consents
     *
     * @throws Exception if any error occurs during the MockMvc performance or assertions.
     */
    @Test
    @WithMockUser(
            username = "user101",
            roles = {TestRole.SUPER_ADMIN}
    )
    void findAllByCustomerId_shouldReturnAllConsentsForSpecificCustomer() throws Exception {
        createAndSaveTestCustomerConsent(testCustomer1, testConsentType1, true);
        createAndSaveTestCustomerConsent(testCustomer1, testConsentType2, false);
        createAndSaveTestCustomerConsent(testCustomer2, testConsentType1, true); // For other customer

        mockMvc.perform(get(
                        API_BASE_URL + "/customer/{customerId}",
                        testCustomer1.getId()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk()) // Expect 200 OK even if list is empty (standard behavior)
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].customer_id").value(testCustomer1.getId()))
                .andExpect(jsonPath("$[1].customer_id").value(testCustomer1.getId()));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.SUPER_ADMIN}
    )
    void update_shouldUpdateCustomerConsentAndSendEmail() throws Exception {
        // --- Setup ---
        AuthenticatedUser mockUser = new AuthenticatedUser("101", Role.SUPER_ADMIN, "<EMAIL>");
        CustomerConsent originalConsent = createAndSaveTestCustomerConsent(testCustomer1, testConsentType1, true);
        Instant originalUpdatedAt = originalConsent.getUpdatedAt();
        UpdateCustomerConsentDto updateDto = new UpdateCustomerConsentDto();
        updateDto.setGiven(false);

        try (MockedStatic<AuthUtil> mockedAuthUtil = mockStatic(AuthUtil.class)) {
            mockedAuthUtil.when(AuthUtil::getRelevantUserDetails).thenReturn(mockUser);

            // --- Execute ---
            ResultActions resultActions = mockMvc.perform(patch(API_BASE_URL + "/{id}", originalConsent.getId()).with(
                            csrf()).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(updateDto)))
                    .andDo(print())
                    .andExpect(status().isOk());

            // --- Assert Response and DB State ---
            CustomerConsent updatedResponseDto = objectMapper.readValue(
                    resultActions.andReturn()
                            .getResponse()
                            .getContentAsString(),
                    CustomerConsent.class);

            assertEquals(originalConsent.getId(), updatedResponseDto.getId());
            assertEquals(false, updatedResponseDto.getGiven());
            assertNotNull(updatedResponseDto.getRevokedAt());
            assertNull(updatedResponseDto.getGivenAt());
            assertTrue(updatedResponseDto.getUpdatedAt().isAfter(originalUpdatedAt));

            CustomerConsent updatedEntityInDb = customerConsentRepository.findById(originalConsent.getId())
                    .orElseThrow();
            assertEquals(false, updatedEntityInDb.getGiven());

            ArgumentCaptor<EmailMessage> emailMessageCaptor = ArgumentCaptor.forClass(EmailMessage.class);

            verify(emailOutboxGateway, times(1)).sendEmail(emailMessageCaptor.capture());

            EmailMessage sentEmail = emailMessageCaptor.getValue();
            assertEquals("24", sentEmail.getTransactionalMessageId());
            assertEquals("<EMAIL>", sentEmail.getTo());
            assertEquals("Consent status changed", sentEmail.getSubject());
            assertEquals("<EMAIL>", sentEmail.getMessageData().get("email"));
        }
    }

    /**
     * Test for {@link CustomerConsentController#update(Integer, UpdateCustomerConsentDto)}.
     * Verifies that the PATCH endpoint successfully updates an existing customer consent.
     * It creates a consent, then sends an update request to change its 'given' status.
     * Expects an HTTP 200 (OK) status and verifies that the response DTO reflects the changes
     * (e.g., updated 'given' status, 'revokedAt' timestamp set, 'updatedAt' timestamp advanced).
     * Finally, it confirms the update in the database.
     *
     * @throws Exception if any error occurs during the MockMvc performance or assertions.
     */
    @Test
    @WithMockUser(
            username = "user101",
            roles = {TestRole.SUPER_ADMIN}
    )
    void update_shouldUpdateCustomerConsent() throws Exception {
        CustomerConsent originalConsent = createAndSaveTestCustomerConsent(testCustomer1, testConsentType1, true);
        Instant originalUpdatedAt = originalConsent.getUpdatedAt();

        UpdateCustomerConsentDto updateDto = new UpdateCustomerConsentDto();
        updateDto.setGiven(false);

        ResultActions resultActions = mockMvc.perform(patch(
                        API_BASE_URL + "/{id}",
                        originalConsent.getId()).with(csrf())
                                                              .contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(updateDto)))
                .andDo(print())
                .andExpect(status().isOk());

        CustomerConsent updatedResponseDto = objectMapper.readValue(
                resultActions.andReturn()
                        .getResponse()
                        .getContentAsString(),
                CustomerConsent.class);

        assertEquals(originalConsent.getId(), updatedResponseDto.getId());
        assertEquals(false, updatedResponseDto.getGiven());
        assertNotNull(updatedResponseDto.getRevokedAt());
        assertNull(updatedResponseDto.getGivenAt());
        assertTrue(updatedResponseDto.getUpdatedAt().isAfter(originalUpdatedAt));

        // Verify in DB
        CustomerConsent updatedEntityInDb = customerConsentRepository.findById(originalConsent.getId()).orElseThrow();
        assertEquals(false, updatedEntityInDb.getGiven());
    }

    /**
     * Test for {@link CustomerConsentController#updateMany(List)}.
     * Verifies that the PATCH endpoint successfully updates multiple customer consents in bulk.
     * It creates two sample consents, then sends a request with a list of {@link UpdateCustomerConsentDto}
     * to change their 'given' status. Expects an HTTP 200 (OK) status and a JSON array
     * reflecting the updated consents. Finally, it confirms the changes in the database.
     *
     * @throws Exception if any error occurs during the MockMvc performance or assertions.
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void updateMany_shouldUpdateManyCustomerConsents() throws Exception {
        CustomerConsent consent1 = createAndSaveTestCustomerConsent(testCustomer1, testConsentType1, true);
        CustomerConsent consent2 = createAndSaveTestCustomerConsent(testCustomer2, testConsentType2, true);

        UpdateCustomerConsentDto updateDto1 = new UpdateCustomerConsentDto();
        updateDto1.setId(consent1.getId()); // Assuming UpdateCustomerConsentDto has setId()
        updateDto1.setGiven(false);

        UpdateCustomerConsentDto updateDto2 = new UpdateCustomerConsentDto();
        updateDto2.setId(consent2.getId());
        updateDto2.setGiven(false);

        List<UpdateCustomerConsentDto> requestList = Arrays.asList(updateDto1, updateDto2);

        mockMvc.perform(patch(API_BASE_URL + "/update-many").with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestList)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(consent1.getId()))
                .andExpect(jsonPath("$[0].given").value(false))
                .andExpect(jsonPath("$[1].id").value(consent2.getId()))
                .andExpect(jsonPath("$[1].given").value(false));

        assertEquals(false, customerConsentRepository.findById(consent1.getId()).orElseThrow().getGiven());
        assertEquals(false, customerConsentRepository.findById(consent2.getId()).orElseThrow().getGiven());
    }
}
