package de.interzero.oneepr.customer.license_third_party_invoice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.file.FileRepository;
import de.interzero.oneepr.customer.http.CrmInterface;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_third_party_invoice.dto.CreateLicenseThirdPartyInvoiceDto;
import de.interzero.oneepr.customer.license_third_party_invoice.dto.UpdateLicenseThirdPartyInvoiceDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.Year;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.not;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link LicenseThirdPartyInvoiceController}.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class LicenseThirdPartyInvoiceControllerTest {

    private static final String API_BASE_URL = Api.THIRD_PARTY_INVOICES;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private LicenseThirdPartyInvoiceRepository licenseThirdPartyInvoiceRepository;

    @Autowired
    private LicenseRepository licenseRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private FileRepository fileRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private CrmInterface crmInterface;

    private License testLicense1;

    /**
     * Sets up the test environment before each test method execution.
     */
    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.findAndRegisterModules();

        fileRepository.deleteAllInBatch();
        licenseThirdPartyInvoiceRepository.deleteAllInBatch();
        licenseRepository.deleteAllInBatch();
        contractRepository.deleteAllInBatch();
        customerRepository.deleteAllInBatch();

        // userId 101
        Customer testCustomer1 = createAndSaveTestCustomer("<EMAIL>", "Cust", "One", "Mr.", 101);
        Contract contract1 = createAndSaveTestContract(testCustomer1);
        testLicense1 = createAndSaveTestLicense("Germany", contract1);
    }

    private Customer createAndSaveTestCustomer(String email,
                                               String firstName,
                                               String lastName,
                                               String salutation,
                                               Integer userId) {
        Customer customer = new Customer();
        customer.setEmail(email);
        customer.setFirstName(firstName);
        customer.setLastName(lastName);
        customer.setSalutation(salutation);
        customer.setUserId(userId);
        customer.setCreatedAt(Instant.now());
        customer.setUpdatedAt(Instant.now());
        customer.setType(Customer.Type.REGULAR);
        return customerRepository.saveAndFlush(customer);
    }

    private Contract createAndSaveTestContract(Customer customer) {
        Contract contract = new Contract();
        contract.setCustomer(customer);
        contract.setTitle("testContract");
        contract.setType(Contract.Type.EU_LICENSE);
        contract.setStatus(Contract.Status.ACTIVE);
        contract.setEndDate(Instant.now().plusSeconds(999999));
        contract.setStartDate(Instant.now());
        contract.setCreatedAt(Instant.now());
        contract.setUpdatedAt(Instant.now());
        return contractRepository.saveAndFlush(contract);
    }

    private License createAndSaveTestLicense(String countryName,
                                             Contract contract) {
        License license = new License();

        license.setCountryName(countryName);
        license.setContract(contract);


        license.setRegistrationNumber("REG-" + System.currentTimeMillis());
        license.setCountryFlag("🇩🇪");
        license.setCountryId(1);
        license.setYear(Year.now().getValue());
        license.setStartDate(Instant.now().minus(30, ChronoUnit.DAYS));

        license.setCountryCode("DE");


        license.setCreatedAt(Instant.now());
        license.setUpdatedAt(Instant.now());

        return licenseRepository.saveAndFlush(license);
    }

    private File createAndSaveTestFile() {
        File file = new File();
        file.setId("12345");
        String nameToSet;
        String extensionToSet;
        int dotIndex = "invoice_doc.pdf".lastIndexOf('.');
        nameToSet = "invoice_doc.pdf".substring(0, dotIndex);
        extensionToSet = "invoice_doc.pdf".substring(dotIndex + 1);
        file.setType(File.Type.INVOICE);
        file.setName(nameToSet);
        file.setOriginalName("invoice_doc.pdf");
        file.setExtension(extensionToSet);
        file.setUserId("101");

        file.setSize("10240");
        if (file.getCreatedAt() == null) {
            file.setCreatedAt(Instant.now().minusSeconds(5));
        }
        if (file.getUpdatedAt() == null) {
            file.setUpdatedAt(Instant.now().minusSeconds(5));
        }

        return fileRepository.saveAndFlush(file);
    }

    private LicenseThirdPartyInvoice createAndSaveTestInvoice(String title,
                                                              License license,
                                                              Instant issuedAt,
                                                              Instant dueDate,
                                                              LicenseThirdPartyInvoice.Status status,
                                                              LicenseThirdPartyInvoice.Issuer issuer,
                                                              Integer price) {
        LicenseThirdPartyInvoice invoice = new LicenseThirdPartyInvoice();
        invoice.setTitle(title);
        invoice.setLicense(license);
        invoice.setIssuedAt(issuedAt);
        invoice.setDueDate(dueDate);
        invoice.setStatus(status);
        invoice.setIssuer(issuer);
        invoice.setPrice(price);
        invoice.setCreatedAt(Instant.now().minusSeconds(10));
        invoice.setUpdatedAt(Instant.now().minusSeconds(10));
        return licenseThirdPartyInvoiceRepository.saveAndFlush(invoice);
    }

    /**
     * Test for {@link LicenseThirdPartyInvoiceController#create(CreateLicenseThirdPartyInvoiceDto)}.
     */
    @Test
    @WithMockUser(
            username = "admin-creator",
            roles = {TestRole.ADMIN}
    )
    void create_shouldCreateLicenseThirdPartyInvoice() throws Exception {
        when(crmInterface.thirdPartyInvoices(any(String.class), any(String.class), any(Instant.class))).thenReturn(
                12345L);

        File testFile = createAndSaveTestFile();
        CreateLicenseThirdPartyInvoiceDto createDto = new CreateLicenseThirdPartyInvoiceDto();
        createDto.setLicenseId(testLicense1.getId());
        createDto.setTitle("Initial Invoice Q1");
        createDto.setStatus(LicenseThirdPartyInvoice.Status.OPEN);
        createDto.setPrice(50000);
        createDto.setIssuer(LicenseThirdPartyInvoice.Issuer.OTHER_THIRD_PARTY);
        createDto.setIssuedAt(Instant.now().minus(5, ChronoUnit.DAYS).toString());
        createDto.setDueDate(Instant.now().plus(25, ChronoUnit.DAYS).toString());
        createDto.setFileId(testFile.getId());


        ResultActions resultActions = mockMvc.perform(post(API_BASE_URL).with(csrf())
                                                              .contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(createDto))
                                                              .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.title").value("Initial Invoice Q1"))
                .andExpect(jsonPath("$.status").value(LicenseThirdPartyInvoice.Status.OPEN.toString()))
                .andExpect(jsonPath("$.price").value(50000))
                .andExpect(jsonPath("$.issuer").value(LicenseThirdPartyInvoice.Issuer.OTHER_THIRD_PARTY.toString()))
                .andExpect(jsonPath("$.license_id").value(testLicense1.getId()))
                .andExpect(jsonPath("$.third_party_invoice_monday_ref").value(12345));

        String responseString = resultActions.andReturn().getResponse().getContentAsString();
        LicenseThirdPartyInvoice createdInvoice = objectMapper.readValue(
                responseString,
                LicenseThirdPartyInvoice.class);

        assertNotNull(createdInvoice.getId());
        assertNotNull(createdInvoice.getCreatedAt());
        assertNotNull(createdInvoice.getUpdatedAt());

        Optional<LicenseThirdPartyInvoice> foundInDbOpt = licenseThirdPartyInvoiceRepository.findById(createdInvoice.getId());
        assertTrue(foundInDbOpt.isPresent());
        LicenseThirdPartyInvoice foundInDb = foundInDbOpt.get();
        assertEquals(testLicense1.getId(), foundInDb.getLicense().getId());
        assertEquals(Integer.valueOf(12345), foundInDb.getThirdPartyInvoiceMondayRef());

        Optional<File> updatedFileOpt = fileRepository.findById(testFile.getId());
        assertTrue(updatedFileOpt.isPresent());
        assertEquals(createdInvoice.getId(), updatedFileOpt.get().getThirdPartyInvoice().getId());
    }

    /**
     * Tests the {@link LicenseThirdPartyInvoiceController#findAll(Integer, String, String)} endpoint.
     * This test verifies several scenarios:
     * 1. Retrieval of all invoices when no filters are applied.
     * 2. Correct filtering of invoices based on a provided {@code license_id} query parameter.
     * 3. Correct filtering of invoices based on a combination of {@code license_id}, {@code from_date},
     * and {@code to_date} query parameters.
     * The test is executed with a mock user having the CLERK role.
     */
    @Test
    @WithMockUser(
            username = "111",
            roles = {TestRole.CLERK}
    )
    void findAll_shouldReturnFilteredInvoices() throws Exception {
        Instant now = Instant.now();
        createAndSaveTestInvoice(
                "Invoice A",
                testLicense1,
                now.minus(10, ChronoUnit.DAYS),
                now.plus(20, ChronoUnit.DAYS),
                LicenseThirdPartyInvoice.Status.OPEN,
                LicenseThirdPartyInvoice.Issuer.OTHER_THIRD_PARTY,
                100);
        createAndSaveTestInvoice(
                "Invoice B",
                testLicense1,
                now.minus(5, ChronoUnit.DAYS),
                now.plus(25, ChronoUnit.DAYS),
                LicenseThirdPartyInvoice.Status.PAYED,
                LicenseThirdPartyInvoice.Issuer.THIRD_PARTY_DUAL_SYSTEM,
                200);
        Customer customer2 = createAndSaveTestCustomer("<EMAIL>", "C", "Two", "Dr", 202);
        Contract contract2 = createAndSaveTestContract(customer2);
        License license2 = createAndSaveTestLicense("France", contract2);
        createAndSaveTestInvoice(
                "Invoice C",
                license2,
                now.minus(2, ChronoUnit.DAYS),
                now.plus(28, ChronoUnit.DAYS),
                LicenseThirdPartyInvoice.Status.OPEN,
                LicenseThirdPartyInvoice.Issuer.OTHER_THIRD_PARTY,
                300);
        mockMvc.perform(get(API_BASE_URL).param("license_id", String.valueOf(testLicense1.getId()))
                                .param("from_date", "2015-01-01") // Add from_date
                                .param("to_date", "2025-12-30")     // Add to_date
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].license_id").value(testLicense1.getId()))
                .andExpect(jsonPath("$[1].license_id").value(testLicense1.getId()));

        String fromDateStr = LocalDate.now().minusDays(8).toString(); // YYYY-MM-DD
        String toDateStr = LocalDate.now().minusDays(3).toString();   // YYYY-MM-DD

        mockMvc.perform(get(API_BASE_URL).param("license_id", testLicense1.getId().toString())
                                .param("from_date", fromDateStr)
                                .param("to_date", toDateStr)
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].title").value("Invoice B"));

        // Test findAll with no filters
        mockMvc.perform(get(API_BASE_URL).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(3)));
    }


    /**
     * Test for {@link LicenseThirdPartyInvoiceController#findOne(Integer)}.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.CUSTOMER}
    )
    void findOne_shouldReturnInvoice_whenCustomerPermitted() throws Exception {
        LicenseThirdPartyInvoice invoice = createAndSaveTestInvoice(
                "Specific Invoice",
                testLicense1,
                Instant.now(),
                Instant.now().plusSeconds(99999),
                LicenseThirdPartyInvoice.Status.OPEN,
                LicenseThirdPartyInvoice.Issuer.OTHER_THIRD_PARTY,
                15000);

        mockMvc.perform(get(API_BASE_URL + "/{id}", invoice.getId()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(invoice.getId()))
                .andExpect(jsonPath("$.title").value("Specific Invoice"))
                .andExpect(jsonPath("$.license_id").value(testLicense1.getId()));
    }

    /**
     * Test for {@link LicenseThirdPartyInvoiceController#update(Integer, UpdateLicenseThirdPartyInvoiceDto)}.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void update_shouldUpdateInvoice() throws Exception {
        LicenseThirdPartyInvoice originalInvoice = createAndSaveTestInvoice(
                "Old Title",
                testLicense1,
                Instant.now(),
                Instant.now().plusSeconds(99999),
                LicenseThirdPartyInvoice.Status.OPEN,
                LicenseThirdPartyInvoice.Issuer.OTHER_THIRD_PARTY,
                20000);
        Instant initialUpdatedAt = originalInvoice.getUpdatedAt();

        UpdateLicenseThirdPartyInvoiceDto updateDto = new UpdateLicenseThirdPartyInvoiceDto();
        updateDto.setTitle("New Updated Title");
        updateDto.setStatus(LicenseThirdPartyInvoice.Status.PAYED);
        updateDto.setPrice(21000);

        mockMvc.perform(put(API_BASE_URL + "/{id}", originalInvoice.getId()).with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.title").value("New Updated Title"))
                .andExpect(jsonPath("$.status").value(LicenseThirdPartyInvoice.Status.PAYED.toString()))
                .andExpect(jsonPath("$.price").value(21000))
                .andExpect(jsonPath("$.updated_at").value(not(initialUpdatedAt.toString())));

        LicenseThirdPartyInvoice updatedInDb = licenseThirdPartyInvoiceRepository.findById(originalInvoice.getId())
                .orElseThrow();
        assertEquals("New Updated Title", updatedInDb.getTitle());
        assertEquals(LicenseThirdPartyInvoice.Status.PAYED, updatedInDb.getStatus());
        assertTrue(updatedInDb.getUpdatedAt().isAfter(initialUpdatedAt));
    }

    /**
     * Test for {@link LicenseThirdPartyInvoiceController#remove(Integer)}.
     * This is a soft delete.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.SUPER_ADMIN}
    )
    void remove_shouldSoftDeleteInvoice() throws Exception {
        LicenseThirdPartyInvoice invoice = createAndSaveTestInvoice(
                "To Be Deleted",
                testLicense1,
                Instant.now(),
                Instant.now().plusSeconds(99999),
                LicenseThirdPartyInvoice.Status.OPEN,
                LicenseThirdPartyInvoice.Issuer.OTHER_THIRD_PARTY,
                5000);
        assertNull(invoice.getDeletedAt());

        mockMvc.perform(delete(API_BASE_URL + "/{id}", invoice.getId()).with(csrf()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(invoice.getId()))
                .andExpect(jsonPath("$.deleted_at").isNotEmpty()); // Check that deleted_at is now set

        LicenseThirdPartyInvoice deletedInDb = licenseThirdPartyInvoiceRepository.findById(invoice.getId())
                .orElseThrow();
        assertNotNull(deletedInDb.getDeletedAt());

        // Verify findOne now returns 404 due to soft delete
        mockMvc.perform(get(API_BASE_URL + "/{id}", invoice.getId()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isNotFound());
    }
}