package de.interzero.oneepr.common.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

import static org.mockito.Mockito.mock;

/**
 * Test configuration that provides mock AWS SDK beans for testing.
 * This configuration is automatically loaded for Spring Boot tests.
 */
@TestConfiguration
public class TestAwsConfig {

    @Bean
    @Primary
    public S3Client mockS3Client() {
        return mock(S3Client.class);
    }

    @Bean
    @Primary
    public S3Presigner mockS3Presigner() {
        return mock(S3Presigner.class);
    }
}
