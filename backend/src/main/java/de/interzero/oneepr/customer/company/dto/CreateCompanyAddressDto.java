package de.interzero.oneepr.customer.company.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Data Transfer Object for creating a company address.
 * Converted from TypeScript CreateCompanyDto address structure with exact same field names.
 */
@Schema(description = "Company address information")
@Data
public class CreateCompanyAddressDto {

    @JsonProperty("country_code")
    @Schema(
            description = "Country code",
            example = "DE"
    )
    private String countryCode;

    @JsonProperty("address_line")
    @Schema(
            description = "Address line",
            example = "Main Street"
    )
    private String addressLine;

    @JsonProperty("city")
    @Schema(
            description = "City",
            example = "Berlin"
    )
    private String city;

    @JsonProperty("zip_code")
    @Schema(
            description = "ZIP code",
            example = "10115"
    )
    private String zipCode;

    @JsonProperty("street_and_number")
    @Schema(
            description = "Street and number",
            example = "Main Street 123"
    )
    private String streetAndNumber;

    @JsonProperty("additional_address")
    @Schema(
            description = "Additional address information",
            example = "Building A"
    )
    private String additionalAddress;

    @JsonProperty("place_id")
    @Schema(
            description = "Google Places ID",
            example = "ChIJAVkDPzdOqEcRcDteW0YgIQQ"
    )
    private String placeId;
}
