package de.interzero.oneepr.customer.http;

import lombok.NonNull;
import org.springframework.http.HttpMethod;

import java.util.Map;

/**
 * This class is a non-working placeholder for services that call the auth service. Originally implemented in the
 * http.service.ts package, this is a replacement for auth().
 * <p>
 * Calls to this class should be re-implemented in springboot instead of calling this class. However, if the work you
 * are doing falls outside the scope of the auth service, you can use this class as a placeholder.
 */
@SuppressWarnings("unused") // remove this whole class once no more methods call it
public class AuthInterface {

    private AuthInterface() {
        throw new IllegalStateException("Utility class");
    }

    @SuppressWarnings("ConstantConditions")
    public static Object auth(@NonNull String url,
                              @NonNull Map<String, Object> params,
                              @NonNull HttpMethod method) {
        if (true) {
            throw new RuntimeException(
                    "This method should not be called. Please re-implement the auth service in Spring Boot.");
        }
        return new Object();
    }
}
