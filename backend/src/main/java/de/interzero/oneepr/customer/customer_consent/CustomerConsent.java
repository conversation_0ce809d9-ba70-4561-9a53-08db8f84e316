package de.interzero.oneepr.customer.customer_consent;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.consent.Consent;
import de.interzero.oneepr.customer.customer.Customer;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "customer_consent",
        schema = "public"
)
public class CustomerConsent {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "customer_consent_id_gen"
    )
    @SequenceGenerator(
            name = "customer_consent_id_gen",
            sequenceName = "customer_consent_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @JsonIgnore
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "customer_id",
            nullable = false
    )
    private Customer customer;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @JsonIgnore
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "consent_id",
            nullable = false
    )
    private Consent consent;

    @NotNull
    @Column(
            name = "given",
            nullable = false
    )
    @JsonProperty("given")
    private Boolean given = false;

    @Column(name = "\"givenAt\"")
    @JsonProperty("given_at")
    private Instant givenAt;

    @Column(name = "\"revokedAt\"")
    @JsonProperty("revoked_at")
    private Instant revokedAt;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Transient
    @JsonProperty("customer_id")
    public Integer getCustomerId() {
        return customer != null ? customer.getId() : null;
    }

    @Transient
    @JsonProperty("consent_id")
    public Integer getConsentId() {
        return consent != null ? consent.getId() : null;
    }
}