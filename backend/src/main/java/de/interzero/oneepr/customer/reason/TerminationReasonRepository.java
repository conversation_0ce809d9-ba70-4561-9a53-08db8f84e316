package de.interzero.oneepr.customer.reason;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TerminationReasonRepository extends JpaRepository<TerminationReason, Integer> {

    /**
     * Deletes all TerminationReason entities associated with a specific termination ID.
     * This is a more efficient bulk operation than fetching and deleting one by one.
     *
     * @param terminationId The ID of the termination whose reasons should be deleted.
     */
    @Modifying
    @Query("DELETE FROM TerminationReason tr WHERE tr.termination.id = :terminationId")
    void deleteAllByTerminationId(Integer terminationId);

    Optional<TerminationReason> findAllByTermination_Id(Integer id);
}
