package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Represents the aggregated resource savings data for a single material fraction (e.g., paper, glass).
 * <p>
 * This DTO is a direct translation of the object created for each fraction within the
 * {@code fractions} map in the {@code getDirectLicenseResources} method
 * from {@code customer.service.ts}.
 */
@Data
public class FractionResourcesDto {

    @JsonProperty("key")
    private String key;

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    @JsonProperty("reported_weight")
    private BigDecimal reportedWeight = BigDecimal.ZERO;

    @JsonProperty("rosources_saved")
    private BigDecimal resourcesSaved = BigDecimal.ZERO;

    @JsonProperty("greenhouse_gases")
    private BigDecimal greenhouseGases = BigDecimal.ZERO;
}