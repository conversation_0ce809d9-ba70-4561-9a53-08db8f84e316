package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.entity.LicenseReportSetFrequency;
import lombok.Data;

/**
 * Originally found in src/customer-commitment/dto/customer-commitment.dto.ts
 */
@Data
public class ReportSetFrequency {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("rhythm")
    private LicenseReportSetFrequency.Rhythm rhythm;

    @JsonProperty("frequency")
    private Frequency frequency;

    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("deleted_at")
    private String deletedAt;

    @Data
    public static class Frequency {

        @JsonProperty("deadline")
        private Object deadline;

        @JsonProperty("open")
        private Object open;
    }
}
