package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents the aggregated total resource savings data across all years for a customer.
 * <p>
 * This DTO is a direct translation of the {@code resources.total} object
 * initialized and populated within the {@code getDirectLicenseResources} method
 * from {@code customer.service.ts}.
 */
@Data
public class TotalResourcesDto {

    @JsonProperty("years")
    private List<Integer> years = new ArrayList<>();

    @JsonProperty("reported_weight")
    private BigDecimal reportedWeight = BigDecimal.ZERO;

    @JsonProperty("rosources_saved")
    private BigDecimal resourcesSaved = BigDecimal.ZERO;

    @JsonProperty("greenhouse_gases")
    private BigDecimal greenhouseGases = BigDecimal.ZERO;

    @JsonProperty("fractions")
    private Map<String, FractionResourcesDto> fractions = new HashMap<>();
}