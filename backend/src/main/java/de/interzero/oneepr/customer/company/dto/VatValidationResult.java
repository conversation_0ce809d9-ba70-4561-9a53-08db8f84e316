package de.interzero.oneepr.customer.company.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data Transfer Object for VAT validation results.
 * Converted from TypeScript VAT validation response structure.
 */
@Schema(description = "Result of VAT ID validation")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VatValidationResult {

    @JsonProperty("system")
    @Schema(
            description = "The validation system used",
            example = "VIES"
    )
    private String system;

    @JsonProperty("is_valid")
    @Schema(
            description = "Whether the VAT ID is valid",
            example = "true"
    )
    private Boolean isValid;

    @JsonProperty("data")
    @Schema(description = "The validation response data")
    private Object data;

    @JsonProperty("error")
    @Schema(
            description = "Error message if validation failed",
            example = "INVALID"
    )
    private String error;

    @JsonProperty("message")
    @Schema(
            description = "Success message if validation passed",
            example = "VALID"
    )
    private String message;
}
