package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.company.dto.CreateCompanyDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateCustomerCountriesDto extends BaseDto {

    @Schema(description = "The company details associated with the customer")
    @JsonProperty("company")
    private CreateCompanyDto company;

    @Schema(description = "The customer details")
    @JsonProperty("customer")
    private CreateCustomerDto customer;

    @Schema(description = "The type of service provided to the customer")
    @JsonProperty("service_type")
    private String serviceType;

    @Schema(description = "The list of country codes associated with the customer")
    @JsonProperty("country_codes")
    private List<String> countryCodes;
}