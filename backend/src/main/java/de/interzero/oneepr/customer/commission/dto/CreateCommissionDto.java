package de.interzero.oneepr.customer.commission.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.commission.Commission;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new commission.
 * Converted from TypeScript create-commission.dto.ts with exact same structure and variable names.
 */
@Getter
@Setter
public class CreateCommissionDto {

    @Schema(description = "User ID")
    @JsonProperty("user_id")
    @NotNull
    private Integer userId;

    @Schema(description = "Commission percentage")
    @JsonProperty("commission_percentage")
    @NotNull
    private Integer commissionPercentage;

    @Schema(description = "Total price")
    @JsonProperty("price_total")
    @NotNull
    private Integer priceTotal;

    @Schema(description = "Coupon ID")
    @JsonProperty("coupon_id")
    private Integer couponId;

    @Schema(description = "Affiliate link")
    @JsonProperty("affiliate_link")
    private String affiliateLink;

    @Schema(description = "Order ID")
    @JsonProperty("order_id")
    @NotNull
    private Integer orderId;

    @Schema(description = "Order customer ID")
    @JsonProperty("order_customer_id")
    @NotNull
    private Integer orderCustomerId;

    @Schema(description = "Service type")
    @JsonProperty("service_type")
    @NotNull
    private Commission.ServiceType serviceType;
}
