package de.interzero.oneepr.customer.cluster.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;
import org.springframework.lang.Nullable;

@Schema(description = "Fields for find all paginated cluster request")
@Data
public class FindAllClustersPaginatedDto {

    @JsonProperty("page")
    @Schema(
            description = "Pagination page",
            example = "1"
    )
    @Min(1)
    private Integer page = 1;

    @JsonProperty("limit")
    @Schema(
            description = "Pagination limit",
            example = "10"
    )
    @Min(1)
    private Integer limit = 10;

    @JsonProperty("name")
    @Schema(
            description = "Filter by name",
            example = "Cluster 1"
    )
    @Nullable
    private String name;

    @JsonProperty("start_date")
    @Schema(
            description = "Filter by start date",
            example = "2025-01-01"
    )
    @Nullable
    private String startDate;

    @JsonProperty("end_date")
    @Schema(
            description = "Filter by start date",
            example = "2025-01-01"
    )
    @Nullable
    private String endDate;
}
