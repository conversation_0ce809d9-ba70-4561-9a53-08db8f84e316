package de.interzero.oneepr.customer.customer.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * An intermediate DTO to hold flattened license and country data for processing.
 * <p>
 * This DTO is required to efficiently transfer data from the database to the service
 * layer without loading complete and potentially heavy entity graphs. It allows a
 * custom JPQL query to select only the fields necessary for the aggregation logic.
 * <p>
 * It originates as a new construct in the Java implementation to translate the data
 * shape produced by the Prisma query's nested `select` in the original NestJS service.
 * <p>
 * Its sole usage is as the result object for the custom query in
 * {@code de.interzero.oneepr.customer.customer.CustomerRepository#findLicenseCountryInfoForActiveCustomers()}.
 * A list of these objects is then consumed by {@code CustomerService#groupByCountry()}.
 */
@Getter
@AllArgsConstructor
public class LicenseCountryInfo {

    private final Integer customerId;

    private final Integer countryId;

    private final String countryCode;

    private final String countryName;

    private final String countryFlag;
}