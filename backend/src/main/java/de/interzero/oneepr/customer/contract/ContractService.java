package de.interzero.oneepr.customer.contract;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service for managing contracts
 */
@Service
@RequiredArgsConstructor
public class ContractService {

    /**
     * Create a pdf for a contract. The original function from the JS code had an additional parameter to accept
     * a DB transaction, but this is not needed in Java as the transaction is propagated.
     *
     * @param contractId the contact to make the pdf for
     */
    @Transactional
    public void generateContractPdf(Integer contractId) {
        // TODO implement this
    }

    /**
     * Create a pdf for a service overview. The original function from the JS code had an additional parameter to accept
     * a DB transaction, but this is not needed in Java as the transaction is propagated.
     *
     * @param contractId the contact to make the pdf for
     */
    public void generateServiceOverviewPdf(Integer contractId) {
        // TODO implement this
    }

}
