package de.interzero.oneepr.customer.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.partner.Partner;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "partner_contract",
        schema = "public"
)
public class PartnerContract {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "partner_contract_id_gen"
    )
    @SequenceGenerator(
            name = "partner_contract_id_gen",
            sequenceName = "partner_contract_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @OneToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @JoinColumn(
            name = "partner_id",
            nullable = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JsonIgnore
    private Partner partner;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "status",
            nullable = false
    )
    @JsonProperty("status")
    private Status status = Status.DRAFT;

    @Column(name = "agreed_on")
    @JsonProperty("agreed_on")
    private Instant agreedOn;

    @NotNull
    @Column(
            name = "start_date",
            nullable = false
    )
    @JsonProperty("start_date")
    private Instant startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    private Instant endDate;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @OneToMany(
            mappedBy = "partnerContract",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL
    )
    @JsonIgnore
    private List<File> files;

    @OneToMany(
            mappedBy = "partnerContract",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL
    )
    @JsonIgnore
    private List<PartnerContractChange> changes;

    @PrePersist
    protected void onCreate() {
        Instant now = Instant.now();
        createdAt = now;
        updatedAt = now;
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }

    public enum Status {
        DRAFT,
        TO_BE_SIGNED,
        ACTIVE,
        ARCHIVED
    }
}
