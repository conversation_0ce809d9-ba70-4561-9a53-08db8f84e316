package de.interzero.oneepr.customer.license_required_information.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new license required information record.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateLicenseRequiredInformationDto extends BaseDto {

    @JsonProperty("setup_required_information_id")
    @Schema(
            description = "The setup required information ID. If its country related information.",
            example = "1"
    )
    private Integer setupRequiredInformationId;

    @JsonProperty("license_id")
    @Schema(
            description = "The license ID. If its country related information.",
            example = "1"
    )
    private Integer licenseId;

    @JsonProperty("setup_general_information_id")
    @Schema(
            description = "The setup required information ID. If its general information.",
            example = "1"
    )
    private Integer setupGeneralInformationId;

    @JsonProperty("contract_id")
    @Schema(
            description = "The contract ID. If its contract related information.",
            example = "1"
    )
    private Integer contractId;

    @NotNull
    @JsonProperty("type")
    @Schema(
            description = "The type of required information",
            example = "TEXT",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private LicenseRequiredInformation.Type type;

    @NotNull
    @JsonProperty("kind")
    @Schema(
            description = "The kind of required information",
            example = "REQUIRED_INFORMATION",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private LicenseRequiredInformation.Kind kind;

    @NotNull
    @JsonProperty("status")
    @Schema(
            description = "The status of required information",
            example = "OPEN",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private LicenseRequiredInformation.Status status;

    @NotBlank
    @JsonProperty("name")
    @Schema(
            description = "The name of required information",
            example = "Company Registration",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String name;

    @NotBlank
    @JsonProperty("description")
    @Schema(
            description = "The description of required information",
            example = "Please provide company registration details",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String description;

    @JsonProperty("question")
    @Schema(
            description = "The question to be answered",
            example = "What is your company registration number?",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String question;

    @JsonProperty("file_id")
    @Schema(
            description = "The file ID",
            example = "123e4567-e89b-12d3-a456-426614174000",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String fileId;

    @JsonProperty("answer")
    @Schema(
            description = "The answer provided",
            example = "12345678",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String answer;
}
