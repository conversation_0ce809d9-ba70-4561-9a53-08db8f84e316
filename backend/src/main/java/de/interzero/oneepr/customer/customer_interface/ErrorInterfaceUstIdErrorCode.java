package de.interzero.oneepr.customer.customer_interface;

import lombok.Getter;

/**
 * A single .java source file can contain at most one public top-level type (enum, class, or interface). The name of that .java file must also match the name of that single public type.
 * Therefore, when need two enums ( UstIdErrorCode and EnUstIdErrorCode) to be accessible from outside their package,
 * Place each public enum in its own separate .java file.
 * This enum is from JS code customer-api\customer-api\src\error-exception\errors.interface.ts -- enum UstIdError
 * Note: Cannot name a package "interface" because interface is a reserved keyword in Java.So this package is named customer_interface
 *
 * @ts-legacy Constant names should comply with a naming convention --- completely copy code from JS code.
 */
@Getter
public enum ErrorInterfaceUstIdErrorCode {
    Code200("Die angefragte USt-IdNr. ist gültig."),
    Code201("Die angefragte USt-IdNr. ist ungültig."),
    Code202("Die angefragte USt-IdNr. ist ungültig. Sie ist nicht in der Unternehmerdatei des betreffenden EU-Mitgliedstaates registriert."),
    Code203("Die angefragte USt-IdNr. ist ungültig. Sie ist erst ab dem ... gültig (siehe Feld 'Gueltig_ab')."),
    Code204("Die angefragte USt-IdNr. ist ungültig. Sie war im Zeitraum von ... bis ... gültig (siehe Feld 'Gueltig_ab' und 'Gueltig_bis')."),
    Code205("Ihre Anfrage kann derzeit durch den angefragten EU-Mitgliedstaat oder aus anderen Gründen nicht beantwortet werden. Bitte versuchen Sie es später noch einmal. Bei wiederholten Problemen wenden Sie sich bitte an das Bundeszentralamt für Steuern - Dienstsitz Saarlouis."),
    Code206("Ihre deutsche USt-IdNr. ist ungültig. Eine Bestätigungsanfrage ist daher nicht möglich. Den Grund hierfür können Sie beim Bundeszentralamt für Steuern - Dienstsitz Saarlouis - erfragen."),
    Code208("Für die von Ihnen angefragte USt-IdNr. läuft gerade eine Anfrage von einem anderen Nutzer. Eine Bearbeitung ist daher nicht möglich. Bitte versuchen Sie es später noch einmal."),
    Code209("Die angefragte USt-IdNr. ist ungültig. Sie entspricht nicht dem Aufbau der für diesen EU-Mitgliedstaat gilt. (Aufbau der USt-IdNr. aller EU-Länder)"),
    Code210("Die angefragte USt-IdNr. ist ungültig. Sie entspricht nicht den Prüfziffernregeln die für diesen EU-Mitgliedstaat gelten."),
    Code211("Die angefragte USt-IdNr. ist ungültig. Sie enthält unzulässige Zeichen (wie z.B. Leerzeichen oder Punkt oder Bindestrich usw.)."),
    Code212("Die angefragte USt-IdNr. ist ungültig. Sie enthält ein unzulässiges Länderkennzeichen."),
    Code213("Sie sind nicht zur Abfrage einer deutschen USt-IdNr. berechtigt."),
    Code214("Ihre deutsche USt-IdNr. ist fehlerhaft. Sie beginnt mit 'DE' gefolgt von 9 Ziffern."),
    Code215("Ihre Anfrage enthält nicht alle notwendigen Angaben für eine einfache Bestätigungsanfrage (Ihre deutsche USt-IdNr. und die ausl. USt-IdNr.).Ihre Anfrage kann deshalb nicht bearbeitet werden."),
    Code216("Ihre Anfrage enthält nicht alle notwendigen Angaben für eine qualifizierte Bestätigungsanfrage (Ihre deutsche USt-IdNr., die ausl. USt-IdNr., Firmenname einschl. Rechtsform und Ort).Es wurde eine einfache Bestätigungsanfrage durchgeführt mit folgenden Ergebnis: Die angefragte USt-IdNr. ist gültig."),
    Code217("Bei der Verarbeitung der Daten aus dem angefragten EU-Mitgliedstaat ist ein Fehler aufgetreten. Ihre Anfrage kann deshalb nicht bearbeitet werden."),
    Code218("Eine qualifizierte Bestätigung ist zur Zeit nicht möglich. Es wurde eine einfache Bestätigungsanfrage mit folgendem Ergebnis durchgeführt:Die angefragte USt-IdNr. ist gültig."),
    Code219("Bei der Durchführung der qualifizierten Bestätigungsanfrage ist ein Fehler aufgetreten. Es wurde eine einfache Bestätigungsanfrage mit folgendem Ergebnis durchgeführt:Die angefragte USt-IdNr. ist gültig."),
    Code221("Die Anfragedaten enthalten nicht alle notwendigen Parameter oder einen ungültigen Datentyp. Weitere Informationen erhalten Sie bei den Hinweisen zum Schnittstelle - Aufruf."),
    Code223("Die angefragte USt-IdNr. ist gültig. Die Druckfunktion steht nicht mehr zur Verfügung, da der Nachweis gem. UStAE zu § 18e.1 zu führen ist."),
    Code999("Eine Bearbeitung Ihrer Anfrage ist zurzeit nicht möglich. Bitte versuchen Sie es später noch einmal.");

    private final String description;

    ErrorInterfaceUstIdErrorCode(String description) {
        this.description = description;
    }

}