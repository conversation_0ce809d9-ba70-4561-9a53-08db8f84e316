package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Data Transfer Object representing the address details of a company.
 * <p>
 * This DTO is a direct translation of the {@code CompanyAddress} model included in
 * the {@code companies} relation of the {@code customer.service.ts#details} method.
 */
@Data
@NoArgsConstructor
public class CompanyAddressDto {

    @Schema(description = "Unique identifier of the company address.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "ISO country code for the address.")
    @JsonProperty("country_code")
    private String countryCode;

    @Schema(description = "Primary address line.")
    @JsonProperty("address_line")
    private String addressLine;

    @Schema(description = "City of the address.")
    @JsonProperty("city")
    private String city;

    @Schema(description = "ZIP or postal code of the address.")
    @JsonProperty("zip_code")
    private String zipCode;

    @Schema(description = "Street name and number.")
    @JsonProperty("street_and_number")
    private String streetAndNumber;

    @Schema(description = "Additional address information.")
    @JsonProperty("additional_address")
    private String additionalAddress;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;
}