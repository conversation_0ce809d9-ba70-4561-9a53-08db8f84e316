package de.interzero.oneepr.customer.customer_consent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ts-legacy This should not inherit from {@link CreateCustomerConsentDto}
 */
@Schema(description = "Fields required to update a customer consent")
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateCustomerConsentDto extends CreateCustomerConsentDto {

    @JsonProperty("id")
    //@ts-legacy no api content here
    private Integer id;
}
