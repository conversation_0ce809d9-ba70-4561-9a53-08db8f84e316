package de.interzero.oneepr.customer.license;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.certificate.Certificate;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.entity.LicensePriceList;
import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.license_other_cost.LicenseOtherCost;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_representative_tier.LicenseRepresentativeTier;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import de.interzero.oneepr.customer.license_third_party_invoice.LicenseThirdPartyInvoice;
import de.interzero.oneepr.customer.service_next_step.ServiceNextStep;
import de.interzero.oneepr.customer.termination.Termination;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "license",
        schema = "public"
)
public class License {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "license_id_gen"
    )
    @SequenceGenerator(
            name = "license_id_gen",
            sequenceName = "license_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "contract_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("contract")
    private Contract contract;

    @NotNull
    @Column(
            name = "registration_number",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("registration_number")
    private String registrationNumber;

    @NotNull
    @Column(
            name = "country_id",
            nullable = false
    )
    @JsonProperty("country_id")
    private Integer countryId;

    @NotNull
    @Column(
            name = "country_code",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("country_code")
    private String countryCode;

    @NotNull
    @Column(
            name = "country_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("country_name")
    private String countryName;

    @NotNull
    @Column(
            name = "country_flag",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("country_flag")
    private String countryFlag;

    @NotNull
    @Column(
            name = "year",
            nullable = false
    )
    @JsonProperty("year")
    private Integer year;

    @NotNull
    @Column(
            name = "start_date",
            nullable = false
    )
    @JsonProperty("start_date")
    private Instant startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    private Instant endDate;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "termination_id")
    @JsonIgnore
    @JsonProperty("termination")
    private Termination termination;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @Column(name = "registration_and_termination_monday_ref")
    @JsonProperty("registration_and_termination_monday_ref")
    private Integer registrationAndTerminationMondayRef;

    @JsonIgnore
    @JsonProperty("certificates")
    @OneToMany(
            mappedBy = "license",
            fetch = FetchType.LAZY,
            cascade = CascadeType.PERSIST
    )
    private List<Certificate> certificates;

    @JsonIgnore
    @JsonProperty("files")
    @OneToMany(
            mappedBy = "license",
            fetch = FetchType.LAZY
    )
    private List<File> files;

    @JsonIgnore
    @JsonProperty("other_costs")
    @OneToMany(
            mappedBy = "license",
            fetch = FetchType.LAZY
    )
    private List<LicenseOtherCost> otherCosts;

    @JsonIgnore
    @JsonProperty("packaging_services")
    @OneToMany(
            mappedBy = "license",
            fetch = FetchType.LAZY
    )
    private List<LicensePackagingService> packagingServices;

    @JsonIgnore
    @JsonProperty("price_list")
    @OneToMany(
            mappedBy = "license",
            fetch = FetchType.LAZY,
            cascade = CascadeType.PERSIST
    )
    private List<LicensePriceList> priceList;

    @JsonIgnore
    @JsonProperty("representative_tiers")
    @OneToMany(
            mappedBy = "license",
            fetch = FetchType.LAZY
    )
    private List<LicenseRepresentativeTier> representativeTiers;

    @JsonIgnore
    @JsonProperty("required_informations")
    @OneToMany(
            mappedBy = "license",
            fetch = FetchType.LAZY,
            cascade = CascadeType.PERSIST
    )
    private List<LicenseRequiredInformation> requiredInformations;

    @JsonIgnore
    @JsonProperty("third_party_invoices")
    @OneToMany(
            mappedBy = "license",
            fetch = FetchType.LAZY
    )
    private List<LicenseThirdPartyInvoice> thirdPartyInvoices;

    @JsonIgnore
    @JsonProperty("next_steps")
    @OneToMany(
            mappedBy = "license",
            fetch = FetchType.LAZY
    )
    private List<ServiceNextStep> nextSteps;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @ColumnDefault("'PENDING'")
    @Column(name = "registration_status")
    @JsonProperty("registration_status")
    private RegistrationStatus registrationStatus = RegistrationStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @ColumnDefault("'PENDING'")
    @Column(name = "clerk_control_status")
    @JsonProperty("clerk_control_status")
    private ClerkControlStatus clerkControlStatus = ClerkControlStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @ColumnDefault("'ACTIVE'")
    @Column(name = "contract_status")
    @JsonProperty("contract_status")
    private ContractStatus contractStatus = ContractStatus.ACTIVE;

    public enum RegistrationStatus {
        PENDING,
        IN_REVIEW,
        REGISTRATION,
        DONE
    }

    public enum ClerkControlStatus {
        PENDING,
        DONE
    }

    public enum ContractStatus {
        ACTIVE,
        TERMINATION_PROCESS,
        TERMINATED
    }
}