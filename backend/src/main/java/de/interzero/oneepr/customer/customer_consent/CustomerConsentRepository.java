package de.interzero.oneepr.customer.customer_consent;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface CustomerConsentRepository extends JpaRepository<CustomerConsent, Integer> {

    // For findByCustomerId to fetch relations
    @Query("SELECT cc FROM CustomerConsent cc LEFT JOIN FETCH cc.customer c LEFT JOIN FETCH cc.consent WHERE c.id = :customerId")
    List<CustomerConsent> findAllByCustomerId(@org.springframework.data.repository.query.Param("customerId") Integer customerId);

    // For findOne and update to fetch relations
    @Query("SELECT cc FROM CustomerConsent cc LEFT JOIN FETCH cc.customer LEFT JOIN FETCH cc.consent WHERE cc.id = :id")
    Optional<CustomerConsent> findByIdAndFetchAll(@org.springframework.data.repository.query.Param("id") Integer id);

    // For validatingUserPermissionCustomerConsent
    @Query("SELECT cc FROM CustomerConsent cc LEFT JOIN FETCH cc.customer WHERE cc.id = :id")
    Optional<CustomerConsent> findByIdAndFetchCustomer(@org.springframework.data.repository.query.Param("id") Integer id);

    /**
     * Updates specific attributes of an existing CustomerConsent record.
     * This directly mimics the update part of the original TypeScript code's transaction.
     */
    @Modifying
    @Query(
            "UPDATE CustomerConsent cc SET cc.given = :given, cc.givenAt = :givenAt, " + "cc.revokedAt = :revokedAt, cc.updatedAt = :updatedAt " + "WHERE cc.customer.id = :customerId AND cc.consent.id = :consentId"
    )
    int updateConsentAttributes(@Param("customerId") Integer customerId,
                                @Param("consentId") Integer consentId,
                                @Param("given") boolean given,
                                @Param("givenAt") Instant givenAt,
                                @Param("revokedAt") Instant revokedAt,
                                @Param("updatedAt") Instant updatedAt);
}