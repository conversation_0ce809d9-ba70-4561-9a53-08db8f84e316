package de.interzero.oneepr.customer.file.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RequestPresignedUrlDto {

    @Schema(
            description = "The filename of the file",
            example = "contract.pdf"
    )
    @JsonProperty("filename")
    private String filename;

    @Schema(
            description = "The type of the file",
            example = "application/pdf"
    )
    @JsonProperty("fileType")
    private String fileType;
}
