package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Data Transfer Object for creating a new customer.
 * <p>
 * This DTO is designed to be used for transferring customer creation data.
 * It maps Java camelCase field names to snake_case JSON properties.
 * For example, the Java field {@code firstName} is represented as {@code "first_name"} in JSON.
 */

@Data
public class CreateCustomerDto {

    @Schema(description = "The first name of the customer")
    @JsonProperty("first_name")
    private String firstName;

    @Schema(description = "The last name of the customer")
    @JsonProperty("last_name")
    private String lastName;

    @Schema(
            description = "The company name of the customer",
            nullable = true
    )
    @JsonProperty("company_name")
    private String companyName;

    @Schema(description = "The salutation of the customer")
    @JsonProperty("salutation")
    private String salutation;

    @Schema(description = "The email of the customer")
    @JsonProperty("email")
    private String email;

    @Schema(
            description = "The password of the customer",
            nullable = true
    )
    @JsonProperty("password")
    private String password;

    @Schema(
            description = "The token magic link of the customer",
            nullable = true
    )
    @JsonProperty("token_magic_link")
    private String tokenMagicLink;

    @Schema(
            description = "The document id of the customer",
            type = "integer",
            format = "int32",
            nullable = true
    )
    @JsonProperty("document_id")
    private Integer documentId;

    @Schema(
            description = "The user id of the customer",
            type = "integer",
            format = "int32"
    )
    @JsonProperty("user_id")
    private Integer userId;

    @Schema(
            description = "The stripe id of the customer",
            nullable = true
    )
    @JsonProperty("id_stripe")
    private String idStripe;

    @Schema(
            description = "Language of the customer",
            nullable = true
    )
    @JsonProperty("language")
    private String language;

    @JsonProperty("currency")
    @Schema(
            description = "Currency of the customer",
            example = "EUR"
    )
    private String currency;

    @Schema(description = "A list of phone numbers for the customer")
    @JsonProperty("phones")
    private List<String> phones;

}