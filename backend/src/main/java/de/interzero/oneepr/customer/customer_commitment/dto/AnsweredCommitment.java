package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Represents a criteria item within a commitment that includes the customer's answer.
 *
 * @ts-legacy Corresponds to (Omit<Criteria, "mode"> & { mode: "COMMITMENT"; answer: string; to_answer?: string })
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AnsweredCommitment extends Commitment {

    @JsonProperty("answer")
    private String answer;

    @JsonProperty("to_answer")
    private String toAnswer;
}