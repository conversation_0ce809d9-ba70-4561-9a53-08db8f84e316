package de.interzero.oneepr.customer.customer_activity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.customer_activity.CustomerActivity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * DTO for updating an existing customer activity record.
 * All fields are optional; only provided fields will be considered for update.
 * making all fields from CreateCustomerActivityDto optional.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCustomerActivityDto {

    /**
     * The ID of the customer associated with this activity.
     * Typically, the customer association is not changed during an update of an activity.
     * If provided, service logic will determine how it's handled (e.g., ignored or validated).
     * Made optional by PartialType.
     */
    @Schema(
            description = "Optional: The ID of the customer. Typically not changed on update.",
            example = "42",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("customer_id")
    private Integer customerId;

    /**
     * The type of the customer activity.
     */
    @Schema(
            description = "Optional: New type of the customer activity.",
            implementation = CustomerActivity.Type.class,
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("type")
    private CustomerActivity.Type type;

    /**
     * Additional metadata associated with the activity, as a key-value map.
     * This can be any valid JSON object.
     *
     * @ts-legacy Original NestJS DTO field 'metadata' had type Record<string, any>. Made optional by PartialType.
     */
    @Schema(
            description = "Optional: New metadata for the activity (represented as a JSON object). If provided, typically replaces existing metadata.",
            example = "{\"details\":\"Updated login context\", \"reason\":\"user_request\"}",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("metadata")
    private Map<String, Object> metadata;
}