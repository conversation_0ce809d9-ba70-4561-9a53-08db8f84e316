package de.interzero.oneepr.customer.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.company_billing.CompanyBilling;
import de.interzero.oneepr.customer.company_email.CompanyEmail;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.entity.CompanyAddress;
import de.interzero.oneepr.customer.entity.CompanyContact;
import de.interzero.oneepr.customer.partner.Partner;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "company",
        schema = "public"
)
public class Company {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "company_id_gen"
    )
    @SequenceGenerator(
            name = "company_id_gen",
            sequenceName = "company_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @Column(
            name = "description",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("description")
    private String description;

    @Column(
            name = "vat",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("vat")
    private String vat;

    @Column(
            name = "tin",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("tin")
    private String tin;

    @Column(
            name = "lucid",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("lucid")
    private String lucid;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "customer_id")
    @JsonProperty("customer")
    private Customer customer;

    @Column(name = "starting")
    @JsonProperty("starting")
    private Instant starting;

    @Column(
            name = "website",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("website")
    private String website;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "partner_id")
    @JsonProperty("partner")
    private Partner partner;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "address_id")
    @JsonProperty("address")
    private CompanyAddress address;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @Column(
            name = "industry_sector",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("industry_sector")
    private String industrySector;

    @Column(
            name = "owner_name",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("owner_name")
    private String ownerName;

    @OneToOne(
            mappedBy = "company",
            cascade = CascadeType.ALL
    )
    @JsonProperty("contacts")
    private CompanyContact contacts;

    @OneToMany(
            mappedBy = "company",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL
    )
    @JsonProperty("emails")
    private List<CompanyEmail> emails;


    @OneToOne(
            mappedBy = "company",
            cascade = CascadeType.ALL
    )
    @JsonProperty("billing")
    private CompanyBilling billing;

}