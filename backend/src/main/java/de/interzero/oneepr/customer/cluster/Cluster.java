package de.interzero.oneepr.customer.cluster;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Entity
@Table(
        name = "cluster",
        schema = "public"
)
public class Cluster {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "cluster_id_gen"
    )
    @SequenceGenerator(
            name = "cluster_id_gen",
            sequenceName = "cluster_id_seq",
            allocationSize = 1
    )
    @JsonProperty("id")
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @JsonProperty("name")
    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String name;

    @JsonProperty("registration_start_date")
    @NotNull
    @Column(
            name = "registration_start_date",
            nullable = false
    )
    private Instant registrationStartDate;

    @JsonProperty("registration_end_date")
    @NotNull
    @Column(
            name = "registration_end_date",
            nullable = false
    )
    private Instant registrationEndDate;

    @JsonProperty("status")
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "status"
    )
    private Status status;

    @JsonProperty("min_household_packaging")
    @NotNull
    @Column(
            name = "min_household_packaging",
            nullable = false
    )
    private Integer minHouseholdPackaging;

    @JsonProperty("max_household_packaging")
    @NotNull
    @Column(
            name = "max_household_packaging",
            nullable = false
    )
    private Integer maxHouseholdPackaging;

    @JsonProperty("type_of_services")
    @Column(name = "type_of_services")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Boolean> typeOfServices;

    @JsonProperty("participating_countries")
    @Column(name = "participating_countries")
    @JdbcTypeCode(SqlTypes.JSON)
    private List<String> participatingCountries;

    @JsonProperty("customers")
    @JsonIgnore
    @OneToMany(
            mappedBy = "cluster",
            fetch = FetchType.LAZY,
            cascade = CascadeType.PERSIST
    )
    private List<ClusterCustomer> customers;

    @JsonProperty("created_at")
    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @JsonProperty("updated_at")
    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    @Column(name = "deleted_at")
    private Instant deletedAt;

    public enum Status {
        ACTIVE,
        INACTIVE
    }

    /**
     * Update clusters auditing fields.
     */
    @PrePersist
    protected void onCreate() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    /**
     * Update clusters auditing fields on update.
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }
}