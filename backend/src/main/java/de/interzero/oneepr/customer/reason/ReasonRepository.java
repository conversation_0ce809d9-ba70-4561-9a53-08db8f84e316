package de.interzero.oneepr.customer.reason;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReasonRepository extends JpaRepository<Reason, Integer> {

    /**
     * Finds all reasons by type (that are not "deleted")
     *
     * @param type the type of reason
     * @return the list of reasons
     */
    List<Reason> findAllByDeletedAtIsNullAndType(Reason.Type type);
}
