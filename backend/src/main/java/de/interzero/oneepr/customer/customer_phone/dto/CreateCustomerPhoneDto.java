package de.interzero.oneepr.customer.customer_phone.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Data Transfer Object for creating a new customer.
 * <p>
 * This DTO is designed to be used for transferring customer creation data.
 * It maps Java camelCase field names to snake_case JSON properties.
 * For example, the Java field {@code customerId} is represented as {@code "customer_id"} in JSON.
 */
@Data
public class CreateCustomerPhoneDto {

    @JsonProperty("customer_id")
    @Schema(description = "The customer id")
    private Integer customerId;

    @JsonProperty("phone_number")
    @Schema(description = "the phone number")
    private String phoneNumber;

}
