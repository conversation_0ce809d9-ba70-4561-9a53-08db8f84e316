package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "license_volume_report_item",
        schema = "public"
)
public class LicenseVolumeReportItem {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "license_volume_report_item_id_gen"
    )
    @SequenceGenerator(
            name = "license_volume_report_item_id_gen",
            sequenceName = "license_volume_report_item_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "license_volume_report_id",
            nullable = false
    )
    private LicenseVolumeReport licenseVolumeReport;

    @NotNull
    @Column(
            name = "setup_fraction_id",
            nullable = false
    )
    private Integer setupFractionId;

    @NotNull
    @Column(
            name = "setup_column_id",
            nullable = false
    )
    private Integer setupColumnId;

    @NotNull
    @Column(
            name = "value",
            nullable = false
    )
    private Integer value;

    @NotNull
    @Column(
            name = "price",
            nullable = false
    )
    private Integer price;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

    @Column(
            name = "setup_column_code",
            length = Integer.MAX_VALUE
    )
    private String setupColumnCode;

    @Column(
            name = "setup_fraction_code",
            length = Integer.MAX_VALUE
    )
    private String setupFractionCode;

}