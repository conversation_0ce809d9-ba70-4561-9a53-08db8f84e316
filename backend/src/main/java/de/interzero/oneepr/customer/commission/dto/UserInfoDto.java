package de.interzero.oneepr.customer.commission.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.commission.Commission;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoDto {

    @Schema(description = "User ID")
    @JsonProperty("user_id")
    private Integer userId;

    @Schema(description = "User type")
    @JsonProperty("user_type")
    private Commission.UserType userType;


}
