package de.interzero.oneepr.customer.license_other_cost.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new "other cost" associated with a license.
 * This DTO contains all the necessary information to record an additional cost item.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateLicenseOtherCostDto {

    @NotNull
    @JsonProperty("license_id")
    @Schema(
            description = "The ID of the license this cost is associated with.",
            example = "123",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer licenseId;

    @NotNull
    @JsonProperty("setup_other_cost_id")
    @Schema(
            description = "The setup ID for this type of other cost.",
            example = "456",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer setupOtherCostId;

    @NotBlank
    @JsonProperty("name")
    @Schema(
            description = "The descriptive name of the other cost.",
            example = "Special Handling Fee",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String name;

    @NotNull
    @JsonProperty("price")
    @Schema(
            description = "The price of the other cost, specified in the smallest currency unit (e.g., cents).",
            example = "5000",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer price;
}