package de.interzero.oneepr.customer.customer_document;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.customer_document.dto.CreateCustomerDocumentDto;
import de.interzero.oneepr.customer.customer_document.dto.UpdateCustomerDocumentDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;

/**
 * Controller for managing customer documents.
 * Exposes endpoints for creating, retrieving, updating, and deleting customer documents.
 * Access control is applied based on user roles.
 */
@Tag(
        name = "customer-document",
        description = "Operations related to customer documents"
)
@RestController
@RequestMapping(Api.CUSTOMER_DOCUMENT)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@RequiredArgsConstructor
public class CustomerDocumentController {

    private final CustomerDocumentService customerDocumentService;

    /**
     * Creates a new customer document.
     *
     * @param createDto DTO containing information for the new document.
     * @return The created customer document.
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(
            summary = "Create customer document",
            responses = {@ApiResponse(
                    responseCode = "201",
                    description = "The customer document has been successfully created.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CustomerDocument.class)
                    )
            )}
    )
    public CustomerDocument create(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Data to create a new customer document.",
            required = true,
            content = @Content(schema = @Schema(implementation = CreateCustomerDocumentDto.class))
    ) @RequestBody CreateCustomerDocumentDto createDto) {
        return this.customerDocumentService.create(createDto);
    }

    /**
     * Retrieves all customer documents.
     *
     * @return A list of all customer documents.
     */
    @GetMapping
    @Operation(
            summary = "Get all customer documents",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "List of customer documents",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(
                                    implementation = List.class,
                                    subTypes = {CustomerDocument.class}
                            )
                    )
            )}
    )
    public List<CustomerDocument> findAll() {
        return this.customerDocumentService.findAll();
    }

    /**
     * Retrieves a specific customer document by its ID.
     * User details are obtained internally via AuthUtil and passed directly to the service.
     *
     * @param idRaw The string ID of the customer document from the path.
     * @return The customer document details.
     * @ts-legacy Explicit ID parsing added.
     */
    @GetMapping("/{id}")
    @Operation(
            summary = "Get customer document by id",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "The customer document details",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CustomerDocument.class)
                    )
            )}
    )
    public CustomerDocument findOne(@Parameter(description = "ID of the customer document to retrieve.") @PathVariable("id") String idRaw) {
        int id;
        try {
            id = Integer.parseInt(idRaw);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Invalid ID format: '" + idRaw + "' must be an integer.");
        }
        return this.customerDocumentService.findOne(id, AuthUtil.getRelevantUserDetails());
    }

    /**
     * Updates an existing customer document by its ID.
     * User details are obtained internally via AuthUtil and passed directly to the service.
     *
     * @param idRaw     The string ID of the customer document to update.
     * @param updateDto DTO containing updated data.
     * @return The updated customer document.
     * @ts-legacy Explicit ID parsing added.
     */
    @PutMapping("/{id}")
    @Operation(
            summary = "Update customer document by id",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "The customer document has been successfully updated.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CustomerDocument.class)
                    )
            )}
    )
    public CustomerDocument update(@Parameter(description = "ID of the customer document to update.") @PathVariable("id") String idRaw,
                                   @io.swagger.v3.oas.annotations.parameters.RequestBody(
                                           description = "Data to update the customer document.",
                                           required = true,
                                           content = @Content(schema = @Schema(implementation = UpdateCustomerDocumentDto.class))
                                   ) @RequestBody UpdateCustomerDocumentDto updateDto) {
        int id;
        try {
            id = Integer.parseInt(idRaw);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Invalid ID format: '" + idRaw + "' must be an integer.");
        }
        return this.customerDocumentService.update(id, updateDto, AuthUtil.getRelevantUserDetails());
    }

    /**
     * Deletes a customer document by its ID.
     * User details are obtained internally via AuthUtil and passed directly to the service.
     *
     * @param idRaw The string ID of the customer document to delete.
     * @ts-legacy Corresponds to DELETE /:id. Explicit ID parsing added. Service method is void,
     * returns HTTP 200 OK.
     */
    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    @Operation(
            summary = "Delete customer document by id",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "The customer document has been successfully deleted."
            )}
    )
    public void remove(@Parameter(description = "ID of the customer document to delete.") @PathVariable("id") String idRaw) {
        int id;
        try {
            id = Integer.parseInt(idRaw);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Invalid ID format: '" + idRaw + "' must be an integer.");
        }
        this.customerDocumentService.remove(id, AuthUtil.getRelevantUserDetails());
    }
}