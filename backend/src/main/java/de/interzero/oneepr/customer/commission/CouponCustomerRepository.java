package de.interzero.oneepr.customer.commission;

import de.interzero.oneepr.customer.entity.CouponCustomer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


@Repository
public interface CouponCustomerRepository extends JpaRepository<CouponCustomer, Integer> {

    /**
     * Check if coupon-customer relationship exists
     *
     * @param couponId   Coupon ID
     * @param customerId Customer ID
     * @return True if relationship exists
     */
    boolean existsByCouponIdAndCustomerId(Integer couponId,
                                          Integer customerId);
}
