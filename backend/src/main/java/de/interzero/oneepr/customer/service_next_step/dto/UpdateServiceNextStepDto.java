package de.interzero.oneepr.customer.service_next_step.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * UpdateServiceNextStepDto
 * DTO for updating an existing service's next step.
 * All fields are optional, allowing for partial updates.
 * Excludes license_id and action_guide_id from the update operations.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateServiceNextStepDto {

    @JsonProperty("title")
    @Schema(
            description = "The title of the next step. If provided, updates the title.",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String title;

    @JsonProperty("available_date")
    @Schema(
            description = "The available date of the next step (e.g., ISO 8601 date-time string). If provided, updates the date.",
            type = "string",
            format = "date-time",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String availableDate;

    @JsonProperty("deadline_date")
    @Schema(
            description = "The deadline date of the next step (e.g., ISO 8601 date-time string). If provided, updates the date.",
            type = "string",
            format = "date-time",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String deadlineDate;

    @JsonProperty("done_at")
    @Schema(
            description = "The done date of the next step (e.g., ISO 8601 date-time string). If provided, updates the date.",
            type = "string",
            format = "date-time",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String doneAt;
}
