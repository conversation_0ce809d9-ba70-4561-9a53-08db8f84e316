package de.interzero.oneepr.customer.cluster;

import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.cluster.dto.ClustersPaginatedDto;
import de.interzero.oneepr.customer.cluster.dto.CreateClusterDto;
import de.interzero.oneepr.customer.cluster.dto.FindAllClustersPaginatedDto;
import de.interzero.oneepr.customer.cluster.dto.UpdateClusterDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;

@Tag(
        name = "Cluster",
        description = "Operations related to cluster"
)
@SecurityRequirement(name = "bearerAuth")
@RestController
@RequestMapping(Api.CLUSTER)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@RequiredArgsConstructor
public class ClusterController {

    private final @Lazy ClusterService clusterService;

    @Operation(summary = "Create cluster")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public Cluster create(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = CreateClusterDto.class)
            )
    ) @RequestBody CreateClusterDto dto) {
        return clusterService.create(dto);
    }

    @Operation(summary = "Get all clusters")
    @GetMapping
    @PreAuthorize("permitAll()")
    public List<Cluster> findAll() {
        return clusterService.findAll();
    }

    @Operation(summary = "Get a cluster by ID")
    @GetMapping("{id}")
    public Cluster findOne(@Parameter(description = "ID of cluster to get") @PathVariable String id) {
        return clusterService.findOne(Integer.valueOf(id));
    }

    @Operation(summary = "Update a cluster")
    @PatchMapping("{id}")
    public Cluster update(@Parameter(description = "ID of cluster to update") @PathVariable String id,
                          @io.swagger.v3.oas.annotations.parameters.RequestBody(
                                  required = true,
                                  content = @Content(
                                          mediaType = "application/json",
                                          schema = @Schema(implementation = UpdateClusterDto.class)
                                  )
                          ) @RequestBody UpdateClusterDto dto) {
        return clusterService.update(Integer.valueOf(id), dto);
    }

    @Operation(summary = "Delete a cluster")
    @DeleteMapping("{id}")
    public void remove(@Parameter(description = "ID of cluster to delete") @PathVariable String id) {
        clusterService.remove(Integer.valueOf(id));
    }


    @Operation(summary = "Find all paginated by filter")
    @GetMapping("list/paginated")
    @ResponseStatus(HttpStatus.ACCEPTED)
    public ClustersPaginatedDto findAllPaginated(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = FindAllClustersPaginatedDto.class)
            )
    ) @RequestBody FindAllClustersPaginatedDto dto) {
        return clusterService.findAllPaginated(dto);
    }

}
