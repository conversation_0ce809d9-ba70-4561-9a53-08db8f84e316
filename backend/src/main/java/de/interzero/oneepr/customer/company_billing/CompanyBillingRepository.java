package de.interzero.oneepr.customer.company_billing;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for managing CompanyBilling entities.
 */
@Repository
public interface CompanyBillingRepository extends JpaRepository<CompanyBilling, Integer> {

    /**
     * Find company billing by company ID.
     * Used in update operations to update billing information.
     * Equivalent to TypeScript: findUnique({ where: { company_id: companyId } })
     *
     * @param companyId Company ID
     * @return Optional CompanyBilling
     */
    Optional<CompanyBilling> findCompanyBillingByCompany_Id(Integer companyId);

}
