package de.interzero.oneepr.customer.file.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class LambdaPresignedResponseDto {

    @Schema(description = "Upload URL")
    @JsonProperty("uploadUrl")
    private String uploadUrl;

    @Schema(description = "Fields for form data")
    @JsonProperty("fields")
    private Map<String, Object> fields;
}
