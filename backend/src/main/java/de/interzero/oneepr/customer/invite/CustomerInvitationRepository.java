package de.interzero.oneepr.customer.invite;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * JPA repository for managing CustomerInvitation entities.
 */
@Repository
public interface CustomerInvitationRepository extends JpaRepository<CustomerInvitation, Integer> {

    /**
     * Finds all customer invitations by customer ID.
     *
     * @param customerId the ID of the customer
     * @return list of invitations for the given customer
     */
    List<CustomerInvitation> findByCustomer_Id(Integer customerId);
}
