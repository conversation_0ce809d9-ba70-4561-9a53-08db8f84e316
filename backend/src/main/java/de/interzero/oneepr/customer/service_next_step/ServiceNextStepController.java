package de.interzero.oneepr.customer.service_next_step;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.service_next_step.dto.CreateServiceNextStepDto;
import de.interzero.oneepr.customer.service_next_step.dto.UpdateServiceNextStepDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;


/**
 * ServiceNextStepController
 * REST controller for managing service next steps.
 * Exposes endpoints for creating, retrieving, updating, and deleting service next steps.
 */
@RestController
@RequestMapping(Api.SERVICE_NEXT_STEPS)
@Tag(
        name = "Service Next Steps",
        description = "APIs for managing service next steps"
)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@RequiredArgsConstructor
public class ServiceNextStepController {

    private final ServiceNextStepService serviceNextStepService;


    /**
     * Retrieves all service next steps, optionally filtered by license ID or action guide ID.
     * <p>
     * The NestJS code did: license_id ? parseInt(license_id) : undefined
     * Spring's @RequestParam(required = false) Integer licenseId handles this.
     *
     * @param licenseId     Optional ID of the license to filter by.
     * @param actionGuideId Optional ID of the action guide to filter by.
     * @return A list of {@link ServiceNextStep} entities matching the criteria.
     */
    @GetMapping
    @Operation(summary = "Get all next steps, optionally filtered")
    @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved next steps"
    )
    public ResponseEntity<List<ServiceNextStep>> findAll(@Parameter(description = "ID of the license to filter by") @RequestParam(
                                                                 value = "license_id",
                                                                 required = false
                                                         ) Integer licenseId,
                                                         @Parameter(description = "ID of the action guide to filter by") @RequestParam(
                                                                 value = "action_guide_id",
                                                                 required = false
                                                         ) Integer actionGuideId) {
        List<ServiceNextStep> nextSteps = serviceNextStepService.findAll(licenseId, actionGuideId);
        return ResponseEntity.ok(nextSteps);
    }

    /**
     * Retrieves a single service next step by its ID.
     * Requires user authentication and passes the user to the service for permission checks.
     *
     * @param id The ID of the service next step to find.
     * @return The found {@link ServiceNextStep} entity.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get a next step by id")
    @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved next step"
    )
    public ResponseEntity<ServiceNextStep> findOne(@Parameter(description = "ID of the service next step to retrieve") @PathVariable("id") Integer id) {
        ServiceNextStep nextStep = serviceNextStepService.findOne(id, AuthUtil.getRelevantUserDetails());
        return ResponseEntity.ok(nextStep);
    }

    /**
     * Creates a new service next step.
     *
     * @param data The DTO containing data for the new service next step.
     * @return The created {@link ServiceNextStep} entity.
     */
    @PostMapping
    @Operation(summary = "Create a next step")
    @ApiResponse(
            responseCode = "201",
            description = "Next step created successfully"
    )
    public ResponseEntity<ServiceNextStep> create(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Data for the new service next step",
            required = true
    ) @RequestBody CreateServiceNextStepDto data) {
        ServiceNextStep createdNextStep = serviceNextStepService.create(data);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdNextStep);
    }

    /**
     * Updates an existing service next step.
     * Requires user authentication and passes the user to the service for permission checks.
     *
     * @param id   The ID of the service next step to update.
     * @param data The DTO containing update data.
     * @return The updated {@link ServiceNextStep} entity.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update a next step")
    @ApiResponse(
            responseCode = "200",
            description = "Next step updated successfully"
    )
    public ResponseEntity<ServiceNextStep> update(@Parameter(description = "ID of the service next step to update") @PathVariable("id") Integer id,
                                                  @io.swagger.v3.oas.annotations.parameters.RequestBody(
                                                          description = "Data to update the service next step",
                                                          required = true
                                                  ) @RequestBody UpdateServiceNextStepDto data) {
        ServiceNextStep updatedNextStep = serviceNextStepService.update(id, data, AuthUtil.getRelevantUserDetails());
        return ResponseEntity.ok(updatedNextStep);
    }

    /**
     * Soft deletes a service next step by its ID.
     * Requires user authentication and passes the user to the service for permission checks.
     *
     * @param id The ID of the service next step to remove (soft delete).
     * @return The "removed" (soft-deleted) {@link ServiceNextStep} entity.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a next step")
    @ApiResponse(
            responseCode = "200",
            description = "Next step deleted successfully"
    )
    public ResponseEntity<ServiceNextStep> remove(@Parameter(description = "ID of the service next step to delete") @PathVariable("id") Integer id) {
        ServiceNextStep removedNextStep = serviceNextStepService.remove(id, AuthUtil.getRelevantUserDetails());
        return ResponseEntity.ok(removedNextStep);
    }
}
