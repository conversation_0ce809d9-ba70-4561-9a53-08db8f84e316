package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.customer.coupon.Coupon;
import de.interzero.oneepr.customer.customer.Customer;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@Getter
@Setter
@Entity
@Table(
        name = "coupon_customer",
        schema = "public"
)
public class CouponCustomer {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "coupon_customer_id_gen"
    )
    @SequenceGenerator(
            name = "coupon_customer_id_gen",
            sequenceName = "coupon_customer_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "coupon_id",
            nullable = false
    )
    private Coupon coupon;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "customer_id",
            nullable = false
    )
    private Customer customer;

}