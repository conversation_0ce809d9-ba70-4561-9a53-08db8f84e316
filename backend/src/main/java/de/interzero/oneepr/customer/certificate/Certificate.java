package de.interzero.oneepr.customer.certificate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.license.License;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "certificate")
public class Certificate {

    /**
     * The enum representing the availability status of a certificate.
     * Corresponds to the Prisma {@code CertificateStatus} enum.
     */
    public enum Status {
        AVAILABLE,
        NOT_AVAILABLE
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "status",
            nullable = false
    )
    @JsonProperty("status")
    private Status status = Status.NOT_AVAILABLE;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @JoinColumn(
            name = "license_id",
            nullable = false
    )
    @JsonIgnore
    private License license;

    @OneToMany(mappedBy = "certificate")
    @JsonIgnore
    @JsonProperty("files")
    private List<File> files;

    @Transient
    @JsonProperty("license_id")
    public Integer getLicenseId() {
        return license != null ? license.getId() : null;
    }
}