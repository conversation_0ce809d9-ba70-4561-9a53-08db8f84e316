package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Originally found in src/customer-commitment/dto/customer-commitment.dto.ts
 */
@Data
public class CustomerServiceSetup {

    @JsonProperty("country")
    private Country country;

    @JsonProperty("year")
    private String year;

    @JsonProperty("packaging_services")
    List<PackagingServiceExtended> packagingServices;

    @JsonProperty("authorize_representative_obligated")
    private Boolean authorizeRepresentativeObligated;

    @JsonProperty("representative_tier")
    private RepresentativeTier representativeTier;

    @JsonProperty("other_costs_obligated")
    private Boolean otherCostsObligated;

    @JsonProperty("other_costs")
    private List<OtherCost> otherCosts;

    @JsonProperty("required_informations")
    private List<RequiredInformation> requiredInformations;

    @JsonProperty("price_list")
    private LicensePriceList priceList;

    @Data
    public static class Country {

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("code")
        private String code;

        @JsonProperty("flag_url")
        private String flagUrl;

        @JsonProperty("authorize_representative_obligated")
        private Boolean authorizeRepresentativeObligated;

        @JsonProperty("other_costs_obligated")
        private Boolean otherCostsObligated;
    }

    /**
     * Extended PackagingService class to include additional fields
     * <p>
     * packaging_services: (PackagingService & {
     * report_set: ReportSet;
     * report_set_frequency: ReportSetFrequency;
     * })[];
     */
    @Data
    public static class PackagingServiceExtended extends PackagingService {

        @JsonProperty("report_set")
        ReportSet reportSet;

        @JsonProperty("report_set_frequency")
        ReportSetFrequency reportSetFrequency;
    }
}
