package de.interzero.oneepr.customer.customer_document.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for updating an existing customer document.
 * All fields are optional; only the fields provided in the request will be considered for update.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCustomerDocumentDto {

    @Schema(
            description = "Optional: The ID of the customer. Typically not updatable for an existing document.",
            example = "123",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("customer_id")
    private Integer customerId;

    @Schema(
            description = "Optional: The new accessible URL of the document.",
            example = "https://example.com/documents/updated_doc123.pdf",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("document_url")
    private String documentUrl;


    @Schema(
            description = "Optional: The new status of the document.",
            example = "VERIFIED",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("status")
    private String status;
}