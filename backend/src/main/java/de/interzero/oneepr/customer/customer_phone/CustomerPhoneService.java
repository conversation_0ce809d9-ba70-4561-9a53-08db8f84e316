package de.interzero.oneepr.customer.customer_phone;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerService;
import de.interzero.oneepr.customer.customer_phone.dto.CreateCustomerPhoneDto;
import de.interzero.oneepr.customer.customer_phone.dto.UpdateCustomerPhoneDto;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CustomerPhoneService {

    private final CustomerPhoneMapper customerPhoneMapper;

    private final CustomerPhoneRepository customerPhoneRepository;

    private final CustomerService customerService;

    /**
     * Creates a new customer phone record.
     *
     * @param createCustomerPhoneDto the DTO containing data for the new customer phone.
     * @return the newly created customer phone entity.
     * @ts-legacy CreatedAt and UpdatedAt timestamps are generated from separate `Instant.now()` calls.
     * Additionally, DeletedAt (as LocalDate) is set upon creation which is an unconventional pattern.
     * No validation of not existing customer
     */
    public CustomerPhone create(CreateCustomerPhoneDto createCustomerPhoneDto) {
        CustomerPhone customerPhone = customerPhoneMapper.toEntity(createCustomerPhoneDto);
        customerPhone.setCustomer(customerService.findById(
                createCustomerPhoneDto.getCustomerId(),
                AuthUtil.getRelevantUserDetails()));
        customerPhone.setCreatedAt(Instant.now());
        customerPhone.setUpdatedAt(Instant.now());
        customerPhone.setDeletedAt(LocalDate.now());

        return customerPhoneRepository.save(customerPhone);
    }

    /**
     * Retrieves all customer phone records.
     *
     * @return a list of all {@link CustomerPhone} entities;
     * the list will be empty if no customer phones are found.
     * @ts-legacy this should return a paginated list of consents instead of all of them.
     */
    public List<CustomerPhone> findAll() {
        return customerPhoneRepository.findAll();
    }

    /**
     * Retrieves a specific customer phone record by its unique identifier.
     *
     * @param id the unique identifier (ID) of the customer phone to find.
     * @return an {@link Optional} containing the {@link CustomerPhone} if found,
     * or an empty {@link Optional} if no customer phone matches the given ID.
     */
    @Transactional(readOnly = true)
    public Optional<CustomerPhone> findOne(Integer id,
                                           AuthenticatedUser user) {
        validatingUserPermissionCustomerPhone(id, user);
        return customerPhoneRepository.findById(id);
    }

    /**
     * Updates an existing customer phone record identified by its ID.
     * This method first attempts to find the customer phone by the given ID.
     * If found, it updates the entity's properties using the data from the
     * {@code updateData} DTO, explicitly sets the {@code updatedAt} timestamp to the current moment,
     * and then persists the changes. If no customer phone is found for the given ID,
     * a {@link org.springframework.web.server.ResponseStatusException} with HTTP status NOT_FOUND is thrown.
     *
     * @param id         the unique identifier of the customer phone to be updated.
     * @param updateData the DTO containing the new values to update the customer phone with.
     * @return the updated and persisted {@link CustomerPhone} entity.
     * @throws org.springframework.web.server.ResponseStatusException if no customer phone record is found for the specified {@code id}.
     * @ts-legacy If Customer is not in the database,TS code will cause the database to reject the update and raise a foreign key constraint violation error.
     */
    @Transactional
    public CustomerPhone update(Integer id,
                                UpdateCustomerPhoneDto updateData,
                                AuthenticatedUser user) {
        validatingUserPermissionCustomerPhone(id, user);
        CustomerPhone customerPhone = customerPhoneRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "CustomerPhone with ID " + id + " not found"));
        customerPhoneMapper.updateEntity(updateData, customerPhone);
        customerPhone.setUpdatedAt(Instant.now());
        if (updateData.getPhoneNumber() != null) {
            customerPhone.setPhoneNumber(updateData.getPhoneNumber());
        }
        if (updateData.getCustomerId() != null) {
            Customer newCustomer = customerService.findById(
                    updateData.getCustomerId(),
                    AuthUtil.getRelevantUserDetails());
            customerPhone.setCustomer(newCustomer);
        }
        customerPhone.setUpdatedAt(Instant.now());
        return this.customerPhoneRepository.save(customerPhone);
    }

    /**
     * Removes the customer phone record identified by the given ID.
     * This method delegates to the repository's {@code deleteById} operation.
     * If no customer phone with the specified ID exists, the operation
     * typically completes silently without throwing an exception.
     *
     * @param id the unique identifier of the customer phone to be deleted.
     * @ts-legacy This is hard delete, not soft delete. And deleted_at not update.
     */
    @Transactional
    public void remove(Integer id,
                       AuthenticatedUser user) {
        validatingUserPermissionCustomerPhone(id, user);
        customerPhoneRepository.deleteById(id);
    }

    /**
     * Validates if the authenticated user has permission to access the specified customer phone.
     * This method checks:
     * 1. If the CustomerPhone record exists for the given ID.
     * 2. If the CustomerPhone has an associated Customer record.
     * 3. If the 'user_id' on the associated Customer matches the ID of the authenticated user.
     * <p>
     * It throws exceptions if validation fails, otherwise completes silently.
     *
     * @param id   The ID of the {@link CustomerPhone} to validate.
     * @param user The {@link AuthenticatedUser} performing the action.
     * @throws ResponseStatusException with {@code HttpStatus.NOT_FOUND} if the customer phone is not found,
     *                                 or if its associated customer is not found (with message "Customer not found").
     * @throws ResponseStatusException with {@code HttpStatus.FORBIDDEN} if the authenticated user
     *                                 does not have permission to access this customer phone.
     * @throws ResponseStatusException with {@code HttpStatus.UNAUTHORIZED} or {@code HttpStatus.BAD_REQUEST}
     *                                 if the authenticated user's ID is missing or in an invalid format.
     */
    public void validatingUserPermissionCustomerPhone(Integer id,
                                                      AuthenticatedUser user) {
        CustomerPhone customerPhone = customerPhoneRepository.findByIdAndFetchCustomer(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "CustomerPhone not found with id: " + id));
        Customer customer = customerPhone.getCustomer();

        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found");
        }

        if (user == null || user.getId() == null || user.getId().trim().isEmpty()) {
            // This handles cases where the AuthenticatedUser might be missing or its ID is not set.
            // The original TS code didn't explicitly check this before `+user.id`, which could lead to
            // NaN or 0 if user.id was null/undefined, potentially causing incorrect permission logic.
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Authenticated user ID is missing or invalid.");
        }

        int authenticatedUserIdAsInt;
        try {
            // The `+user.id` in TypeScript converts the string ID to a number.
            authenticatedUserIdAsInt = Integer.parseInt(user.getId());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Authenticated user ID format is invalid: " + user.getId());
        }
        if (customer.getUserId() == null || !customer.getUserId().equals(authenticatedUserIdAsInt)) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this customer phone");
        }

    }
}