package de.interzero.oneepr.customer.file;

import de.interzero.oneepr.customer.file.dto.CreateFileDto;
import de.interzero.oneepr.customer.file.dto.LambdaPresignedResponseDto;
import de.interzero.oneepr.customer.file.dto.RequestPresignedUrlDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static de.interzero.oneepr.common.string.Api.FILES;
import static de.interzero.oneepr.common.string.Role.*;

@Tag(
        name = "File",
        description = "Operations related to file management"
)
@SecurityRequirement(name = "bearerAuth")
@RestController
@RequestMapping(FILES)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@RequiredArgsConstructor
public class FileController {

    private final FileService fileService;

    /**
     * Get file by relation and related entity ID
     *
     * @param relation the relation type (e.g., "required_information_id", "contract_id", etc.)
     * @param id the ID of the related entity
     * @return ResponseEntity containing the file as ByteArrayResource
     */
    @PreAuthorize("permitAll()")
    @GetMapping("/relation")
    @Operation(summary = "Get file by relation")
    public ResponseEntity<ByteArrayResource> getFileByRelative(@RequestParam("relation") String relation,
                                                               @RequestParam("id") String id) {

        FileService.FileWithBuffer fileWithBuffer = fileService.getFileByRelative(relation, id);
        File file = fileWithBuffer.getFile();
        byte[] buffer = fileWithBuffer.getBuffer();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(file.getExtension()));
        headers.setContentDisposition(ContentDisposition.attachment().filename(file.getOriginalName()).build());

        return ResponseEntity.ok().headers(headers).body(new ByteArrayResource(buffer));
    }

    /**
     * Get file by id
     *
     * @param fileId fileId which is generated in the created api
     * @return
     */
    @PreAuthorize("permitAll()")
    @GetMapping("/{id}")
    @Operation(summary = "Get file by ID")
    public ResponseEntity<ByteArrayResource> getFile(@PathVariable("id") String fileId) {

        FileService.FileWithBuffer fileWithBuffer = fileService.getFile(fileId);
        File file = fileWithBuffer.getFile();
        byte[] buffer = fileWithBuffer.getBuffer();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(file.getExtension()));
        headers.setContentDisposition(ContentDisposition.attachment().filename(file.getOriginalName()).build());

        return ResponseEntity.ok().headers(headers).body(new ByteArrayResource(buffer));
    }

    /**
     * Request presigned url from aws lambda
     *
     * @param requestPresignedUrlDto request body contains the filename and fileType that the frontend will use the url to upload file
     * @return the presigned url
     */
    @PreAuthorize("permitAll()")
    @PostMapping("/request-presigned-url")
    @Operation(summary = "Request presigned url")
    public LambdaPresignedResponseDto requestUrl(@RequestBody RequestPresignedUrlDto requestPresignedUrlDto) {

        return fileService.requestUrl(requestPresignedUrlDto);
    }

    /**
     * @param createFileDto the file information and related ids
     * @param file          the bytes file
     * @param request       the request header contains the user id and role
     * @return uploaded file
     * @throws IOException if the file handling throws exceptions
     * @ts-legacy the userRole is not uesd
     */
    @PostMapping
    @Operation(summary = "Save file to storage and database")
    public File create(@RequestPart("data") CreateFileDto createFileDto,
                       @RequestPart("file") MultipartFile file,
                       HttpServletRequest request) throws IOException {

        String userId = request.getHeader("x-user-id");
        String userRole = request.getHeader("x-user-role");

        return fileService.uploadFile(createFileDto, file, userId, userRole);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete file from storage and database")
    public void delete(@PathVariable("id") String fileId) {
        fileService.deleteFile(fileId);
    }
}
