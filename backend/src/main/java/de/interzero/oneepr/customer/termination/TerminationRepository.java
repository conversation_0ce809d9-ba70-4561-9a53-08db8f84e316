package de.interzero.oneepr.customer.termination;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository for managing {@link Termination} entities.
 */
@Repository
public interface TerminationRepository extends JpaRepository<Termination, Integer> {

    /**
     * Finds a single Termination by its ID, eagerly fetching all related entities required by the service.
     * This graph includes contracts, action guides, licenses, files, and nested reasons.
     *
     * @param id The ID of the termination to find.
     * @return An Optional containing the Termination with its relations, or empty if not found.
     */
    @Override
    @NonNull
    Optional<Termination> findById(@NonNull Integer id);

    /**
     * Finds a single Termination by its ID, specifically fetching relations needed for the permission check.
     *
     * @param id The ID of the termination to find.
     * @return An Optional containing the Termination with customer data, or empty if not found.
     */
    Optional<Termination> findWithCustomerById(Integer id);

    /**
     * Finds a single Termination by its ID, fetching all relations needed for the update logic.
     *
     * @param id The ID of the termination to find.
     * @return An Optional containing the Termination with its relations loaded.
     */
    Optional<Termination> findWithAllUpdateRelationsById(Integer id);

    /**
     * Finds a single Termination by ID, fetching all relations needed for the revoke logic.
     *
     * @param id The ID of the termination to find.
     * @return An Optional containing the Termination with its relations.
     */
    Optional<Termination> findWithRevokeRelationsById(Integer id);
}

