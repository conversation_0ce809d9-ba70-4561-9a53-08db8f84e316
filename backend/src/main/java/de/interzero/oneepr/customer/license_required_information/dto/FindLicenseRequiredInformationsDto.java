package de.interzero.oneepr.customer.license_required_information.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for filtering license required information records.
 * This DTO is used as query parameters to find information related to a specific license or contract.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FindLicenseRequiredInformationsDto extends BaseDto {

    @JsonProperty("license_id")
    @Schema(
            description = "The license ID. If its country related information.",
            example = "1",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer licenseId;

    @JsonProperty("contract_id")
    @Schema(
            description = "The contract ID. If its contract related information.",
            example = "1",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer contractId;
}
