package de.interzero.oneepr.customer.recommend_country.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The original TypeScript code used a Prisma groupBy which has a similar nested _count structure.
 * This Java implementation achieves the desired JSON output structure through specific DTO mapping.
 */
@Getter
@Setter
@NoArgsConstructor
@JsonPropertyOrder({"_count", "name"})
public class TargetCountFormatDto {

    @JsonProperty("_count")
    private CountValueDto countData;

    @JsonProperty("name")
    private String countryName;

    /**
     * Constructor to facilitate mapping.
     *
     * @param countryName The name of the country.
     * @param count       The recommendation count for this country.
     */
    public TargetCountFormatDto(String countryName,
                                long count) {
        this.countryName = countryName;
        this.countData = new CountValueDto(count);
    }
}