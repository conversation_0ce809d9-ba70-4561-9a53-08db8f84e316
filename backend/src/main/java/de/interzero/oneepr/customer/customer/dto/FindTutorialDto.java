package de.interzero.oneepr.customer.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for querying customer tutorial statuses.
 * <p>
 * This DTO is a direct translation of the {@code FindTutorialCustomersDto}
 * from {@code src/customer/dto/find-tutorial.dto.ts}. It is used as a filter
 * in the {@code findTutorialStatus} service method.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FindTutorialDto {

    @Schema(description = "Customer id number")
    private String customerId;

    @Schema(description = "Customer service type")
    private ServiceType serviceType;

    public enum ServiceType {
        EU_LICENSE,
        DIRECT_LICENSE,
        ACTION_GUIDE
    }
}