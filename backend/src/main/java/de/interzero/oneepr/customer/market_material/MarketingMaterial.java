package de.interzero.oneepr.customer.market_material;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.entity.MarketingMaterialPartner;
import de.interzero.oneepr.customer.file.File;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(
        name = "marketing_material",
        schema = "public"
)
public class MarketingMaterial {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "marketing_material_id_gen"
    )
    @SequenceGenerator(
            name = "marketing_material_id_gen",
            sequenceName = "marketing_material_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    private LocalDate startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    private LocalDate endDate;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "category",
            nullable = false
    )
    @JsonProperty("category")
    private Category category;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "partner_restriction",
            nullable = false
    )
    @JsonProperty("partner_restriction")
    private PartnerRestriction partnerRestriction;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @JsonIgnore
    @JsonProperty("files")
    @OneToMany(
            mappedBy = "marketingMaterial",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL,
            orphanRemoval = true
    )
    private Set<File> files;

    @JsonIgnore
    @JsonProperty("partners")
    @OneToMany(
            mappedBy = "marketingMaterial",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL,
            orphanRemoval = true
    )
    private Set<MarketingMaterialPartner> partners;

    @PrePersist
    protected void onCreate() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }

    public enum Category {
        STANDARD,
        SPECIFIC_MATERIAL
    }

    public enum PartnerRestriction {
        ALL,
        CLUSTER,
        SPECIFIC
    }
}
