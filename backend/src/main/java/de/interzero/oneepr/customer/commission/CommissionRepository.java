package de.interzero.oneepr.customer.commission;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CommissionRepository extends JpaRepository<Commission, Integer>, JpaSpecificationExecutor<Commission> {

    /**
     * Find commission by ID with customer relationship
     *
     * @param id Commission ID
     * @return Commission with customer
     */
    @Query("SELECT c FROM Commission c LEFT JOIN FETCH c.orderCustomer WHERE c.id = :id")
    Optional<Commission> findByIdWithCustomer(@Param("id") Integer id);

    /**
     * Mark commissions as used by user ID
     *
     * @param userId User ID
     * @return Number of updated records
     */
    @Modifying
    @Query("UPDATE Commission c SET c.used = true WHERE c.userId = :userId")
    int markAsUsedByUserId(@Param("userId") Integer userId);
}
