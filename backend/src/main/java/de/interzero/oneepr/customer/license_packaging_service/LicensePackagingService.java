package de.interzero.oneepr.customer.license_packaging_service;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.entity.LicenseReportSet;
import de.interzero.oneepr.customer.entity.LicenseReportSetFrequency;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "license_packaging_service",
        schema = "public"
)
public class LicensePackagingService {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "license_packaging_service_id_gen"
    )
    @SequenceGenerator(
            name = "license_packaging_service_id_gen",
            sequenceName = "license_packaging_service_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "setup_packaging_service_id",
            nullable = false
    )
    @JsonProperty("setup_packaging_service_id")
    private Integer setupPackagingServiceId;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "license_id",
            nullable = false
    )
    @JsonProperty("license")
    @JsonIgnore
    private License license;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "description",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("description")
    private String description;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @JsonProperty("obliged")
    @NotNull
    @Column(
            name = "obliged",
            nullable = false
    )
    private Boolean obliged = false;

    @JsonProperty("report_set")
    @JsonIgnore
    @Nullable
    @OneToOne(
            mappedBy = "licensePackagingService",
            cascade = CascadeType.PERSIST
    )
    private LicenseReportSet reportSet;

    @JsonProperty("report_set_frequency")
    @JsonIgnore
    @Nullable
    @OneToOne(
            mappedBy = "licensePackagingService",
            cascade = CascadeType.PERSIST
    )
    private LicenseReportSetFrequency reportSetFrequency;

    @JsonProperty("volume_reports")
    @JsonIgnore
    @OneToMany(
            mappedBy = "packagingService",
            cascade = CascadeType.PERSIST
    )
    private List<LicenseVolumeReport> volumeReports;
}