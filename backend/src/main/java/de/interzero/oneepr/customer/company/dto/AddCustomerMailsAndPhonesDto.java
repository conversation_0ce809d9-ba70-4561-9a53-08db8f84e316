package de.interzero.oneepr.customer.company.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Data Transfer Object for adding customer phones and company emails.
 * Converted from TypeScript AddCustomerMailsAndPhones with exact same field names.
 */
@Schema(description = "Data for adding customer phones and company emails")
@Data
public class AddCustomerMailsAndPhonesDto extends BaseDto {

    @JsonProperty("customer_phones")
    @Schema(description = "List of customer phone numbers")
    private List<String> customerPhones;

    @JsonProperty("company_emails")
    @Schema(description = "List of company email addresses")
    private List<String> companyEmails;

    @JsonProperty("company_id")
    @Schema(description = "Company ID")
    private Integer companyId;

    @JsonProperty("customer_id")
    @Schema(description = "Customer ID")
    private Integer customerId;
}
