package de.interzero.oneepr.customer.license_required_information.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * Data Transfer Object for declining a required information request.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DeclineLicenseRequiredInformationDto extends BaseDto {

    @NotEmpty
    @JsonProperty("reason_ids")
    @Schema(
            description = "The decline reason IDs",
            example = "[1, 2]",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private List<Integer> reasonIds;

    @NotBlank
    @JsonProperty("title")
    @Schema(
            description = "The title of the decline",
            example = "Decline reason title",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String title;
}
