package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Data Transfer Object representing aggregated customer data grouped by country.
 * <p>
 * This DTO is a direct translation of the accumulator object created within the
 * {@code .reduce()} function in the {@code groupByCountry} method
 * from {@code customer.service.ts}. It holds counts of licensed customers
 * for a specific country.
 */
@Data
public class GroupedByCountryDto {

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("country_code")
    private String countryCode;

    @JsonProperty("country_name")
    private String countryName;

    @JsonProperty("country_flag")
    private String countryFlag;

    @JsonProperty("licensed_customer_count")
    private long licensedCustomerCount;

    @JsonProperty("unlicensed_customer_count")
    private long unlicensedCustomerCount;
}