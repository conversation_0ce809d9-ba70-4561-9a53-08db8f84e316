package de.interzero.oneepr.customer.license_third_party_invoice;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface LicenseThirdPartyInvoiceRepository extends JpaRepository<LicenseThirdPartyInvoice, Integer> {

    @Query(
            "SELECT DISTINCT ltpi FROM LicenseThirdPartyInvoice ltpi " + "LEFT JOIN ltpi.license l " + "WHERE ltpi.deletedAt IS NULL " + "AND (:licenseId IS NULL OR l.id = :licenseId) " + "AND (CAST(:fromDate AS java.time.Instant) IS NULL OR ltpi.issuedAt >= :fromDate) " + "AND (CAST(:toDate AS java.time.Instant) IS NULL OR ltpi.issuedAt <= :toDate) " + "ORDER BY ltpi.id DESC"
    )
    /**
     * Finds all non-deleted third-party invoices, allowing for optional filtering
     * by license ID and a date range based on the invoice's issued date.
     *
     * @param licenseId Optional ID of the license to filter invoices by. If null, this filter is ignored.
     * @param fromDate Optional start date (inclusive) for the issued date range filter. If null, this filter is ignored.
     * @param toDate Optional end date (inclusive) for the issued date range filter. If null, this filter is ignored.
     * @return A list of {@link LicenseThirdPartyInvoice} entities matching the filter criteria,
     * ordered by ID in descending order.
     */
    List<LicenseThirdPartyInvoice> findWithFilters(@Param("licenseId") Integer licenseId,
                                                   @Param("fromDate") Instant fromDate,
                                                   @Param("toDate") Instant toDate);

    /**
     * Finds a LicenseThirdPartyInvoice by its ID, ensuring it is not soft-deleted.
     *
     * @param id The ID of the LicenseThirdPartyInvoice.
     * @return An Optional containing the LicenseThirdPartyInvoice if found and not deleted.
     */
    @Query(
            "SELECT ltpi FROM LicenseThirdPartyInvoice ltpi " + "WHERE ltpi.id = :id AND ltpi.deletedAt IS NULL"
    )
    Optional<LicenseThirdPartyInvoice> findActiveByIdWithDetails(@Param("id") Integer id);

    /**
     * Finds a LicenseThirdPartyInvoice by its ID, ensuring it is not soft-deleted.
     *
     * @param id The ID of the LicenseThirdPartyInvoice.
     * @return An Optional containing the LicenseThirdPartyInvoice if found and not deleted.
     */
    Optional<LicenseThirdPartyInvoice> findByIdAndDeletedAtIsNull(Integer id);
}