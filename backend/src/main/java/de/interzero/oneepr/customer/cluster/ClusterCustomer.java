package de.interzero.oneepr.customer.cluster;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.customer.Customer;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@Getter
@Setter
@Entity
@Table(
        name = "cluster_customers",
        schema = "public"
)
public class ClusterCustomer {

    @JsonProperty("id")
    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "cluster_customers_id_gen"
    )
    @SequenceGenerator(
            name = "cluster_customers_id_gen",
            sequenceName = "cluster_customers_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @JsonIgnore
    @JsonProperty("cluster")
    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "cluster_id",
            nullable = false
    )
    private Cluster cluster;

    @JsonIgnore
    @JsonProperty("customer")
    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "customer_id",
            nullable = false
    )
    private Customer customer;

    @Transient
    @JsonProperty("cluster_id")
    public Integer getClusterId() {
        return cluster != null ? cluster.getId() : null;
    }

    @Transient
    @JsonProperty("customer_id")
    public Integer getCustomerId() {
        return customer != null ? customer.getId() : null;
    }
}