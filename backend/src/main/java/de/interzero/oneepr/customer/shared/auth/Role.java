package de.interzero.oneepr.customer.shared.auth;

/**
 * This Java class is a direct adaptation of an {@code AuthenticatedUser}
 * concept and structure originating from the Node.js codebase.
 * Each role constant is associated with a specific, lowercase string value
 * (e.g., {@code SYSTEM} corresponds to "system").
 */
public enum Role {
    SYSTEM("system"),
    CUSTOMER("customer"),
    ADMIN("admin"),
    CLERK("clerk"),
    SUPER_ADMIN("super_admin"),
    MARKE<PERSON>NG_MANAGER("marketing_manager"),
    BROKER("broker"),
    BROKER_MANAGER("broker_manager"),
    PARTNER("partner");


    private final String value;

    Role(String value) {
        this.value = value;
    }

    /**
     * Returns the string value of the enum constant.
     * This is often useful for serialization (e.g., with Jackson for JSON APIs)
     * so that the lowercase string value is used instead of the uppercase enum name.
     *
     * @return The string value of the role (e.g., "system").
     */
    @Override
    public String toString() {
        return this.value;
    }
}