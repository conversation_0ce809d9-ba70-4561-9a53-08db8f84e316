package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.customer.license.License;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.Map;

@Getter
@Setter
@Entity
@Table(
        name = "license_price_list",
        schema = "public"
)
public class LicensePriceList {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "license_price_list_id_gen"
    )
    @SequenceGenerator(
            name = "license_price_list_id_gen",
            sequenceName = "license_price_list_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @Column(
            name = "setup_price_list_id",
            nullable = false
    )
    private Integer setupPriceListId;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "license_id",
            nullable = false
    )
    private License license;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String name;

    @NotNull
    @Column(
            name = "description",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String description;

    @NotNull
    @Column(
            name = "condition_type",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String conditionType;

    @NotNull
    @Column(
            name = "condition_type_value",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String conditionTypeValue;

    @NotNull
    @Column(
            name = "start_date",
            nullable = false
    )
    private Instant startDate;

    @NotNull
    @Column(
            name = "end_date",
            nullable = false
    )
    private Instant endDate;

    @Column(name = "basic_price")
    private Integer basicPrice;

    @Column(name = "minimum_price")
    private Integer minimumPrice;

    @Column(name = "registration_fee")
    private Integer registrationFee;

    @Column(name = "handling_fee")
    private Integer handlingFee;

    @Column(name = "variable_handling_fee")
    private Double variableHandlingFee;

    @Column(name = "thresholds")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> thresholds;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private Instant deletedAt;

}