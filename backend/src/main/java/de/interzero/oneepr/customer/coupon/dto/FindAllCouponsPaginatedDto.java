package de.interzero.oneepr.customer.coupon.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for filtering and paginating coupon search results.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FindAllCouponsPaginatedDto {

    @JsonProperty("page")
    @Schema(
            description = "Pagination page",
            example = "1"
    )
    private Integer page = 1;

    @JsonProperty("limit")
    @Schema(
            description = "Pagination limit",
            example = "10"
    )
    private Integer limit = 10;

    @JsonProperty("is_active")
    @Schema(
            description = "Filter by active status",
            example = "true"
    )
    private String isActive;

    @JsonProperty("lead_type")
    @Schema(
            description = "Filter coupons with 'code' or 'link' or 'all'",
            example = "all"
    )
    private String leadType;

    @JsonProperty("code")
    @Schema(
            description = "Filter coupons by code",
            example = "123456"
    )
    private String code;

    @JsonProperty("include_uses")
    @Schema(
            description = "Include coupon uses with count",
            example = "true"
    )
    private String includeUses;

    @JsonProperty("start_date")
    @Schema(
            description = "Filter by redeemable by new customers",
            example = "true"
    )
    private String startDate;

    @JsonProperty("end_date")
    @Schema(
            description = "Filter by end date",
            example = "2025-01-01"
    )
    private String endDate;
}
