package de.interzero.oneepr.customer.decline;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import de.interzero.oneepr.customer.reason.DeclineReason;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "decline",
        schema = "public"
)
public class Decline {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "decline_id_gen"
    )
    @SequenceGenerator(
            name = "decline_id_gen",
            sequenceName = "decline_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "title",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("title")
    private String title;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "license_required_information_id")
    @JsonIgnore
    @JsonProperty("license_required_information")
    private LicenseRequiredInformation licenseRequiredInformation;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "license_volume_report_id")
    @JsonIgnore
    @JsonProperty("license_volume_report")
    private LicenseVolumeReport licenseVolumeReport;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "license_volume_report_item_id")
    @JsonIgnore
    @JsonProperty("license_volume_report_item")
    private LicenseVolumeReportItem licenseVolumeReportItem;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @OneToMany(
            mappedBy = "decline",
            cascade = CascadeType.ALL,
            orphanRemoval = true,
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("decline_reasons")
    private List<DeclineReason> declineReasons = new ArrayList<>();

    /**
     * Helper method to add a DeclineReason to this Decline.
     * This ensures that both sides of the bidirectional relationship are always in sync.
     *
     * @param declineReason The child entity to add.
     */
    public void addDeclineReason(DeclineReason declineReason) {
        this.declineReasons.add(declineReason);
        declineReason.setDecline(this);
    }
}
