package de.interzero.oneepr.admin.report_set_fractions.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new report set fraction.
 */
@Getter
@Setter
public class CreateReportSetFractionDto extends BaseDto {

    @Schema(
            description = "Name of the report set fraction",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "Description of the report set fraction",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("description")
    private String description;

    @Schema(
            description = "Icon of the report set fraction",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("icon")
    private String icon;

    @Schema(
            description = "ID of the fraction icon",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("fraction_icon_id")
    private Integer fractionIconId;

    @Schema(description = "Is the report set fraction active")
    @JsonProperty("is_active")
    private Boolean isActive;

    @Schema(
            description = "ID of the associated report set",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("report_set_id")
    private Integer reportSetId;

    @Schema(description = "ID of the parent fraction (if any)")
    @JsonProperty("parent_id")
    private Integer parentId;
}