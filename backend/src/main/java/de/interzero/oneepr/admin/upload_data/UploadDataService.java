package de.interzero.oneepr.admin.upload_data;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.entity.Broker;
import de.interzero.oneepr.admin.entity.BrokerCompany;
import de.interzero.oneepr.admin.entity.UploadFileHistory;
import de.interzero.oneepr.admin.price_list.PriceList;
import de.interzero.oneepr.admin.price_list.PriceListRepository;
import de.interzero.oneepr.admin.price_list.dto.CreatePriceListDto;
import de.interzero.oneepr.admin.upload_data.constants.GermanyFraction;
import de.interzero.oneepr.admin.upload_data.constants.ImportFieldConstants;
import de.interzero.oneepr.admin.upload_data.dto.*;
import de.interzero.oneepr.admin.upload_files.dto.LambdaPresignedResponseDto;
import de.interzero.oneepr.admin.upload_files.dto.RequestPresignedUrlDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.io.IOException;
import java.security.SecureRandom;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;


@Slf4j
@Service
@RequiredArgsConstructor
public class UploadDataService {

    static final List<String> EXPECTED_ORDER_HEADERS = List.of(
            ImportFieldConstants.REGISTRATION_NUMBER,
            ImportFieldConstants.YEAR,
            ImportFieldConstants.TYPE,
            ImportFieldConstants.GLASS,
            ImportFieldConstants.PAPER_PPK,
            ImportFieldConstants.FERROUS_METALS,
            ImportFieldConstants.ALUMINIUM_COMPOSITES,
            ImportFieldConstants.LIQUID_COMPOSITES,
            ImportFieldConstants.OTHER_PPK_COMPOSITES,
            ImportFieldConstants.PLASTICS_COMPOSITES,
            ImportFieldConstants.OTHER_MATERIALS);

    static final List<String> EXPECTED_COMPANY_HEADERS = List.of(
            ImportFieldConstants.COMPANY_NAME,
            ImportFieldConstants.REGISTRATION_NUMBER,
            ImportFieldConstants.VAT,
            ImportFieldConstants.TAX,
            ImportFieldConstants.COUNTRY_CODE,
            ImportFieldConstants.ADDRESS_STREET,
            ImportFieldConstants.ADDRESS_NUMBER,
            ImportFieldConstants.CITY,
            ImportFieldConstants.CONTACT_EMAIL, ImportFieldConstants.CONTACT_NAME, ImportFieldConstants.PHONE_NUMBER);

    static final List<String> REQUIRED_FIELDS = List.of(
            ImportFieldConstants.COMPANY_NAME,
            ImportFieldConstants.REGISTRATION_NUMBER,
            ImportFieldConstants.VAT,
            ImportFieldConstants.TAX,
            ImportFieldConstants.ADDRESS_STREET,
            ImportFieldConstants.CITY,
            ImportFieldConstants.CONTACT_EMAIL, ImportFieldConstants.CONTACT_NAME, ImportFieldConstants.PHONE_NUMBER);

    // Validation patterns - exact same as TypeScript
    private static final Pattern emailRegex = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    private static final Pattern phoneRegex = Pattern.compile("^[\\d\\s()+-]{7,20}$");

    private static final SecureRandom secureRandom = new SecureRandom();

    private final UploadFileHistoryRepository uploadFileHistoryRepository;

    private final PriceListRepository priceListRepository;

    private final BrokerCompanyRepository brokerCompanyRepository;

    private final BrokerCompanyOrderRepository brokerCompanyOrderRepository;

    private final BrokerRepository brokerRepository;

    private final ObjectMapper objectMapper;

    private final S3Presigner s3Presigner;

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    /**
     * Get company and price list - exact same logic as TypeScript
     *
     * @param registrationNumber The registration number
     * @param year               The year
     * @return CompletableFuture with company, priceList, and errors
     */
    private CompanyAndPriceListDto getCompanyAndPriceList(String registrationNumber,
                                                          String year) {
        BrokerCompany company = brokerCompanyRepository.findFirstByRegisterNumber(registrationNumber).orElse(null);
        PriceList priceList = priceListRepository.findFirstByTypeAndConditionTypeValue(
                PriceList.Type.DIRECT_LICENSE,
                year).orElse(null);

        List<ErrorDto> errors = new ArrayList<>();
        if (company == null) {
            errors.add(new ErrorDto(
                    ImportFieldConstants.REGISTRATION_NUMBER,
                                    "Company with registration number " + registrationNumber + " not found"));
        }
        if (priceList == null) {
            errors.add(new ErrorDto("Year", "Price list for year " + year + " not found"));
        }
        return new CompanyAndPriceListDto(company, priceList, errors);
    }

    /**
     * Get fractions - exact same logic as TypeScript
     *
     * @param processedOrder The processed order data
     * @param priceList      The price list
     * @return Fractions and errors
     */
    @SuppressWarnings("all")
    private FractionsDto getFractions(Map<String, Object> processedOrder,
                                      PriceList priceList) throws JsonProcessingException {
        List<FractionDataDto> fractions = new ArrayList<>();
        List<ErrorDto> errors = new ArrayList<>();

        for (Map.Entry<String, Object> entry : processedOrder.entrySet()) {
            String name = entry.getKey();
            GermanyFraction fraction = GermanyFraction.findByName(name);
            if (fraction != null) {
                Object weight = entry.getValue();
                double value = 0; // Placeholder

                if (!(weight instanceof Number) || ((Number) weight).doubleValue() < 0) {
                    String errorMessage = "Invalid weight type for fraction " + name + ": " + weight;
                    errors.add(new ErrorDto(name, errorMessage));
                    FractionDataDto fractionData = new FractionDataDto(
                            fraction.getCode(),
                                                                       fraction.getName(),
                                                                       weight,
                                                                       value,
                                                                       errorMessage);
                    fractions.add(fractionData);
                    continue;
                }

                // Price list fraction
                if (priceList.getThresholds() != null && !priceList.getThresholds().isEmpty()) {
                    List<CreatePriceListDto.ThresholdDto> thresholds = objectMapper.readValue(
                            priceList.getThresholds(), new TypeReference<>() {
                            });

                    if (thresholds != null && !thresholds.isEmpty()) {
                        CreatePriceListDto.ThresholdDto thresholdDto = thresholds.getFirst();
                        if (thresholdDto != null) {
                            Map<String, CreatePriceListDto.FractionDto> fractionsMap = thresholdDto.getFractions();
                            if (fractionsMap != null && fractionsMap.containsKey(fraction.getCode())) {
                                value = fractionsMap.get(fraction.getCode()).getValue();
                            }
                        }
                    }
                }


                FractionDataDto fractionData = new FractionDataDto(
                        fraction.getCode(),
                                                                   fraction.getName(),
                                                                   weight,
                                                                   value,
                                                                   null);
                fractions.add(fractionData);
            }
        }

        return new FractionsDto(fractions, errors);
    }

    /**
     * Process order preview - exact same logic as TypeScript
     *
     * @param chunk List of processed orders
     * @return CompletableFuture with order previews
     */
    private CompletableFuture<List<OrderPreviewResultDto>> processOrderPreview(List<Map<String, Object>> chunk) {
        List<CompletableFuture<OrderPreviewResultDto>> futures = chunk.stream()
                .map(processedOrder -> CompletableFuture.supplyAsync(() -> {
                    OrderPreviewResultDto resultDto = new OrderPreviewResultDto();
                    try {
                        List<ErrorDto> errors = new ArrayList<>();
                        CompanyAndPriceListDto companyAndPriceListDto = getCompanyAndPriceList(
                                (String) processedOrder.get(ImportFieldConstants.REGISTRATION_NUMBER),
                                String.valueOf(processedOrder.get("Year")));
                        BrokerCompany company = companyAndPriceListDto.getCompany();
                        PriceList priceList = companyAndPriceListDto.getPriceList();
                        List<ErrorDto> companyErrors = companyAndPriceListDto.getErrors();

                        if (!companyErrors.isEmpty()) {
                            errors.addAll(companyErrors);
                        }

                        if (company == null || priceList == null) {
                            resultDto.setErrors(companyErrors);
                            return resultDto;
                        }
                        brokerCompanyOrderRepository.findFirstByCompanyAndYear(
                                        company,
                                        ((Number) processedOrder.get("Year")).intValue())
                                .ifPresent(existingOrder -> errors.add(new ErrorDto(
                                        ImportFieldConstants.REGISTRATION_NUMBER,
                                        "Duplicate order for company id " + company.getId() + " in year " + processedOrder.get(
                                                "Year"))));

                        FractionsDto fractionsResult = getFractions(processedOrder, priceList);
                        List<FractionDataDto> fractions = fractionsResult.getFractions();
                        List<ErrorDto> fractionErrors = fractionsResult.getErrors();

                        if (!fractionErrors.isEmpty()) {
                            errors.addAll(fractionErrors);
                        }
                        return new OrderPreviewResultDto(
                                company.getRegisterNumber(),
                                String.valueOf(processedOrder.get("Year")),
                                fractions,
                                errors);
                    } catch (Exception error) {
                        resultDto.setErrors(List.of(new ErrorDto(
                                "General",
                                "Error processing order: " + error.getMessage())));
                        return resultDto;
                    }
                }))
                .toList();

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream().map(CompletableFuture::join).toList());
    }

    /**
     * Process company preview - exact same logic as TypeScript
     *
     * @param chunk List of processed companies
     * @return CompletableFuture with company previews
     */
    @SuppressWarnings("all")
    private CompletableFuture<List<CompanyPreviewResultDto>> processCompanyPreview(List<Map<String, Object>> chunk) {
        List<CompletableFuture<CompanyPreviewResultDto>> futures = chunk.stream()
                .map(processedCompany -> CompletableFuture.supplyAsync(() -> {

                    try {
                        List<ErrorDto> errors = new ArrayList<>();
                        brokerCompanyRepository.findFirstByRegisterNumber((String) processedCompany.get(
                                        ImportFieldConstants.REGISTRATION_NUMBER)).ifPresent(existingCompany -> errors.add(new ErrorDto(
                                ImportFieldConstants.REGISTRATION_NUMBER,
                                "Company with ID " + processedCompany.get(ImportFieldConstants.REGISTRATION_NUMBER) + " already exists")));

                        // Required fields validation Set to constant
                        for (String field : REQUIRED_FIELDS) {
                            Object value = processedCompany.get(field);
                            if (value == null || (value instanceof String str && str.trim().isEmpty())) {
                                errors.add(new ErrorDto(field, field + " cannot be empty"));
                            }
                        }

                        // VAT and TAX validation - exact same logic as TypeScript
                        String vat = trimOrNull(processedCompany.get("VAT"));
                        String tax = trimOrNull(processedCompany.get("TAX"));

                        if (vat != null && tax != null) {
                            errors.add(new ErrorDto("VAT & TAX", "Only one of VAT or TAX can be defined at a time"));
                        } else if (vat == null && tax == null) {
                            errors.add(new ErrorDto("VAT & TAX", "Either VAT or TAX must be defined"));
                        }

                        // Convert VAT/TAX to string if needed - exact same logic as TypeScript
                        if (vat != null && !(processedCompany.get("VAT") instanceof String)) {
                            vat = toStringOrNull(processedCompany.get("VAT"));
                            if (vat == null) {
                                errors.add(new ErrorDto("VAT", "Failed to convert VAT to a string"));
                            }
                        }

                        if (tax != null && !(processedCompany.get("TAX") instanceof String)) {
                            tax = toStringOrNull(processedCompany.get("TAX"));
                            if (tax == null) {
                                errors.add(new ErrorDto("TAX", "Failed to convert TAX to a string"));
                            }
                        }

                        // Email validation - exact same regex as TypeScript
                        String email = trimOrNull(processedCompany.get("Contact Email"));
                        if (email != null && (!emailRegex.matcher(email).matches() || email.length() > 254)) {
                            errors.add(new ErrorDto("Contact Email", "Invalid email format"));
                        }

                        // Phone validation - exact same regex as TypeScript
                        String phone = trimOrNull(processedCompany.get("Phone Number"));
                        if (phone != null && !phoneRegex.matcher(phone).matches()) {
                            errors.add(new ErrorDto("Phone Number", "Invalid phone number format"));
                        }

                        // Build result - exact same structure as TypeScript
                        CompanyPreviewResultDto result = new CompanyPreviewResultDto();

                        // 从 Map 中提取值并设置到 DTO 对象
                        result.setRegisterNumber((String) processedCompany.get(ImportFieldConstants.REGISTRATION_NUMBER));
                        result.setName((String) processedCompany.get("Company Name"));
                        result.setAddressNumber(trimOrNull(processedCompany.get("Address Number")));
                        result.setAddressStreet((String) processedCompany.get("Address Street"));
                        result.setCity((String) processedCompany.get("city"));
                        result.setContactEmail(email);
                        result.setContactName((String) processedCompany.get("Contact Name"));
                        result.setPhoneNumber(phone);
                        result.setVat(vat);
                        result.setTax(tax);
                        result.setCountryCode(trimOrNull(processedCompany.get("Country Code")));
                        result.setErrors(errors);
                        return result;

                    } catch (Exception error) {
                        CompanyPreviewResultDto result = new CompanyPreviewResultDto();
                        ErrorDto errorDto = new ErrorDto("General", "Error processing order: " + error.getMessage());
                        result.setErrors(List.of(errorDto));
                        return result;
                    }
                }))
                .toList();

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream().map(CompletableFuture::join).toList());
    }


    /**
     * Helper method to validate headers - exact same logic as TypeScript
     *
     * @param fileHeaders     The actual headers
     * @param expectedHeaders The expected headers
     */
    private void validateHeaders(List<String> fileHeaders,
                                 List<String> expectedHeaders) {
        List<String> errors = new ArrayList<>();
        if (fileHeaders.size() != expectedHeaders.size()) {
            errors.add(String.format("Expected %d headers, but found %d.", expectedHeaders.size(), fileHeaders.size()));
        }
        int minLength = Math.min(fileHeaders.size(), expectedHeaders.size());
        for (int i = 0; i < minLength; i++) {
            if (!fileHeaders.get(i).equals(expectedHeaders.get(i))) {
                errors.add(String.format(
                        "Header mismatch at column %d: expected \"%s\", but found \"%s\"",
                        i + 1,
                        expectedHeaders.get(i),
                        fileHeaders.get(i)));
            }
        }
        if (!errors.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, String.join(" ", errors));
        }
    }

    /**
     * Process Excel rows from buffer - helper method for Excel processing
     *
     * @param file            The uploaded file
     * @param expectedHeaders Expected headers for validation
     * @return List of processed rows
     */
    private List<Map<String, Object>> processExcelRows(MultipartFile file,
                                                       List<String> expectedHeaders) throws IOException {
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        // Get headers from first row
        Row headerRow = sheet.getRow(0);
        List<String> headers = new ArrayList<>();
        if (headerRow != null) {
            for (Cell cell : headerRow) {
                headers.add(getCellValueAsString(cell));
            }
        }
        // Validate headers
        validateHeaders(headers, expectedHeaders);

        // Process data rows
        List<Map<String, Object>> results = new ArrayList<>();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                Map<String, Object> rowData = new HashMap<>();
                for (int j = 0; j < headers.size(); j++) {
                    Cell cell = row.getCell(j);
                    Object cellValue = getCellValue(cell);
                    rowData.put(headers.get(j), cellValue);
                }
                results.add(rowData);
            }
        }
        return results;

    }

    /**
     * Preview uploaded Excel file data - exact same logic as TypeScript
     *
     * @param file The uploaded Excel file
     * @param type The type of data (order data or company data)
     * @return Preview data
     */
    @SuppressWarnings("java:S1452")
    public List<?> preview(MultipartFile file,
                           String type) throws IOException {
        if ("order".equals(type)) {
            List<Map<String, Object>> results = processExcelRows(file, EXPECTED_ORDER_HEADERS);
            return processOrderPreview(results).join();
        } else if ("company".equals(type)) {
            List<Map<String, Object>> results = processExcelRows(file, EXPECTED_COMPANY_HEADERS);
            return processCompanyPreview(results).join();
        } else {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid type");
        }
    }

    /**
     * Generate filename with date structure - exact same as TypeScript
     *
     * @param filename The original filename
     * @return Generated filename with path
     */
    public String generateFilename(String filename) {
        LocalDate today = LocalDate.now();
        int year = today.getYear();
        String month = String.format("%02d", today.getMonthValue());
        String day = String.format("%02d", today.getDayOfMonth());

        String hash = generateHash();
        return String.format("%d/%s/%s/%s-%s", year, month, day, hash, filename);
    }


    /**
     * Generate hash - exact same algorithm as TypeScript crypto.randomBytes
     *
     * @return Generated hash
     */
    public String generateHash() {
        byte[] randomBytes = new byte[8];
        secureRandom.nextBytes(randomBytes);

        StringBuilder hash = new StringBuilder();
        for (byte b : randomBytes) {
            int value = Math.abs(b) % 36;
            if (value < 10) {
                hash.append(value);
            } else {
                hash.append((char) ('A' + value - 10));
            }
        }
        return hash.substring(0, 8);
    }

    /**
     * Request presigned URL - exact same logic as TypeScript
     *
     * @param requestPresignedUrlDto The request DTO
     * @return Response data
     */
    public LambdaPresignedResponseDto requestUrl(RequestPresignedUrlDto requestPresignedUrlDto) {
        try {
            String filename = requestPresignedUrlDto.getFilename();
            String fileType = requestPresignedUrlDto.getFileType();

            // Create the PutObjectRequest
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(filename)
                    .contentType(fileType)
                    .build();

            // Create the presigned request
            PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                    .signatureDuration(java.time.Duration.ofMinutes(15)) // 15 minutes expiration
                    .putObjectRequest(putObjectRequest)
                    .build();

            // Generate the presigned URL
            PresignedPutObjectRequest presignedRequest = s3Presigner.presignPutObject(presignRequest);

            // Create response DTO
            LambdaPresignedResponseDto response = new LambdaPresignedResponseDto();
            response.setUploadUrl(presignedRequest.url().toString());

            // For direct S3 upload, we don't need additional fields like Lambda did
            response.setFields(new HashMap<>());

            return response;
        } catch (Exception e) {
            log.error("Error generating presigned URL", e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Problems generating url");
        }
    }

    /**
     * Create upload file history - exact same logic as TypeScript
     *
     * @param brokerId The broker ID
     * @param file     The uploaded file
     * @return Created record
     */
    @Transactional
    public UploadFileHistory create(int brokerId,
                                    MultipartFile file) {
        try {
            String originalname = file.getOriginalFilename();
            String mimetype = file.getContentType();
            String filename = generateFilename(originalname);

            // Request presigned URL
            RequestPresignedUrlDto requestDto = new RequestPresignedUrlDto();
            requestDto.setFilename(filename);
            requestDto.setFileType(mimetype);

            LambdaPresignedResponseDto urlResponse = requestUrl(requestDto);
            String fileUrl = urlResponse.getUploadUrl();

            Broker broker = brokerRepository.findById(brokerId)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Broker not found"));
            UploadFileHistory uploadFileHistory = new UploadFileHistory();
            uploadFileHistory.setFileName(filename);
            uploadFileHistory.setFileUrl(fileUrl);
            uploadFileHistory.setBroker(broker);
            uploadFileHistoryRepository.save(uploadFileHistory);
            return uploadFileHistory;
        } catch (Exception err) {
            log.error("Error creating upload file history", err);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, err.getMessage());
        }
    }

    /**
     * Find all upload data - exact same logic as TypeScript
     *
     * @param id The broker ID
     * @return List of upload data
     */
    public List<UploadFileHistory> findAll(int id) {
        return uploadFileHistoryRepository.findAllByBrokerIdAndDeletedAtIsNull(id);
    }

    /**
     * Helper method equivalent to TypeScript trimOrNull
     *
     * @param value The value to trim
     * @return Trimmed string or null
     */
    private String trimOrNull(Object value) {
        if (value instanceof String str) {
            String trimmed = str.trim();
            return trimmed.isEmpty() ? null : trimmed;
        }
        return null;
    }

    /**
     * Helper method equivalent to TypeScript toStringOrNull
     *
     * @param value The value to convert
     * @return String value or null
     */
    private String toStringOrNull(Object value) {
        if (value == null) {
            return null;
        }
        String str = String.valueOf(value).trim();
        return str.isEmpty() ? null : str;
    }

    /**
     * Helper method to get cell value as string
     *
     * @param cell The cell to extract value from
     * @return String value of the cell
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return "";
        }
    }

    /**
     * Helper method to get cell value as appropriate type
     *
     * @param cell The cell to extract value from
     * @return Object value of the cell
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // Return as integer if it's a whole number
                    if (numericValue == Math.floor(numericValue)) {
                        return (int) numericValue;
                    }
                    return numericValue;
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return null;
            default:
                return null;
        }
    }
}
