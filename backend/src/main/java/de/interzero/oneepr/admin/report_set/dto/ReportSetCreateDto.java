package de.interzero.oneepr.admin.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.report_set.ReportSet;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ReportSetCreateDto extends BaseDto {

    @Schema(
            description = "ID of the packaging service",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "1"
    )
    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @Schema(
            description = "Name of the report set",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "Report set name"
    )
    @JsonProperty("name")
    private String name;


    @Schema(
            description = "Mode of the report set",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "ON_PLATAFORM",
            implementation = ReportSet.ReportSetMode.class
    )
    @JsonProperty("mode")
    private ReportSet.ReportSetMode mode;

    @Schema(
            description = "Type of the report set",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "FRACTIONS",
            implementation = ReportSet.ReportSetType.class
    )
    @JsonProperty("type")
    private ReportSet.ReportSetType type;

}
