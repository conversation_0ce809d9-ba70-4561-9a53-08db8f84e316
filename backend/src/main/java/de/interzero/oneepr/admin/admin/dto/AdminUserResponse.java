package de.interzero.oneepr.admin.admin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

@Data
@Schema(description = "Admin user response")
public class AdminUserResponse {

    @Schema(example = "1")
    private Integer id;

    @Schema(example = "<EMAIL>")
    private String email;

    @Schema(example = "Admin User")
    private String name;

    @Schema(example = "ADMIN")
    private String type;

    @Schema(example = "true")
    @JsonProperty("is_active")
    private Boolean isActive;

    @Schema(example = "2021-01-01T00:00:00.000Z")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(example = "2021-01-01T00:00:00.000Z")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "User role")
    private RoleDto role;

    @Data
    public static class RoleDto {

        @Schema(example = "9")
        private Integer id;

        @Schema(example = "Admin")
        private String name;

        @Schema(example = "Admin")
        @JsonProperty("display_name")
        private String displayName;
    }
}
