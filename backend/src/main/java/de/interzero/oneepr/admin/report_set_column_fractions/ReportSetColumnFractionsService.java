package de.interzero.oneepr.admin.report_set_column_fractions;

import de.interzero.oneepr.admin.report_set_column_fractions.dto.CreateReportSetColumnFractionDto;
import de.interzero.oneepr.admin.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.report_set_columns.ReportSetColumnRepository;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFractionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

/**
 * Service for managing the association between report set columns and fractions.
 */
@Service
@RequiredArgsConstructor
public class ReportSetColumnFractionsService {

    private final ReportSetColumnFractionRepository reportSetColumnFractionRepository;

    private final ReportSetColumnRepository reportSetColumnRepository;

    private final ReportSetFractionRepository reportSetFractionRepository;

    private static final String NOT_FOUND_MESSAGE = "Report set column fraction not found";

    /**
     * Creates a new association between a column and a fraction.
     *
     * @param data The DTO containing the codes for the column and fraction.
     * @return The newly created ReportSetColumnFraction entity.
     */
    @Transactional
    public ReportSetColumnFraction create(CreateReportSetColumnFractionDto data) {
        if (data.getColumnCode() == null || data.getFractionCode() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set column fraction data");
        }

        ReportSetColumn reportSetColumn = reportSetColumnRepository.findByCode(data.getColumnCode())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Report set column not found"));

        ReportSetFraction reportSetFraction = reportSetFractionRepository.findByCode(data.getFractionCode())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Report set fraction not found"));

        ReportSetColumnFraction association = new ReportSetColumnFraction();
        association.setReportSetColumn(reportSetColumn);
        association.setReportSetFraction(reportSetFraction);
        return reportSetColumnFractionRepository.save(association);
    }

    /**
     * Finds all non-deleted column-fraction associations.
     *
     * @return A list of all active associations.
     */
    @Transactional(readOnly = true)
    public List<ReportSetColumnFraction> findAll() {
        return reportSetColumnFractionRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Finds a single non-deleted column-fraction association by its ID.
     *
     * @param id The ID of the association to find.
     * @return The found ReportSetColumnFraction entity.
     */
    @Transactional(readOnly = true)
    public ReportSetColumnFraction findOne(Integer id) {
        return reportSetColumnFractionRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND_MESSAGE));
    }

    /**
     * Soft-deletes a column-fraction association by setting its deleted_at timestamp.
     *
     * @param id The ID of the association to remove.
     */
    @Transactional
    public void remove(Integer id) {
        ReportSetColumnFraction association = reportSetColumnFractionRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND_MESSAGE));
        association.setDeletedAt(Instant.now());
        reportSetColumnFractionRepository.save(association);
    }
}