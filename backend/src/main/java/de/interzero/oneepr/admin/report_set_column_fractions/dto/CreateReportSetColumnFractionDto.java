package de.interzero.oneepr.admin.report_set_column_fractions.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating a single ReportSetColumnFraction association.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateReportSetColumnFractionDto extends BaseDto {

    @JsonProperty("column_code")
    @Schema(
            description = "Code of the associated report set column",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String columnCode;

    @JsonProperty("fraction_code")
    @Schema(
            description = "Code of the report set fraction",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String fractionCode;
}