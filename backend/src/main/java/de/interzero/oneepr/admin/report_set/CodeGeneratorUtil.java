package de.interzero.oneepr.admin.report_set;

import java.security.SecureRandom;

/**
 * Utility class for generating random codes.
 * This is a Java equivalent of the TypeScript generateCode function.
 */
public class CodeGeneratorUtil {

    private static final String CHARACTERS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    private CodeGeneratorUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * Generates a random code using alphanumeric characters.
     *
     * @param length The length of the code to generate (default: 6)
     * @return A random code string
     */
    public static String generateCode(int length) {
        StringBuilder code = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int randomIndex = SECURE_RANDOM.nextInt(CHARACTERS.length());
            code.append(CHARACTERS.charAt(randomIndex));
        }
        return code.toString();
    }

    /**
     * Generates a random code with default length of 6 characters. Only used when duplicate report set
     *
     * @return A random code string of length 6
     */
    public static String generateCode() {
        return generateCode(6);
    }
}
