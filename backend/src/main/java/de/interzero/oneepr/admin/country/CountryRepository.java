package de.interzero.oneepr.admin.country;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CountryRepository extends JpaRepository<Country, Integer>, JpaSpecificationExecutor<Country> {

    Optional<Country> findByCode(String code);

    List<Country> findByIsPublishedTrueOrderByNameAsc();

    @Override
    @NonNull
    List<Country> findAll(@Nullable Specification<Country> spec,
                          @NonNull Sort sort);
}