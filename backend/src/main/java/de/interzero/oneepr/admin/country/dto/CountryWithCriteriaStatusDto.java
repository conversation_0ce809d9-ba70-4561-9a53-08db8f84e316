package de.interzero.oneepr.admin.country.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.country.Country;
import lombok.Getter;
import lombok.Setter;

/**
 * DTO extending the Country entity to include boolean flags for specific criteria types.
 * This is a plain data object and is decoupled from the JPA entity.
 */
@Getter
@Setter
public class CountryWithCriteriaStatusDto extends Country {

    // --- Boolean flags for criteria status ---
    @JsonProperty("has_authorize_representative_criteria")
    private boolean hasAuthorizeRepresentativeCriteria;

    @JsonProperty("has_other_cost_criteria")
    private boolean hasOtherCostCriteria;

    @JsonProperty("has_representative_tier_criteria")
    private boolean hasRepresentativeTierCriteria;
}