package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.price_list.PriceList;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

/**
 * A helper Data Transfer Object representing a PriceList entity.
 * <p>
 * This DTO is used within CountryPriceListResponseDto to represent the nested 'price_list'
 * object that was included via the Prisma 'include' statement. Its purpose is to provide a
 * structured representation of a price list record.
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PriceListDto {

    @Schema(
            description = "The unique identifier of the price list.",
            example = "1"
    )
    @JsonProperty("id")
    private Integer id;

    @Schema(
            description = "The type of the price list.",
            example = "EU_LICENSE"
    )
    @JsonProperty("type")
    private PriceList.Type type;

    @Schema(
            description = "The name of the price list.",
            example = "EU License 2025"
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "The description of the price list.",
            example = "Standard EU License for 2025"
    )
    @JsonProperty("description")
    private String description;

    @Schema(
            description = "The start date of the price list validity.",
            example = "2025-01-01T00:00:00Z"
    )
    @JsonProperty("start_date")
    private Instant startDate;

    @Schema(
            description = "The end date of the price list validity.",
            example = "2025-12-31T23:59:59Z"
    )
    @JsonProperty("end_date")
    private Instant endDate;

    @Schema(
            description = "The basic price.",
            example = "1000"
    )
    @JsonProperty("basic_price")
    private Integer basicPrice;

    @Schema(
            description = "The minimum price.",
            example = "50"
    )
    @JsonProperty("minimum_price")
    private Integer minimumPrice;

    @Schema(
            description = "The registration fee.",
            example = "200"
    )
    @JsonProperty("registration_fee")
    private Integer registrationFee;

    @Schema(
            description = "The price, if not covered by other fees.",
            example = "150"
    )
    @JsonProperty("price")
    private Integer price;

    @Schema(
            description = "Variable part of the handling fee.",
            example = "2.5"
    )
    @JsonProperty("variable_handling_fee")
    private Double variableHandlingFee;

    @Schema(
            description = "The condition type for the price list.",
            example = "LICENSE_YEAR"
    )
    @JsonProperty("condition_type")
    private PriceList.ConditionType conditionType;

    @Schema(
            description = "The value associated with the condition type.",
            example = "2025"
    )
    @JsonProperty("condition_type_value")
    private String conditionTypeValue;

    @Schema(
            description = "The handling fee.",
            example = "10"
    )
    @JsonProperty("handling_fee")
    private Integer handlingFee;

    @Schema(
            description = "JSON object containing threshold data.",
            example = "{\"level1\": 100, \"level2\": 500}"
    )
    @JsonProperty("thresholds")
    private String thresholds;

    @Schema(description = "The timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "The timestamp of the last update.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    /**
     * Creates a PriceListDto from a PriceList entity.
     * This method handles the conversion, ensuring that if the entity is null,
     * the DTO will also be null. It triggers any lazy-loading required to populate the fields.
     *
     * @param entity The PriceList entity to convert.
     * @return A populated PriceListDto, or null if the entity was null.
     */
    public static PriceListDto fromEntity(PriceList entity) {
        // Return null if the source entity is null to avoid NullPointerException
        if (entity == null) {
            return null;
        }

        return PriceListDto.builder()
                .id(entity.getId())
                .type(entity.getType())
                .name(entity.getName())
                .description(entity.getDescription())
                .startDate(entity.getStartDate())
                .endDate(entity.getEndDate())
                .basicPrice(entity.getBasicPrice())
                .minimumPrice(entity.getMinimumPrice())
                .registrationFee(entity.getRegistrationFee())
                .price(entity.getPrice())
                .variableHandlingFee(entity.getVariableHandlingFee())
                .conditionType(entity.getConditionType())
                .conditionTypeValue(entity.getConditionTypeValue())
                .handlingFee(entity.getHandlingFee())
                .thresholds(entity.getThresholds())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }
}