package de.interzero.oneepr.admin.country;

import de.interzero.oneepr.admin.country.dto.CountryOverviewDto;
import de.interzero.oneepr.admin.country.dto.CountryWithCriteriaStatusDto;
import de.interzero.oneepr.admin.country.dto.CreateCountryDto;
import de.interzero.oneepr.admin.country.dto.UpdateCountryDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@RestController
@RequestMapping(Api.COUNTRIES)
@Tag(name = "Country")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class CountryController {

    private final CountryService countryService;

    @PostMapping
    @Operation(summary = "Create a new country")
    @ApiResponse(
            responseCode = "201",
            description = "Country successfully created",
            content = @Content(schema = @Schema(implementation = Country.class))
    )
    public ResponseEntity<Country> create(@RequestBody CreateCountryDto data) {
        Country createdCountry = this.countryService.create(data);
        URI location = ServletUriComponentsBuilder.fromCurrentRequest()
                .path("/{id}")
                .buildAndExpand(createdCountry.getId())
                .toUri();
        return ResponseEntity.created(location).body(createdCountry);
    }

    @GetMapping
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get all countries")
    @ApiResponse(
            responseCode = "200",
            description = "List of all countries"
    )
    public List<Country> findAll() {
        return this.countryService.findAll();
    }

    @GetMapping("/overview")
    @Operation(summary = "Get countries overview")
    @ApiResponse(
            responseCode = "200",
            description = "Countries overview with search results"
    )
    public List<CountryOverviewDto> overview(@RequestParam(required = false) String search) {
        return this.countryService.overview(search);
    }

    @GetMapping("/published")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get published countries")
    @ApiResponse(
            responseCode = "200",
            description = "List of published countries"
    )
    public List<Country> published() {
        return this.countryService.published();
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get country by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Country details",
            content = @Content(schema = @Schema(implementation = Country.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country not found"
    )
    public Country findOne(@PathVariable String id) {
        try {
            return this.countryService.findOne(Integer.valueOf(id));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid ID format: " + id);
        }
    }

    @GetMapping("/code/{code}")
    @Operation(summary = "Get country by code")
    @ApiResponse(
            responseCode = "200",
            description = "Country details by code",
            content = @Content(schema = @Schema(implementation = Country.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country not found"
    )
    public CountryWithCriteriaStatusDto findOneByCode(@PathVariable String code) {
        return this.countryService.findOneByCode(code);
    }

    @GetMapping("/code/{code}/overview")
    @Operation(summary = "Get country overview by code")
    @ApiResponse(
            responseCode = "200",
            description = "Country overview by code",
            content = @Content(schema = @Schema(implementation = CountryOverviewDto.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country not found"
    )
    public CountryOverviewDto countryOverview(@PathVariable String code) {
        return this.countryService.countryOverview(code);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update country by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Country successfully updated",
            content = @Content(schema = @Schema(implementation = Country.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country not found"
    )
    public Country update(@PathVariable String id,
                          @RequestBody UpdateCountryDto data) {
        try {
            return this.countryService.update(Integer.valueOf(id), data);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid ID format: " + id);
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete country by ID")
    @ApiResponse(
            responseCode = "204",
            description = "Country successfully deleted"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country not found"
    )
    public ResponseEntity<Void> remove(@PathVariable String id) {
        try {
            this.countryService.remove(Integer.valueOf(id));
            return ResponseEntity.noContent().build();
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid ID format: " + id);
        }
    }
}