package de.interzero.oneepr.admin.upload_files;

import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.shared.dto.CustomHeadersDto;
import de.interzero.oneepr.admin.upload_files.dto.CreateFileDto;
import de.interzero.oneepr.admin.upload_files.dto.LambdaPresignedResponseDto;
import de.interzero.oneepr.admin.upload_files.dto.RequestPresignedUrlDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.security.SecureRandom;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;

@Service
@RequiredArgsConstructor
public class UploadFilesService {

    private final CountryRepository countryRepository;

    private final FilesRepository filesRepository;

    private final RestTemplate restTemplate;

    @Value("${file.lambda.request-presigned-url}")
    private String requestPresignedUrl;

    @Value("${file.lambda.download-file-url}")
    private String downloadFileUrl;


    /**
     * Request presigned url from lambda function
     *
     * @param requestPresignedUrlDto The found RequestPresignedUrlDto entity.
     * @return The found Object entity.
     */
    public LambdaPresignedResponseDto requestUrl(RequestPresignedUrlDto requestPresignedUrlDto) {

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<RequestPresignedUrlDto> request = new HttpEntity<>(requestPresignedUrlDto, headers);
        ResponseEntity<LambdaPresignedResponseDto> response = restTemplate.postForEntity(
                requestPresignedUrl,
                request,
                LambdaPresignedResponseDto.class);
        LambdaPresignedResponseDto data = response.getBody();
        if (data == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Problems generating url");
        }
        return data;
    }

    /**
     * upload file to s3 and database
     *
     * @param data             The found CreateFileDto entity.
     * @param customHeadersDto The found CustomHeadersDto entity.
     * @param file             upload file,MultipartFile type
     * @return The found Files entity.
     * @ts-legacy the files entity seems to do not have the primary key, so I use the UUID here
     */
    @Transactional(timeout = 20)
    public Files uploadFile(CreateFileDto data,
                            CustomHeadersDto customHeadersDto,
                            MultipartFile file) {
        String userId = customHeadersDto.getUserId();
        String userRole = customHeadersDto.getUserRole();

        long size = file.getSize();
        String originalname = file.getOriginalFilename();
        String mimetype = file.getContentType();

        if (data.getCountryId() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid country ID");
        }
        // Note: Country validation would need Country repository
        Optional<Country> country = countryRepository.findById(data.getCountryId());
        if (country.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Country not found");
        }

        String filename = generateFilename(data.getDocumentType(), originalname);

        RequestPresignedUrlDto urlRequest = new RequestPresignedUrlDto();
        urlRequest.setFilename(filename);
        urlRequest.setFileType(mimetype);

        LambdaPresignedResponseDto urlResponse = requestUrl(urlRequest);
        if (urlResponse == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Presigned url or Fields not found!");
        }
        String uploadUrl = urlResponse.getUploadUrl();
        Map<String, Object> fields = urlResponse.getFields();
        if (uploadUrl == null || fields == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Presigned url or Fields not found!");
        }


        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
        // Add all fields from the presigned URL response
        for (Map.Entry<String, Object> entry : fields.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof String || value instanceof byte[]) {
                formData.add(key, value);
            }
        }

        // Add the file
        formData.add("file", file.getResource());

        // Upload file to S3 using the presigned URL
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(formData, headers);
        restTemplate.postForEntity(uploadUrl, requestEntity, String.class);

        // Create file record in database
        Files fileEntity = new Files();
        fileEntity.setId(UUID.randomUUID().toString());
        fileEntity.setUserId(userId);
        fileEntity.setCreatorType(userRole);
        fileEntity.setDocumentType(String.valueOf(data.getDocumentType()));
        fileEntity.setCountryId(data.getCountryId());
        fileEntity.setSize(String.valueOf(size));
        fileEntity.setName(filename);
        fileEntity.setExtension(mimetype);
        fileEntity.setOriginalName(originalname);
        fileEntity.setCreatedAt(Instant.now());
        return filesRepository.save(fileEntity);
    }


    /**
     * get file from s3
     *
     * @param fileId file id
     * @return result map
     */
    public Map<String, Object> getFile(String fileId) {
        Optional<Files> fileOptional = filesRepository.findById(fileId);
        if (fileOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "File not found");
        }

        Files file = fileOptional.get();

        try {
            String url = downloadFileUrl + "?fileName=" + file.getName();

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(List.of(MediaType.APPLICATION_OCTET_STREAM));

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.GET, entity, byte[].class);

            Map<String, Object> result = new HashMap<>();
            result.put("file", file);
            result.put("buffer", response.getBody());

            return result;
        } catch (Exception error) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Error downloading PDF file");
        }
    }

    /**
     * generate filename
     *
     * @param docType  document type
     * @param filename file name
     * @return result filename
     */
    public String generateFilename(CreateFileDto.DocumentType docType,
                                   String filename) {
        LocalDate today = LocalDate.now();
        int year = today.getYear();
        String month = String.format("%02d", today.getMonthValue());
        String day = String.format("%02d", today.getDayOfMonth());
        String hash = generateHash();
        return docType + "/" + year + "/" + month + "/" + day + "/" + hash + "-" + filename;
    }

    /**
     * generate hash length 8 0-9 A-Z
     *
     * @return result hash string
     */
    public String generateHash() {
        SecureRandom random = new SecureRandom();
        byte[] randomBytes = new byte[8];
        random.nextBytes(randomBytes);
        StringBuilder hash = new StringBuilder();
        for (byte b : randomBytes) {
            int value = Math.abs(b) % 36;
            if (value < 10) {
                hash.append((char) ('0' + value));
            } else {
                hash.append((char) ('A' + value - 10));
            }
        }
        return hash.substring(0, 8);
    }
}
