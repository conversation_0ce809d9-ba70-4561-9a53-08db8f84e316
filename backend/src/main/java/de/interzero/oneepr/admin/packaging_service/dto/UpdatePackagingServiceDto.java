package de.interzero.oneepr.admin.packaging_service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for partially updating a packaging service.
 * It inherits all fields from the CreatePackagingServiceDto and adds its own.
 * All fields are considered optional for partial updates.
 */
@Getter
@Setter
public class UpdatePackagingServiceDto extends CreatePackagingServiceDto {

    @JsonProperty("status")
    @Schema(
            description = "Status of the packaging service",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String status;
}