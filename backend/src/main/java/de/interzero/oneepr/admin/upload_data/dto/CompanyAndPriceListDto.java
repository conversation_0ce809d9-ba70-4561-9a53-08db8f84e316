package de.interzero.oneepr.admin.upload_data.dto;

import de.interzero.oneepr.admin.entity.BrokerCompany;
import de.interzero.oneepr.admin.price_list.PriceList;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
public class CompanyAndPriceListDto {

    @Schema(description = "Company")
    private BrokerCompany company;

    @Schema(description = "Price list")
    private PriceList priceList;

    @Schema(description = "Errors")
    private List<ErrorDto> errors;

}
