package de.interzero.oneepr.admin.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.Instant;

/**
 * Represents a single item within a ReportSetPriceList for response serialization.
 * It is a sub-component of the ReportSetDetailDto.
 */
@Data
public class ReportSetPriceListItemDetailDto {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("price")
    private Integer price;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("fraction_code")
    private String fractionCode;

    @JsonProperty("price_list_id")
    private Integer priceListId;
}