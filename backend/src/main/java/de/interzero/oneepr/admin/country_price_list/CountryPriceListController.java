package de.interzero.oneepr.admin.country_price_list;

import de.interzero.oneepr.admin.country_price_list.dto.CreateCountryPriceListDto;
import de.interzero.oneepr.common.string.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;

@RestController
@RequestMapping(Api.COUNTRY_PRICE_LISTS)
@Tag(name = "Country Price List")
@Secured({SUPER_ADMIN, ADMIN, CLERK})
@RequiredArgsConstructor
public class CountryPriceListController {

    private final CountryPriceListService countryPriceListService;

    @PostMapping
    @Operation(summary = "Create a new country price list association")
    @ApiResponse(
            responseCode = "201",
            description = "Country price list created successfully",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = CountryPriceList.class)
            )
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid input data or association already exists",
            content = @Content
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country or Price List not found",
            content = @Content
    )
    public CountryPriceList create(@Valid @RequestBody CreateCountryPriceListDto createDto) {
        return countryPriceListService.create(createDto);
    }

    @GetMapping
    @Operation(summary = "Get all active country price lists")
    @ApiResponse(
            responseCode = "200",
            description = "Country price lists retrieved successfully"
    )
    public List<CountryPriceList> findAll() {
        return countryPriceListService.findAll();
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get an active country price list by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Country price list retrieved successfully",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = CountryPriceList.class)
            )
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country price list not found",
            content = @Content
    )
    public CountryPriceList findOne(@PathVariable("id") Integer id) {
        return countryPriceListService.findOne(id);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Soft-delete a country price list by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Country price list deleted successfully",
            content = @Content
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country price list not found",
            content = @Content
    )
    public ResponseEntity<Void> remove(@PathVariable("id") Integer id) {
        countryPriceListService.remove(id);
        return ResponseEntity.ok().build();
    }
}