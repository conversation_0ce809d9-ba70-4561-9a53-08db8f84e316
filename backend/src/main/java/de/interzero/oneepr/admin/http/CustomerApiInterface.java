package de.interzero.oneepr.admin.http;

import org.springframework.http.HttpMethod;
import org.springframework.lang.NonNull;

import java.util.Map;

/**
 * @ts-legacy This component abstracts the external API call to the customer service,
 * It replaces the 'httpModuleService.customer' call.
 * This is a placeholder implementation following the established utility class pattern.
 */
public final class CustomerApiInterface {

    private CustomerApiInterface() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * Fetches customer counts grouped by country from the external customer service.
     * This is a placeholder and should be re-implemented in a proper Spring Boot way.
     *
     * @return A map where the key is the country code and the value is the customer info.
     */
    @SuppressWarnings("ConstantConditions")
    public static Object customer(@NonNull String url,
                                  @NonNull Map<String, Object> params,
                                  @NonNull HttpMethod method) {
        if (true) {
            throw new RuntimeException(
                    "This method should not be called. Please re-implement the customer API interface in Spring Boot.");
        }
        return new Object();
    }
}
