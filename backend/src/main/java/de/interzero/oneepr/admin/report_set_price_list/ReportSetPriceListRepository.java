package de.interzero.oneepr.admin.report_set_price_list;

import de.interzero.oneepr.admin.price_list.ReportSetPriceListItem;
import de.interzero.oneepr.admin.report_set.ReportSet;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ReportSetPriceListRepository extends JpaRepository<ReportSetPriceList, Integer> {

    /**
     * Finds all price lists that have not been soft-deleted.
     *
     * @return A list of active ReportSetPriceList entities.
     */
    List<ReportSetPriceList> findAllByDeletedAtIsNull();

    /**
     * Finds a single price list by its ID, but only if it has not been soft-deleted.
     *
     * @param id The ID of the price list.
     * @return An Optional containing the price list if found and active, otherwise empty.
     */
    Optional<ReportSetPriceList> findByIdAndDeletedAtIsNull(Integer id);

    /**
     * Finds all price lists associated with a given report set ID.
     * This is used for the cascading soft-delete operation.
     *
     * @param reportSetId The ID of the parent ReportSet.
     * @return A list of all associated ReportSetPriceList entities.
     */
    List<ReportSetPriceList> findAllByReportSet_Id(Integer reportSetId);

    List<ReportSetPriceList> findByReportSetInAndDeletedAtIsNullOrderByCreatedAtAsc(List<ReportSet> reportSets);

    List<ReportSetPriceList> findByReportSet_IdAndDeletedAtIsNull(Integer reportSetId);

    /**
     * Finds all active (non-deleted) price lists for a given Report Set ID, ordered by their
     * creation date.
     * <p>
     * This query uses a {@code LEFT JOIN FETCH} to eagerly load the collection of associated
     * {@code items} ({@link ReportSetPriceListItem}) for each price list. This is a critical
     * performance optimization that prevents N+1 query problems by retrieving all necessary
     * data in a single database round trip.
     *
     * @param reportSetId The ID of the {@link ReportSet} for which to retrieve price lists.
     * @return An ordered list of {@link ReportSetPriceList} entities, each with its {@code items}
     * collection fully initialized.
     */
    @EntityGraph(attributePaths = "items")
    List<ReportSetPriceList> findByReportSet_IdAndDeletedAtIsNullOrderByCreatedAtAsc(Integer reportSetId);

    /**
     * Finds all active (non-deleted) price lists for a given collection of report sets and a
     * specific license year, eagerly fetching all associated items and their nested fractions.
     * <p>
     * This comprehensive fetch strategy is a key performance optimization, allowing the entire
     * required object graph (PriceList -> Items -> Fraction) to be loaded in a single database
     * query and avoiding subsequent N+1 problems when accessing these nested relations.
     *
     * @param reportSets The list of {@link ReportSet} entities to which the price lists must belong.
     * @param year       The target license year to filter by.
     * @return A list of {@link ReportSetPriceList} entities with their {@code items} and nested
     * {@code reportSetFraction} relations fully initialized.
     */
    @Query(
            "SELECT pl FROM ReportSetPriceList pl  LEFT JOIN FETCH pl.items item  LEFT JOIN FETCH item.reportSetFraction  WHERE pl.reportSet IN :reportSets AND pl.licenseYear = :year AND pl.deletedAt IS NULL"
    )
    List<ReportSetPriceList> findByReportSetInAndLicenseYearWithItems(@Param("reportSets") List<ReportSet> reportSets,
                                                                      @Param("year") Integer year);
}