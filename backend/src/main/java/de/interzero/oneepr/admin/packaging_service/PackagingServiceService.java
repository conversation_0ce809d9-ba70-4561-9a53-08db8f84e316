package de.interzero.oneepr.admin.packaging_service;

import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.criteria.CriteriaRepository;
import de.interzero.oneepr.admin.packaging_service.dto.CreatePackagingServiceDto;
import de.interzero.oneepr.admin.packaging_service.dto.UpdatePackagingServiceDto;
import de.interzero.oneepr.admin.report_set.ReportSetRepository;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

/**
 * Service for managing packaging services.
 */
@Service
@RequiredArgsConstructor
public class PackagingServiceService {

    private final PackagingServiceRepository packagingServiceRepository;

    private final CountryRepository countryRepository;

    private final ReportSetRepository reportSetRepository;

    private final CriteriaRepository criteriaRepository;

    private static final String PACKAGING_SERVICE_NOT_FOUND = "Packaging service not found";

    /**
     * Creates a new PackagingService record.
     *
     * @param data The DTO containing creation data.
     * @return The newly created PackagingService entity.
     */
    @Transactional
    public PackagingService create(CreatePackagingServiceDto data) {
        Country country = countryRepository.findById(data.getCountryId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Country not found"));

        PackagingService packagingService = new PackagingService();
        packagingService.setName(data.getName());
        packagingService.setDescription(data.getDescription());
        packagingService.setCountry(country);
        return packagingServiceRepository.save(packagingService);
    }

    /**
     * Finds all non-deleted packaging services, with an optional filter by country.
     *
     * @param countryId Optional ID of the country to filter by.
     * @return A list of packaging services.
     */
    @Transactional(readOnly = true)
    public List<PackagingService> findAll(Integer countryId) {
        if (countryId != null) {
            return packagingServiceRepository.findAllByCountry_IdAndDeletedAtIsNull(countryId);
        }
        return packagingServiceRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Finds a single non-deleted packaging service by its ID.
     *
     * @param id The ID of the packaging service to find.
     * @return The found PackagingService entity.
     */
    @Transactional(readOnly = true)
    public PackagingService findOne(Integer id) {
        return packagingServiceRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PACKAGING_SERVICE_NOT_FOUND));
    }

    /**
     * Partially updates an existing packaging service record.
     *
     * @param id   The ID of the packaging service to update.
     * @param data DTO containing the fields to update.
     * @return The updated PackagingService entity.
     */
    @Transactional
    public PackagingService update(Integer id,
                                   UpdatePackagingServiceDto data) {
        PackagingService packagingService = packagingServiceRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PACKAGING_SERVICE_NOT_FOUND));

        ModelMapperConfig.mapPresentFields(data, packagingService);

        return packagingServiceRepository.save(packagingService);
    }

    /**
     * Soft-deletes a packaging service and its dependent ReportSets and Criterias.
     *
     * @param id The ID of the packaging service to remove.
     * @return The updated PackagingService entity, now marked as deleted.
     */
    @Transactional
    public PackagingService remove(Integer id) {
        PackagingService packagingService = packagingServiceRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PACKAGING_SERVICE_NOT_FOUND));

        Instant now = Instant.now();

        // Soft-delete dependent entities first
        reportSetRepository.softDeleteByPackagingServiceId(id, now);
        criteriaRepository.softDeleteByPackagingServiceId(id, now);

        // Soft-delete the main entity
        packagingService.setDeletedAt(now);
        return packagingServiceRepository.save(packagingService);
    }
}
