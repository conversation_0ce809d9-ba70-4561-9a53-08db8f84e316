package de.interzero.oneepr.admin.other_cost;

import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.other_cost.dto.CreateOtherCostDto;
import de.interzero.oneepr.admin.other_cost.dto.UpdateOtherCostDto;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

/**
 * Service for managing other costs.
 */
@Service
@RequiredArgsConstructor
public class OtherCostService {

    private final OtherCostRepository otherCostRepository;

    private final CountryRepository countryRepository;

    private static final String OTHER_COST_NOT_FOUND = "Other cost not found";

    /**
     * Creates a new OtherCost record.
     *
     * @param data The DTO containing creation data.
     * @return The newly created OtherCost entity.
     */
    @Transactional
    public OtherCost create(CreateOtherCostDto data) {
        Country country = countryRepository.findById(data.getCountryId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Country not found"));

        if (data.getPrice() < 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Price must be greater than 0");
        }

        OtherCost otherCost = new OtherCost();
        otherCost.setName(data.getName());
        otherCost.setPrice(data.getPrice());
        otherCost.setCountry(country);

        return otherCostRepository.save(otherCost);
    }

    /**
     * Finds all non-deleted other costs.
     *
     * @return A list of all active other costs.
     */
    @Transactional(readOnly = true)
    public List<OtherCost> findAll() {
        return otherCostRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Finds a single non-deleted other cost by its ID.
     *
     * @param id The ID of the other cost to find.
     * @return The found OtherCost entity.
     */
    @Transactional(readOnly = true)
    public OtherCost findOne(Integer id) {
        return otherCostRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, OTHER_COST_NOT_FOUND));
    }

    /**
     * Partially updates an existing other cost record.
     *
     * @param id   The ID of the other cost to update.
     * @param data DTO containing the fields to update.
     * @return The updated OtherCost entity.
     */
    @Transactional
    public OtherCost update(Integer id,
                            UpdateOtherCostDto data) {
        OtherCost otherCost = otherCostRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, OTHER_COST_NOT_FOUND));

        ModelMapperConfig.mapPresentFields(data, otherCost);

        return otherCostRepository.save(otherCost);
    }

    /**
     * Soft-deletes an other cost by setting its deleted_at timestamp.
     *
     * @param id The ID of the other cost to remove.
     * @return The updated entity with the `deletedAt` timestamp set.
     */
    @Transactional
    public OtherCost remove(Integer id) {
        OtherCost otherCost = otherCostRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, OTHER_COST_NOT_FOUND));

        otherCost.setDeletedAt(Instant.now());
        return otherCostRepository.save(otherCost);
    }
}
