package de.interzero.oneepr.admin.mail;

import de.interzero.oneepr.customer.http.AdminInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Customer.io implementation of {@link EmailOutboxGateway}.
 * <p>
 * Sends an email via the admin service, which proxies the request to Customer.io.
 * Uses the {@link AdminInterface} placeholder method to send POST request with email payload.
 * <p>
 * This implementation is used in production when mail.gateway != sandbox.
 */
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(
        prefix = "mail",
        name = "gateway",
        havingValue = "customerio",
        matchIfMissing = true
)
@Slf4j
public class CustomerIoEmailGateway implements EmailOutboxGateway {

    @Override
    public void sendEmail(EmailMessage emailMessage) {
        try {
            Map<String, Object> payload = new HashMap<>();
            payload.put("transactional_message_id", emailMessage.getTransactionalMessageId());
            payload.put("to", emailMessage.getTo());
            payload.put("from", emailMessage.getFrom());
            payload.put("subject", emailMessage.getSubject());
            payload.put("message_data", emailMessage.getMessageData());

            String url = "/integrations/customer-io/send";

            AdminInterface.admin(url, payload, HttpMethod.POST);

            log.info("Email sent via Customer.io: {}", emailMessage.getTransactionalMessageId());
        } catch (Exception e) {
            log.error("Failed to send email via Customer.io", e);
            throw new EmailDeliveryException("Failed to send email via Customer.io", e);
        }
    }
}
