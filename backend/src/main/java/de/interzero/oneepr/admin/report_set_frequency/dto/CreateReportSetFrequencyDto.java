package de.interzero.oneepr.admin.report_set_frequency.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.report_set_frequency.ReportSetFrequency;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new report set frequency.
 */
@Getter
@Setter
public class CreateReportSetFrequencyDto extends BaseDto {

    @Schema(
            description = "ID of the associated packaging service",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @Schema(
            description = "Rhythm of the report set frequency",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = ReportSetFrequency.Rhythm.class
    )
    @JsonProperty("rhythm")
    private ReportSetFrequency.Rhythm rhythm;

    @Schema(
            description = "Json config of the report set frequency. The structure depends on the 'rhythm' value.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = """
                    {
                      "deadline": { "day": 15, "month": "MARCH" },
                      "open": { "day": 1, "month": "JANUARY" }
                    }
                    """
    )
    @JsonProperty("frequency")
    private Object frequency;
}