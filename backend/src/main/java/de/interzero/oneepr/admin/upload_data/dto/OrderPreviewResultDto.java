package de.interzero.oneepr.admin.upload_data.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderPreviewResultDto {

    @Schema(
            description = "The register number of the company",
            example = "1234567890"
    )
    @JsonProperty("register_number")
    private String registerNumber;

    @Schema(
            description = "The year of the order",
            example = "2024"
    )
    @JsonProperty("year")
    private String year;

    @Schema(description = "The fractions of the order")
    @JsonProperty("fractions")
    private List<FractionDataDto> fractions;

    @Schema(description = "The errors of the order")
    @JsonProperty("errors")
    private List<ErrorDto> errors;

}
