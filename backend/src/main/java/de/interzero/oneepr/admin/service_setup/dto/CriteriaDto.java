package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * A helper Data Transfer Object representing a Criteria entity.
 * <p>
 * This DTO is used within PackagingServiceWithCriteriaDto to represent the list of
 * filtered criteria. Its purpose is to provide a structured representation of a criteria
 * record as part of the nested response for the findServiceSetupReportFrequencies endpoint.
 * For this specific use case, only the ID is strictly necessary to match the shape,
 * but a basic DTO is used for clarity and future-proofing.
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CriteriaDto {

    @JsonProperty("id")
    private Integer id;
}