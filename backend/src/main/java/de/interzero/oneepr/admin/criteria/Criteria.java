package de.interzero.oneepr.admin.criteria;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.required_information.RequiredInformation;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "criteria",
        schema = "public"
)
public class Criteria {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "criteria_id_seq"
    )
    @SequenceGenerator(
            name = "criteria_id_seq",
            sequenceName = "criteria_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @Column(
            name = "help_text",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("help_text")
    private String helpText;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "input_type")
    @JsonProperty("input_type")
    private InputType inputType;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "mode",
            nullable = false
    )
    @JsonProperty("mode")
    private Mode mode;

    @Column(
            name = "title",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("title")
    private String title;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "type",
            nullable = false
    )
    @JsonProperty("type")
    private Type type;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "calculator_type")
    @JsonProperty("calculator_type")
    private CalculatorType calculatorType;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "country_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("country_id")
    private Country country;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "packaging_service_id")
    @JsonIgnore
    @JsonProperty("packaging_service_id")
    private PackagingService packagingService;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "required_information_id")
    @JsonIgnore
    @JsonProperty("required_information_id")
    private RequiredInformation requiredInformation;

    @OneToMany(
            mappedBy = "criteria"
    )
    @JsonIgnore
    @JsonProperty("options")
    private List<CriteriaOption> options = new ArrayList<>();


    @Transient
    @JsonProperty("country_id")
    public Integer getCountryId() {
        return (this.country != null) ? this.country.getId() : null;
    }

    @Transient
    @JsonProperty("packaging_service_id")
    public Integer getPackagingServiceId() {
        return (this.packagingService != null) ? this.packagingService.getId() : null;
    }

    @Transient
    @JsonProperty("required_information_id")
    public Integer getRequiredInformationId() {
        return (this.requiredInformation != null) ? this.requiredInformation.getId() : null;
    }

    public enum CalculatorType {
        LICENSE_FEES,
        TOTAL_IN_TONS,
        TOTAL_IN_KG
    }

    public enum Mode {
        COMMITMENT,
        CALCULATOR
    }

    public enum InputType {
        RADIO,
        SELECT,
        YES_NO,
        RANGE
    }

    public enum Type {
        PACKAGING_SERVICE,
        REPORT_SET,
        REPORT_FREQUENCY,
        AUTHORIZE_REPRESENTATIVE,
        REPRESENTATIVE_TIER,
        OTHER_COST,
        PRICE_LIST,
        REQUIRED_INFORMATION
    }

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    /**
     * Update clusters auditing fields on update.
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }
}