package de.interzero.oneepr.admin.upload_data.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CompanyPreviewResultDto {

    @Schema(description = "Company registration number")
    @JsonProperty("register_number")
    private String registerNumber;

    @Schema(description = "Company name")
    @JsonProperty("name")
    private String name;

    @Schema(description = "Company address number")
    @JsonProperty("address_number")
    private String addressNumber;

    @Schema(description = "Company address street")
    @JsonProperty("address_street")
    private String addressStreet;

    @Schema(description = "Company city")
    @JsonProperty("city")
    private String city;

    @Schema(description = "Company contact email")
    @JsonProperty("contact_email")
    private String contactEmail;

    @Schema(description = "Company contact name")
    @JsonProperty("contact_name")
    private String contactName;

    @Schema(description = "Company phone number")
    @JsonProperty("phone_number")
    private String phoneNumber;

    @Schema(description = "Company VAT number")
    @JsonProperty("vat")
    private String vat;

    @Schema(description = "Company TAX number")
    @JsonProperty("tax")
    private String tax;

    @Schema(description = "Company country code")
    @JsonProperty("country_code")
    private String countryCode;

    @Schema(description = "Errors")
    @JsonProperty("errors")
    private List<ErrorDto> errors;

}
