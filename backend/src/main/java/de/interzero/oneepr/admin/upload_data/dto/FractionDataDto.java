package de.interzero.oneepr.admin.upload_data.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FractionDataDto {

    @Schema(description = "Code of the fraction")
    private String code;

    @Schema(description = "Name of the fraction")
    private String name;

    @Schema(description = "Weight of the fraction")
    private Object weight;

    @Schema(description = "Value of the fraction")
    private double value;

    @Schema(description = "Error message")
    private String error;
}
