package de.interzero.oneepr.admin.report_set;

import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.fraction_icon.FractionIcon;
import de.interzero.oneepr.admin.fraction_icon.FractionIconRepository;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.admin.price_list.ReportSetPriceListItem;
import de.interzero.oneepr.admin.report_set.dto.*;
import de.interzero.oneepr.admin.report_set_column_fractions.ReportSetColumnFraction;
import de.interzero.oneepr.admin.report_set_column_fractions.ReportSetColumnFractionRepository;
import de.interzero.oneepr.admin.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.report_set_columns.ReportSetColumnRepository;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFractionRepository;
import de.interzero.oneepr.admin.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.admin.report_set_price_list.ReportSetPriceListRepository;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@SuppressWarnings("java:S6539")
public class ReportSetService {

    private final ReportSetRepository reportSetRepository;

    private final ReportSetFractionRepository reportSetFractionRepository;

    private final ReportSetColumnRepository reportSetColumnRepository;

    private final ReportSetColumnFractionRepository reportColumnFractionRepository;

    private final ReportSetPriceListRepository reportSetPriceListRepository;

    private final ReportSetPriceListItemRepository reportSetPriceListItemRepository;

    private final FilesRepository filesRepository;

    private final FractionIconRepository fractionIconRepository;

    private final PackagingServiceRepository packagingServiceRepository;

    private static final String REPORT_SET_NOT_FOUND = "Report set not found";

    private static final String REPORT_SET_FRACTION_NOT_FOUND = "Report set fraction not found";

    private static final String REPORT_SET_FRACTION_ICON_NOT_FOUND = "Report set fraction icon not found";

    private static final String REPORT_SET_COLUMN_NOT_FOUND = "Report set column not found";

    private static final String PACKAGING_SERVICE_NOT_FOUND = "PackagingService not found";

    private static final String DELETED_AT = "deletedAt";

    /**
     * Creates and persists a new ReportSet based on the provided DTO using ModelMapper.
     *
     * @param data The data transfer object containing the details for the new report set.
     * @return The newly created and persisted ReportSet entity.
     */
    @Transactional
    public ReportSet create(ReportSetCreateDto data) {
        ReportSet reportSet = ModelMapperConfig.mapPresentFields(data, ReportSet.class);
        if (data.getPackagingServiceId() != null) {
            PackagingService packagingService = packagingServiceRepository.findById(data.getPackagingServiceId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PACKAGING_SERVICE_NOT_FOUND));
            reportSet.setPackagingService(packagingService);
        }
        return reportSetRepository.save(reportSet);
    }

    /**
     * @param query packaging_service_id and is_delete is Null
     * @return A list of Report Set.
     */
    public List<ReportSet> findAll(ReportSetFindAllDto query) {
        return reportSetRepository.findAll(byPackagingServiceIdAndNotDeleted(query.getPackagingServiceId()));
    }

    /**
     * find report set by id
     *
     * @param id report set id
     * @return report set
     */
    public ReportSet findOne(Integer id) {
        return reportSetRepository.findReportSetByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));
    }

    /**
     * Update By id and UpdateReportSetDto
     * SuppressWarnings for code complexity
     * @param id   report set id
     * @param data UpdateReportSetDto
     * @return report set
     */
    @Transactional
    @SuppressWarnings("java:S3776")
    public ReportSetDetailDto update(Integer id,
                                     UpdateReportSetDto data) {

        ReportSet reportSet = reportSetRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));

        reportSet.setId(id);
        reportSet.setName(data.getName());
        if (data.getSheetFileId() != null) {
            reportSet.setSheetFile(filesRepository.findById(data.getSheetFileId()).orElse(null));
        }
        reportSet.setSheetFileDescription(data.getSheetFileDescription());
        reportSetRepository.save(reportSet);

        List<Integer> priceListIds = Optional.ofNullable(data.getPriceLists())
                .orElseGet(ArrayList::new)
                .stream()
                .map(ReportSetPriceListDto::getId)
                .toList();

        if (!priceListIds.isEmpty()) {
            List<String> fractionCodes = Optional.ofNullable(data.getFractions())
                    .orElseGet(ArrayList::new)
                    .stream()
                    .map(ReportSetFraction::getCode)
                    .toList();
            reportSetPriceListItemRepository.deleteByFractionCodeIn(fractionCodes);
        }

        if (!CollectionUtils.isEmpty(data.getColumns())) {
            List<String> columnCode = data.getColumns().stream().map(ReportSetColumnCreateDto::getCode).toList();
            reportColumnFractionRepository.deleteReportSetColumnFractionsByColumnCodeIn(columnCode);
        }

        reportSetFractionRepository.deleteByReportSetId(id);
        reportSetColumnRepository.deleteByReportSetId(id);

        if (!CollectionUtils.isEmpty(data.getFractions())) {
            List<ReportSetFraction> fractions = data.getFractions().stream().map(fraction -> {
                ReportSetFraction reportSetFraction = new ReportSetFraction();
                reportSetFraction.setCode(fraction.getCode());
                reportSetFraction.setParentId(fraction.getParentId());
                reportSetFraction.setName(fraction.getName());
                reportSetFraction.setDescription(fraction.getDescription());
                reportSetFraction.setIcon(fraction.getIcon());
                if (fraction.getFractionIconId() != null) {
                    reportSetFraction.setFractionIcon(fractionIconRepository.findById(fraction.getFractionIconId())
                                                              .orElse(null));
                }
                reportSetFraction.setLevel(fraction.getLevel());
                reportSetFraction.setOrder(fraction.getOrder());
                if (fraction.getParentId() != null) {
                    reportSetFraction.setParent(reportSetFractionRepository.findById(fraction.getParentId())
                                                        .orElse(null));
                }
                reportSetFraction.setReportSet(reportSet);
                reportSetFraction.setHasSecondLevel(fraction.getHasSecondLevel());
                reportSetFraction.setHasThirdLevel(fraction.getHasThirdLevel());
                reportSet.addFraction(reportSetFraction);
                return reportSetFraction;
            }).toList();
            reportSetFractionRepository.saveAll(fractions);
        }

        if (!CollectionUtils.isEmpty(data.getColumns())) {
            List<ReportSetColumn> columns = data.getColumns().stream().map(fraction -> {
                ReportSetColumn reportSetColumn = new ReportSetColumn();
                reportSetColumn.setCode(fraction.getCode());
                if (fraction.getParentId() != null) {
                    reportSetColumn.setParent(reportSetColumnRepository.findById(fraction.getParentId()).orElse(null));
                }
                reportSetColumn.setName(fraction.getName());
                reportSetColumn.setDescription(fraction.getDescription());
                reportSetColumn.setUnitType(fraction.getUnitType());
                reportSetColumn.setLevel(fraction.getLevel());
                reportSetColumn.setOrder(fraction.getOrder());
                reportSetColumn.setParentId(fraction.getParentId());
                reportSetColumn.setReportSet(reportSet);
                reportSet.addColumn(reportSetColumn);
                return reportSetColumn;
            }).toList();
            reportSetColumnRepository.saveAll(columns);
        }

        if (!CollectionUtils.isEmpty(data.getColumns())) {
            List<ReportSetColumnFractionDetailDto> columnFractions = data.getColumns()
                    .stream()
                    .filter(column -> column.getFractions() != null)
                    .flatMap(column -> column.getFractions().stream())
                    .toList();
            columnFractions.forEach(fraction -> {
                ReportSetColumnFraction reportSetColumnFraction = new ReportSetColumnFraction();
                ReportSetColumn reportSetColumn = reportSetColumnRepository.findByCode(fraction.getColumnCode())
                        .orElseThrow(() -> new ResponseStatusException(
                                HttpStatus.NOT_FOUND,
                                REPORT_SET_COLUMN_NOT_FOUND));
                reportSetColumnFraction.setReportSetColumn(reportSetColumn);
                reportSetColumnFraction.setReportSetFraction(reportSetFractionRepository.findByCode(fraction.getFractionCode())
                                                                     .orElseThrow(() -> new ResponseStatusException(
                                                                             HttpStatus.NOT_FOUND,
                                                                             REPORT_SET_FRACTION_ICON_NOT_FOUND)));
                reportSetColumnFraction = reportColumnFractionRepository.save(reportSetColumnFraction);
                reportSetColumn.addFraction(reportSetColumnFraction);
            });
        }

        if (data.getPriceLists() != null) {
            for (ReportSetPriceListDto dto : data.getPriceLists()) {
                // Step 1: Create and map the parent ReportSetPriceList entity from the DTO.
                ReportSetPriceList priceListEntity = new ReportSetPriceList();

                // Handle upsert logic: if an ID is provided, fetch the existing entity.
                if (dto.getId() != null && dto.getId() > 0) {
                    priceListEntity = reportSetPriceListRepository.findById(dto.getId())
                            .orElse(priceListEntity);
                }

                priceListEntity.setTitle(dto.getTitle());
                priceListEntity.setStartDate(dto.getStartDate());
                priceListEntity.setEndDate(dto.getEndDate());
                priceListEntity.setType(dto.getType());
                priceListEntity.setFixedPrice(dto.getFixedPrice());
                priceListEntity.setBasePrice(dto.getBasePrice());
                priceListEntity.setMinimumFee(dto.getMinimumFee());
                priceListEntity.setLicenseYear(dto.getLicenseYear());
                priceListEntity.setReportSet(reportSet);
                reportSet.addPriceList(priceListEntity);
                // Step 2: Save the parent entity FIRST to get a managed instance with a generated ID.
                ReportSetPriceList savedPriceListEntity = reportSetPriceListRepository.save(priceListEntity);

                // Step 3: Now create and save the child items, linking them to the SAVED parent.
                if (dto.getItems() != null && !dto.getItems().isEmpty()) {
                    List<ReportSetPriceListItem> itemEntities = dto.getItems().stream().map(itemDto -> {
                        ReportSetPriceListItem itemEntity = new ReportSetPriceListItem();
                        itemEntity.setPrice(itemDto.getPrice());
                        itemEntity.setReportSetFraction(reportSetFractionRepository.findByCode(itemDto.getFractionCode())
                                                                .orElseThrow(() -> new ResponseStatusException(
                                                                        HttpStatus.NOT_FOUND,
                                                                        itemDto.getFractionCode() + REPORT_SET_FRACTION_ICON_NOT_FOUND)));

                        itemEntity.setPriceList(savedPriceListEntity);
                        savedPriceListEntity.addItem(itemEntity);
                        return itemEntity;
                    }).toList();

                    // Step 4: Save the fully constructed and linked child items.
                    reportSetPriceListItemRepository.saveAllAndFlush(itemEntities);
                }
            }
        }
        ReportSet finalReportSet = reportSetRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));
        boolean hasCriteria = !finalReportSet.getPackagingService().getCriterias().isEmpty();
        return convertToDetailDto(finalReportSet, hasCriteria);
    }

    /**
     * Converts a ReportSet entity to its detailed DTO representation. This is the primary
     * method to call from within a transactional context to ensure all lazy-loaded
     * collections are properly initialized and mapped.
     *
     * @param reportSet The ReportSet entity to convert, fetched within an active transaction.
     * @param hasCriteria The pre-calculated hasCriteria flag.
     * @return A fully populated ReportSetDetailDto ready for API response.
     */
    private ReportSetDetailDto convertToDetailDto(ReportSet reportSet,
                                                  boolean hasCriteria) {
        if (reportSet == null) {
            return null;
        }

        ReportSetDetailDto dto = new ReportSetDetailDto();

        // Map scalar fields
        dto.setId(reportSet.getId());
        dto.setName(reportSet.getName());
        dto.setMode(reportSet.getMode());
        dto.setType(reportSet.getType());
        dto.setCreatedAt(reportSet.getCreatedAt());
        dto.setUpdatedAt(reportSet.getUpdatedAt());
        dto.setDeletedAt(reportSet.getDeletedAt());
        dto.setSheetFileDescription(reportSet.getSheetFileDescription());

        // Map direct relations by calling their respective converters
        dto.setSheetFile(convertToFileDto(reportSet.getSheetFile()));
        dto.setHasCriteria(hasCriteria);

        // Map collections, triggering lazy loads within the transaction
        if (reportSet.getColumns() != null) {
            dto.setColumns(reportSet.getColumns().stream().map(this::convertToColumnDto).toList());
        }
        if (reportSet.getFractions() != null) {
            dto.setFractions(reportSet.getFractions().stream().map(this::convertToFractionDto).toList());
        }
        if (reportSet.getPriceLists() != null) {
            dto.setPriceLists(reportSet.getPriceLists().stream().map(this::convertToPriceListDto).toList());
        }

        return dto;
    }

    private FilesDetailDto convertToFileDto(Files entity) {
        if (entity == null) {
            return null;
        }
        FilesDetailDto dto = new FilesDetailDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setExtension(entity.getExtension());
        dto.setSize(entity.getSize());
        dto.setCreatorType(entity.getCreatorType());
        dto.setDocumentType(entity.getDocumentType());
        dto.setUserId(entity.getUserId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setOriginalName(entity.getOriginalName());
        dto.setCountryId(entity.getCountryId());
        return dto;
    }

    // --- Collection Converters (with recursion and nested objects) ---

    private ReportSetColumnDetailDto convertToColumnDto(ReportSetColumn entity) {
        if (entity == null) {
            return null;
        }
        ReportSetColumnDetailDto dto = new ReportSetColumnDetailDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setUnitType(entity.getUnitType());
        dto.setParentId(entity.getParentId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setDeletedAt(entity.getDeletedAt());
        dto.setLevel(entity.getLevel());
        dto.setOrder(entity.getOrder());
        dto.setCode(entity.getCode());
        dto.setReportSetId(entity.getReportSetId());
        dto.setParentCode(entity.getParentCode());
        if (entity.getChildren() != null) {
            dto.setChildren(entity.getChildren().stream().map(this::convertToColumnDto).toList());
        }
        if (entity.getFractions() != null) {
            dto.setFractions(entity.getFractions().stream().map(this::convertToColumnFractionDto).toList());
        }
        return dto;
    }

    private ReportSetFractionDetailDto convertToFractionDto(ReportSetFraction entity) {
        if (entity == null) {
            return null;
        }
        ReportSetFractionDetailDto dto = new ReportSetFractionDetailDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setParentId(entity.getParentId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setDeletedAt(entity.getDeletedAt());
        dto.setIsActive(entity.getIsActive());
        dto.setLevel(entity.getLevel());
        dto.setOrder(entity.getOrder());
        dto.setCode(entity.getCode());
        dto.setIcon(entity.getIcon());
        dto.setHasSecondLevel(entity.getHasSecondLevel());
        dto.setHasThirdLevel(entity.getHasThirdLevel());
        dto.setReportSetId(entity.getReportSetId());
        dto.setParentCode(entity.getParentCode());
        dto.setFractionIcon(convertToFractionIconDto(entity.getFractionIcon()));
        if (entity.getChildren() != null) {
            dto.setChildren(entity.getChildren().stream().map(this::convertToFractionDto).collect(Collectors.toSet()));
        }
        return dto;
    }

    private ReportSetPriceListDetailDto convertToPriceListDto(ReportSetPriceList entity) {

        if (entity == null) {
            return null;
        }
        ReportSetPriceListDetailDto dto = new ReportSetPriceListDetailDto();
        dto.setId(entity.getId());
        dto.setTitle(entity.getTitle());
        dto.setStartDate(entity.getStartDate());
        dto.setEndDate(entity.getEndDate());
        dto.setBasePrice(entity.getBasePrice());
        dto.setFixedPrice(entity.getFixedPrice());
        dto.setMinimumFee(entity.getMinimumFee());
        dto.setType(entity.getType());
        dto.setLicenseYear(entity.getLicenseYear());
        if (!entity.getItems().isEmpty()) {
            dto.setItems(entity.getItems().stream().map(this::convertToPriceListItemDto).toList());
        }
        return dto;
    }

    // --- Innermost and Utility Converters ---

    private ReportSetPriceListItemDetailDto convertToPriceListItemDto(ReportSetPriceListItem entity) {
        if (entity == null) {
            return null;
        }
        ReportSetPriceListItemDetailDto dto = new ReportSetPriceListItemDetailDto();
        dto.setId(entity.getId());
        dto.setPrice(entity.getPrice());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setFractionCode(entity.getFractionCode());
        dto.setPriceListId(entity.getPriceListId());
        return dto;
    }

    private FractionIconDetailDto convertToFractionIconDto(FractionIcon entity) {
        if (entity == null) {
            return null;
        }
        FractionIconDetailDto dto = new FractionIconDetailDto();
        dto.setId(entity.getId());
        dto.setImageUrl(entity.getImageUrl());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setDeletedAt(entity.getDeletedAt());
        dto.setFileId(entity.getFileId());
        return dto;
    }

    private ReportSetColumnFractionDetailDto convertToColumnFractionDto(ReportSetColumnFraction entity) {
        if (entity == null) {
            return null;
        }
        ReportSetColumnFractionDetailDto dto = new ReportSetColumnFractionDetailDto();
        dto.setId(entity.getId());
        dto.setColumnCode(entity.getColumnCode());
        dto.setFractionCode(entity.getFractionCode());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }

    /**
     * Duplicates an existing report set with all its related data.
     * SuppressWarnings for code complexity
     * @param id The ID of the report set to duplicate
     * @return A map indicating success
     * @ts-legacy the reportSetRepository.findById(id) do not query deleted_at = null we handle it in our code
     */
    @Transactional
    @SuppressWarnings({"java:S3776", "java:S6541"})
    public Map<String, Boolean> duplicate(Integer id) {
        ReportSet reportSet = reportSetRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));

        ReportSet duplicatedReportSet = new ReportSet();
        duplicatedReportSet.setPackagingService(reportSet.getPackagingService());
        duplicatedReportSet.setName(reportSet.getName() + " - " + CodeGeneratorUtil.generateCode());
        duplicatedReportSet.setMode(reportSet.getMode());
        duplicatedReportSet.setType(reportSet.getType());

        duplicatedReportSet = reportSetRepository.save(duplicatedReportSet);

        Map<String, String> codeDict = new HashMap<>();

        List<ReportSetFraction> originalFractions = reportSet.getFractions()
                .stream()
                .filter(f -> f.getDeletedAt() == null)
                .sorted(Comparator.comparing(ReportSetFraction::getLevel))
                .toList();

        List<ReportSetFraction> newFractions = new ArrayList<>();
        for (ReportSetFraction originalFraction : originalFractions) {
            if (!codeDict.containsKey(originalFraction.getCode())) {
                codeDict.put(originalFraction.getCode(), CodeGeneratorUtil.generateCode());
            }
            if (originalFraction.getParentCode() != null && originalFraction.getLevel() != 1 && !codeDict.containsKey(
                    originalFraction.getParentCode())) {
                codeDict.put(originalFraction.getParentCode(), CodeGeneratorUtil.generateCode());
            }

            ReportSetFraction newFraction = new ReportSetFraction();
            newFraction.setReportSet(duplicatedReportSet);
            newFraction.setCode(codeDict.get(originalFraction.getCode()));
            if (codeDict.containsKey(originalFraction.getParentCode())) {
                newFraction.setParent(reportSetFractionRepository.findByCode(codeDict.get(originalFraction.getParentCode()))
                                              .orElse(null));
            }
            newFraction.setName(originalFraction.getName());
            newFraction.setDescription(originalFraction.getDescription());
            newFraction.setIsActive(originalFraction.getIsActive());
            newFraction.setIcon(originalFraction.getIcon());
            newFraction.setFractionIcon(fractionIconRepository.findById(originalFraction.getFractionIconId())
                                                .orElse(null));
            newFraction.setLevel(originalFraction.getLevel());
            newFraction.setOrder(originalFraction.getOrder());
            newFraction.setHasSecondLevel(originalFraction.getHasSecondLevel());
            newFraction.setHasThirdLevel(originalFraction.getHasThirdLevel());

            newFractions.add(newFraction);
        }

        reportSetFractionRepository.saveAll(newFractions);

        // Duplicate columns
        List<ReportSetColumn> originalColumns = reportSet.getColumns()
                .stream()
                .filter(c -> c.getDeletedAt() == null)
                .sorted(Comparator.comparing(ReportSetColumn::getLevel))
                .toList();

        List<ReportSetColumn> newColumns = new ArrayList<>();
        List<ReportSetColumnFraction> newColumnFractions = new ArrayList<>();

        for (ReportSetColumn originalColumn : originalColumns) {
            if (!codeDict.containsKey(originalColumn.getCode())) {
                codeDict.put(originalColumn.getCode(), CodeGeneratorUtil.generateCode());
            }
            if (originalColumn.getParentCode() != null && originalColumn.getLevel() != 1 && !codeDict.containsKey(
                    originalColumn.getParentCode())) {
                codeDict.put(originalColumn.getParentCode(), CodeGeneratorUtil.generateCode());
            }

            ReportSetColumn newColumn = new ReportSetColumn();
            newColumn.setReportSet(duplicatedReportSet);
            newColumn.setName(originalColumn.getName());
            newColumn.setDescription(originalColumn.getDescription());
            newColumn.setUnitType(originalColumn.getUnitType());
            newColumn.setLevel(originalColumn.getLevel());
            newColumn.setOrder(originalColumn.getOrder());
            newColumn.setCode(codeDict.get(originalColumn.getCode()));
            if (codeDict.containsKey(originalColumn.getParentCode())) {
                newColumn.setParent(reportSetColumnRepository.findByCode(codeDict.get(originalColumn.getParentCode()))
                                            .orElse(null));
            }

            newColumns.add(newColumn);

            // Collect column fractions for later processing
            if (originalColumn.getFractions() != null) {
                for (ReportSetColumnFraction originalFraction : originalColumn.getFractions()) {
                    if (originalFraction.getDeletedAt() == null) {
                        ReportSetColumnFraction newColumnFraction = new ReportSetColumnFraction();
                        newColumnFraction.setReportSetColumn(reportSetColumnRepository.findByCode(codeDict.get(
                                        originalFraction.getColumnCode()))
                                                                     .orElseThrow(() -> new ResponseStatusException(
                                                                             HttpStatus.NOT_FOUND,
                                                                             REPORT_SET_COLUMN_NOT_FOUND)));
                        newColumnFraction.setReportSetFraction(reportSetFractionRepository.findByCode(codeDict.get(
                                        originalFraction.getFractionCode()))
                                                                       .orElseThrow(() -> new ResponseStatusException(
                                                                               HttpStatus.NOT_FOUND,
                                                                               REPORT_SET_FRACTION_NOT_FOUND)));
                        newColumnFractions.add(newColumnFraction);
                    }
                }
            }
        }

        reportSetColumnRepository.saveAll(newColumns);
        reportColumnFractionRepository.saveAll(newColumnFractions);

        // Duplicate price lists
        List<ReportSetPriceList> originalPriceLists = reportSet.getPriceLists()
                .stream()
                .filter(pl -> pl.getDeletedAt() == null)
                .toList();

        for (ReportSetPriceList originalPriceList : originalPriceLists) {
            ReportSetPriceList newPriceList = new ReportSetPriceList();
            newPriceList.setTitle(originalPriceList.getTitle());
            newPriceList.setStartDate(originalPriceList.getStartDate());
            newPriceList.setEndDate(originalPriceList.getEndDate());
            newPriceList.setType(originalPriceList.getType());
            newPriceList.setFixedPrice(originalPriceList.getFixedPrice());
            newPriceList.setBasePrice(originalPriceList.getBasePrice());
            newPriceList.setMinimumFee(originalPriceList.getMinimumFee());
            newPriceList.setReportSet(duplicatedReportSet);

            ReportSetPriceList savedPriceList = reportSetPriceListRepository.save(newPriceList);

            // Duplicate price list items
            if (originalPriceList.getItems() != null) {
                List<ReportSetPriceListItem> newItems = new ArrayList<>();
                for (ReportSetPriceListItem originalItem : originalPriceList.getItems()) {
                    if (originalItem.getDeletedAt() == null) {
                        ReportSetPriceListItem newItem = new ReportSetPriceListItem();
                        newItem.setPrice(originalItem.getPrice());

                        // Find the new fraction using the mapped code
                        String originalFractionCode = originalItem.getReportSetFraction().getCode();
                        String newFractionCode = codeDict.get(originalFractionCode);
                        ReportSetFraction newFraction = reportSetFractionRepository.findByCode(newFractionCode)
                                .orElseThrow(() -> new ResponseStatusException(
                                        HttpStatus.NOT_FOUND,
                                        "New fraction not found"));

                        newItem.setReportSetFraction(newFraction);
                        newItem.setPriceList(savedPriceList);
                        newItems.add(newItem);
                    }
                }
                reportSetPriceListItemRepository.saveAll(newItems);
            }
        }

        return Map.of("ok", true);
    }

    /**
     * Soft deletes a report set by setting its deleted_at timestamp.
     *
     * @param id The ID of the report set to delete
     */
    @Transactional
    public void remove(Integer id) {
        ReportSet reportSet = reportSetRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));

        reportSet.setDeletedAt(Instant.now());
        reportSetRepository.save(reportSet);
    }

    private static Specification<ReportSet> byPackagingServiceIdAndNotDeleted(Integer packagingServiceId) {
        return (root, query, cb) -> {
            Join<ReportSet, ReportSetColumn> columnsJoin = root.join("columns", JoinType.LEFT);
            Join<ReportSet, ReportSetFraction> fractionsJoin = root.join("fractions", JoinType.LEFT);

            Predicate predicate = cb.isNull(root.get(DELETED_AT));

            predicate = cb.and(
                    predicate,
                    cb.or(columnsJoin.isNull(), cb.isNull(columnsJoin.get(DELETED_AT))),
                    cb.or(fractionsJoin.isNull(), cb.isNull(fractionsJoin.get(DELETED_AT))));

            if (packagingServiceId != null) {
                predicate = cb.and(predicate, cb.equal(root.get("packagingService").get("id"), packagingServiceId));
            }

            return predicate;
        };
    }

}
