package de.interzero.oneepr.common.util;

import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * A service class providing common utility functions.
 *
 * @ts-legacy This class provides a real implementation for the `utils.formatDateToDDMMYYYY` function.
 * This version uses the system's default timezone to more closely match the original JavaScript behavior.
 */
@Service
public class DateService {

    private static final DateTimeFormatter DD_MM_YYYY_FORMATTER = DateTimeFormatter.ofPattern("dd-MM-yyyy")
            .withZone(ZoneId.systemDefault());

    /**
     * Formats a given Instant into a string with the pattern "dd-MM-yyyy".
     * The conversion uses the server's default timezone.
     *
     * @param date The Instant object to format. Can be null.
     * @return A formatted date string (e.g., "16-06-2025"), or null if the input is null.
     */
    public String formatDateToDDMMYYYY(Instant date) {
        if (date == null) {
            return null;
        }
        return DD_MM_YYYY_FORMATTER.format(date);
    }
}
