package de.interzero.oneepr.auth.auth;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Getter
@Configuration
public class RsaKeyProperties {


    private final RSAPrivateKey privateKey;

    private final RSAPublicKey publicKey;

    public RsaKeyProperties(@Value("${rsa.private-key}") String privateKeyString,
                            @Value("${rsa.public-key}") String publicKeyString) {
        this.privateKey = convertToRSAPrivateKey(privateKeyString);
        this.publicKey = convertToRSAPublicKey(publicKeyString);
    }

    /**
     * Helper method to convert string to RSA private key
     *
     * @param rsaPrivateKeyString the RSA private key as a Base64 encoded string
     * @return RSAPrivateKey object
     */
    private RSAPrivateKey convertToRSAPrivateKey(String rsaPrivateKeyString) {
        byte[] privateKeyBytes = Base64.getDecoder().decode(rsaPrivateKeyString);

        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            throw new IllegalStateException("Error while converting private key", e);
        }
    }

    /**
     * Helper method to convert string to RSA public key
     *
     * @param rsaPublicKeyString the RSA public key as a Base64 encoded string
     * @return RSAPublicKey object
     */
    private RSAPublicKey convertToRSAPublicKey(String rsaPublicKeyString) {
        byte[] publicKeyBytes = Base64.getDecoder().decode(rsaPublicKeyString);

        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
            return (RSAPublicKey) keyFactory.generatePublic(keySpec);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            throw new IllegalStateException("Error while converting public key", e);
        }
    }
}
