package de.interzero.oneepr.auth.auth.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NonNull;
import de.interzero.oneepr.auth.user.User;
import org.springframework.lang.Nullable;

/**
 * Response object for the login endpoint.
 * @param accessToken JWT access token for the user
 * @param expiresIn Expiration time of the access token in seconds
 * @param refreshToken JWT refresh token for the user
 * @param user User details
 */
public record LoginResponse(
    @JsonProperty("access_token") @NonNull String accessToken,
    @JsonProperty("expires_in") @NonNull Long expiresIn,
    @JsonProperty("refresh_token") @Nullable String refreshToken,
    @JsonProperty("user") @Nullable UserInfo user
) {

    /**
     * User information record containing essential user details.
     * @param id User ID
     * @param email User email address
     * @param name User name // TODO figure out if this is a username or full name
     * @param isActive Indicates if the user is active
     * @param status User status
     * @param roleId Role ID of the user
     * @param role Role name of the user
     * @param hasPassword Indicates if the user has a password set
     */
    public record UserInfo(
        @JsonProperty("id") @NonNull Integer id,
        @JsonProperty("email") @NonNull String email,
        @JsonProperty("name") @NonNull String name,
        @JsonProperty("is_active") @NonNull Boolean isActive,
        @JsonProperty("status") @NonNull User.Status status,
        @JsonProperty("role_id") @NonNull Integer roleId,
        @JsonProperty("role") @NonNull String role,
        @JsonProperty("has_password") @NonNull Boolean hasPassword
    ) {}
}
