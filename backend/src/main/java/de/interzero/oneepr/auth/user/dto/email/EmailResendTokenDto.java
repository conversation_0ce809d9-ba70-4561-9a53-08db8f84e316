package de.interzero.oneepr.auth.user.dto.email;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.auth.user.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "DTO for resending token with type and magic link")
@Data
@EqualsAndHashCode(callSuper = true)
public class EmailResendTokenDto extends EmailDto {

    @JsonProperty("type_resend_token")
    @Schema(
            description = "Type of token resend",
            example = "PASSWORD"
    )
    private User.TypeResendToken typeResendToken;

    @JsonProperty("token_magic_link")
    @Schema(
            description = "Magic link token",
            example = "abc123xyz-token"
    )
    private String tokenMagicLink;
}