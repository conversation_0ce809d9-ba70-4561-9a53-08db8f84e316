package de.interzero.oneepr.auth.user;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link User} entities.
 */
@Repository
public interface UserRepository extends JpaRepository<User, Integer>, JpaSpecificationExecutor<User> {

    /**
     * Find a user by email address, ignoring case.
     *
     * @param email email address of the user
     * @return optional user
     */
    Optional<User> findByEmailIgnoreCase(String email);

    /**
     * Finds an active and non-deleted user by email.
     *
     * @param email user's email
     * @return optional user
     */
    Optional<User> findByEmailAndIsActiveTrueAndDeletedAtIsNull(String email);

    /**
     * Find first active user by email (case-insensitive) who is not deleted.
     *
     * @param email the email to search for
     * @return optional user entity
     */
    Optional<User> findFirstByEmailIgnoreCaseAndIsActiveTrueAndDeletedAtIsNull(String email);

    /**
     * Find user by email ignoring case and not deleted.
     *
     * @param email email to check
     * @return optional user
     */
    Optional<User> findFirstByEmailIgnoreCaseAndDeletedAtIsNull(String email);

    /**
     * Find user by id and not deleted.
     *
     * @param id user ID
     * @return optional user
     */
    Optional<User> findByIdAndDeletedAtIsNull(Integer id);

    /**
     * Finds a user by email if the user is not soft-deleted.
     *
     * @param email the email address of the user
     * @return an Optional containing the user if found, otherwise empty
     */
    Optional<User> findByEmailAndDeletedAtIsNull(String email);

    /**
     * Finds a user by their email or ID.
     *
     * <p>This mirrors the original Prisma query:
     * {@code findFirst({ where: { OR: [ { email }, { id } ] } })}</p>
     *
     * @param email the email of the user
     * @param id    the ID of the user
     * @return an Optional containing the user if found, or empty otherwise
     */
    @Query("SELECT u FROM User u WHERE u.email = :email OR u.id = :id")
    Optional<User> findFirstByEmailOrId(@Param("email") String email,
                                        @Param("id") Integer id);

    /**
     * Finds a user by email and status list if not deleted.
     *
     * @param email    the email to match
     * @param statuses the list of valid statuses
     * @return an Optional containing the user if found, or empty otherwise
     */
    Optional<User> findFirstByEmailAndDeletedAtIsNullAndStatusIn(String email,
                                                                 List<User.Status> statuses);

    /**
     * Finds a user by ID and magic link token.
     *
     * @param id             user ID
     * @param tokenMagicLink magic link token
     * @return optional user
     */
    Optional<User> findByIdAndTokenMagicLink(Integer id,
                                             String tokenMagicLink);

    /**
     * Find active and non-deleted user by ID.
     *
     * @param id user ID
     * @return optional user
     */
    Optional<User> findByIdAndIsActiveTrueAndDeletedAtIsNull(Integer id);
}
