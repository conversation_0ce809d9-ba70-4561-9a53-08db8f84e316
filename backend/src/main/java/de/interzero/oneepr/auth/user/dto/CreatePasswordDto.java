package de.interzero.oneepr.auth.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "Fields required to create or set a password for a user")
@Data
@EqualsAndHashCode(callSuper = true)
public class CreatePasswordDto extends BaseDto {

    @JsonProperty("userId")
    @Schema(
            description = "ID of the user",
            example = "123"
    )
    private Integer userId;

    @JsonProperty("password")
    @Schema(
            description = "New password to set",
            example = "SecurePassword123!"
    )
    private String password;
}
