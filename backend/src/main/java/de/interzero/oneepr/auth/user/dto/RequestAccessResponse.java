package de.interzero.oneepr.auth.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "Response returned after access request is created")
@Data
public class RequestAccessResponse {

    @Schema(
            description = "Response message",
            example = "Email sent"
    )

    private String message;

    @Schema(
            description = "Operation success flag",
            example = "true"
    )
    @JsonProperty("success")
    private boolean success;

    @Schema(description = "Hashed token used for access")
    @JsonProperty("token")
    private String token;

    @Schema(description = "Generated callback URL with token")
    @JsonProperty("callbackUrl")
    private String callbackUrl;
}