package de.interzero.oneepr.auth.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "DTO for updating user password")
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatePasswordDto extends BaseDto {

    @JsonProperty("old_password")
    @Schema(
            description = "Old password",
            example = "oldPassword123"
    )
    private String oldPassword;

    @JsonProperty("new_password")
    @Schema(
            description = "New password",
            example = "newSecurePassword456"
    )
    private String newPassword;
}
