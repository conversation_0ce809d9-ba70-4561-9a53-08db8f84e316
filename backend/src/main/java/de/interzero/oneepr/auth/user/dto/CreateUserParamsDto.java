package de.interzero.oneepr.auth.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "Optional query parameter to indicate user creation by admin")
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateUserParamsDto extends BaseDto {

    @JsonProperty("create_by_admin")
    @Schema(
            description = "Create a user via admin",
            example = "true",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String createByAdmin;
}
