package de.interzero.oneepr.auth.role;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.auth.user.User;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "one_epr_role",
        schema = "public"
)
public class Role implements Serializable {

    @JsonProperty("id")
    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "one_epr_role_id_gen"
    )
    @SequenceGenerator(
            name = "one_epr_role_id_gen",
            sequenceName = "one_epr_role_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @JsonProperty("name")
    @Column(nullable = false)
    private String name;

    @JsonProperty("display_name")
    @Column(
            name = "display_name",
            nullable = false
    )
    private String displayName;

    @JsonProperty("is_active")
    @Column(name = "is_active")
    private Boolean isActive = true;

    @JsonProperty("created_at")
    @Column(name = "created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    @Column(name = "updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    @Column(name = "deleted_at")
    private Instant deletedAt;

    @JsonProperty("users")
    @JsonIgnore
    @OneToMany(
            mappedBy = "role",
            cascade = CascadeType.ALL,
            orphanRemoval = true
    )
    private List<User> users;

    @PrePersist
    public void prePersist() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = Instant.now();
    }

}
