package de.interzero.oneepr.auth.user;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for {@link ChangeUserEmail} entities.
 * Provides methods for managing user email change requests.
 */
@Repository
public interface ChangeUserEmailRepository extends JpaRepository<ChangeUserEmail, UUID> {

    /**
     * Marks all {@link ChangeUserEmail} entries for the given user as deleted
     * by setting their {@code deletedAt} timestamp.
     *
     * @param userId the ID of the user whose change email requests should be marked as deleted
     * @param now    the current timestamp to set as {@code deletedAt}
     */
    @Modifying
    @Query("UPDATE ChangeUserEmail c SET c.deletedAt = :now WHERE c.user.id = :userId")
    void markDeletedByUser_Id(@Param("userId") Integer userId,
                              @Param("now") Instant now);

    /**
     * Finds the first email change request for a given user that has not been deleted.
     *
     * @param userId the ID of the user
     * @return an {@link Optional} containing the {@link ChangeUserEmail} if found, or empty otherwise
     */
    Optional<ChangeUserEmail> findFirstByUser_IdAndDeletedAtIsNull(Integer userId);
}
