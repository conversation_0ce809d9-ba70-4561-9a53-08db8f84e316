#database
spring.jpa.hibernate.ddl-auto=validate
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=*****************************************
spring.datasource.username=oneepr-local-user
spring.datasource.password=oneepr-local-password
# Flyway configuration
spring.flyway.enabled=true
spring.flyway.out-of-order=true
spring.flyway.locations=classpath:db/migration

# AWS S3 Configuration for docker stack
aws.region=eu-west-2
aws.s3.bucket-name=interzero-oneepr-files-docker