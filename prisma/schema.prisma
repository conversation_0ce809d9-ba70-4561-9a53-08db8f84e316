generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Broker {
  id                Int                 @id @default(autoincrement())
  user_id           Int                 @unique
  name              String
  email             String              @unique
  phone             String
  enroled_at        DateTime
  company_name      String              @map("company_name")
  vat               String?
  tax               String?
  created_at        DateTime            @default(now())
  updated_at        DateTime?           @updatedAt
  deleted_at        DateTime?
  is_active         Boolean             @default(true)
  BrokerCompany     BrokerCompany[]
  UploadFileHistory UploadFileHistory[]

  @@map("broker")
}

model BrokerCompany {
  id                 Int                  @id @default(autoincrement())
  broker_id          Int
  name               String
  register_number    String
  vat                String?
  tax                String?
  country_code       String?
  address_number     String?
  address_street     String
  city               String
  contact_name       String
  contact_email      String
  phone_number       String
  file_id            Int?
  file               UploadFileHistory?   @relation(fields: [file_id], references: [id])
  created_at         DateTime             @default(now())
  updated_at         DateTime?            @updatedAt
  deleted_at         DateTime?
  broker             Broker               @relation(fields: [broker_id], references: [id])
  BrokerCompanyOrder BrokerCompanyOrder[]

  @@map("broker_company")
}

model BrokerCompanyOrder {
  id              Int                      @id @default(autoincrement())
  customer_number String
  company_id      Int
  transfer_date   DateTime
  order_number    String
  year            Int
  fractions       Json[]
  status          BrokerCompanyOrderStatus
  created_at      DateTime                 @default(now())
  deleted_at      DateTime?
  updated_at      DateTime?                @updatedAt
  type            BrokerCompanyOrderType
  net_value       Int
  file_id         Int
  company         BrokerCompany            @relation(fields: [company_id], references: [id])
  file            UploadFileHistory        @relation(fields: [file_id], references: [id])

  @@map("broker_company_order")
}

model UploadFileHistory {
  id                 Int                  @id @default(autoincrement())
  file_name          String
  file_url           String
  broker_id          Int
  created_at         DateTime             @default(now())
  deleted_at         DateTime?
  updated_at         DateTime?            @updatedAt
  BrokerCompanyOrder BrokerCompanyOrder[]
  broker             Broker               @relation(fields: [broker_id], references: [id])
  BrokerCompany      BrokerCompany[]

  @@map("upload_file_history")
}

model Files {
  id                   String               @id @default(uuid())
  name                 String
  extension            String
  size                 String
  creator_type         String
  document_type        String
  user_id              String?
  created_at           DateTime             @default(now())
  updated_at           DateTime?            @updatedAt
  original_name        String
  country_id           Int?
  fraction_icon        FractionIcon?
  report_set           ReportSet?
  required_information RequiredInformation?
  Settings             Settings?

  @@map("files")
}

model Notifications {
  id         String            @id @default(uuid())
  user_id    Int
  service    Services          @default(CUSTOMER)
  subject    String
  message    String
  type       TypeNotifications @default(EMAIL)
  created_at DateTime          @default(now())
  updated_at DateTime?         @updatedAt
  deleted_at DateTime?

  @@map("notifications")
}

model Country {
  id                                 Int                   @id @default(autoincrement())
  name                               String                @unique
  created_at                         DateTime              @default(now())
  updated_at                         DateTime?             @updatedAt
  authorize_representative_obligated Boolean               @default(false)
  code                               String                @unique
  flag_url                           String
  other_costs_obligated              Boolean               @default(false)
  is_published                       Boolean               @default(false)
  followers                          CountryFollower[]
  country_price_lists                CountryPriceList[]
  criterias                          Criteria[]
  other_costs                        OtherCost[]
  packaging_services                 PackagingService[]
  representative_tiers               RepresentativeTier[]
  required_informations              RequiredInformation[]

  @@map("country")
}

model CountryFollower {
  id              Int       @id @default(autoincrement())
  country_id      Int
  user_id         Int
  user_email      String
  user_first_name String
  user_last_name  String
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt
  deleted_at      DateTime?
  country         Country   @relation(fields: [country_id], references: [id])

  @@map("country_follower")
}

model PackagingService {
  id                     Int                  @id @default(autoincrement())
  name                   String
  description            String
  country_id             Int
  created_at             DateTime             @default(now())
  updated_at             DateTime             @updatedAt
  deleted_at             DateTime?
  criterias              Criteria[]
  country                Country              @relation(fields: [country_id], references: [id])
  report_sets            ReportSet[]
  report_set_frequencies ReportSetFrequency[]

  @@map("packaging_service")
}

model ReportSet {
  id                     Int                  @id @default(autoincrement())
  name                   String
  mode                   ReportSetMode
  type                   ReportSetType
  created_at             DateTime             @default(now())
  updated_at             DateTime             @updatedAt
  deleted_at             DateTime?
  packaging_service_id   Int
  sheet_file_id          String?              @unique
  sheet_file_description String?
  packaging_service      PackagingService     @relation(fields: [packaging_service_id], references: [id])
  sheet_file             Files?               @relation(fields: [sheet_file_id], references: [id])
  columns                ReportSetColumn[]
  fractions              ReportSetFraction[]
  price_lists            ReportSetPriceList[]

  @@map("report_set")
}

model ReportSetFrequency {
  id                   Int              @id @default(autoincrement())
  rhythm               ReportSetRhythm
  created_at           DateTime         @default(now())
  updated_at           DateTime         @updatedAt
  deleted_at           DateTime?
  frequency            Json
  packaging_service_id Int
  packaging_service    PackagingService @relation(fields: [packaging_service_id], references: [id])

  @@map("report_set_frequency")
}

model ReportSetFraction {
  id               Int                       @id @default(autoincrement())
  name             String
  description      String
  report_set_id    Int
  parent_id        Int?
  created_at       DateTime                  @default(now())
  updated_at       DateTime                  @updatedAt
  deleted_at       DateTime?
  is_active        Boolean                   @default(true)
  level            Int                       @default(1)
  order            Int                       @default(1)
  code             String                    @unique
  parent_code      String?
  fraction_icon_id Int?
  icon             String                    @default("aluminium")
  has_second_level Boolean                   @default(false)
  has_third_level  Boolean                   @default(false)
  columns          ReportSetColumnFraction[]
  fraction_icon    FractionIcon?             @relation(fields: [fraction_icon_id], references: [id])
  parent           ReportSetFraction?        @relation("ReportSetFractionToReportSetFraction", fields: [parent_code], references: [code])
  children         ReportSetFraction[]       @relation("ReportSetFractionToReportSetFraction")
  report_set       ReportSet                 @relation(fields: [report_set_id], references: [id])
  price_list_items ReportSetPriceListItem[]

  @@map("report_set_fraction")
}

model FractionIcon {
  id                   Int                 @id @default(autoincrement())
  file_id              String              @unique
  created_at           DateTime            @default(now())
  updated_at           DateTime            @updatedAt
  deleted_at           DateTime?
  image_url            String              @default("")
  file                 Files               @relation(fields: [file_id], references: [id])
  report_set_fractions ReportSetFraction[]

  @@map("fraction_icon")
}

model ReportSetColumn {
  id            Int                       @id @default(autoincrement())
  name          String
  description   String
  unit_type     ReportSetColumnUnitType
  report_set_id Int
  parent_id     Int?
  created_at    DateTime                  @default(now())
  updated_at    DateTime                  @updatedAt
  deleted_at    DateTime?
  level         Int                       @default(1)
  order         Int                       @default(1)
  code          String                    @unique
  parent_code   String?
  parent        ReportSetColumn?          @relation("ReportSetColumnParent", fields: [parent_code], references: [code])
  children      ReportSetColumn[]         @relation("ReportSetColumnParent")
  report_set    ReportSet                 @relation(fields: [report_set_id], references: [id])
  fractions     ReportSetColumnFraction[]

  @@map("report_set_column")
}

model ReportSetColumnFraction {
  id                  Int               @id @default(autoincrement())
  created_at          DateTime          @default(now())
  updated_at          DateTime          @updatedAt
  deleted_at          DateTime?
  fraction_code       String
  column_code         String
  report_set_column   ReportSetColumn   @relation(fields: [column_code], references: [code])
  report_set_fraction ReportSetFraction @relation(fields: [fraction_code], references: [code])

  @@map("report_set_column_fraction")
}

model ReportSetPriceList {
  id            Int                      @id @default(autoincrement())
  report_set_id Int
  title         String
  start_date    DateTime
  end_date      DateTime
  created_at    DateTime                 @default(now())
  updated_at    DateTime                 @updatedAt
  deleted_at    DateTime?
  base_price    Int?
  fixed_price   Int?
  minimum_fee   Int?
  type          ReportSetPriceListType
  license_year  Int                      @default(2025)
  report_set    ReportSet                @relation(fields: [report_set_id], references: [id])
  items         ReportSetPriceListItem[]

  @@map("report_set_price_list")
}

model ReportSetPriceListItem {
  id            Int                @id @default(autoincrement())
  price         Int
  created_at    DateTime           @default(now())
  updated_at    DateTime           @updatedAt
  deleted_at    DateTime?
  fraction_code String
  price_list_id Int
  fraction      ReportSetFraction  @relation(fields: [fraction_code], references: [code])
  price_list    ReportSetPriceList @relation(fields: [price_list_id], references: [id])

  @@map("report_set_price_list_item")
}

model RequiredInformation {
  id          Int                     @id @default(autoincrement())
  country_id  Int?
  type        RequiredInformationType
  name        String
  description String
  created_at  DateTime                @default(now())
  updated_at  DateTime                @updatedAt
  deleted_at  DateTime?
  question    String?
  file_id     String?                 @unique
  kind        RequiredInformationKind @default(COUNTRY_INFORMATION)
  criterias   Criteria[]
  country     Country?                @relation(fields: [country_id], references: [id])
  file        Files?                  @relation(fields: [file_id], references: [id])

  @@map("required_information")
}

model OtherCost {
  id         Int       @id @default(autoincrement())
  name       String
  price      Int
  country_id Int
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime?
  country    Country   @relation(fields: [country_id], references: [id])

  @@map("other_cost")
}

model RepresentativeTier {
  id         Int       @id @default(autoincrement())
  name       String
  price      Int
  country_id Int
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime?
  country    Country   @relation(fields: [country_id], references: [id])

  @@map("representative_tier")
}

model PriceList {
  id                    Int                    @id @default(autoincrement())
  type                  PriceListType
  name                  String
  description           String
  start_date            DateTime
  end_date              DateTime
  basic_price           Int?
  minimum_price         Int?
  registration_fee      Int?
  variable_handling_fee Float?
  price                 Int?
  created_at            DateTime               @default(now())
  updated_at            DateTime               @updatedAt
  deleted_at            DateTime?
  condition_type        PriceListConditionType
  condition_type_value  String
  handling_fee          Int?
  thresholds            Json?
  country_price_lists   CountryPriceList[]

  @@map("price_list")
}

model CountryPriceList {
  id            Int       @id @default(autoincrement())
  country_id    Int
  price_list_id Int
  created_at    DateTime  @default(now())
  updated_at    DateTime  @updatedAt
  deleted_at    DateTime?
  country       Country   @relation(fields: [country_id], references: [id])
  price_list    PriceList @relation(fields: [price_list_id], references: [id])

  @@map("country_price_list")
}

model Criteria {
  id                      Int                     @id @default(autoincrement())
  created_at              DateTime                @default(now())
  deleted_at              DateTime?
  help_text               String?
  input_type              CriteriaInputType?
  mode                    CriteriaMode
  title                   String?
  type                    CriteriaType
  updated_at              DateTime                @updatedAt
  country_id              Int
  packaging_service_id    Int?
  required_information_id Int?
  calculator_type         CriteriaCalculatorType?
  country                 Country                 @relation(fields: [country_id], references: [id])
  packaging_service       PackagingService?       @relation(fields: [packaging_service_id], references: [id])
  required_information    RequiredInformation?    @relation(fields: [required_information_id], references: [id])
  options                 CriteriaOption[]

  @@map("criteria")
}

model CriteriaOption {
  id              Int       @id @default(autoincrement())
  criteria_id     Int
  value           String
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt
  deleted_at      DateTime?
  option_value    String
  option_to_value String?
  criteria        Criteria  @relation(fields: [criteria_id], references: [id])

  @@map("criteria_option")
}

model Settings {
  id                        Int       @id @default(autoincrement())
  key                       String    @unique
  value                     Json
  term_or_condition_file_id String?   @unique
  created_at                DateTime  @default(now())
  updated_at                DateTime  @updatedAt
  deleted_at                DateTime?
  file                      Files?    @relation(fields: [term_or_condition_file_id], references: [id])

  @@map("settings")
}

enum BrokerCompanyOrderStatus {
  OPEN
  CANCELLED
}

enum BrokerCompanyOrderType {
  INITIAL_REPORT
  REPORT
}

enum TypeNotifications {
  EMAIL
  SMS
}

enum Services {
  CUSTOMER
  AUTH
  CALCULATOR
  CLERK
  PAYMENT
  SHOP
  SUBSCRIPTION
  FRONT_END
}

enum ReportSetType {
  FRACTIONS
  CATEGORIES

  @@map("report_set_type")
}

enum ReportSetMode {
  ON_PLATAFORM
  BY_EXCEL

  @@map("report_set_mode")
}

enum ReportSetRhythm {
  ANNUALLY
  MONTHLY
  QUARTERLY

  @@map("report_set_rhythm")
}

enum ReportSetColumnUnitType {
  KG
  UNITS
  EACH
}

enum ReportSetPriceListType {
  FIXED_PRICE
  PRICE_PER_CATEGORY
  PRICE_PER_VOLUME_BASE_PRICE
  PRICE_PER_VOLUME_MINIMUM_FEE

  @@map("report_set_price_list_type")
}

enum RequiredInformationKind {
  COUNTRY_INFORMATION
  GENERAL_INFORMATION

  @@map("required_information_kind")
}

enum RequiredInformationType {
  TEXT
  NUMBER
  DOCUMENT
  FILE
  IMAGE

  @@map("required_information_type")
}

enum PriceListType {
  EU_LICENSE
  DIRECT_LICENSE
  ACTION_GUIDE

  @@map("price_list_type")
}

enum PriceListConditionType {
  LICENSE_YEAR
}

enum CriteriaMode {
  COMMITMENT
  CALCULATOR

  @@map("criteria_mode")
}

enum CriteriaType {
  PACKAGING_SERVICE
  REPORT_SET
  REPORT_FREQUENCY
  AUTHORIZE_REPRESENTATIVE
  REPRESENTATIVE_TIER
  OTHER_COST
  PRICE_LIST
  REQUIRED_INFORMATION

  @@map("criteria_type")
}

enum CriteriaInputType {
  RADIO
  SELECT
  YES_NO
  RANGE

  @@map("criteria_input_type")
}

enum CriteriaCalculatorType {
  LICENSE_FEES
  TOTAL_IN_TONS
  TOTAL_IN_KG

  @@map("criteria_calculator_type")
}
