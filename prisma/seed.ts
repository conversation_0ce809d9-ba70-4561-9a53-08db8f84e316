import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  await prisma.country.createMany({
    data: [
      {
        name: "Austria",
        code: "AT",
        flag_url: "https://cdn.kcak11.com/CountryFlags/countries/at.svg",
      },
      {
        name: "Belgium",
        code: "BE",
        flag_url: "https://cdn.kcak11.com/CountryFlags/countries/be.svg",
      },
      {
        name: "Czech Republic",
        code: "CZ",
        flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cz.svg",
      },
      {
        name: "France",
        code: "FR",
        flag_url: "https://cdn.kcak11.com/CountryFlags/countries/fr.svg",
      },
      {
        name: "Germany",
        code: "DE",
        flag_url: "https://cdn.kcak11.com/CountryFlags/countries/de.svg",
      },
      {
        name: "Italy",
        code: "IT",
        flag_url: "https://cdn.kcak11.com/CountryFlags/countries/it.svg",
      },
      {
        name: "Portugal",
        code: "PT",
        flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pt.svg",
      },
      {
        name: "Spain",
        code: "ES",
        flag_url: "https://cdn.kcak11.com/CountryFlags/countries/es.svg",
      },
    ],
    skipDuplicates: true,
  });

  await prisma.priceList.createMany({
    data: [
      {
        type: "EU_LICENSE",
        name: "EU License 2023",
        description: "EU License 2023",
        condition_type: "LICENSE_YEAR",
        condition_type_value: "2023",
        start_date: new Date("2023-01-01"),
        end_date: new Date("2023-12-31"),
        basic_price: null,
        minimum_price: null,
        registration_fee: 14900,
        handling_fee: 29900,
        variable_handling_fee: 10,
      },
      {
        type: "EU_LICENSE",
        name: "EU License 2024",
        description: "EU License 2024",
        condition_type: "LICENSE_YEAR",
        condition_type_value: "2024",
        start_date: new Date("2024-01-01"),
        end_date: new Date("2024-12-31"),
        basic_price: null,
        minimum_price: null,
        registration_fee: 14900,
        handling_fee: 29900,
        variable_handling_fee: 10,
      },
      {
        type: "EU_LICENSE",
        name: "EU License 2025",
        description: "EU License 2025",
        condition_type: "LICENSE_YEAR",
        condition_type_value: "2025",
        start_date: new Date("2025-01-01"),
        end_date: new Date("2025-12-31"),
        basic_price: null,
        minimum_price: null,
        registration_fee: 14900,
        handling_fee: 29900,
        variable_handling_fee: 10,
      },
      {
        type: "ACTION_GUIDE",
        name: "Action Guide",
        description: "Action Guide",
        condition_type: "LICENSE_YEAR",
        condition_type_value: "2025",
        start_date: new Date("2025-01-01"),
        end_date: new Date("2025-12-31"),
        basic_price: 14900,
        minimum_price: null,
        registration_fee: null,
        handling_fee: null,
        variable_handling_fee: null,
      },
    ],
  });

  console.log("Database seeded successfully");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
