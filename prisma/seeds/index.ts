import { ConsentType, PrismaClient, ReasonType } from "@prisma/client";

const prisma = new PrismaClient();

const CONSENTS = [
  {
    name: "General terms and conditions",
    description: 'I have read and accepted the <a href="#">General terms and conditions</a>',
    type: ConsentType.PURCHASE,
    version: 1,
  },
  {
    name: "Data protection regulations",
    description: 'I here by accepted the <a href="#">data protection regulations</a>',
    type: ConsentType.PURCHASE,
    version: 1,
  },
  {
    name: "Contract concerning",
    description:
      'I have read the terms and conditions of the <a href="#">Contract concerning participation in the dual system Interseroh</a> and hereby declare my explicit consent.',
    type: ConsentType.PURCHASE,
    version: 1,
  },
  {
    name: "Data protection*",
    description: 'I here by accept the <a href="#">data protection regulations</a>',
    type: ConsentType.ACCOUNT,
    version: 1,
  },
  {
    name: "Result by mail – we need you consent to send you e-mails*",
    description:
      'Yes, I would like to subscribe to the Lizenzero Newsletter with important information about packaging regulations and promotions and would like to receive tailored information on the basis of my personalized user profile and the possibility to submit purchase reviews. If you do not wish to receive (further) advertising, please inform us of your wish, for example by telephone, by e-<NAME_EMAIL>, via menu item “My Account” in your customer account or click on the "Unsubscribe" link at the end of our e-mail.',
    type: ConsentType.ACCOUNT,
    version: 1,
  },
];

const REASONS = [
  // License information
  {
    type: ReasonType.LICENSE_INFORMATION,
    title: "Signature doesn't match with ID",
    value: "SIGNATURE_DOES_NOT_MATCH_WITH_ID",
  },
  { type: ReasonType.LICENSE_INFORMATION, title: "Illegible signature", value: "ILLEGIBLE_SIGNATURE" },
  { type: ReasonType.LICENSE_INFORMATION, title: "Missing signature", value: "MISSING_SIGNATURE" },
  {
    type: ReasonType.LICENSE_INFORMATION,
    title: "Document doesn't match with template",
    value: "DOCUMENT_DOES_NOT_MATCH_WITH_TEMPLATE",
  },
  { type: ReasonType.LICENSE_INFORMATION, title: "Others", value: "OTHERS" },
  // Volume report
  {
    type: ReasonType.LICENSE_VOLUME_REPORT,
    title: "Review overall report",
    value: "REVIEW_OVERALL_REPORT",
  },
  { type: ReasonType.LICENSE_VOLUME_REPORT, title: "Wrong punctuation", value: "WRONG_PUNCTUATION" },
  { type: ReasonType.LICENSE_VOLUME_REPORT, title: "Reported amount too large", value: "REPORT_AMOUNT_TOO_LARGE" },
  {
    type: ReasonType.LICENSE_VOLUME_REPORT,
    title: "Reported amount too small or zero",
    value: "REPORT_AMOUNT_TOO_SMALL_OR_ZERO",
  },
  { type: ReasonType.LICENSE_VOLUME_REPORT, title: "Others", value: "OTHERS" },
  // Termination
  {
    type: ReasonType.TERMINATION,
    title: "My company no longer exists",
    value: "MY_COMPANY_NO_LONGER_EXISTS",
  },
  {
    type: ReasonType.TERMINATION,
    title: "Too expensive",
    value: "TOO_EXPENSIVE",
  },
  {
    type: ReasonType.TERMINATION,
    title: "It is not for me",
    value: "IT_IS_NOT_FOR_ME",
  },
  {
    type: ReasonType.TERMINATION,
    title: "My sales are too small",
    value: "MY_SALES_ARE_TOO_SMALL",
  },
  {
    type: ReasonType.TERMINATION,
    title: "I closed/changed my business",
    value: "I_CLOSED_CHANGED_MY_BUSINESS",
  },
  {
    type: ReasonType.TERMINATION,
    title: "I hired a competitor",
    value: "I_HIRED_A_COMPETITOR",
  },
  {
    type: ReasonType.TERMINATION,
    title: "It is too complicated",
    value: "IT_IS_TOO_COMPLICATED",
  },
  {
    type: ReasonType.TERMINATION,
    title: "I'm not selling in Europe anymore",
    value: "I_AM_NOT_SELLING_IN_EUROPE_ANYMORE",
  },
  {
    type: ReasonType.TERMINATION,
    title: "Extraordinary Termination",
    value: "EXTRAORDINARY_TERMINATION",
  },
  {
    type: ReasonType.TERMINATION,
    title: "Other",
    value: "OTHER",
  },
] as const;

async function seed() {
  try {
    console.info(`* Seeding the database`);

    await prisma.$transaction(async (tx) => {
      await tx.consent.createMany({ data: CONSENTS });

      await tx.reason.createMany({
        data: REASONS.map((reason) => ({
          title: reason.title,
          value: reason.value,
          type: reason.type,
        })),
      });
    });

    console.info(`* Database seeded.`);
  } catch (error) {
    console.error("Error seeding the database:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

seed();
