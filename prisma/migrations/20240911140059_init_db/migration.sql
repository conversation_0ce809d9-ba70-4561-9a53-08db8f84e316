-- Create<PERSON><PERSON>
CREATE TYPE "TypeNotifications" AS ENUM ('EMAIL', 'SMS');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "Services" AS ENUM ('CUSTOMER', 'AUTH', 'CALCULATOR', 'CLERK', 'PAYMENT', 'SHOP', 'SUBSCRIPTION', 'FRONT_END');

-- CreateTable
CREATE TABLE "log" (
    "id" SERIAL NOT NULL,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "audit_log" (
    "id" SERIAL NOT NULL,
    "action" TEXT NOT NULL,
    "entity_name" TEXT NOT NULL,
    "entity_id" INTEGER NOT NULL,
    "changes" TEXT NOT NULL,
    "admin_user_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audit_log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "system_log" (
    "id" SERIAL NOT NULL,
    "log_level" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "trace" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "system_log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "country" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "country_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "error_log" (
    "id" SERIAL NOT NULL,
    "error_code" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "occurrences" INTEGER NOT NULL,
    "last_occurred_at" TIMESTAMP(3) NOT NULL,
    "resolved" BOOLEAN NOT NULL,
    "resolution_notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "error_log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "scheduled_task" (
    "id" SERIAL NOT NULL,
    "task_name" TEXT NOT NULL,
    "schedule" TEXT NOT NULL,
    "last_run_at" TIMESTAMP(3),
    "last_run_status" TEXT,
    "next_run_at" TIMESTAMP(3),

    CONSTRAINT "scheduled_task_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "task_log" (
    "id" SERIAL NOT NULL,
    "scheduled_task_id" INTEGER NOT NULL,
    "run_at" TIMESTAMP(3) NOT NULL,
    "status" TEXT NOT NULL,
    "output" TEXT NOT NULL,

    CONSTRAINT "task_log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bulk_upload_request" (
    "id" SERIAL NOT NULL,
    "admin_user_id" INTEGER NOT NULL,
    "upload_type" TEXT NOT NULL,
    "file_path" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "total_records" INTEGER NOT NULL,
    "processed_records" INTEGER NOT NULL,
    "success_records" INTEGER NOT NULL,
    "failed_records" INTEGER NOT NULL,
    "start_time" TIMESTAMP(3) NOT NULL,
    "end_time" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "bulk_upload_request_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bulk_upload_error" (
    "id" SERIAL NOT NULL,
    "bulk_upload_request_id" INTEGER NOT NULL,
    "record_number" INTEGER NOT NULL,
    "error_description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "bulk_upload_error_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bulk_file_upload" (
    "id" SERIAL NOT NULL,
    "admin_user_id" INTEGER NOT NULL,
    "description" TEXT NOT NULL,
    "file_paths" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "total_files" INTEGER NOT NULL,
    "processed_files" INTEGER NOT NULL,
    "success_files" INTEGER NOT NULL,
    "failed_files" INTEGER NOT NULL,
    "start_time" TIMESTAMP(3) NOT NULL,
    "end_time" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "bulk_file_upload_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "files" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "extension" TEXT NOT NULL,
    "size" TEXT NOT NULL,
    "creator_type" TEXT NOT NULL,
    "document_type" TEXT NOT NULL,
    "country_id" TEXT,
    "user_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notifications" (
    "id" TEXT NOT NULL,
    "user_id" INTEGER NOT NULL,
    "service" "Services" NOT NULL DEFAULT 'CUSTOMER',
    "subject" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "type" "TypeNotifications" NOT NULL DEFAULT 'EMAIL',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "criteria" (
    "id" SERIAL NOT NULL,
    "criteria_type" TEXT NOT NULL,
    "service_type" TEXT NOT NULL,
    "packaging_id" INTEGER NOT NULL,
    "control" JSONB NOT NULL,

    CONSTRAINT "criteria_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "packaging" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "countryId" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "packaging_pkey" PRIMARY KEY ("id")
);
