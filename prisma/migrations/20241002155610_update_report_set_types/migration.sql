/*
  Warnings:

  - The values [ANNUAL] on the enum `report_set_rhythm` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "report_set_rhythm_new" AS ENUM ('ANNUALLY', 'MONTHLY', 'QUARTERLY');
ALTER TABLE "report_set_frequency" ALTER COLUMN "rhythm" TYPE "report_set_rhythm_new" USING ("rhythm"::text::"report_set_rhythm_new");
ALTER TYPE "report_set_rhythm" RENAME TO "report_set_rhythm_old";
ALTER TYPE "report_set_rhythm_new" RENAME TO "report_set_rhythm";
DROP TYPE "report_set_rhythm_old";
COMMIT;
