/*
  Warnings:

  - Added the required column `icon` to the `report_set_fraction` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "report_set_fraction" ADD COLUMN     "icon" TEXT NOT NULL,
ADD COLUMN     "is_active" BOOLEAN NOT NULL DEFAULT true;

-- CreateTable
CREATE TABLE "packaging" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "countryId" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "packaging_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "report_set_fraction_column_fraction" (
    "id" SERIAL NOT NULL,
    "report_set_fraction_column_id" INTEGER NOT NULL,
    "report_set_fraction_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "report_set_fraction_column_fraction_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "report_set_fraction_column_fraction" ADD CONSTRAINT "report_set_fraction_column_fraction_report_set_fraction_co_fkey" FOREIGN KEY ("report_set_fraction_column_id") REFERENCES "report_set_fraction_column"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_fraction_column_fraction" ADD CONSTRAINT "report_set_fraction_column_fraction_report_set_fraction_id_fkey" FOREIGN KEY ("report_set_fraction_id") REFERENCES "report_set_fraction"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
