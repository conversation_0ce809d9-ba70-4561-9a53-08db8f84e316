import { Test, TestingModule } from "@nestjs/testing";
import { CompanyEmailService } from "./company-email.service";
import { DatabaseService } from "@/database/database.service";
import { CreateCompanyEmailDto } from "./dto/create-company-email.dto";
import { UpdateCompanyEmailDto } from "./dto/update-company-email.dto";

describe("CompanyEmailService", () => {
  let service: CompanyEmailService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    companyEmail: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CompanyEmailService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<CompanyEmailService>(CompanyEmailService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("Should create a new company email", async () => {
      const createDto: CreateCompanyEmailDto = {
        company_email: "<EMAIL>",
        company_id: 1,
      };

      const expectedResult = {
        id: 1,
        email: "<EMAIL>",
        company_id: 1,
        created_at: expect.any(Date),
        updated_at: expect.any(Date),
      };

      mockDatabaseService.companyEmail.create.mockResolvedValue(expectedResult);

      const result = await service.create(createDto);

      expect(result).toEqual(expectedResult);
      expect(mockDatabaseService.companyEmail.create).toHaveBeenCalledWith({
        data: {
          email: createDto.company_email,
          company_id: createDto.company_id,
          created_at: expect.any(Date),
          updated_at: expect.any(Date),
        },
      });
    });
  });

  describe("findAll", () => {
    it("Should return all undeleted emails", async () => {
      const expectedEmails = [
        { id: 1, email: "<EMAIL>", company_id: 1 },
        { id: 2, email: "<EMAIL>", company_id: 1 },
      ];

      mockDatabaseService.companyEmail.findMany.mockResolvedValue(expectedEmails);

      const result = await service.findAll();

      expect(result).toEqual(expectedEmails);
      expect(mockDatabaseService.companyEmail.findMany).toHaveBeenCalledWith({
        where: {
          deleted_at: null,
        },
      });
    });
  });

  describe("findOne", () => {
    it("Should return a specific email", async () => {
      const expectedEmail = {
        id: 1,
        email: "<EMAIL>",
        company_id: 1,
        deleted_at: null,
      };

      mockDatabaseService.companyEmail.findUnique.mockResolvedValue(expectedEmail);

      const result = await service.findOne(1);

      expect(result).toEqual(expectedEmail);
      expect(mockDatabaseService.companyEmail.findUnique).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
      });
    });

    it("Should return null when email is not found", async () => {
      mockDatabaseService.companyEmail.findUnique.mockResolvedValue(null);

      const result = await service.findOne(999);

      expect(result).toBeNull();
    });
  });

  describe("update", () => {
    it("Should update a company email", async () => {
      const updateDto: UpdateCompanyEmailDto = {
        company_email: "<EMAIL>",
      };

      const expectedResult = {
        id: 1,
        email: "<EMAIL>",
        company_id: 1,
        updated_at: expect.any(Date),
      };

      mockDatabaseService.companyEmail.update.mockResolvedValue(expectedResult);

      const result = await service.update(1, updateDto);

      expect(result).toEqual(expectedResult);
      expect(mockDatabaseService.companyEmail.update).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
        data: {
          ...updateDto,
          updated_at: expect.any(Date),
        },
      });
    });
  });

  describe("remove", () => {
    it("Should mark an email as deleted", async () => {
      const expectedResult = {
        id: 1,
        email: "<EMAIL>",
        deleted_at: expect.any(Date),
      };

      mockDatabaseService.companyEmail.update.mockResolvedValue(expectedResult);

      const result = await service.remove(1);

      expect(result).toEqual(expectedResult);
      expect(mockDatabaseService.companyEmail.update).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
        data: {
          deleted_at: expect.any(Date),
        },
      });
    });
  });
});
