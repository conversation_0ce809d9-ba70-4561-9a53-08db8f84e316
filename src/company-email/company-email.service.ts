import { DatabaseService } from "@/database/database.service";
import { Injectable } from "@nestjs/common";
import { CreateCompanyEmailDto } from "./dto/create-company-email.dto";
import { UpdateCompanyEmailDto } from "./dto/update-company-email.dto";

@Injectable()
export class CompanyEmailService {
  constructor(private databaseService: DatabaseService) {}

  create(data: CreateCompanyEmailDto) {
    return this.databaseService.companyEmail.create({
      data: {
        email: data.company_email,
        company_id: data.company_id,
        created_at: new Date(),
        updated_at: new Date(),
      },
    });
  }

  findAll() {
    return this.databaseService.companyEmail.findMany({
      where: {
        deleted_at: null,
      },
    });
  }

  findOne(id: number) {
    return this.databaseService.companyEmail.findUnique({
      where: { id, deleted_at: null },
    });
  }

  update(id: number, data: UpdateCompanyEmailDto) {
    return this.databaseService.companyEmail.update({
      where: { id, deleted_at: null },
      data: {
        ...data,
        updated_at: new Date(),
      },
    });
  }

  remove(id: number) {
    return this.databaseService.companyEmail.update({
      where: { id, deleted_at: null },
      data: {
        deleted_at: new Date(),
      },
    });
  }
}
