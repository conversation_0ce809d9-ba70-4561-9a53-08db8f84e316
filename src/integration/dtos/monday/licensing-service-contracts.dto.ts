import { ApiProperty } from "@nestjs/swagger";

export class LicensingServiceContractsDto {
  @ApiProperty()
  companyName: string;

  @ApiProperty()
  country?: string;

  @ApiProperty()
  purchaseStatus?: string;

  @ApiProperty()
  registrationFee?: number;

  @ApiProperty()
  handlingFee?: number;

  @ApiProperty()
  volumeDependent?: number;

  @ApiProperty()
  serviceType?: string;

  @ApiProperty()
  startingYear?: string;

  @ApiProperty()
  endingYear?: string;

  @ApiProperty()
  terminationDate?: string;

  @ApiProperty()
  renewalStatus?: string;

  @ApiProperty()
  customerId?: string;
}
