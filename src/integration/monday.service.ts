import { CreateCustomerDto } from "@/customer/dto/create-customer.dto";

import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { LicenseThirdPartyInvoiceStatus, TerminationStatus } from "@prisma/client";
import axios from "axios";
import { DirectLicenseContractsDto } from "./dtos/monday/direct-license-contracts.dto";
import { IntegrationType, LicenseServiceContract, WebhookResponse } from "./interfaces/monday";
import { DatabaseService } from "@/database/database.service";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ROLE } from "@/shared/auth/const";
import { Role } from "@/shared/auth/role.enum";

@Injectable()
export class MondayService {
  constructor(private databaseService: DatabaseService) {}
  async findOneCustomerMondayById(id: number) {
    Logger.log("MondayService / findOneCustomerMondayById");
    try {
      const baseUrl = process.env.CRM_API_URL;

      const crmUrl = `${baseUrl}/customers/${id}`;
      await axios.get(crmUrl, {
        headers: {
          [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
          [HEADER_USER_ROLE]: Role.SYSTEM,
        },
      });
    } catch (error) {
      console.error("CompanyService/findOneCustomerMondayById - ", error);
    }
  }

  async updateBoardAccountsMonday(data: any, customerId: number) {
    Logger.log("MondayService / updateBoardAccountsMonday");
    try {
      const baseUrl = process.env.CRM_API_URL;

      const crmUrl = `${baseUrl}/accounts`;

      const companyCrm = {
        customerId: String(customerId),
        vatId: data.vat,
        taxId: data.tin,
        city: data?.address?.city,
        postCode: data?.address?.zip_code,
        country: data?.address?.country_code,
        street: data?.address?.street_and_number,
        companyName: data.name,
      };

      await axios.put(crmUrl, companyCrm, {
        headers: {
          [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
          [HEADER_USER_ROLE]: Role.SYSTEM,
        },
      });
    } catch (error) {
      console.error("CompanyService/updateBoardAccountsMonday - ", error.message);
    }
  }

  async updateCustomerEmailMonday(data: { email: string; customerId: number }) {
    Logger.log("MondayService / updateCustomerEmailMonday");
    try {
      const baseUrl = process.env.CRM_API_URL;

      const crmUrl = `${baseUrl}/accounts`;

      const companyCrm = {
        customerId: String(data.customerId),
        email: data.email,
      };

      await axios.patch(crmUrl, companyCrm, {
        headers: {
          [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
          [HEADER_USER_ROLE]: Role.SYSTEM,
        },
      });
    } catch (error) {
      console.error("CompanyService/updateCustomerEmailMonday - ", error.message);
    }
  }

  async sendDataBoardCustomer(data: CreateCustomerDto, customerId: number) {
    Logger.log("MondayService / sendDataBoardCustomer");
    try {
      const baseUrl = process.env.CRM_API_URL;

      const crmUrl = `${baseUrl}/accounts`;

      const customerName = `${data.first_name} ${data.last_name}`;

      const customerCrm = {
        customerId: String(customerId),
        email: data.email,
        customerName,
        itemName: customerName,
      };

      await axios.post(crmUrl, customerCrm, {
        headers: {
          [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
          [HEADER_USER_ROLE]: Role.SYSTEM,
        },
      });
    } catch (error) {
      console.error("CustomerService/sendDataBoardCustomer - ", error.message);
    }
  }

  async createLicenseServiceContract(dto: LicenseServiceContract, customerId: number) {
    Logger.log("MondayService / createLicenseServiceContract");
    const variableHandlingFee = dto.volumeDependent * 100;

    try {
      const response = await axios.post(
        `${process.env.CRM_API_URL}/licensing-service-contracts`,
        {
          ...dto,
          volumeDependent: variableHandlingFee,
          serviceType: dto.serviceType,
          purchaseStatus: "Deal in progress",
          handlingFee: dto?.handlingFee ? dto.handlingFee / 100 : 0,
          registrationFee: dto?.registrationFee ? dto.registrationFee / 100 : 0,
          customerId: String(customerId),
        },
        {
          headers: {
            [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
            [HEADER_USER_ROLE]: Role.SYSTEM,
          },
        }
      );

      const { data } = response.data;
      const { change_multiple_column_values, create_item } = data;
      const { id } = change_multiple_column_values ? change_multiple_column_values : create_item;

      return id;
    } catch (error) {
      console.error("MondayService/createLicenseServiceContract - ", error.message);
    }
  }

  async createOpportunity(customerId: string, customerName: string, licenseServiceContractIds: string[]) {
    try {
      await axios.post(
        `${process.env.CRM_API_URL}/opportunities`,
        {
          customerId: customerId,
          customerName: customerName,
          licenseServiceContractIds: licenseServiceContractIds,
        },
        {
          headers: {
            [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
            [HEADER_USER_ROLE]: Role.SYSTEM,
          },
        }
      );
    } catch (err) {
      console.error("MondayService/createOpportunity - ", err.message);
    }
  }

  async createDirectLicensingContract({
    activeYear,
    contractVolumeValue,
    customerId,
    endingYear,
    startingYear,
    terminationDate,
    country,
    status,
  }: DirectLicenseContractsDto) {
    Logger.log("MondayService / createDirectLicensingContract");
    try {
      const response = await axios.post(
        `${process.env.CRM_API_URL}/direct-licenses`,
        {
          customerId: String(customerId),
          serviceType: "2",
          activeYear,
          contractVolumeValue,
          endingYear,
          country,
          startingYear,
          status,
          terminationDate,
        },
        {
          headers: {
            [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
            [HEADER_USER_ROLE]: Role.SYSTEM,
          },
        }
      );

      const { data } = response.data;
      const { change_multiple_column_values, create_item } = data;
      const { id } = change_multiple_column_values ? change_multiple_column_values : create_item;

      return id;
    } catch (error) {
      console.error("MondayService/createDirectLicensingContract - ", error.message);
    }
  }

  async createLicenseObligation({
    country,
    email,
    customerId,
    licenseType,
  }: {
    country: string;
    email: string;
    customerId: string;
    licenseType: string[];
  }) {
    Logger.log("MondayService / createLicenseObligation");
    try {
      await axios.post(
        `${process.env.CRM_API_URL}/license-obligations`,
        {
          country: country,
          email,
          packagingObligations: licenseType,
          customerId: String(customerId),
        },
        {
          headers: {
            [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
            [HEADER_USER_ROLE]: Role.SYSTEM,
          },
        }
      );
    } catch (error) {
      console.error("MondayService/createLicenseObligation - ", error.message);
    }
  }

  async removeItem(customerId: number, countryName: string) {
    Logger.log("MondayService / removeItem");

    await axios.delete(`${process.env.CRM_API_URL}/licensing-service-contracts`, {
      data: {
        customerId,
        countryName,
      },
      headers: {
        [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      },
    });

    await axios.delete(`${process.env.CRM_API_URL}/license-obligations`, {
      data: {
        customerId,
        countryName,
      },
      headers: {
        [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      },
    });
  }

  async handleWebhook(integrationType: IntegrationType, data: WebhookResponse) {
    Logger.log("MondayService / handleWebhook - ", integrationType);
    const newValue = data.event.value.label.text;
    const itemId = data.event.pulseId;

    switch (integrationType) {
      case IntegrationType.VOLUME_REPORTS:
        const volumeReport = await this.databaseService.licenseVolumeReport.findFirst({
          where: {
            volume_report_monday_ref: itemId,
          },
        });

        if (!volumeReport) {
          throw new NotFoundException("Volume Report Not Found!");
        }

        await this.databaseService.licenseVolumeReport.update({
          data: {
            stage: newValue,
          },
          where: {
            id: volumeReport.id,
          },
        });

        break;

      case IntegrationType.REGISTRATION_AND_TERMINATIONS:
        const license = await this.databaseService.license.findFirst({
          where: {
            registration_and_termination_monday_ref: itemId,
          },
        });

        if (!license) {
          throw new NotFoundException("License Not Found!");
        }

        const newValueStatus =
          newValue === "Open"
            ? TerminationStatus.REQUESTED
            : newValue === "Pending"
            ? TerminationStatus.PENDING
            : TerminationStatus.COMPLETED;

        await this.databaseService.termination.update({
          data: {
            status: newValueStatus,
          },
          where: {
            id: license.id,
          },
        });

        break;

      case IntegrationType.THIRD_PARTY_INVOICES:
        const thirdPartyInvoice = await this.databaseService.licenseThirdPartyInvoice.findFirst({
          where: {
            third_party_invoice_monday_ref: itemId,
          },
        });

        if (!thirdPartyInvoice) {
          throw new NotFoundException("Third Party Invoice Not Found!");
        }

        await this.databaseService.licenseThirdPartyInvoice.update({
          data: {
            status: newValue.trim().replace(/\s+/g, "_").toUpperCase() as LicenseThirdPartyInvoiceStatus,
          },
          where: {
            id: thirdPartyInvoice.id,
          },
        });

        break;

      default:
        break;
    }
  }
}
