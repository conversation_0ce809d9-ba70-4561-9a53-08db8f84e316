import { ApiProperty } from "@nestjs/swagger";

export class CartTypeRevenueOutputDto {
  @ApiProperty()
  total: number;
  @ApiProperty()
  growth: number;
}

class CartPerCustomerOutputDto {
  @ApiProperty({ type: CartTypeRevenueOutputDto })
  direct_licensing: CartTypeRevenueOutputDto;

  @ApiProperty({ type: CartTypeRevenueOutputDto })
  action_guide: CartTypeRevenueOutputDto;

  @ApiProperty({ type: CartTypeRevenueOutputDto })
  other_services: CartTypeRevenueOutputDto;

  @ApiProperty({ type: CartTypeRevenueOutputDto })
  eu_licensing: CartTypeRevenueOutputDto;

  @ApiProperty({ type: CartTypeRevenueOutputDto })
  overall: CartTypeRevenueOutputDto;
}

export class AverageRevenueOutputDto {
  @ApiProperty()
  cart_overall: number;
  @ApiProperty({ type: CartPerCustomerOutputDto })
  cart_per_customer: CartPerCustomerOutputDto;
}
