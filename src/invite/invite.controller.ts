import { Controller, Get, Post, Body, Param } from "@nestjs/common";
import { InviteService } from "./invite.service";
import { CreateInviteDto } from "./dto/create-invite.dto";

@Controller("invite")
export class InviteController {
  constructor(private readonly inviteService: InviteService) {}

  @Get(":id")
  findAll(@Param("id") id: number) {
    return this.inviteService.findByCustomerId(id);
  }

  @Post()
  create(@Body() createInviteDto: CreateInviteDto) {
    return this.inviteService.create(createInviteDto);
  }
}
