import { Test, TestingModule } from "@nestjs/testing";
import { InviteService } from "./invite.service";
import { DatabaseService } from "@/database/database.service";
import { CreateInviteDto } from "./dto/create-invite.dto";

describe("InviteService", () => {
  let service: InviteService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    customerInvitation: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InviteService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<InviteService>(InviteService);
    databaseService = module.get<DatabaseService>(DatabaseService);

    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create", () => {
    it("should create a new invitation", async () => {
      const createInviteDto: CreateInviteDto = {
        product: "Premium Subscription",
        commission: 25.5,
        orderNumber: "ORD-12345",
        leadSource: "referral",
        customerId: 123,
        invitedCustomerId: 456,
      };

      const expectedCreatedInvite = {
        id: 1,
        comission: 25.5,
        comission_date: expect.any(Date),
        product: "Premium Subscription",
        order_number: "ORD-12345",
        customer_id: 123,
        lead_source: "referral",
        invited_customer_id: 456,
      };

      mockDatabaseService.customerInvitation.create.mockResolvedValue(expectedCreatedInvite);

      const result = await service.create(createInviteDto);

      expect(result).toEqual(expectedCreatedInvite);
      expect(mockDatabaseService.customerInvitation.create).toHaveBeenCalledWith({
        data: {
          comission: createInviteDto.commission,
          comission_date: expect.any(Date),
          product: createInviteDto.product,
          order_number: createInviteDto.orderNumber,
          customer_id: createInviteDto.customerId,
          lead_source: createInviteDto.leadSource,
          invited_customer_id: createInviteDto.invitedCustomerId,
        },
      });
    });

    it("should handle null invitedCustomerId", async () => {
      const createInviteDto: CreateInviteDto = {
        product: "Basic Plan",
        commission: 10.0,
        orderNumber: "ORD-67890",
        leadSource: "website",
        customerId: 789,
        invitedCustomerId: null,
      };

      const expectedCreatedInvite = {
        id: 2,
        comission: 10.0,
        comission_date: expect.any(Date),
        product: "Basic Plan",
        order_number: "ORD-67890",
        customer_id: 789,
        lead_source: "website",
        invited_customer_id: null,
      };

      mockDatabaseService.customerInvitation.create.mockResolvedValue(expectedCreatedInvite);

      const result = await service.create(createInviteDto);

      expect(result).toEqual(expectedCreatedInvite);
      expect(mockDatabaseService.customerInvitation.create).toHaveBeenCalledWith({
        data: {
          comission: createInviteDto.commission,
          comission_date: expect.any(Date),
          product: createInviteDto.product,
          order_number: createInviteDto.orderNumber,
          customer_id: createInviteDto.customerId,
          lead_source: createInviteDto.leadSource,
          invited_customer_id: null,
        },
      });
    });

    it("should handle database errors during creation", async () => {
      const createInviteDto: CreateInviteDto = {
        product: "Premium Subscription",
        commission: 25.5,
        orderNumber: "ORD-12345",
        leadSource: "referral",
        customerId: 123,
        invitedCustomerId: 456,
      };

      const databaseError = new Error("Database connection error");
      mockDatabaseService.customerInvitation.create.mockRejectedValue(databaseError);

      await expect(service.create(createInviteDto)).rejects.toThrow(databaseError);
    });
  });

  describe("findByCustomerId", () => {
    it("should return invitations for a specific customer", async () => {
      const customerId = 123;
      const expectedInvitations = [
        {
          id: 1,
          comission: 25.5,
          comission_date: new Date(),
          product: "Premium Subscription",
          order_number: "ORD-12345",
          customer_id: 123,
          lead_source: "referral",
          invited_customer_id: 456,
        },
        {
          id: 2,
          comission: 15.0,
          comission_date: new Date(),
          product: "Basic Plan",
          order_number: "ORD-67890",
          customer_id: 123,
          lead_source: "website",
          invited_customer_id: 789,
        },
      ];

      mockDatabaseService.customerInvitation.findMany.mockResolvedValue(expectedInvitations);

      const result = await service.findByCustomerId(customerId);

      expect(result).toEqual(expectedInvitations);
      expect(mockDatabaseService.customerInvitation.findMany).toHaveBeenCalledWith({
        where: {
          customer_id: customerId,
        },
      });
    });

    it("should handle string input for customerId by converting to number", async () => {
      const customerIdString = "123";
      const customerIdNumber = 123;
      const expectedInvitations = [
        {
          id: 1,
          comission: 25.5,
          comission_date: new Date(),
          product: "Premium Subscription",
          order_number: "ORD-12345",
          customer_id: 123,
          lead_source: "referral",
          invited_customer_id: 456,
        },
      ];

      mockDatabaseService.customerInvitation.findMany.mockResolvedValue(expectedInvitations);

      const result = await service.findByCustomerId(customerIdString as unknown as number);

      expect(result).toEqual(expectedInvitations);
      expect(mockDatabaseService.customerInvitation.findMany).toHaveBeenCalledWith({
        where: {
          customer_id: customerIdNumber,
        },
      });
    });

    it("should return empty array when no invitations are found", async () => {
      const customerId = 999;
      mockDatabaseService.customerInvitation.findMany.mockResolvedValue([]);

      const result = await service.findByCustomerId(customerId);

      expect(result).toEqual([]);
      expect(mockDatabaseService.customerInvitation.findMany).toHaveBeenCalledWith({
        where: {
          customer_id: customerId,
        },
      });
    });

    it("should handle database errors during query", async () => {
      const customerId = 123;
      const databaseError = new Error("Database query error");
      mockDatabaseService.customerInvitation.findMany.mockRejectedValue(databaseError);

      await expect(service.findByCustomerId(customerId)).rejects.toThrow(databaseError);
    });
  });
});
