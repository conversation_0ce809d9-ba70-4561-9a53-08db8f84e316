import { Module } from "@nestjs/common";
import { LicenseVolumeReportItemService } from "./license-volume-report-item.service";
import { LicenseVolumeReportItemController } from "./license-volume-report-item.controller";
import { CustomerIoModule } from "@/customer-io/customer-io.module";
import { HttpApiModule } from "@/http/http.module";

@Module({
  imports: [CustomerIoModule, HttpApiModule],
  controllers: [LicenseVolumeReportItemController],
  providers: [LicenseVolumeReportItemService],
  exports: [LicenseVolumeReportItemService],
})
export class LicenseVolumeReportItemModule {}
