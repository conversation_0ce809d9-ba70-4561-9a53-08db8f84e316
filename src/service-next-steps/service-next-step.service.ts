import { DatabaseService } from "@/database/database.service";
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { CreateServiceNextStepDto } from "./dto/create-service-next-step.dto";
import { UpdateServiceNextStepDto } from "./dto/update-service-next-step.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

@Injectable()
export class ServiceNextStepService {
  constructor(private databaseService: DatabaseService) {}

  async findAll({ license_id, action_guide_id }: { license_id?: number; action_guide_id?: number }) {
    return await this.databaseService.serviceNextStep.findMany({
      where: {
        deleted_at: null,
        ...(license_id && { license_id: license_id }),
        ...(action_guide_id && { action_guide_id: action_guide_id }),
      },
      include: {
        license: true,
      },
    });
  }

  async findOne(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionServiceNextSteps(id, user);

    const serviceNextStep = await this.databaseService.serviceNextStep.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        license: {
          include: {
            contract: {
              include: {
                customer: true,
              },
            },
          },
        },
        action_guide: {
          include: {
            contract: {
              include: {
                customer: true,
              },
            },
          },
        },
      },
    });

    if (serviceNextStep.license) {
      if (user.role === Role.CUSTOMER && serviceNextStep.license.contract.customer.user_id !== +user.id) {
        throw new ForbiddenException("You are not allowed to access this service next step");
      }
    }

    if (serviceNextStep.action_guide) {
      if (user.role === Role.CUSTOMER && serviceNextStep.action_guide.contract.customer.user_id !== +user.id) {
        throw new ForbiddenException("You are not allowed to access this service next step");
      }
    }

    return serviceNextStep;
  }

  async create(data: CreateServiceNextStepDto) {
    if (!data.license_id && !data.action_guide_id) {
      throw new BadRequestException("License or Action Guide ID is required");
    }

    return await this.databaseService.serviceNextStep.create({
      data: {
        license_id: data.license_id || null,
        action_guide_id: data.action_guide_id || null,
        title: data.title,
        available_date: data.available_date,
        deadline_date: data.deadline_date,
        done_at: data.done_at,
        created_at: new Date(),
        updated_at: new Date(),
      },
    });
  }

  async update(id: number, data: UpdateServiceNextStepDto, user: AuthenticatedUser) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("Service Next Step ID is invalid");
    }

    const serviceNextStep = await this.databaseService.serviceNextStep.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        license: {
          include: {
            contract: {
              include: {
                customer: true,
              },
            },
          },
        },
        action_guide: {
          include: {
            contract: {
              include: {
                customer: true,
              },
            },
          },
        },
      },
    });

    if (!serviceNextStep) {
      throw new NotFoundException("Service Next Step not found");
    }

    if (serviceNextStep.license) {
      if (user.role === Role.CUSTOMER && serviceNextStep.license.contract.customer.user_id !== +user.id) {
        throw new ForbiddenException("You are not allowed to access this service next step");
      }
    }

    if (serviceNextStep.action_guide) {
      if (user.role === Role.CUSTOMER && serviceNextStep.action_guide.contract.customer.user_id !== +user.id) {
        throw new ForbiddenException("You are not allowed to access this service next step");
      }
    }

    const updatedServiceNextStep = await this.databaseService.serviceNextStep.update({
      where: { id: Number(id) },
      data: {
        title: data.title,
        available_date: data.available_date,
        deadline_date: data.deadline_date,
        done_at: data.done_at,
        updated_at: new Date(),
      },
    });

    return updatedServiceNextStep;
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionServiceNextSteps(id, user);

    return await this.databaseService.serviceNextStep.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }

  async validatingUserPermissionServiceNextSteps(id: number, user: AuthenticatedUser) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("Service Next Step ID is invalid");
    }

    const serviceNextStep = await this.databaseService.serviceNextStep.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        license: {
          include: {
            contract: {
              include: {
                customer: true,
              },
            },
          },
        },
      },
    });

    if (!serviceNextStep) {
      throw new NotFoundException("Service Next Step not found");
    }

    const { license } = serviceNextStep;
    if (!license) {
      throw new NotFoundException("License not found");
    }

    const { contract } = license;
    if (!contract) {
      throw new NotFoundException("Contract not found");
    }

    const { customer } = contract;
    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You are not allowed to access this service next step");
    }
  }
}
