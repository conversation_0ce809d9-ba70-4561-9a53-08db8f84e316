import { CustomerServiceSetup } from "@/customer-commitment/dto/customer-commitment.dto";
import { DatabaseService } from "@/database/database.service";
import { HttpModuleService } from "@/http/http.service";
import { calculateDirectLicenseNetValue } from "@/shared/utils/calculate-direct-license-net-value";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { Contract, Customer, Prisma, Termination } from "@prisma/client";
import { lastValueFrom } from "rxjs";
import { Fraction } from "./interfaces/fraction";
import { Status } from "./interfaces/status";
import { JourneyStep } from "./interfaces/journey-step";
import { formatDateToUnix } from "@/shared/utils/format-date";
import { orUndefined } from "@/shared/utils/or-undefined";

const attributesFilter = [
  `version`,
  `timestamp`,
  `customer_id`,
  `company.contacts`,
  `company.created_at`,
  `company.deleted_at`,
  `company.updated_at`,
  `company.address.id`,
  `company.address.deleted_at`,
  `company.address.updated_at`,
  `company.address.created_at`,
] as const;

type ContractWithTermination = Contract & {
  termination?: Termination | null;
};

@Injectable()
export class CustomerIoService {
  constructor(private readonly databaseService: DatabaseService, private httpModuleService: HttpModuleService) {}

  private async dataProcessingShoppingCart(email: string, tx?: Prisma.TransactionClient) {
    const client = tx || this.databaseService;

    const result: any = {
      volume_report: [],
      licensing_services: [],
      direct_licensing: [],
      obligations: [],
      information_packages: [],
      shopping_items: [],
      action_guides: [],
    };

    const opportunityStatus = {
      licensing_services: "obligation_assessment",
      action_guides: "obligation_assessment",
      direct_licensing: "obligation_assessment",
    };

    const contractStatus = {
      licensing_services: "in_cart",
      action_guides: "in_cart",
      direct_licensing: "in_cart",
    };

    const shoppingCart = await client.shoppingCart.findFirst({
      where: {
        email: email,
        status: "OPEN",
        deleted_at: null,
      },
      include: {
        customer_commitments: {
          where: {
            deleted_at: null,
          },
        },
        items: {
          where: {
            deleted_at: null,
          },
        },
      },
    });

    if (!shoppingCart) return result;

    const items = shoppingCart.items;

    items.map((item) => {
      let newOpportunityStatus = "obligation_assessment";
      let status = "in_cart";

      switch (shoppingCart.journey_step as JourneyStep) {
        case JourneyStep.COMPANY_INFORMATIONS:
        case JourneyStep.SHOPPING_CART: {
          newOpportunityStatus = "configuration";
          status = "volumes_entered";

          break;
        }
        case JourneyStep.BILLING: {
          newOpportunityStatus = "payment_method";
          status = "volumes_entered";
          break;
        }
        case JourneyStep.PURCHASE: {
          newOpportunityStatus = "order_overview";
          status = "volumes_entered";
          break;
        }
      }

      opportunityStatus.action_guides = newOpportunityStatus;
      opportunityStatus.direct_licensing = newOpportunityStatus;
      opportunityStatus.licensing_services = newOpportunityStatus;

      contractStatus.action_guides = status;
      contractStatus.direct_licensing = status;
      contractStatus.licensing_services = status;

      if (item.service_type === "ACTION_GUIDE") return result.action_guides.push({ country: item.country_name });

      if (!["EU_LICENSE", "DIRECT_LICENSE"].includes(item.service_type)) return;

      const country = item.country_name;
      const priceList = item.price_list as any;

      const customerCommitment = shoppingCart.customer_commitments.find((i) => i.country_code === item.country_code);

      const setup = customerCommitment ? (customerCommitment.service_setup as CustomerServiceSetup) : null;

      result.obligations.push({
        country,
        packaging_obligation: {
          obligated: setup ? setup.packaging_services.filter((i) => i.obliged).map((i) => i.name) : null,
          status: "in_cart",
        },
      });

      if (item.service_type === "EU_LICENSE") {
        if (!!customerCommitment) {
          if (status === "in_cart") status = "commitment_assessment_made";

          if (contractStatus.licensing_services === "in_cart")
            contractStatus.licensing_services = "commitment_assessment_made";

          if (opportunityStatus.licensing_services === "obligation_assessment")
            opportunityStatus.licensing_services = "obligation_assessment_result_received";
        }

        result.licensing_services.push({
          country,
          packaging: {
            variant: "EU_LICENSE",
            costs: {
              registration_fee: priceList.registration_fee,
              handling_fee: priceList.handling_fee,
              variable_handling_fee: priceList.variableHandling_fee,
            },
            status: status,
          },
        });
      }

      if (item.service_type === "DIRECT_LICENSE") {
        const packagingService = item.packaging_services[0];

        const fractions = Object.values(packagingService.fractions || {}) as {
          code: string;
          name: string;
          weight: number;
        }[];

        result.direct_licensing.push({
          country,
          packaging: {
            variant: "DIRECT_LICENSE",
            cost: calculateDirectLicenseNetValue({ priceList: item.price_list, fractions, currentFractions: null }),
            status: status,
          },
        });
      }

      const volumeReportYearIndex = result.volume_report.findIndex((volume) => volume?.year === item?.year);

      const dataVolumeReport = {
        country: item.country_name,
        ...(() => {
          return (item.packaging_services as any)?.reduce((accumulator, currentValue) => {
            const fractions = Object.values(currentValue.fractions || {}) as {
              code: string;
              name: string;
              weight: number;
            }[];

            return {
              ...accumulator,
              [currentValue.name.replace(/ /g, "_")]: fractions?.reduce((accumulator, currentValue) => {
                return {
                  ...accumulator,
                  [currentValue.name.replace(/ /g, "_")]: currentValue.weight / 1000,
                };
              }, {}),
            };
          }, {});
        })(),
      };

      if (volumeReportYearIndex >= 0) {
        const volumeReportYear = result.volume_report[volumeReportYearIndex];

        result.volume_report[volumeReportYearIndex] = {
          ...volumeReportYear,
          volume_report: [...volumeReportYear.volume_report, dataVolumeReport],
        };
      } else {
        result.volume_report.push({
          year: item.year.toString(),
          volume_report: [dataVolumeReport],
        });
      }

      const packagingCosts: Record<string, number> = {};

      if (setup) {
        for (const packagingService of setup.packaging_services) {
          packagingCosts[packagingService.name] = priceList.registration_fee;
        }
      }

      result.information_packages.push({
        country,
        packaging: {
          costs: packagingCosts,
          status: status,
        },
      });

      return {
        country,
        license: {
          type: item.service_type,
          priceList: {
            ...(item.service_type === "EU_LICENSE" && {
              registrationFee: priceList.registration_fee,
              handlingFee: priceList.handling_fee,
              variableHandlingFee: priceList.variableHandling_fee,
            }),
            ...(item.service_type === "DIRECT_LICENSE" && {
              basic_price: priceList.basic_price,
              minimum_price: priceList.minimum_price,
            }),
          },
          representativeTier: setup?.representative_tier?.price,
        },
      };
    });

    result.licensing_services = {
      opportunity_stage: opportunityStatus.licensing_services,
      contract: { status: contractStatus.licensing_services },
      countries: result.licensing_services.map(({ country, packaging }) => {
        const countryPack = result.information_packages.find((pack) => pack.country === country);
        const countryObligation = result.obligations.find((obligation) => obligation.country === country);
        const obligation = countryObligation?.packaging_obligation?.obligated;

        return {
          country,
          packaging: {
            costs: {
              handling_fee: packaging?.costs?.handling_fee,
              registration_fee: packaging?.costs?.registration_fee,
              total: packaging?.costs?.handling_fee + packaging?.costs?.registration_fee || 0,
            },
            status: countryPack?.packaging?.status || countryPack?.status,
            variant: packaging?.variant,
            // commitment_assessment_status: packaging?.status,
            ...(obligation?.length ? { obligation } : {}),
          },
        };
      }),
    };

    result.action_guides = {
      opportunity_stage: opportunityStatus.action_guides,
      contract: { status: contractStatus.action_guides },
      countries: result.action_guides,
    };

    result.direct_licensing = {
      opportunity_stage: opportunityStatus.direct_licensing,
      contract: { status: contractStatus.direct_licensing },
      countries: result.direct_licensing,
    };

    return result;
  }

  findFractionById(fractions: Fraction[], id: number): Fraction | undefined {
    let result: Fraction | undefined;

    function search(fractionList: Fraction[]) {
      for (const fraction of fractionList) {
        if (fraction.id === id) {
          if (!result || new Date(fraction.created_at) < new Date(result.created_at)) {
            result = fraction;
          }
        }
        if (fraction.children) {
          search(fraction.children);
        }
      }
    }

    search(fractions);
    return result;
  }

  private dataProcessingContract(contract: ContractWithTermination) {
    if (!contract) return undefined;

    return {
      status: contract.status.toLocaleLowerCase(),
      start_date: formatDateToUnix(contract?.start_date),
      order_date: formatDateToUnix(contract?.updated_at),
      termination_date: formatDateToUnix(contract?.termination?.completed_at),
      cancelation_date: formatDateToUnix(contract?.end_date),
    };
  }

  validityAttributes(value: unknown | undefined): unknown | string {
    //Customer-io asks to delete an attribute with an empty string

    const response = "";

    if (value === undefined) return response;

    if (Array.isArray(value)) {
      return value.length ? value : response;
    }

    if (typeof value === "object" && value !== null && Object.keys(value).length === 0) return response;

    if (typeof value === "object" && value === null) return response;
    if (typeof value === "object" && (value as any)?.countries && !(value as any)?.countries?.length) return response;

    if (typeof value === "string" && !value.length) return response;

    return value;
  }

  private async dataProcessingPurchase(customerId: number, tx?: Prisma.TransactionClient) {
    const client = tx || this.databaseService;

    const contracts = await client.contract.findMany({
      where: { customer_id: customerId, deleted_at: null },
      include: {
        licenses: {
          include: {
            packaging_services: {
              include: {
                report_set: true,
                report_set_frequency: true,
                volume_reports: {
                  include: {
                    volume_report_items: {
                      include: {
                        volume_report: true,
                      },
                    },
                  },
                },
              },
            },
            price_list: true,
          },
        },
        action_guides: true,
        termination: true,
      },
    });

    const volume_report = [];
    const obligations = [];

    let direct_licensing: any = [];
    let action_guides: any = [];
    let licensing_services: any = [];

    const information_packages = [];

    if (contracts?.length) {
      contracts.forEach((contract) => {
        const licenses = contract.licenses;

        licenses.forEach((license) => {
          const packages = license.packaging_services;
          const status = license.termination_id ? "terminated" : "purchased";

          const country = license.country_name;

          if (contract.type === `EU_LICENSE`) {
            licensing_services.push({
              country,
              packaging: {
                costs: {
                  registration_fee: license.price_list.reduce((acc, curr) => acc + curr.registration_fee, 0),
                  handling_fee: license.price_list.reduce((acc, curr) => acc + curr.handling_fee, 0),
                },
                variant: license.price_list?.[0]?.name,
                status: status,
              },
            });
          } else if (contract.type === `DIRECT_LICENSE`) {
            direct_licensing.push({
              country,
              packaging: {
                variant: license.price_list?.[0]?.name,
                cost: license.price_list.reduce((acc, curr) => acc + curr.registration_fee + curr.handling_fee, 0),
                status: status,
              },
            });
          }

          information_packages.push({
            country,
            costs: license.price_list.reduce((acc, curr) => acc + curr.registration_fee + curr.handling_fee, 0),
            status: status,
          });

          obligations.push({
            country,
            packaging_obligation: {
              obligated: packages.map((pack) => pack.name),
              status: status,
            },
          });
        });

        contract.action_guides.forEach((action_guide) => {
          action_guides.push({ country: action_guide.country_name });
        });
      });

      if (action_guides?.length) {
        const contractActionGuides = contracts.find((contract) => contract?.type === `ACTION_GUIDE`);
        const contract = this.dataProcessingContract(contractActionGuides);

        action_guides = {
          opportunity_stage: "won",
          ...(contract && { contract }),
          countries: action_guides,
        };
      }

      if (licensing_services?.length) {
        const contractLicensing = contracts.find((contract) => contract?.type === `EU_LICENSE`);
        const contract = this.dataProcessingContract(contractLicensing);

        licensing_services = {
          opportunity_stage: "won",
          ...(contract && { contract }),
          countries: licensing_services.map(({ country, packaging }) => {
            const countryPack = information_packages.find((pack) => pack.country === country);
            const countryObligation = obligations.find((obligation) => obligation.country === country);

            return {
              name: country,
              packaging: {
                costs: {
                  handling_fee: packaging?.costs?.handling_fee,
                  registration_fee: packaging?.costs?.registration_fee,
                  total: packaging?.costs?.handling_fee + packaging?.costs?.registration_fee || 0,
                },
                status: countryPack?.packaging?.status || countryPack?.status,
                variant: packaging?.variant,
                // commitment_assessment_status: packaging?.status,
                obligation: countryObligation?.packaging_obligation?.obligated,
              },
            };
          }),
        };
      }

      if (direct_licensing?.length) {
        const contractRirect = contracts.find((contract) => contract?.type === `DIRECT_LICENSE`);
        const contract = this.dataProcessingContract(contractRirect);

        direct_licensing = {
          opportunity_stage: "won",
          ...(contract && { contract }),
          countries: direct_licensing,
        };
      }

      const allLicenses = contracts.map((c) => c.licenses).flat();

      const licensesYear = allLicenses.reduce((acc, curr) => {
        const yearKey = curr?.year?.toString();

        if (!yearKey) {
          return acc;
        }

        return {
          ...acc,
          [yearKey]: [...(acc?.[yearKey] || []), curr],
        };
      }, {});

      for (const year in licensesYear) {
        volume_report.push({
          year: year,
          volume_report: licensesYear[year].map((license) => {
            const packages = license.packaging_services;
            const country = license.country_name;

            return {
              country,
              ...packages.reduce(
                (acc, pack) => ({
                  ...acc,
                  ...pack.volume_reports.reduce((acc, volume) => {
                    const reportTable = volume?.report_table as any;
                    const fractions = reportTable?.fractions;

                    return {
                      ...acc,
                      [pack.name.replace(/ /g, "_")]: volume.volume_report_items.reduce((acc, curr) => {
                        if (!fractions) return acc;

                        const selectFraction = this.findFractionById(fractions, curr.setup_fraction_id);
                        if (!selectFraction || !curr.value) return acc;

                        acc[selectFraction.name.replace(/ /g, "_")] = curr.value / 1000;
                        return acc;
                      }, {} as Record<string, number>),
                    };
                  }, {}),
                }),
                {}
              ),
            };
          }),
        });
      }
    }

    return {
      obligations,
      volume_report: volume_report.filter((i) => i?.volume_report?.length),
      information_packages,
      licensing_services: licensing_services.countries ? licensing_services : { countries: [] },
      direct_licensing: direct_licensing.countries ? direct_licensing : { countries: [] },
      action_guides: action_guides.countries ? action_guides : { countries: [] },
    };
  }

  private deleteAttribute(obj: Record<string, any>, path: string) {
    const keys = path.split(".");
    const lastKey = keys.pop();
    let current = obj;

    if (keys.length === 0) {
      delete obj[path];
      return;
    }

    for (const key of keys) {
      if (!(key in current)) return;
      current = current[key];
    }

    if (lastKey && current && typeof current === "object") {
      delete current[lastKey];
    }
  }

  private async dataProcessingRequiredInformation(customerId: number, tx?: Prisma.TransactionClient) {
    try {
      const client = tx || this.databaseService;

      const contracts = await client.contract.findMany({
        where: { customer_id: customerId, deleted_at: null },
        include: {
          general_informations: true,
          action_guides: true,
          termination: true,
          licenses: {
            include: {
              required_informations: true,
            },
          },
        },
      });

      const general_information = [];
      const country_information = [];

      contracts.forEach((contract) => {
        contract.general_informations.forEach((general) => {
          general_information.push({
            name: general.name,
            status: Status[general.status],
          });
        });

        contract.licenses.forEach((license) => {
          country_information.push({
            country: license.country_name,
            information: license.required_informations.length
              ? license.required_informations.map((information) => ({
                  name: information.name,
                  status: Status[information.status],
                }))
              : undefined,
          });
        });
      });

      return {
        ...(general_information.length && { general_information }),
        ...(country_information.length && { country_information }),
      };
    } catch (err) {
      console.log("🚀 ~ CustomerIoService ~ processNewData ~ err:", err.message);
    }
  }

  async processCartData(customerId: number, tx?: Prisma.TransactionClient) {
    try {
      const client = tx || this.databaseService;

      const customer = await client.customer.findFirst({
        where: {
          id: customerId,
          deleted_at: null,
        },
      });

      if (!customer) return;

      const { volume_report, licensing_services, action_guides, direct_licensing } = await this.dataProcessing(
        customer,
        tx
      );

      this.updateAttributesByCustomerId(customerId, {
        billing_email: customer?.email,
        volume_report: this.validityAttributes(volume_report),
        direct_licensing: this.validityAttributes(direct_licensing),
        licensing_services: this.validityAttributes(licensing_services),
        action_guides: this.validityAttributes(action_guides),
      });
    } catch (err) {
      console.log("🚀 ~ CustomerIoService ~ processCartData ~ err:", err.message);
    }
  }

  async processPurchaseData(customerId: number, tx?: Prisma.TransactionClient) {
    try {
      const client = tx || this.databaseService;

      const customer = await client.customer.findFirst({
        where: {
          id: customerId,
          deleted_at: null,
        },
      });

      if (!customer) return;

      const { volume_report, licensing_services, action_guides, direct_licensing } = await this.dataProcessingPurchase(
        customerId,
        tx
      );

      const required_information = await this.dataProcessingRequiredInformation(customerId, tx);

      this.updateAttributesByCustomerId(customerId, {
        billing_email: customer.email,
        licensing_services: this.validityAttributes(licensing_services),
        direct_licensing: this.validityAttributes(direct_licensing),
        volume_report: this.validityAttributes(volume_report),
        action_guides: this.validityAttributes(action_guides),
        required_information: this.validityAttributes(required_information),
      });
    } catch (err) {
      console.log("🚀 ~ CustomerIoService ~ processPurchaseData ~ err:", err.message);
    }
  }

  async processRequiredInformation(customerId: number, tx?: Prisma.TransactionClient) {
    try {
      const client = tx || this.databaseService;

      const customer = await client.customer.findFirst({
        where: {
          id: customerId,
          deleted_at: null,
        },
      });

      if (!customer) return;

      const required_information = await this.dataProcessingRequiredInformation(customerId, tx);

      this.updateAttributesByCustomerId(customerId, {
        required_information: this.validityAttributes(required_information),
      });
    } catch (err) {
      console.log("🚀 ~ CustomerIoService ~ processNewData ~ err:", err.message);
    }
  }

  async dataProcessing(customer: Customer, tx?: Prisma.TransactionClient) {
    const responeDataProcessingPurchase = await this.dataProcessingPurchase(customer.id, tx);
    const responeDataProcessingShoppingCart = await this.dataProcessingShoppingCart(customer.email, tx);

    const mergeData = (attribute: string) => {
      const countriesPurchase = responeDataProcessingPurchase[attribute]?.countries ?? [];
      const countriesCart = responeDataProcessingShoppingCart[attribute]?.countries ?? [];

      const opportunity_stage = countriesCart.length
        ? responeDataProcessingShoppingCart[attribute]?.opportunity_stage
        : responeDataProcessingPurchase[attribute]?.opportunity_stage;

      const contract = countriesCart.length
        ? responeDataProcessingShoppingCart[attribute]?.contract
        : responeDataProcessingPurchase[attribute]?.contract;

      return {
        ...responeDataProcessingPurchase[attribute],
        ...responeDataProcessingShoppingCart[attribute],
        contract,
        opportunity_stage,
        countries: Array.from(
          new Map([...countriesPurchase, ...countriesCart].map((item) => [item.country, item])).values()
        ),
      };
    };

    const direct_licensing = mergeData(`direct_licensing`);
    const licensing_services = mergeData(`licensing_services`);
    const action_guides = mergeData(`action_guides`);
    let volume_report: any = [];

    volume_report = [
      ...responeDataProcessingPurchase.volume_report,
      ...responeDataProcessingShoppingCart.volume_report,
    ].reduce((acc, curr) => {
      const accFind = acc?.findIndex((item) => item.year === curr.year);

      if (accFind >= 0) {
        const volumeReportYear = acc[accFind];

        const uniqueVolumeReport = Array.from(
          new Map(
            [...volumeReportYear.volume_report, ...curr.volume_report].map((item) => [item.country, item])
          ).values()
        );

        acc[accFind] = {
          ...volumeReportYear,
          volume_report: uniqueVolumeReport,
        };

        return acc;
      }

      return [...acc, curr];
    }, []);

    return { volume_report, licensing_services, action_guides, direct_licensing };
  }

  async updateCompanyByCustomerId(customerId: number) {
    const company = await this.databaseService.company.findFirst({
      where: {
        customer_id: customerId,
        deleted_at: null,
      },
      include: {
        address: true,
        emails: true,
        contacts: true,
      },
    });

    if (!company) throw new HttpException("Company not found", HttpStatus.NOT_FOUND);

    await this.updateAttributesByCustomerId(customerId, {
      company_lucid_id: company?.lucid,
      company_name: company?.name,
      company: {
        id: company?.id,
        name: company?.name,
        description: orUndefined(company?.description),
        tin: orUndefined(company?.tin),
        vat: orUndefined(company?.vat),
        lucid: orUndefined(company?.lucid),
        partner_id: orUndefined(company?.partner_id),
        website: orUndefined(company?.website),
        starting: orUndefined(company?.starting),
        deleted_at: company?.deleted_at,
        updated_at: company?.updated_at,
        emails: orUndefined(company?.emails?.map((item) => item.email)),
        address: {
          id: company?.address?.id,
          street_and_number: company?.address?.street_and_number,
          additional_address: orUndefined(company?.address?.additional_address),
          zip_code: company?.address?.zip_code,
          city: company?.address?.city,
          country_code: company?.address?.country_code,
          deleted_at: company?.address?.deleted_at,
          updated_at: company?.address?.updated_at,
        },
      },
    });
  }

  async updateAttributesByCustomerId(customerId: number, attributes: unknown, tx?: Prisma.TransactionClient) {
    if (!customerId || typeof attributes !== `object`) return;

    const client = tx || this.databaseService;

    const customer = await client.customer.findFirst({
      where: {
        id: customerId,
        deleted_at: null,
      },
    });

    if (!customer) throw new HttpException("Customer not found", HttpStatus.NOT_FOUND);

    attributesFilter.forEach((attributeFilter) => this.deleteAttribute(attributes, attributeFilter));

    try {
      await lastValueFrom(
        this.httpModuleService.admin({
          url: `/integrations/customer-io/${customer.email}`,
          params: {
            ...attributes,
            id: customer.id,
            email: customer.email,
          },
          method: "post",
        })
      ).catch((error) => {
        console.error(error?.response?.data);
      });
    } catch (err) {}
  }

  async updateEmailByCustomerId(customerId: number, oldEmail: string) {
    if (!customerId || !oldEmail) return;

    const customer = await this.databaseService.customer.findFirst({
      where: {
        id: customerId,
        deleted_at: null,
      },
    });

    if (!customer) throw new HttpException("Customer not found", HttpStatus.NOT_FOUND);

    try {
      await lastValueFrom(
        this.httpModuleService.admin({
          url: `/integrations/customer-io/update-email/${oldEmail}`,
          params: {
            newEmail: customer.email,
          },
          method: "post",
        })
      ).catch((error) => {
        console.error(error?.response?.data);
      });
    } catch (err) {}
  }
}
