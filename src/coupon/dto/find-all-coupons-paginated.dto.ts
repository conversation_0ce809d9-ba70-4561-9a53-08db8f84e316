import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, <PERSON><PERSON><PERSON>, Min } from "class-validator";
import { Type } from "class-transformer";
import { ApiPropertyOptional } from "@nestjs/swagger";

export class FindAllCouponsPaginatedDto {
  @ApiPropertyOptional({
    description: "Pagination page",
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({
    description: "Pagination limit",
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: "Filter by active status",
    example: true,
  })
  @IsOptional()
  is_active?: string;

  @ApiPropertyOptional({
    description: "Filter coupons with 'code' or 'link' or 'all'",
    example: "all",
  })
  @IsOptional()
  @IsString()
  @Type(() => String)
  lead_type?: string;

  @ApiPropertyOptional({
    description: "Filter coupons by code",
    example: "123456",
  })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiPropertyOptional({
    description: "Include coupon uses with count",
    example: true,
  })
  @IsOptional()
  @IsString()
  include_uses?: string;

  @ApiPropertyOptional({
    description: "Filter by redeemable by new customers",
    example: true,
  })
  @IsOptional()
  @IsString()
  start_date?: string;

  @ApiPropertyOptional({
    description: "Filter by end date",
    example: "2025-01-01",
  })
  @IsOptional()
  @IsString()
  end_date?: string;
}
