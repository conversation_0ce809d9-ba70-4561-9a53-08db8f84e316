import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { CouponDiscountType, CouponMode, CouponType } from "@prisma/client";
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Min,
} from "class-validator";

export class CreateCouponDto {
  @ApiPropertyOptional({
    example: JSON.stringify([
      {
        buyProduct: "direct_license | eu_license | action_guide",
        buyAtLeast: 1,
        receiveProduct: "workshop | action_guide",
        receiveAtLeast: 1,
      },
      { or: "or" },
      {
        buyProduct: "direct_license | eu_license | action_guide",
        buyAtLeast: 1,
        discountType: "PERCENTAGE | ABSOLUTE",
        discountValue: 1000,
      },
    ]),
    description: "Buy X get Y products or discount json object",
  })
  @IsObject()
  @IsOptional()
  buy_x_get_y?: Record<string, any>;

  @ApiPropertyOptional({
    example: "OFF100",
    description: "Unique code for the coupon",
  })
  @IsString()
  code: string;

  @ApiPropertyOptional({
    example: "10% discount on all products",
    description: "Description of the coupon",
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    enum: CouponDiscountType,
    description:
      "Type of discount (PERCENTAGE or ABSOLUTE or BUY_X_PRODUCTS_GET_Y_PRODUCTS or BUY_X_PRODUCTS_GET_Y_DISCOUNT)",
    example: "PERCENTAGE",
  })
  @IsEnum(CouponDiscountType)
  discount_type: CouponDiscountType;

  @ApiPropertyOptional({
    example: {
      direct_license: {
        years: [2025, 2024, 2023],
      },
      eu_license: {
        countries: ["FR", "DE", "COUNTRY CODE"],
      },
      action_guide: {
        countries: ["FR", "DE", "COUNTRY CODE"],
      },
    },
    description: "JSON object containing eligible products information",
  })
  @IsObject()
  @IsOptional()
  elegible_products?: Record<string, any>;

  @ApiProperty({
    example: "2023-12-31T23:59:59.000Z",
    description: "End date of the coupon validity",
  })
  @IsDateString()
  end_date: string;

  @ApiProperty({
    example: true,
    description: "Is the coupon active",
  })
  @IsBoolean()
  is_active: boolean;

  @ApiPropertyOptional({
    example: "http://example.com/coupon/special-offer",
    description: "Unique link for the coupon",
  })
  @IsString()
  @IsOptional()
  link?: string;

  @ApiProperty({
    example: 1000,
    description: "Maximum amount for the coupon (always in cents)",
  })
  @IsNumber()
  @IsOptional()
  max_amount?: number;

  @ApiProperty({
    example: 5,
    description: "Maximum number of times this coupon can be used",
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  max_uses?: number;

  @ApiProperty({
    example: 1,
    description: "Maximum number of times a customer can use this coupon",
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  max_uses_per_customer?: number;

  @ApiProperty({
    example: 100,
    description: "Minimum amount for the coupon to be applicable (always in cents)",
  })
  @IsNumber()
  @IsOptional()
  min_amount?: number;

  @ApiPropertyOptional({
    example: 2,
    description: "Minimum number of products required to use this coupon",
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  min_products?: number;

  @ApiProperty({
    enum: CouponMode,
    description: "Mode of the coupon (GENERAL, INDIVIDUAL, GROUP_SEGMENT)",
    example: "GENERAL",
  })
  @IsEnum(CouponMode)
  mode: CouponMode;

  @ApiPropertyOptional({
    example: "Internal note about this coupon",
    description: "Note about the coupon",
  })
  @IsString()
  @IsOptional()
  note?: string;

  @ApiProperty({
    example: false,
    description: "Whether the coupon is redeemable by new customers",
  })
  @IsBoolean()
  redeemable_by_new_customers: boolean;

  @ApiPropertyOptional({
    example: "uuid",
    description: "ID of the shopping cart",
  })
  @IsString()
  @IsUUID()
  @IsOptional()
  shopping_cart_id?: string;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "Start date of the coupon validity",
  })
  @IsDateString()
  start_date: string;

  @ApiProperty({
    enum: CouponType,
    description: "Type of coupon (SYSTEM, CUSTOMER)",
    example: "SYSTEM",
  })
  @IsEnum(CouponType)
  type: CouponType;

  @ApiProperty({
    example: 10000,
    description: "Value of the coupon (percentage or absolute amount, always in cents)",
  })
  @IsNumber()
  value: number;

  @ApiPropertyOptional({
    example: [1, 2, 3],
    description: "Array of customer IDs that can use this coupon",
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  customers?: number[];
}
