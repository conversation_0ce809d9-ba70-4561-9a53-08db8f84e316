import { DatabaseService } from "@/database/database.service";
import { CreateCustomerActivityDto } from "./dto/create-customer-activity.dto";
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { UpdateCustomerActivityDto } from "./dto/update-customer-activity.dto";
import { FindAllCustomerActivityDto } from "./dto/find-all-customer-activity";
import { Role } from "@/shared/auth/role.enum";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";

@Injectable()
export class CustomerActivityService {
  constructor(private readonly databaseService: DatabaseService) {}

  async findOne(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionCustomerActivity(id, user);

    return await this.databaseService.customerActivity.findUnique({
      where: { id, deleted_at: null },
    });
  }

  async findAll(query?: FindAllCustomerActivityDto) {
    const customerActivities = await this.databaseService.customerActivity.findMany({
      where: {
        ...(query.customer_id && { customer_id: query.customer_id }),
        deleted_at: null,
      },
      orderBy: {
        created_at: "desc",
      },
    });

    return customerActivities;
  }

  async create(createCustomerActivityDto: CreateCustomerActivityDto) {
    const createdCustomerActivity = await this.databaseService.customerActivity.create({
      data: {
        ...createCustomerActivityDto,
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      },
    });

    return createdCustomerActivity;
  }

  async update(id: number, data: UpdateCustomerActivityDto, user: AuthenticatedUser) {
    await this.validatingUserPermissionCustomerActivity(id, user);

    const updatedCustomerActivity = await this.databaseService.customerActivity.update({
      where: { id },
      data: {
        ...data,
        updated_at: new Date(),
      },
    });

    return updatedCustomerActivity;
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionCustomerActivity(id, user);

    await this.databaseService.customerActivity.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }

  async validatingUserPermissionCustomerActivity(id: number, user: AuthenticatedUser) {
    if (!id || isNaN(id)) {
      throw new BadRequestException("Invalid CustomerActivity ID");
    }

    const customerActivity = await this.databaseService.customerActivity.findUnique({
      where: {
        id,
        deleted_at: null,
      },
      include: {
        customer: true,
      },
    });

    if (!customerActivity) throw new NotFoundException("Customer activity not found");

    const { customer } = customerActivity;

    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You do not have permission to access this customer activity");
    }
  }
}
