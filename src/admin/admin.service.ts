import { Injectable, InternalServerErrorException, NotFoundException } from "@nestjs/common";
import { lastValueFrom } from "rxjs";
import { HttpModuleService } from "../http/http.service";
import { CreateAdminDto } from "./dto/admin-create.dto";
import { UpdateAdminDto } from "./dto/admin-update.dto";
import { FindAllAdminDto } from "./dto/admin-find-all";
import { Role } from "@/shared/auth/role.enum";

@Injectable()
export class AdminService {
  constructor(private readonly httpModuleService: HttpModuleService) {}

  async create(createAdminDto: CreateAdminDto) {
    try {
      const createAdminResponse = await lastValueFrom(
        this.httpModuleService.admin({
          url: "/user",
          params: {
            email: createAdminDto.email,
            name: createAdminDto.name,
            password: createAdminDto.password,
            is_active: true,
            type: "ADMIN",
            role_id: createAdminDto.role_id,
          },
          method: "post",
        })
      );

      return createAdminResponse.data;
    } catch (err) {
      throw new InternalServerErrorException("Failed to create admin user");
    }
  }

  async findAll(query: FindAllAdminDto) {
    try {
      const adminRoles = [Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.MARKETING_MANAGER, Role.BROKER_MANAGER];
      const usersResponse = await lastValueFrom(
        this.httpModuleService.auth({
          url: "/user",
          params: {
            role: query.role || adminRoles.join(","),
            ...(query.is_active !== undefined && { is_active: query.is_active }),
            ...(query.name !== undefined && { name: query.name }),
          },
          method: "GET",
        })
      );

      return usersResponse.data;
    } catch (err) {
      throw new InternalServerErrorException("Failed to retrieve admin users");
    }
  }

  async findOne(id: number) {
    try {
      const adminResponse = await lastValueFrom(
        this.httpModuleService.auth({
          url: `/user/${id}`,
          method: "GET",
          params: {},
        })
      );

      return adminResponse.data;
    } catch (err) {
      if (err.response?.status === 404) {
        throw new NotFoundException("Admin user not found");
      }
      throw new InternalServerErrorException("Failed to retrieve admin user");
    }
  }

  async update(id: number, updateAdminDto: UpdateAdminDto) {
    try {
      const updateAdminResponse = await lastValueFrom(
        this.httpModuleService.admin({
          url: `/user/${id}`,
          params: {
            ...updateAdminDto,
          },
          method: "patch",
        })
      );

      return updateAdminResponse.data;
    } catch (err) {
      if (err.response?.status === 404) {
        throw new NotFoundException("Admin user not found");
      }
      throw new InternalServerErrorException("Failed to update admin user");
    }
  }

  async remove(id: number) {
    try {
      const deleteAdminResponse = await lastValueFrom(
        this.httpModuleService.admin({
          url: `/user/${id}`,
          params: {
            type: "ADMIN",
          },
          method: "delete",
        })
      );

      return deleteAdminResponse.data;
    } catch (err) {
      if (err.response?.status === 404) {
        throw new NotFoundException("Admin user not found");
      }
      throw new InternalServerErrorException("Failed to delete admin user");
    }
  }
}
