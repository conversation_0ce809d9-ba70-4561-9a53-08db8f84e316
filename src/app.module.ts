import { DashboardModule } from "@/dashboard/dashboard.module";
import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ScheduleModule } from "@nestjs/schedule";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { CertificateModule } from "./certificate/certificate.module";
import { CompanyEmailModule } from "./company-email/company-email.module";
import { CompanyModule } from "./company/company.module";
import { ConsentModule } from "./consent/consent.module";
import { ContractModule } from "./contract/contract.module";
import { CustomerCommitmentModule } from "./customer-commitment/customer-commitment.module";
import { CustomerConsentModule } from "./customer-consent/customer-consent.module";
import { CustomerDocumentModule } from "./customer-document/customer-document.module";
import { CustomerInviteTokenModule } from "./customer-invite-token/customer-invite-token.module";
import { CustomerIoModule } from "./customer-io/customer-io.module";
import { CustomerPhoneModule } from "./customer-phone/customer-phone.module";
import { CustomerModule } from "./customer/customer.module";
import { DatabaseModule } from "./database/database.module";
import { FileModule } from "./file/file.module";
import { IntegrationModule } from "./integration/integration.module";
import { InviteModule } from "./invite/invite.module";
import { LicenseOtherCostModule } from "./license-other-cost/license-other-cost.module";
import { LicensePackagingServiceModule } from "./license-packaging-service/license-packaging-service.module";
import { LicenseRepresentativeTierModule } from "./license-representative-tier/license-representative-tier.module";
import { LicenseRequiredInformationModule } from "./license-required-information/license-required-information.module";
import { LicenseThirdPartyInvoiceModule } from "./license-third-party-invoice/license-third-party-invoice.module";
import { LicenseVolumeReportItemModule } from "./license-volume-report-item/license-volume-report-item.module";
import { LicenseVolumeReportModule } from "./license-volume-report/license-volume-report.module";
import { LicenseModule } from "./license/license.module";
import { PartnerModule } from "./partner/partner.module";
import { PurchaseModule } from "./purchase/purchase.module";
import { RecommendCountryModule } from "./recommend-country/recommend-country.module";
import { ServiceNextStepModule } from "./service-next-steps/service-next-step.module";
import { ShoppingCartModule } from "./shopping-cart/shopping-cart.module";
import { TerminationModule } from "./termination/termination.module";
import { MarketingMaterialsModule } from "./marketing-materials/marketing-materials.module";
import { CouponModule } from "./coupon/coupon.module";
import { ClusterModule } from "./cluster/cluster.module";
import { PartnerResultsModule } from "./partner-results/partner-results.module";
import { CommissionModule } from "./commission/commission.module";
import { APP_GUARD } from "@nestjs/core";
import { AuthGuard } from "./shared/auth/auth.guard";
import { CustomerActivityModule } from "./customer-activity/customer-activity.module";
import { ReasonModule } from "./reason/reason.module";
import { LicenseReportSetModule } from "./license-report-set/license-report-set.module";

@Module({
  imports: [
    DatabaseModule,
    CustomerPhoneModule,
    CustomerDocumentModule,
    CustomerModule,
    CompanyModule,
    CompanyEmailModule,
    RecommendCountryModule,
    ShoppingCartModule,
    PartnerModule,
    ScheduleModule.forRoot(),
    ConsentModule,
    CustomerConsentModule,
    CustomerIoModule,
    HttpModule,
    LicenseModule,
    LicensePackagingServiceModule,
    LicenseRequiredInformationModule,
    LicenseVolumeReportModule,
    LicenseVolumeReportItemModule,
    LicenseThirdPartyInvoiceModule,
    LicenseOtherCostModule,
    LicenseRepresentativeTierModule,
    ServiceNextStepModule,
    CertificateModule,
    ContractModule,
    PurchaseModule,
    FileModule,
    CustomerInviteTokenModule,
    InviteModule,
    TerminationModule,
    IntegrationModule,
    CustomerCommitmentModule,
    DashboardModule,
    MarketingMaterialsModule,
    CouponModule,
    ClusterModule,
    PartnerResultsModule,
    CommissionModule,
    CustomerActivityModule,
    ReasonModule,
    LicenseReportSetModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
})
export class AppModule {}
