import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "../database/database.service";
import { CreatePriceListDto } from "./dto/price-list-create.dto";
import { UpdatePriceListDto } from "./dto/price-list-update.dto";
import { PriceListType, Prisma } from "@prisma/client";

@Injectable()
export class PriceListService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(data: CreatePriceListDto) {
    return this.databaseService.priceList.create({
      data: {
        name: data.name,
        description: data.description,
        type: data.type,
        condition_type: data.condition_type,
        condition_type_value: data.condition_type_value,
        start_date: new Date(data.start_date).toISOString(),
        end_date: new Date(data.end_date).toISOString(),
        basic_price: data.basic_price,
        minimum_price: data.minimum_price,
        registration_fee: data.registration_fee,
        handling_fee: data.handling_fee,
        variable_handling_fee: data.variable_handling_fee,
        price: data.price,
        thresholds: data.thresholds || Prisma.JsonNull,
      },
    });
  }

  async findAll(params: { search?: string; service_type?: PriceListType; license_year?: string }) {
    const where: Prisma.PriceListWhereInput = {
      deleted_at: null,
    };

    if (params.service_type) where.type = params.service_type as PriceListType;
    if (params.license_year) where.condition_type_value = params.license_year as string;
    if (params.search) where.name = { contains: params.search, mode: "insensitive" };

    const priceLists = await this.databaseService.priceList.findMany({
      where,
      orderBy: {
        condition_type_value: "asc",
      },
    });

    return priceLists;
  }

  async findOne(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid price list ID");

    const priceList = await this.databaseService.priceList.findUnique({
      where: { id, deleted_at: null },
    });

    if (!priceList) {
      throw new NotFoundException("Price list not found");
    }

    return priceList;
  }

  async update(id: number, data: UpdatePriceListDto) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid price list ID");

    const priceList = await this.databaseService.priceList.findUnique({
      where: { id },
    });

    if (!priceList) {
      throw new NotFoundException("Price list not found");
    }

    return this.databaseService.priceList.update({
      where: { id },
      data,
    });
  }

  async remove(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid price list ID");

    const priceList = await this.databaseService.priceList.findUnique({
      where: { id },
    });

    if (!priceList) {
      throw new NotFoundException("Price list not found");
    }

    await this.databaseService.priceList.update({
      where: { id },
      data: { deleted_at: new Date() },
    });
  }
}
