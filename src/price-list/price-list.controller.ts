import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from "@nestjs/swagger";

import { PriceListService } from "./price-list.service";
import { CreatePriceListDto } from "./dto/price-list-create.dto";
import { UpdatePriceListDto } from "./dto/price-list-update.dto";
import { PriceListConditionType, PriceListType } from "@prisma/client";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("PriceList")
@Controller("price-lists")
export class PriceListController {
  constructor(private readonly priceListService: PriceListService) {}

  @Post()
  @ApiOperation({ summary: "Create a new price list" })
  @ApiResponse({
    status: 201,
    description: "Price list created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string" },
        type: { type: "string", enum: Object.values(PriceListType) },
        name: { type: "string" },
        price: { type: "number", nullable: true },
        condition_type: { type: "string", enum: Object.values(PriceListConditionType) },
        condition_type_value: { type: "string" },
        start_date: { type: "string", format: "date-time" },
        end_date: { type: "string", format: "date-time" },
        basic_price: { type: "number", nullable: true },
        minimum_price: { type: "number", nullable: true },
        registration_fee: { type: "number", nullable: true },
        handling_fee: { type: "number", nullable: true },
        variable_handling_fee: { type: "number", nullable: true },
        thresholds: { type: "object" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
      example: {
        id: 1,
        description: "Price list description",
        type: "EU_LICENSE",
        name: "Price list name",
        price: 1000,
        condition_type: "LICENSE_YEAR",
        condition_type_value: "2024",
        start_date: "2024-03-20T10:00:00Z",
        end_date: "2024-03-20T10:00:00Z",
        basic_price: 100,
        minimum_price: 100,
        registration_fee: 100,
        handling_fee: 100,
        variable_handling_fee: 100,
        thresholds: {},
      },
    },
  })
  @ApiResponse({ status: 400, description: "Invalid request body" })
  create(@Body() data: CreatePriceListDto) {
    return this.priceListService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all price lists" })
  @ApiQuery({ name: "search", type: String, required: false })
  @ApiQuery({ name: "service_type", type: String, required: false })
  @ApiQuery({ name: "license_year", type: String, required: false })
  @ApiResponse({
    status: 200,
    description: "Price lists retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          description: { type: "string" },
          type: { type: "string", enum: Object.values(PriceListType) },
          name: { type: "string" },
          price: { type: "number", nullable: true },
          condition_type: { type: "string", enum: Object.values(PriceListConditionType) },
          condition_type_value: { type: "string" },
          start_date: { type: "string", format: "date-time" },
          end_date: { type: "string", format: "date-time" },
          basic_price: { type: "number", nullable: true },
          minimum_price: { type: "number", nullable: true },
          registration_fee: { type: "number", nullable: true },
          handling_fee: { type: "number", nullable: true },
          variable_handling_fee: { type: "number", nullable: true },
          thresholds: { type: "object" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
        },
      },
      example: [
        {
          id: 1,
          description: "Price list description",
          type: "EU_LICENSE",
          name: "Price list name",
          price: 1000,
          condition_type: "LICENSE_YEAR",
          condition_type_value: "2024",
          start_date: "2024-03-20T10:00:00Z",
          end_date: "2024-03-20T10:00:00Z",
          basic_price: 100,
          minimum_price: 100,
          registration_fee: 100,
          handling_fee: 100,
          variable_handling_fee: 100,
        },
      ],
    },
  })
  findAll(
    @Query("search") search?: string,
    @Query("service_type") service_type?: PriceListType,
    @Query("license_year") license_year?: string
  ) {
    return this.priceListService.findAll({
      search,
      service_type,
      license_year,
    });
  }

  @Get(":id")
  @ApiOperation({ summary: "Get price list by ID" })
  @ApiResponse({
    status: 201,
    description: "Price list retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string" },
        type: { type: "string", enum: Object.values(PriceListType) },
        name: { type: "string" },
        price: { type: "number", nullable: true },
        condition_type: { type: "string", enum: Object.values(PriceListConditionType) },
        condition_type_value: { type: "string" },
        start_date: { type: "string", format: "date-time" },
        end_date: { type: "string", format: "date-time" },
        basic_price: { type: "number", nullable: true },
        minimum_price: { type: "number", nullable: true },
        registration_fee: { type: "number", nullable: true },
        handling_fee: { type: "number", nullable: true },
        variable_handling_fee: { type: "number", nullable: true },
        thresholds: { type: "object" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
      example: {
        id: 1,
        description: "Price list description",
        type: "EU_LICENSE",
        name: "Price list name",
        price: 1000,
        condition_type: "LICENSE_YEAR",
        condition_type_value: "2024",
        start_date: "2024-03-20T10:00:00Z",
        end_date: "2024-03-20T10:00:00Z",
        basic_price: 100,
        minimum_price: 100,
        registration_fee: 100,
        handling_fee: 100,
        variable_handling_fee: 100,
        thresholds: {},
      },
    },
  })
  @ApiResponse({ status: 404, description: "Price list not found" })
  @ApiResponse({ status: 400, description: "Invalid price list ID" })
  findOne(@Param("id") id: string) {
    return this.priceListService.findOne(+id);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update price list by ID" })
  @ApiResponse({
    status: 200,
    description: "Price list updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string" },
        type: { type: "string", enum: Object.values(PriceListType) },
        name: { type: "string" },
        price: { type: "number", nullable: true },
        condition_type: { type: "string", enum: Object.values(PriceListConditionType) },
        condition_type_value: { type: "string" },
        start_date: { type: "string", format: "date-time" },
        end_date: { type: "string", format: "date-time" },
        basic_price: { type: "number", nullable: true },
        minimum_price: { type: "number", nullable: true },
        registration_fee: { type: "number", nullable: true },
        handling_fee: { type: "number", nullable: true },
        variable_handling_fee: { type: "number", nullable: true },
        thresholds: { type: "object" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
      example: {
        id: 1,
        description: "Price list description",
        type: "EU_LICENSE",
        name: "Price list name",
        price: 1000,
        condition_type: "LICENSE_YEAR",
        condition_type_value: "2024",
        start_date: "2024-03-20T10:00:00Z",
        end_date: "2024-03-20T10:00:00Z",
        basic_price: 100,
        minimum_price: 100,
        registration_fee: 100,
        handling_fee: 100,
        variable_handling_fee: 100,
        thresholds: {},
      },
    },
  })
  @ApiResponse({ status: 404, description: "Price list not found" })
  @ApiResponse({ status: 400, description: "Invalid price list ID" })
  update(@Param("id") id: string, @Body() data: UpdatePriceListDto) {
    return this.priceListService.update(+id, data);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete price list by ID" })
  @ApiResponse({ status: 200, description: "Price list deleted successfully" })
  @ApiResponse({ status: 404, description: "Price list not found" })
  @ApiResponse({ status: 400, description: "Invalid price list ID" })
  remove(@Param("id") id: string) {
    return this.priceListService.remove(+id);
  }
}
