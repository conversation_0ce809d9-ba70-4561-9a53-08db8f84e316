import { ApiProperty } from "@nestjs/swagger";
import { PriceListType, PriceListConditionType } from "@prisma/client";

export class CreatePriceListDto {
  @ApiProperty({
    required: true,
    enum: PriceListType,
    description: "Type of the price list",
  })
  type: PriceListType;

  @ApiProperty({
    required: true,
    description: "Name of the price list",
  })
  name: string;

  @ApiProperty({
    required: true,
    description: "Description of the price list",
  })
  description: string;

  @ApiProperty({
    required: true,
    enum: PriceListConditionType,
    description: "Condition type of the price list",
  })
  condition_type: PriceListConditionType;

  @ApiProperty({
    required: true,
    description: "Condition type value of the price list",
  })
  condition_type_value: string;

  @ApiProperty({
    required: true,
    description: "Start date of the price list",
  })
  start_date: Date;

  @ApiProperty({
    required: true,
    description: "End date of the price list",
  })
  end_date: Date;

  @ApiProperty({
    required: false,
    description: "Basic price for EU_LICENSE or DIRECT_LICENSE",
  })
  basic_price?: number;

  @ApiProperty({
    required: false,
    description: "Minimum price for EU_LICENSE or DIRECT_LICENSE",
  })
  minimum_price?: number;

  @ApiProperty({
    required: false,
    description: "Registration fee for EU_LICENSE or DIRECT_LICENSE",
  })
  registration_fee?: number;

  @ApiProperty({
    required: false,
    description: "Variable handling fee for EU_LICENSE or DIRECT_LICENSE",
  })
  handling_fee?: number;

  @ApiProperty({
    required: false,
    description: "Variable handling fee for EU_LICENSE or DIRECT_LICENSE",
  })
  variable_handling_fee?: number;

  @ApiProperty({
    required: false,
    description: "Thresholds for DIRECT_LICENSE",
  })
  thresholds?: {
    title: string;
    value: number;
    helper_text: string;
    fractions: Record<
      string,
      {
        code: string;
        name: string;
        value: number;
      }
    >;
  }[];

  @ApiProperty({
    required: false,
    description: "Price for ACTION_GUIDE or WORKSHOP",
  })
  price?: number;
}
