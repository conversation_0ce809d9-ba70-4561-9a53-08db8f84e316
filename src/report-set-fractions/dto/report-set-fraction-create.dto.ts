import { ApiProperty } from "@nestjs/swagger";

export class CreateReportSetFractionDto {
  @ApiProperty({
    required: true,
    description: "Name of the report set fraction",
  })
  name: string;

  @ApiProperty({
    required: true,
    description: "Description of the report set fraction",
  })
  description: string;

  @ApiProperty({
    required: true,
    description: "Icon of the report set fraction",
  })
  icon: string;

  @ApiProperty({
    required: true,
    description: "ID of the fraction icon",
  })
  fraction_icon_id: number;

  @ApiProperty({
    required: false,
    description: "Is the report set fraction active",
  })
  is_active?: boolean;

  @ApiProperty({
    required: true,
    description: "ID of the associated report set",
  })
  report_set_id: number;

  @ApiProperty({
    required: false,
    description: "ID of the parent fraction (if any)",
  })
  parent_id?: number;
}
