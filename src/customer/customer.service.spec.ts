import { Test, TestingModule } from "@nestjs/testing";
import { CustomerService } from "./customer.service";
import { DatabaseService } from "@/database/database.service";
import { CustomerIoService } from "@/customer-io/customer-io.service";
import { CustomerInviteTokenService } from "@/customer-invite-token/customer-invite-token.service";
import { MondayService } from "@/integration/monday.service";
import { HttpModuleService } from "@/http/http.service";
import { CompanyService } from "@/company/company.service";
import { BadRequestException, ConflictException, HttpException, NotFoundException } from "@nestjs/common";
import { CustomerType } from "@prisma/client";
import { of } from "rxjs";
import { CreateCustomerCountriesDto } from "./dto/create-customer-countries.dto";
import axios from "axios";
import { CouponService } from "@/coupon/coupon.service";
import { UpdateCustomerDto } from "./dto/update-customer.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

jest.mock("axios");

describe("CustomerService", () => {
  let service: CustomerService;
  let mockDatabaseService: any;
  let mockCustomerIoService: any;
  let mockCustomerInviteTokenService: any;
  let mockMondayService: any;
  let mockHttpModuleService: any;
  let mockCompanyService: any;

  const mockCustomer = {
    id: 1,
    type: CustomerType.REGULAR,
    first_name: "John",
    last_name: "Doe",
    email: "<EMAIL>",
    user_id: 1,
    company_name: "Test Company",
    salutation: "Mr",
    created_at: new Date(),
    updated_at: new Date(),
    deleted_at: null,
  };

  beforeEach(async () => {
    mockDatabaseService = {
      customerTutorial: {
        findMany: jest.fn(),
      },
      customer: {
        findMany: jest.fn().mockReturnValue([mockCustomer]),
        findFirst: jest.fn().mockReturnValue(mockCustomer),
        findUnique: jest.fn().mockReturnValue(mockCustomer),
        create: jest.fn().mockReturnValue(mockCustomer),
        update: jest.fn().mockReturnValue(mockCustomer),
        count: jest.fn().mockReturnValue(1),
        createMany: jest.fn(),
      },
      customerPhone: {
        deleteMany: jest.fn(),
        createMany: jest.fn(),
      },
      consent: {
        findMany: jest.fn().mockReturnValue([{ id: 1, type: "ACCOUNT" }]),
      },
      customerConsent: {
        createMany: jest.fn(),
      },
      shoppingCart: {
        updateMany: jest.fn(),
      },
      $transaction: jest.fn((callback) => callback(mockDatabaseService)),
    };

    mockCustomerIoService = {
      updateAttributesByCustomerId: jest.fn(),
    };

    mockCustomerInviteTokenService = {
      create: jest.fn(),
    };

    mockMondayService = {
      sendDataBoardCustomer: jest.fn(),
    };

    mockHttpModuleService = {
      auth: jest.fn().mockReturnValue(
        of({
          status: 200,
          data: {
            id: 1,
            name: "John Doe",
            email: "<EMAIL>",
            is_active: true,
            role_id: 1,
            password: null,
          },
        })
      ),
    };

    mockCompanyService = {
      create: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomerService,
        CouponService,
        { provide: DatabaseService, useValue: mockDatabaseService },
        { provide: CustomerIoService, useValue: mockCustomerIoService },
        { provide: CustomerInviteTokenService, useValue: mockCustomerInviteTokenService },
        { provide: MondayService, useValue: mockMondayService },
        { provide: HttpModuleService, useValue: mockHttpModuleService },
        { provide: CompanyService, useValue: mockCompanyService },
      ],
    }).compile();

    service = module.get<CustomerService>(CustomerService);
  });

  describe("findAll", () => {
    it("should return paginated customers", async () => {
      const params = { page: 1, limit: 10 };
      const result = await service.findAll(params);

      expect(result).toEqual({
        customers: [mockCustomer],
        count: 1,
        pages: 1,
        current_page: 1,
      });
    });

    it("should throw BadRequestException for invalid page", async () => {
      await expect(service.findAll({ page: -1 })).rejects.toThrow(BadRequestException);
    });
  });

  describe("findById", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should return a customer by id", async () => {
      const result = await service.findById(1, user);
      expect(result).toEqual(mockCustomer);
    });

    it("should throw NotFoundException when customer not found", async () => {
      mockDatabaseService.customer.findUnique.mockReturnValue(null);
      await expect(service.findById(999, user)).rejects.toThrow(NotFoundException);
    });
  });

  describe("create", () => {
    const createDto = {
      first_name: "John",
      last_name: "Doe",
      email: "<EMAIL>",
      salutation: "Mr",
      user_id: 1,
      phones: ["1234567890"],
    };

    it("should throw ConflictException if email exists", async () => {
      mockDatabaseService.customer.findFirst.mockReturnValue(mockCustomer);
      await expect(service.create(createDto)).rejects.toThrow(ConflictException);
    });
  });

  describe("update", () => {
    const updateDto: UpdateCustomerDto = {
      first_name: "John Updated",
      last_name: "Doe Updated",
      email: "<EMAIL>",
      phones: ["1234567890"],
      salutation: "Mr",
      user_id: 1,
    };

    beforeEach(() => {
      (axios.patch as jest.Mock).mockResolvedValue({ data: { success: true } });

      mockDatabaseService.customer.findUnique.mockResolvedValue(mockCustomer);
      mockDatabaseService.customer.findFirst.mockResolvedValue(null);
      mockDatabaseService.customer.update.mockResolvedValue({
        ...mockCustomer,
        ...updateDto,
        phones: [
          {
            id: 1,
            phone_number: "1234567890",
            customer_id: 1,
            phone_type: "PHONE",
            created_at: new Date(),
            updated_at: new Date(),
            deleted_at: null,
          },
        ],
      });
    });

    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should throw NotFoundException when customer not found", async () => {
      mockDatabaseService.customer.findUnique.mockResolvedValue(null);
      await expect(service.update(999, updateDto, user)).rejects.toThrow(NotFoundException);
    });

    it("should throw ConflictException when new email already exists", async () => {
      mockDatabaseService.customer.findFirst.mockResolvedValue({ id: 2, email: updateDto.email });
      await expect(service.update(1, updateDto, user)).rejects.toThrow("This email is already in use");
    });
  });

  describe("remove", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should soft delete a customer", async () => {
      const result = await service.remove(1, user);
      expect(result).toEqual({
        statusCode: 200,
        message: "Customer deleted successfully",
      });
    });

    it("should throw NotFoundException when customer not found", async () => {
      mockDatabaseService.customer.findUnique.mockReturnValue(null);
      await expect(service.remove(999, user)).rejects.toThrow(new HttpException("Customer not found", 404));
    });
  });

  describe("findTutorialStatus", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should return tutorial status", async () => {
      mockDatabaseService.customerTutorial.findMany.mockReturnValue([{ id: 1, is_finished: true }]);

      const result = await service.findTutorialStatus({ customer_id: "1" }, user);
      expect(result).toHaveLength(1);
    });
  });

  describe("createWithCountries", () => {
    const createDto: CreateCustomerCountriesDto = {
      customer: {
        first_name: "John",
        last_name: "Doe",
        email: "<EMAIL>",
        salutation: "Mr",
        phones: ["98124181"],
        user_id: 1,
      },
      company: {
        name: "Test Company",
        address: {
          additional_address: "Test address",
          address_line: "Test address line",
          city: "Test city",
          country_code: "1251734",
          place_id: "Place id test",
          street_and_number: "street test number",
          zip_code: "1251361",
        },
        customer_id: 1,
        lucid: "124125",
        partner_id: 125135,
        tin: "1325125",
        vat: null,
        contact: {
          company_id: 1,
        },
      },
      service_type: "EU_LICENSE",
      country_codes: ["FR"],
    };

    it("should throw ConflictException when email already exists", async () => {
      mockDatabaseService.customer.findFirst.mockResolvedValue({
        id: 1,
        email: "<EMAIL>",
      });

      await expect(service.createWithCountries(createDto)).rejects.toThrow(ConflictException);
    });
  });
});
