import { CompanyModule } from "@/company/company.module";
import { CouponModule } from "@/coupon/coupon.module";
import { CustomerIoModule } from "@/customer-io/customer-io.module";
import { HttpApiModule } from "@/http/http.module";
import { MondayService } from "@/integration/monday.service";
import { Module } from "@nestjs/common";
import { CustomerController } from "./customer.controller";
import { CustomerService } from "./customer.service";
@Module({
  imports: [HttpApiModule, CustomerIoModule, CompanyModule, CouponModule],
  providers: [CustomerService, MondayService],
  controllers: [CustomerController],
  exports: [CustomerService],
})
export class CustomerModule {}
