export function formatDateToDDMMYYYY(dateInput?: string | Date | null): string | undefined {
  if (!dateInput) return undefined;

  const date = new Date(dateInput);
  if (isNaN(date.getTime())) return undefined;

  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
}

export function formatDateToUnix(date?: Date) {
  if (!date) return undefined;

  return Math.floor(new Date(date).getTime() / 1000);
}
