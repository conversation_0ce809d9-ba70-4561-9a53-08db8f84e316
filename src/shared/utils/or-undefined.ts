export function orUndefined<T>(value: T | undefined): T | undefined {
  if (value === undefined) return undefined;

  if (Array.isArray(value)) {
    return value.length ? value : undefined;
  }

  if (typeof value === "object" && value !== null && Object.keys(value).length === 0) return undefined;

  if (typeof value === "object" && value === null) return undefined;

  if (typeof value === "string" && !value.length) return undefined;

  return value;
}
