<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">

  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>5a387515-51bd-4efc-a5a0-1e5804245e79</title>
    <style type="text/css">
      * { margin: 0; padding: 0; text-indent: 0; font-family: Arial, sans-serif; } p { color: black; font-style: normal;
      font-weight: normal; text-decoration: none; font-size: 8pt; margin: 0pt; } .a, a { color: black; font-family:
      Arial, sans-serif; font-style: normal; font-weight: normal; text-decoration: none; font-size: 8pt; } h1 { color:
      black; font-family: Arial, sans-serif; font-style: normal; font-weight: bold; text-decoration: none; font-size:
      15.5pt; } .s2 { color: black; font-family: Arial, sans-serif; font-style: normal; font-weight: bold;
      text-decoration: none; font-size: 12pt; } h3 { color: black; font-family: Arial, sans-serif; font-style: normal;
      font-weight: bold; text-decoration: none; font-size: 8pt; } .s4 { color: black; font-family: Arial, sans-serif;
      font-style: normal; font-weight: bold; text-decoration: none; font-size: 7.5pt; } .s5 { color: black; font-family:
      Arial, sans-serif; font-style: normal; font-weight: normal; text-decoration: none; font-size: 7.5pt; }
    </style>
  </head>

  <body style="background: #fff;">
    <p style="padding-left: 5pt;text-indent: 0pt;">
      <span>
        <table border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 24px">
          <tr>
            <td><img
                width="339"
                height="76"
                src="data:image/jpg;base64,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"
              />
            </td>
          </tr>
        </table>
      </span>
    </p>
    <div>
      <a href="http://www.lizenzero.de/" class="a" target="_blank" style="display: block;">
        <EMAIL>
      </a>
      <a href="http://www.lizenzero.de/" target="_blank" style="display: block;">
        www.lizenzero.de
      </a>
    </div>

    <table style="width: 100%; margin: 24px 0;">
      <tr>
        <td>
          <div>
            <p>{{billing.full_name}}</p>
            <p>{{billing.street_and_number}}, {{billing.city}}</p>
            <p>{{billing.zip_code}}</p>
            <p>{{billing.country}}</p>
          </div>
        </td>
        <td>
          <div style="text-align: right;">
            <p>Customer No.: {{customer.id}}</p>
            {{#if tax_id}}
              <p>Your Tax ID: {{tax_id}}</p>
            {{/if}}
            {{#if vat_id}}
              <p>Your VAT ID: {{vat_id}}</p>
            {{/if}}
            <p>Order No.: {{order_id}}</p>
            <p>Date: {{created_at}}</p>
            {{#if service_period}}
              <p>Service period: {{service_period}}</p>
            {{/if}}
          </div>
        </td>
      </tr>
    </table>

    <p style="font-size: 14px; margin: 32px 0">
      Based on the service agreement in place between our companies and the statement of services, we are issuing you
      with an invoice/a credit note:
    </p>

    {{! <h1 style="font-size: 20px; margin: 12px 0">
    Packaging licence for sales packaging in the dual system Interseroh+
  </h1> }}

    <p style="font-size: 15px; font-weight: bold; margin-bottom: 12px;">
      Invoice Nr.
      {{order_id}}
      {{! <span style=" color: #F00;">| Specify Transaction-Nr. 1284515377 on Payment</span> }}
    </p>
    <table
      cellpadding="10"
      cellspacing="0"
      style="width: 100%; text-align: left;border-collapse: collapse; color: #0f0f0f;"
    >
      <tr style="font-size: 14px;">
        <th style="border-bottom: 1px solid #0f0f0f; padding: 8px 4px; font-weight: medium;">Pos</th>
        <th style="border-bottom: 1px solid #0f0f0f; padding: 8px 4px; font-weight: medium;">Service</th>
        <th style="border-bottom: 1px solid #0f0f0f; padding: 8px 4px; font-weight: medium;">Description</th>
        <th style="border-bottom: 1px solid #0f0f0f; padding: 8px 4px; font-weight: medium;">Contract Number</th>
        <th style="border-bottom: 1px solid #0f0f0f; padding: 8px 4px; font-weight: medium;">Amount</th>
      </tr>
      {{#each items}}
        <tr style="font-size: 12px;">
          <td style="border-bottom: 1px solid #dbdbdb; padding: 12px 4px;">{{index}}</td>
          <td style="border-bottom: 1px solid #dbdbdb; padding: 12px 4px;">{{service}}</td>
          <td style="border-bottom: 1px solid #dbdbdb; padding: 12px 4px;">{{breaklines description}}</td>
          <td style="border-bottom: 1px solid #dbdbdb; padding: 12px 4px;">{{contract_number}}</td>
          <td style="border-bottom: 1px solid #dbdbdb; padding: 12px 4px;">€{{amount}}</td>
        </tr>
      {{/each}}
      <tr style="font-size: 12px;">
        <td colspan="3" style="border-bottom: 1px solid #dbdbdb; padding: 8px 4px;"></td>
        <td colspan="1" style="border-bottom: 1px solid #dbdbdb; padding: 8px 4px;">Total costs net:</td>
        <td style="border-bottom: 1px solid #dbdbdb; padding: 4px;">€{{subtotal}}</td>
      </tr>
      {{#if coupon_code}}
        <tr style="font-size: 12px;">
          <td colspan="3" style="border-bottom: 1px solid #dbdbdb; padding: 8px 4px;"></td>
          <td colspan="1" style="border-bottom: 1px solid #dbdbdb; padding: 8px 4px;">- Coupon({{coupon_code}}):</td>
          <td style="border-bottom: 1px solid #dbdbdb; padding: 4px;">€{{coupon_value}}</td>
        </tr>
      {{/if}}
      {{#if vat_value}}
        <tr style="font-size: 12px;">
          <td colspan="3" style="border-bottom: 1px solid #dbdbdb; padding: 8px 4px;"></td>
          <td colspan="1" style="border-bottom: 1px solid #dbdbdb; padding: 8px 4px;">plus.
            {{vat_percentage}}% VAT:</td>
          <td style="border-bottom: 1px solid #dbdbdb; padding: 4px;">€{{vat_value}}</td>
        </tr>
      {{/if}}
      <tr style="font-size: 12px; font-weight: bold;">
        <td colspan="3" style="border-bottom: 1px solid #dbdbdb; padding: 8px 4px;"></td>
        <td colspan="1" style="border-bottom: 1px solid #dbdbdb; padding: 8px 4px;">Total costs:</td>
        <td style="border-bottom: 1px solid #dbdbdb; padding: 4px;">€{{total}}</td>
      </tr>
    </table>
    {{#if vat_value}}
      <p style="margin: 16px 0">
        Please note: the recipient of the service is responsible for the tax.
      </p>
    {{/if}}
    {{#if (gt total 0)}}
      <p style="margin: 16px 0">
        Selected payment method:
        {{payment_method_name}}
      </p>
    {{/if}}
    <p style="margin: 16px 0">
      <span style="color: #E15041;">Attention new bank details!</span>
      Please transfer to the following IBAN
      <b>DE17 3704 0044 0179 4353 01</b>.
    </p>
    <div style="margin-top: 48px; text-align: center;">
      <p style="text-indent: 0pt;text-align: left;"><span>
          <table border="0" cellspacing="0" cellpadding="0" style="margin: 0 auto;">
            <tr>
              <td><img
                  width="120"
                  height="26"
                  src="data:image/jpg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAAaAHgDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9UqDXzf8As5anpnwo+Hni3WPFXx7g+JWkprB83XNRu1EGmsQiiAu0j7CSynZuCgsNqjJLepaJ8fPhx4l8azeENK8a6Lf+JYg27TYLxGlJUEuq4OGZQrFlGSu05Awa3nSlGTUdUutn/kYxqxkk5aN9Lr/M76ivmj4Zftr+F/iF+0B4s8CprOgrodp9ji0DUobvc+rzyopkROcNtclcL6V6l4r/AGifhh4F8Ur4b8QePNB0fXOA9nd3yI8OVDDzcnEeVII3kZBBGc0SoVYS5XF3tf5BGtTkuZSVr2PRaK5nxd8TfCPgCewh8S+JdL0GS/WWS1Go3aQecsShpWXcRkIpBY9ADk1zXiX9pb4W+D9I0fU9a8daNp1prFtHe2HnXAElxA4OyVY/v7Dg/NjHHWoVOcrcsW7+TLc4R3a+89LornT8R/Ci+DB4uPibSR4V8sS/22b6IWe0tsB87ds+98vXrx1rM+HPxq8CfF1LpvBvivTPELWv+visrgNLEM4DMhwwUnOGIwcHBpckrN2dkHPG6V9WdrRXn/hn9oH4beNNa0/SPD/jfRNb1PUGmW2ttPvEneQxIJJPuk4wpB5x7VW0L9pL4WeKPFo8MaT8QPD+oa4zBI7S3v42Mrn+CNs7ZG/2VJPXjg1Xsqn8r+5/5C9pD+Zfej0mivlk/tinw1+1t8Rfh34yvfD3h7wJ4a0WLULfVbnfFdPM6WTeWztIVfP2mTCqgY7V64OffrHxto3jjwBP4h8MaxbaxpdxaTPb39hMJEJVWBww6MCCCOoIIOCKxxnNgqEq843UYuWnVKLla+17IdGcK8+SL1vb8bfqdRXzpcSvJcySM7NIXLFycknPXNXfDcixeIdMd2CqLmMlieB8w5qvqdhPpt/Pb3EZjlRyCCOvPUeo96/jHjLiqpxdgcPio0HTjSnKL97mV5RjJNuys9Gl3s7PofpGXYFYCrKDldySe1tm0fQiHKL64or5ziVmkQRgmQnCheue2KK/RMP4uTrRbjl0nb+WTf8A7jPInkCi9ay+a/4J8QeH/wDlHt8d/wDseIv/AEdZ16dqvw+8OeA/jT+xtfeHtGs9GvNV0yEahNZQrE12wghy8u0De582Tc55bdyTX3hH8IPAkPh6/wBATwT4dTQr+cXV5pi6VALa5mGCJJItm13yqncQT8o9Kv3Hw98K3l3od1P4a0ee60JQmkzyWETPp64AxbsVzEMKowmPuj0r+o5Y9Nuydm5fjFL8LXPzxYJpK7Wlvwk3+tj4R/Z50n4a+Bv26/i9omv6b4Z0O5jvrX/hF7O/tIIRFMXGxbMMoCSN5kZVY8McjAODjxD4TxwSfDn436d8QvH/AIS8J+JbjUbtfEGn+ItDS91i7kAJLWshnjDSCYSlVjDFZAGJAZa/UfWvhT4J1nxtZeJNQ8HaBfeIopEmj1e50uCS7R48eWwmKlwU2rtOcjAxjFR+K/g94C8W+J7bV9c8EeHNZ1ZsA3+oaTbzzkKPl/eOhbjtzxTWOje7T1Ue28e1+j/PUTwctk1o332l3t2/I/Pf40+C7LV9H/Yr8K6nq114q0LULuXTjeXFrJp8l3p8t1YpGnl7t8Y8h1QENkqAQea6D48+BdN8DftMX7/DL4qeDfBeuWXh630u98N+PYTFZ21msMKxxWs1xA8Lq6CMlVbeD5nzEM4X9Btd8DeG/FOpaVqOteH9L1fUNIl8/Tru/so55bKTKtvhdlJjbKIcqQcqvoKx/iD8LfBnj6S1ufE/hHQvEdzb/u4ZtW02G6eNCclVMikgE84FTHH/AA3TslK+3V32en5DlgtJWavpbforb7/mfl18TPGus/EX9h7wRIvhnTPCvhXRPGs2nakmiwubOXEQaO527ixUmadWIchnUYIJVR9A/CPRtCvv20/CWq6Z8UvD/iXWrbw/LHPY+AfDvkabPZCOZFS5mguJERkbyiBIP4LdepSvt5PBnh+PwqfDKaFpi+HPINt/Y62cYs/KPWPycbNv+zjFZXw++G3hH4fxXreF/C2i+G2u2AuDpGnQ2pm2k7d/lqN2NzYz0yfWieOjKEoqNvitt9q297vp/Vgjg5RnGTlfbv07W0PzW/Y3+C9jrn7IPxh8faRpbXvxCij1LSNOuBueSGD7BGZFgRf+WjrPKoIBJO0DHOeT8Xaj4P1L9hT4NaR4Pk0yX4qJ4pZjZ6W0Ta2sjvdruIj/AHw3sLXb64hx91cfrR4R8D+HPAGnSad4X8P6X4b0+WUzyWmkWUdrE8hUKXKRqAWIVRnGcKPSsHRfhB4D0TxrP4h07wT4dsNf3NN/atrpMEd15kgYSP5qoG3NubJzk5Oeta/2knUlNp73WvZWs/LXoR9QapxgmtrPTzvdeZ8e+FPAHh/4h/8ABTv4taf4v0LTfEVtB4XtrgW2o2qXEKzfZ9MTeEcEZ2u4BxxuNdH/AMEmB5v7N3iNH+df+EquV2nkY+yWnH619fWvgbw3YeKr3xPbeH9Lt/Et7CLe61mKyjW8njAQBHmC72UCOPgkj5F9BR4R8D+HPAGmyaf4Y8P6X4csJZjO9rpFlHaxPIVClykagFiFUZxnCj0rkq4tVaTpW6RXl7t7/ff/ADOinhXTqKpfrL8bW+6xS8ReFtIg0LUpotOt45Ut5HVljA2kKTkV5ZF4q1iCNUTU7kKowAZCcV7y6iRGVgGVhggjIIrGh8P6Wk6sum2ispyCIFBB/Kvw3ingvEZniqNfKMQsKlFxkoqUea8rp/u3BO2q1ufYYHMoUYSjiIc+t1eztp53NsDiiiiv2E+fP//Z"
                />
              </td>
            </tr>
          </table>
        </span>
      </p>
      <p style="text-indent: 0pt;text-align: left;"><br /></p>
      <p style="text-indent: 0pt;text-align: center;"><a href="http://www.lizenzero.de/" class="s4" target="_blank">Our
          standard terms and conditions of business apply: for details, please visit www.lizenzero.de.</a></p>
      <p class="s5" style="text-indent: 0pt;text-align: center;">Interseroh+ GmbH | lizenzero.de I Stollwerckstraße 9a |
        51149 Cologne |</p>
      <p class="s5" style="padding-left: 75pt;text-indent: 15pt;line-height: 109%;text-align: left;">Local Court, HRB
        104034, VAT ID: DE345747730 | Directors: Michael Bürstner, Frank Kurrat | Bank details: Commerzbank AG | IBAN:
        DE17 3704 0044 0179 4353 01 | SWIFT/BIC: COBADEFFXXX</p>
      <p class="s5" style="text-indent: 0pt;line-height: 9pt;text-align: center;">An Interzero company</p>
    </div>
  </body>

</html>