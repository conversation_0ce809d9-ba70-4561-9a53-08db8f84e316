import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { ROLES_KEY } from "./role.decorator";
import { Role } from "./role.enum";
import { PUBLIC_ROUTE_KEY } from "./public-route.decorator";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE } from "./const";

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    return true;
    const isPublicRoute = this.reflector.get(PUBLIC_ROUTE_KEY, context.getHandler());

    if (isPublicRoute) return true;

    const request = context.switchToHttp().getRequest();

    const requestApiKey = request.headers[HEADER_SYSTEM_API_KEY];

    if (requestApiKey !== process.env.SYSTEM_API_KEY) {
      throw new UnauthorizedException();
    }

    const userRole = request.headers[HEADER_USER_ROLE];

    if (userRole === Role.SYSTEM) return true;

    const userId = request.headers[HEADER_USER_ID];

    if (!userId) throw new UnauthorizedException();

    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles || !requiredRoles.length) return true;

    const isAuthorized = requiredRoles.includes(userRole);

    return isAuthorized;
  }
}
