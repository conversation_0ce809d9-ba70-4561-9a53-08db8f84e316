export enum Role {
  SYSTEM = "system",
  CUSTOMER = "customer",
  ADMIN = "admin",
  CLERK = "clerk",
  SUPER_ADMIN = "super_admin",
  MARKETING_MANAGER = "marketing_manager",
  BROKER = "broker",
  BROKER_MANAGER = "broker_manager",
  PARTNER = "partner",
}

export type RoleType =
  | "system"
  | "customer"
  | "admin"
  | "clerk"
  | "super_admin"
  | "marketing_manager"
  | "broker"
  | "partner"
  | "broker_manager";
