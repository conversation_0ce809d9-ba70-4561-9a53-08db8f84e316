import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { DatabaseService } from "../database/database.service";
import { ReportSetRhythm } from "@prisma/client";

jest.setTimeout(30000);

describe("ReportSetFrequencyController (e2e)", () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validApi<PERSON>ey,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockFrequency = {
    open: {
      day: 1,
      month: "January",
    },
    deadline: {
      day: 15,
      month: "January",
    },
  };

  const mockReportSetFrequency = {
    id: 1,
    rhythm: ReportSetRhythm.ANNUALLY,
    frequency: JSON.stringify(mockFrequency),
    packaging_service_id: 123,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockReportSetFrequencies = [
    mockReportSetFrequency,
    {
      id: 2,
      rhythm: ReportSetRhythm.MONTHLY,
      frequency: JSON.stringify({
        open: { day: 1 },
        deadline: { day: 10 },
      }),
      packaging_service_id: 456,
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
      deleted_at: null,
    },
  ];

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DatabaseService)
      .useValue({
        reportSetFrequency: {
          create: jest.fn().mockResolvedValue(mockReportSetFrequency),
          findMany: jest.fn().mockResolvedValue(mockReportSetFrequencies),
          findUnique: jest.fn().mockResolvedValue(mockReportSetFrequency),
          update: jest.fn().mockImplementation((params) => {
            return Promise.resolve({
              ...mockReportSetFrequency,
              ...params.data,
            });
          }),
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/report-set-frequencies (GET)", () => {
    it("should return report set frequencies when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/report-set-frequencies")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.reportSetFrequency.findMany).toHaveBeenCalled();
          expect(response.body).toEqual(mockReportSetFrequencies);
        });
    });

    it("should return report set frequencies when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/report-set-frequencies").set(adminHeaders).expect(200);
    });

    it("should return report set frequencies when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/report-set-frequencies").set(clerkHeaders).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-frequencies").set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-frequencies").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get("/report-set-frequencies").expect(401);
    });
  });

  describe("/report-set-frequencies/:id (GET)", () => {
    it("should return a single report set frequency when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/report-set-frequencies/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.reportSetFrequency.findUnique).toHaveBeenCalledWith({
            where: { id: 1, deleted_at: null },
          });
          expect(response.body).toEqual(mockReportSetFrequency);
        });
    });

    it("should reject when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-frequencies/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).get("/report-set-frequencies/1").expect(401);
    });
  });

  describe("/report-set-frequencies (POST)", () => {
    const createReportSetFrequencyDto = {
      rhythm: ReportSetRhythm.ANNUALLY,
      frequency: mockFrequency,
      packaging_service_id: 123,
    };

    it("should create a new report set frequency when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .post("/report-set-frequencies")
        .set(authHeaders)
        .send(createReportSetFrequencyDto)
        .expect(201)
        .then((response) => {
          expect(databaseService.reportSetFrequency.create).toHaveBeenCalledWith({
            data: {
              rhythm: createReportSetFrequencyDto.rhythm,
              frequency: JSON.stringify(createReportSetFrequencyDto.frequency),
              packaging_service_id: createReportSetFrequencyDto.packaging_service_id,
            },
          });
          expect(response.body).toEqual(mockReportSetFrequency);
        });
    });

    it("should reject creation when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/report-set-frequencies")
        .set(invalidApiKeyHeaders)
        .send(createReportSetFrequencyDto)
        .expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer()).post("/report-set-frequencies").send(createReportSetFrequencyDto).expect(401);
    });
  });

  describe("/report-set-frequencies/:id (PUT)", () => {
    const updateReportSetFrequencyDto = {
      rhythm: ReportSetRhythm.QUARTERLY,
      frequency: {
        open: {
          option: "first",
          weekDay: "Monday",
        },
        deadline: {
          option: "last",
          weekDay: "Friday",
        },
      },
    };

    it("should update a report set frequency when authenticated with valid API key", () => {
      const expectedResponse = {
        ...mockReportSetFrequency,
        rhythm: updateReportSetFrequencyDto.rhythm,
        frequency: JSON.stringify(updateReportSetFrequencyDto.frequency),
      };

      return request(app.getHttpServer())
        .put("/report-set-frequencies/1")
        .set(authHeaders)
        .send(updateReportSetFrequencyDto)
        .expect(200)
        .then((response) => {
          expect(databaseService.reportSetFrequency.update).toHaveBeenCalledWith({
            where: { id: 1 },
            data: {
              ...updateReportSetFrequencyDto,
              frequency: JSON.stringify(updateReportSetFrequencyDto.frequency),
            },
          });
          expect(response.body).toEqual(expectedResponse);
        });
    });

    it("should reject update when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .put("/report-set-frequencies/1")
        .set(invalidApiKeyHeaders)
        .send(updateReportSetFrequencyDto)
        .expect(401);
    });

    it("should reject update when not authenticated", () => {
      return request(app.getHttpServer())
        .put("/report-set-frequencies/1")
        .send(updateReportSetFrequencyDto)
        .expect(401);
    });
  });

  describe("/report-set-frequencies/:id (DELETE)", () => {
    it("should reject deletion when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/report-set-frequencies/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/report-set-frequencies/1").expect(401);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/report-set-frequencies").set(systemHeaders).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/report-set-frequencies").set(invalidSystemHeaders).expect(401);
    });
  });
});
