import { ApiProperty } from "@nestjs/swagger";
import { ReportSetRhythm } from "@prisma/client";

export class CreateReportSetFrequencyDto {
  @ApiProperty({
    required: true,
    description: "ID of the associated packaging service",
  })
  packaging_service_id: number;

  @ApiProperty({
    required: true,
    enum: ReportSetRhythm,
    description: "Rhythm of the report set frequency",
  })
  rhythm: ReportSetRhythm;

  @ApiProperty({
    required: true,
    description: "Json config of the report set frequency",
  })
  frequency: Frequency;
}

export type Frequency = AnnualyFrequency | QuarterlyFrequency | MonthlyFrequency;

export interface AnnualyFrequency {
  deadline: {
    day: number;
    month: string;
  };
  open: {
    day: number;
    month: string;
  };
}

export interface QuarterlyFrequency {
  deadline: {
    option: string;
    weekDay: string;
  };
  open: {
    option: string;
    weekDay: string;
  };
}

export interface MonthlyFrequency {
  deadline: {
    day: number;
  };
  open: {
    day: number;
  };
}
