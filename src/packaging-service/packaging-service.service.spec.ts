import { Test, TestingModule } from "@nestjs/testing";
import { PackagingServiceService } from "./packaging-service.service";
import { DatabaseService } from "../database/database.service";
import { NotFoundException, BadRequestException } from "@nestjs/common";
import { UpdatePackagingServiceDto } from "./dto/packaging-service-update.dto";

describe("PackagingServiceService", () => {
  let service: PackagingServiceService;
  let databaseServiceMock: any;

  beforeEach(async () => {
    databaseServiceMock = {
      $transaction: jest.fn((callback): any => callback(databaseServiceMock)),
      packagingService: {
        create: jest.fn(),
        findMany: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [PackagingServiceService, { provide: DatabaseService, useValue: databaseServiceMock }],
    }).compile();

    service = module.get<PackagingServiceService>(PackagingServiceService);
  });

  describe("create", () => {
    it("should create a packaging service successfully", async () => {
      const mockCreateDto = {
        name: "Test Packaging Service",
        description: "Description for test packaging service",
        country_id: 1,
      };

      const mockResult = { id: 1, ...mockCreateDto };
      databaseServiceMock.packagingService.create.mockReturnValueOnce(mockResult);

      const result = await service.create(mockCreateDto);

      expect(result).toEqual(mockResult);
      expect(databaseServiceMock.packagingService.create).toHaveBeenCalledWith({
        data: mockCreateDto,
      });
    });
  });

  describe("findAll", () => {
    it("should return all packaging services", async () => {
      const mockServices = [
        { id: 1, name: "Service 1", description: "Service 1 Description" },
        { id: 2, name: "Service 2", description: "Service 2 Description" },
      ];

      databaseServiceMock.packagingService.findMany.mockReturnValueOnce(mockServices);

      const result = await service.findAll({});
      expect(result).toEqual(mockServices);
    });

    it("should return packaging services filtered by countryId", async () => {
      const countryId = 1;
      const mockServices = [{ id: 1, name: "Service 1", description: "Service 1 Description" }];

      databaseServiceMock.packagingService.findMany.mockReturnValueOnce(mockServices);

      const result = await service.findAll({ countryId });
      expect(result).toEqual(mockServices);
      expect(databaseServiceMock.packagingService.findMany).toHaveBeenCalledWith({
        where: { country_id: countryId, deleted_at: null },
      });
    });
  });

  describe("findOne", () => {
    it("should return the packaging service by id", async () => {
      const mockService = { id: 1, name: "Test Packaging", description: "Test Description" };

      databaseServiceMock.packagingService.findUnique.mockReturnValueOnce(mockService);

      const result = await service.findOne(1);
      expect(result).toEqual(mockService);
      expect(databaseServiceMock.packagingService.findUnique).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
      });
    });

    it("should throw NotFoundException if packaging service is not found", async () => {
      databaseServiceMock.packagingService.findUnique.mockReturnValueOnce(null);

      await expect(service.findOne(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe("update", () => {
    it("should update a packaging service successfully", async () => {
      const mockService = { id: 1, name: "Updated Service", description: "Updated Description" };
      const mockUpdateDto: UpdatePackagingServiceDto = {
        name: "Updated Service",
        description: "Updated Description",
        status: "active",
      };

      databaseServiceMock.packagingService.findUnique.mockReturnValueOnce({ id: 1, name: "Old Service" });
      databaseServiceMock.packagingService.update.mockReturnValueOnce(mockService);

      const result = await service.update(1, mockUpdateDto);
      expect(result).toEqual(mockService);
      expect(databaseServiceMock.packagingService.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: mockUpdateDto,
      });
    });

    it("should throw NotFoundException if packaging service to update is not found", async () => {
      databaseServiceMock.packagingService.findUnique.mockReturnValueOnce(null);

      await expect(service.update(999, { name: "Updated", status: "active" })).rejects.toThrow(NotFoundException);
    });
  });

  describe("remove", () => {
    it("should throw BadRequestException if ID is invalid", async () => {
      await expect(service.remove(NaN)).rejects.toThrow(BadRequestException);
    });

    it("should throw NotFoundException if packaging service is not found", async () => {
      databaseServiceMock.packagingService.findUnique.mockReturnValueOnce(null);

      await expect(service.remove(999)).rejects.toThrow(NotFoundException);
    });
  });
});
