import { ApiProperty } from "@nestjs/swagger";
import { LicenseRequiredInformationStatus } from "@prisma/client";

export class UpdateLicenseRequiredInformationDto {
  @ApiProperty({
    description: "The status of required information",
    enum: LicenseRequiredInformationStatus,
    example: LicenseRequiredInformationStatus.DONE,
  })
  status?: LicenseRequiredInformationStatus;

  @ApiProperty({
    description: "The answer provided",
    example: "12345678",
    required: false,
  })
  answer?: string;
}
