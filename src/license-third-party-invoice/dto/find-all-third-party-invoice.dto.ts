import { ApiProperty } from "@nestjs/swagger";

export class FindAllThirdPartyInvoiceDto {
  @ApiProperty({
    description: "The license ID",
  })
  license_id: number;

  @ApiProperty({
    description: "The from date of the invoice",
    example: "2025-01-01",
  })
  from_date: string;

  @ApiProperty({
    description: "The to date of the invoice",
    example: "2025-20-01",
  })
  to_date: string;
}
