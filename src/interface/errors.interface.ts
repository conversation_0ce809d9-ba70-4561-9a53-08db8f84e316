export enum UstIdError {
  Code200 = "Die angefragte USt-IdNr. ist gültig.",
  Code201 = "Die angefragte USt-IdNr. ist ungültig.",
  Code202 = "Die angefragte USt-IdNr. ist ungültig. Sie ist nicht in der Unternehmerdatei des betreffenden EU-Mitgliedstaates registriert.",
  Code203 = "Die angefragte USt-IdNr. ist ungültig. Sie ist erst ab dem ... gültig (siehe Feld 'Gueltig_ab').",
  Code204 = "Die angefragte USt-IdNr. ist ungültig. Sie war im Zeitraum von ... bis ... gültig (siehe Feld 'Gueltig_ab' und 'Gueltig_bis').",
  Code205 = "Ihre Anfrage kann derzeit durch den angefragten EU-Mitgliedstaat oder aus anderen Gründen nicht beantwortet werden. Bitte versuchen Sie es später noch einmal. Bei wiederholten Problemen wenden Sie sich bitte an das Bundeszentralamt für Steuern - Dienstsitz Saarlouis.",
  Code206 = "Ihre deutsche USt-IdNr. ist ungültig. Eine Bestätigungsanfrage ist daher nicht möglich. Den Grund hierfür können Sie beim Bundeszentralamt für Steuern - Dienstsitz Saarlouis - erfragen.",
  Code208 = "Für die von Ihnen angefragte USt-IdNr. läuft gerade eine Anfrage von einem anderen Nutzer. Eine Bearbeitung ist daher nicht möglich. Bitte versuchen Sie es später noch einmal.",
  Code209 = "Die angefragte USt-IdNr. ist ungültig. Sie entspricht nicht dem Aufbau der für diesen EU-Mitgliedstaat gilt. (Aufbau der USt-IdNr. aller EU-Länder)",
  Code210 = "Die angefragte USt-IdNr. ist ungültig. Sie entspricht nicht den Prüfziffernregeln die für diesen EU-Mitgliedstaat gelten.",
  Code211 = "Die angefragte USt-IdNr. ist ungültig. Sie enthält unzulässige Zeichen (wie z.B. Leerzeichen oder Punkt oder Bindestrich usw.).",
  Code212 = "Die angefragte USt-IdNr. ist ungültig. Sie enthält ein unzulässiges Länderkennzeichen.",
  Code213 = "Sie sind nicht zur Abfrage einer deutschen USt-IdNr. berechtigt.",
  Code214 = "Ihre deutsche USt-IdNr. ist fehlerhaft. Sie beginnt mit 'DE' gefolgt von 9 Ziffern.",
  Code215 = "Ihre Anfrage enthält nicht alle notwendigen Angaben für eine einfache Bestätigungsanfrage (Ihre deutsche USt-IdNr. und die ausl. USt-IdNr.).Ihre Anfrage kann deshalb nicht bearbeitet werden.",
  Code216 = "Ihre Anfrage enthält nicht alle notwendigen Angaben für eine qualifizierte Bestätigungsanfrage (Ihre deutsche USt-IdNr., die ausl. USt-IdNr., Firmenname einschl. Rechtsform und Ort).Es wurde eine einfache Bestätigungsanfrage durchgeführt mit folgenden Ergebnis: Die angefragte USt-IdNr. ist gültig.",
  Code217 = "Bei der Verarbeitung der Daten aus dem angefragten EU-Mitgliedstaat ist ein Fehler aufgetreten. Ihre Anfrage kann deshalb nicht bearbeitet werden.",
  Code218 = "Eine qualifizierte Bestätigung ist zur Zeit nicht möglich. Es wurde eine einfache Bestätigungsanfrage mit folgendem Ergebnis durchgeführt:Die angefragte USt-IdNr. ist gültig.",
  Code219 = "Bei der Durchführung der qualifizierten Bestätigungsanfrage ist ein Fehler aufgetreten. Es wurde eine einfache Bestätigungsanfrage mit folgendem Ergebnis durchgeführt:Die angefragte USt-IdNr. ist gültig.",
  Code221 = "Die Anfragedaten enthalten nicht alle notwendigen Parameter oder einen ungültigen Datentyp. Weitere Informationen erhalten Sie bei den Hinweisen zum Schnittstelle - Aufruf.",
  Code223 = "Die angefragte USt-IdNr. ist gültig. Die Druckfunktion steht nicht mehr zur Verfügung, da der Nachweis gem. UStAE zu § 18e.1 zu führen ist.",
  Code999 = "Eine Bearbeitung Ihrer Anfrage ist zurzeit nicht möglich. Bitte versuchen Sie es später noch einmal.",
}

export enum EnUstIdError {
  Code200 = "The requested VAT ID is valid.",
  Code201 = "The requested VAT ID is invalid.",
  Code202 = "The requested VAT ID is invalid. It is not registered in the entrepreneur file of the respective EU member state.",
  Code203 = "The requested VAT ID is invalid. It is only valid from ... (see field 'Valid_from').",
  Code204 = "The requested VAT ID is invalid. It was valid from ... to ... (see fields 'Valid_from' and 'Valid_to').",
  Code205 = "Your request cannot be answered at the moment by the requested EU member state or for other reasons. Please try again later. If you encounter repeated problems, please contact the Federal Central Tax Office - Saarlouis office.",
  Code206 = "Your German VAT ID is invalid. A confirmation request is therefore not possible. You can inquire about the reason at the Federal Central Tax Office - Saarlouis office.",
  Code208 = "There is currently another user making a request for the requested VAT ID. Processing is therefore not possible. Please try again later.",
  Code209 = "The requested VAT ID is invalid. It does not comply with the structure applicable to this EU member state. (Structure of VAT ID for all EU countries)",
  Code210 = "The requested VAT ID is invalid. It does not comply with the check digit rules applicable to this EU member state.",
  Code211 = "The requested VAT ID is invalid. It contains invalid characters (such as spaces or periods or hyphens, etc.).",
  Code212 = "The requested VAT ID is invalid. It contains an invalid country code.",
  Code213 = "You are not authorized to query a German VAT ID.",
  Code214 = "Your German VAT ID is incorrect. It starts with 'DE' followed by 9 digits.",
  Code215 = "Your request does not contain all the necessary information for a simple confirmation request (your German VAT ID and the foreign VAT ID). Therefore, your request cannot be processed.",
  Code216 = "Your request does not contain all the necessary information for a qualified confirmation request (your German VAT ID, the foreign VAT ID, company name including legal form and location). A simple confirmation request was carried out with the following result: The requested VAT ID is valid.",
  Code217 = "An error occurred while processing the data from the requested EU member state. Therefore, your request cannot be processed.",
  Code218 = "A qualified confirmation is currently not possible. A simple confirmation request was carried out with the following result: The requested VAT ID is valid.",
  Code219 = "An error occurred during the execution of the qualified confirmation request. A simple confirmation request was carried out with the following result: The requested VAT ID is valid.",
  Code221 = "The request data does not contain all the necessary parameters or an invalid data type. For more information, please refer to the interface call notes.",
  Code223 = "The requested VAT ID is valid. The print function is no longer available because the proof according to UStAE to § 18e.1 must be provided.",
  Code999 = "Processing your request is currently not possible. Please try again later.",
}
