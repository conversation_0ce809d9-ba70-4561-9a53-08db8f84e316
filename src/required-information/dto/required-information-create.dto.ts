import { ApiProperty } from "@nestjs/swagger";
import { RequiredInformationType } from "@prisma/client";

export class CreateRequiredInformationDto {
  @ApiProperty({
    required: true,
    description: "ID of the country",
  })
  country_id: number;

  @ApiProperty({
    required: true,
    enum: RequiredInformationType,
    description: "Type of the required information",
  })
  type: RequiredInformationType;

  @ApiProperty({
    required: true,
    description: "Name of the required information",
  })
  name: string;

  @ApiProperty({
    required: true,
    description: "Description of the required information",
  })
  description: string;

  @ApiProperty({
    required: false,
    description: "Question for the required information",
  })
  question?: string;

  @ApiProperty({
    required: false,
    description: "ID of the associated file",
  })
  file_id?: string;
}
