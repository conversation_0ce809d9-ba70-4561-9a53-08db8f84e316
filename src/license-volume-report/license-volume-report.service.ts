import { DatabaseService } from "@/database/database.service";
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { LicenseVolumeReportStatus } from "@prisma/client";
import { CreateLicenseVolumeReportDto } from "./dto/create-license-volume-report.dto";
import { DeclineLicenseVolumeReportDto } from "./dto/decline-license-volume-report.dto";
import { UpdateLicenseVolumeReportDto } from "./dto/update-license-volume-report.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

@Injectable()
export class LicenseVolumeReportService {
  constructor(private prisma: DatabaseService) {}

  create(createLicenseVolumeReportDto: CreateLicenseVolumeReportDto) {
    return this.prisma.licenseVolumeReport.create({
      data: createLicenseVolumeReportDto,
    });
  }

  findAll() {
    return this.prisma.licenseVolumeReport.findMany({
      where: { deleted_at: null },
    });
  }

  async findOne(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionVolumeReport(id, user);

    return this.prisma.licenseVolumeReport.findUnique({
      where: { id },
    });
  }

  async update(id: number, updateLicenseVolumeReportDto: UpdateLicenseVolumeReportDto, user: AuthenticatedUser) {
    await this.validatingUserPermissionVolumeReport(id, user);

    return this.prisma.licenseVolumeReport.update({
      where: { id },
      data: updateLicenseVolumeReportDto,
    });
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionVolumeReport(id, user);

    return this.prisma.licenseVolumeReport.update({
      where: { id },
      data: { deleted_at: new Date() },
    });
  }

  async decline(id: number, data: DeclineLicenseVolumeReportDto, user: AuthenticatedUser) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("License Volume Report ID is invalid");
    }

    if (!Array.isArray(data.reason_ids) || !data.reason_ids.length) {
      throw new BadRequestException("At least one reason ID is required");
    }

    const licenseVolumeReport = await this.prisma.licenseVolumeReport.findUnique({
      where: { id: Number(id), deleted_at: null },
    });

    if (!licenseVolumeReport) {
      throw new NotFoundException("License Volume Report not found");
    }

    await this.validatingUserPermissionVolumeReport(id, user);

    return await this.prisma.licenseVolumeReport.update({
      where: { id: Number(id) },
      data: {
        status: LicenseVolumeReportStatus.DECLINED,
        updated_at: new Date(),
        decline: {
          create: {
            title: data.title,
            decline_reasons: {
              create: data.reason_ids.map((reason_id) => ({
                reason_id,
              })),
            },
          },
        },
      },
    });
  }

  async validatingUserPermissionVolumeReport(id: number, user: AuthenticatedUser) {
    const licenseVolumeReport = await this.prisma.licenseVolumeReport.findUnique({
      where: { id },
      include: {
        packaging_service: {
          include: {
            license: {
              include: {
                contract: {
                  include: {
                    customer: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!licenseVolumeReport) {
      throw new NotFoundException("License Volume Report not found");
    }

    const { packaging_service } = licenseVolumeReport;

    if (!packaging_service) {
      throw new NotFoundException("Packaging service not found");
    }

    const { license } = packaging_service;

    if (!license) {
      throw new NotFoundException("License not found");
    }

    const { contract } = license;

    if (!contract) {
      throw new NotFoundException("Contract not found");
    }
    const { customer } = contract;

    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You do not have permission to access this volume report");
    }
  }
}
