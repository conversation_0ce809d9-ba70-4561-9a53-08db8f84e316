import { Test, TestingModule } from "@nestjs/testing";
import { LicenseVolumeReportService } from "./license-volume-report.service";
import { DatabaseService } from "@/database/database.service";
import { CreateLicenseVolumeReportDto } from "./dto/create-license-volume-report.dto";
import { UpdateLicenseVolumeReportDto } from "./dto/update-license-volume-report.dto";
import { LicenseVolumeReportStatus } from "@prisma/client";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

describe("LicenseVolumeReportService", () => {
  let service: LicenseVolumeReportService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    licenseVolumeReport: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LicenseVolumeReportService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<LicenseVolumeReportService>(LicenseVolumeReportService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create a new license volume report", async () => {
      const createDto: CreateLicenseVolumeReportDto = {
        setup_report_set_fraction_id: 1,
        license_packaging_service_id: 1,
        status: LicenseVolumeReportStatus.DECLINED,
        year: 2024,
        interval: "Q1",
        report_table: { data: "test" },
      };

      const expectedResult = {
        id: 1,
        ...createDto,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockDatabaseService.licenseVolumeReport.create.mockResolvedValue(expectedResult);

      const result = await service.create(createDto);

      expect(result).toEqual(expectedResult);
      expect(mockDatabaseService.licenseVolumeReport.create).toHaveBeenCalledWith({
        data: createDto,
      });
    });
  });

  describe("findAll", () => {
    it("should return all non-deleted license volume reports", async () => {
      const expectedReports = [
        {
          id: 1,
          status: LicenseVolumeReportStatus.DECLINED,
          year: 2024,
          interval: "Q1",
        },
        {
          id: 2,
          status: LicenseVolumeReportStatus.NEW,
          year: 2024,
          interval: "Q2",
        },
      ];

      mockDatabaseService.licenseVolumeReport.findMany.mockResolvedValue(expectedReports);

      const result = await service.findAll();

      expect(result).toEqual(expectedReports);
      expect(mockDatabaseService.licenseVolumeReport.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null },
      });
    });
  });

  describe("findOne", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should return a specific license volume report", async () => {
      const expectedReport = {
        id: 1,
        status: LicenseVolumeReportStatus.DECLINED,
        year: 2024,
        interval: "Q1",
      };

      mockDatabaseService.licenseVolumeReport.findUnique.mockResolvedValue(expectedReport);

      jest.spyOn(service, "validatingUserPermissionVolumeReport").mockImplementation(async () => {
        return Promise.resolve();
      });

      const result = await service.findOne(1, user);

      expect(result).toEqual(expectedReport);
      expect(mockDatabaseService.licenseVolumeReport.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
      });
    });

    it("should return null when report is not found", async () => {
      mockDatabaseService.licenseVolumeReport.findUnique.mockResolvedValue(null);

      jest.spyOn(service, "validatingUserPermissionVolumeReport").mockImplementation(async () => {
        return Promise.resolve();
      });

      const result = await service.findOne(999, user);

      expect(result).toBeNull();
      expect(mockDatabaseService.licenseVolumeReport.findUnique).toHaveBeenCalledWith({
        where: { id: 999 },
      });
    });
  });

  describe("update", () => {
    it("should update a license volume report", async () => {
      const updateDto: UpdateLicenseVolumeReportDto = {
        status: LicenseVolumeReportStatus.APPROVED,
        interval: "Q2",
      };

      const user: AuthenticatedUser = {
        email: "<EMAIL>",
        id: "1",
        role: Role.ADMIN,
      };

      const expectedResult = {
        id: 1,
        status: LicenseVolumeReportStatus.APPROVED,
        interval: "Q2",
        year: 2024,
        updated_at: new Date(),
      };

      mockDatabaseService.licenseVolumeReport.update.mockResolvedValue(expectedResult);

      jest.spyOn(service, "validatingUserPermissionVolumeReport").mockImplementation(async () => {
        return Promise.resolve();
      });

      const result = await service.update(1, updateDto, user);

      expect(result).toEqual(expectedResult);
      expect(mockDatabaseService.licenseVolumeReport.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateDto,
      });
    });
  });

  describe("remove", () => {
    it("should soft delete a license volume report", async () => {
      const expectedResult = {
        id: 1,
        deleted_at: expect.any(Date),
      };

      const user: AuthenticatedUser = {
        email: "<EMAIL>",
        id: "1",
        role: Role.ADMIN,
      };

      jest.spyOn(service, "validatingUserPermissionVolumeReport").mockImplementation(async () => {
        return Promise.resolve();
      });

      mockDatabaseService.licenseVolumeReport.update.mockResolvedValue(expectedResult);

      const result = await service.remove(1, user);

      expect(result.deleted_at).toBeDefined();
      expect(mockDatabaseService.licenseVolumeReport.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { deleted_at: expect.any(Date) },
      });
    });
  });
});
