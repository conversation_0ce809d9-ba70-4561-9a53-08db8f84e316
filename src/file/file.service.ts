import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { RequestPresignedUrlDto } from "./dto/request-presigned-url.dto";
import axios from "axios";
import { CreateFileDto } from "./dto/create-file.dto";
import { DatabaseService } from "@/database/database.service";
import * as crypto from "node:crypto";
import { FileType, Prisma } from "@prisma/client";

interface UploadFileParams extends CreateFileDto {
  user: { id: string; role: string };
}

const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB
export const FILE_RELATIONS = [
  "required_information_id",
  "contract_id",
  "certificate_id",
  "license_id",
  "termination_id",
  "general_information_id",
  "third_party_invoice_id",
  "marketing_material_id",
  "partner_contract_id",
  "order_id",
] as const;

@Injectable()
export class FileService {
  constructor(private readonly prisma: DatabaseService) {}

  async uploadFile(data: UploadFileParams, file: Express.Multer.File, tx?: Prisma.TransactionClient) {
    const { size, originalname, mimetype } = file;

    const filename = this.generateFilename(data.type, originalname);

    if (size > MAX_FILE_SIZE) {
      throw new BadRequestException("File size cannot exceed 25MB");
    }

    return this.prisma.$transaction(
      async (internalTransaction) => {
        const transaction = tx || internalTransaction;
        const { uploadUrl, fields } = await this.requestUrl({
          filename,
          fileType: mimetype,
        });

        if (!uploadUrl || !fields) throw new BadRequestException("Presigned url or Fields not found!");

        const formData = new FormData();

        Object.entries(fields).forEach(([key, value]) => {
          if (typeof value === "string" || value instanceof Blob) {
            formData.append(key, value as string | Blob);
          }
        });

        const blob = new Blob([file.buffer]);

        formData.append("file", blob);

        await axios.post(uploadUrl, formData);

        return await transaction.file.create({
          data: {
            user_id: data.user.id,
            size: String(size),
            name: filename,
            extension: mimetype,
            original_name: originalname,
            type: data.type,
            required_information_id: Number(data.required_information_id) || null,
            contract_id: Number(data.contract_id) || null,
            certificate_id: Number(data.certificate_id) || null,
            license_id: Number(data.license_id) || null,
            termination_id: Number(data.termination_id) || null,
            marketing_material_id: Number(data.marketing_material_id) || null,
            order_id: Number(data.order_id) || null,
          },
        });
      },
      {
        timeout: 45000,
        maxWait: 45000,
      }
    );
  }

  async getFile(fileId: string) {
    const file = await this.prisma.file.findUnique({
      where: {
        id: fileId,
      },
    });

    if (!file) throw new NotFoundException("File not found");

    const lambdaDownloadFile = "https://3qthmn2fk2clgkiikb3dzy6o7e0fyttk.lambda-url.us-east-2.on.aws";

    try {
      const response = await axios.get(`${lambdaDownloadFile}?fileName=${file.name}`, {
        responseType: "arraybuffer",
      });

      return { file, buffer: Buffer.from(response.data) };
    } catch (error) {
      console.log(error);
      throw new BadRequestException("Error downloading PDF file");
    }
  }

  async getFileByRelative(relation: (typeof FILE_RELATIONS)[number], relativeId: string) {
    if (!FILE_RELATIONS.includes(relation)) throw new BadRequestException("Invalid relation");

    const file = await this.prisma.file.findFirst({
      where: {
        [relation]: parseInt(relativeId),
      },
    });

    if (!file) throw new NotFoundException("File not found");

    const lambdaDownloadFile = "https://3qthmn2fk2clgkiikb3dzy6o7e0fyttk.lambda-url.us-east-2.on.aws";

    try {
      const response = await axios.get(`${lambdaDownloadFile}?fileName=${file.name}`, {
        responseType: "arraybuffer",
      });

      return { file, buffer: Buffer.from(response.data) };
    } catch (error) {
      console.log(error);
      throw new BadRequestException("Error downloading PDF file");
    }
  }

  async deleteFile(fileId: string, tx?: Prisma.TransactionClient) {
    const client = tx || this.prisma;

    await client.file.update({
      where: {
        id: fileId,
      },
      data: {
        deleted_at: new Date(),
      },
    });
  }

  async requestUrl(requestPresignedUrlDto: RequestPresignedUrlDto) {
    const lambdaRequesPresignedUrl = "https://3pdf2nhm7hl4n37hjo4upqgjme0pdvsd.lambda-url.us-east-2.on.aws/";

    try {
      const response = await axios.post(lambdaRequesPresignedUrl, requestPresignedUrlDto);

      const { data } = response;

      if (!data) throw new BadRequestException("Problems generating url");

      return data;
    } catch (error) {
      console.log(error);
      throw new BadRequestException("Problems generating url");
    }
  }

  generateFilename(docType: FileType, filename: string): string {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");

    const hash = this.generateHash();
    const formatedFilename = `${docType}/${year}/${month}/${day}/${hash}-${filename}`;

    return formatedFilename;
  }

  generateHash() {
    const randomBytes = crypto.randomBytes(8);
    const hash = Array.from(randomBytes, (byte) => (byte % 36).toString(36))
      .join("")
      .toUpperCase()
      .substring(0, 8);
    return hash;
  }
}
