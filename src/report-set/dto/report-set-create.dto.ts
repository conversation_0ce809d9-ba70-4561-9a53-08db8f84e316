import { ApiProperty } from "@nestjs/swagger";
import { ReportSetMode, ReportSetType } from "@prisma/client";

export class CreateReportSetDto {
  @ApiProperty({
    required: true,
    description: "ID of the packaging service",
    example: 1,
  })
  packaging_service_id: number;

  @ApiProperty({
    required: true,
    description: "Name of the report set",
    example: "Report set name",
  })
  name: string;

  @ApiProperty({
    required: true,
    enum: ReportSetMode,
    description: "Mode of the report set",
    example: "ON_PLATAFORM",
  })
  mode: ReportSetMode;

  @ApiProperty({
    required: true,
    enum: ReportSetType,
    description: "Type of the report set",
    example: "FRACTIONS",
  })
  type: ReportSetType;
}
