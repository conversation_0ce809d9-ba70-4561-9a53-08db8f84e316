import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsString, Min } from "class-validator";
import { Type } from "class-transformer";
import { ApiPropertyOptional } from "@nestjs/swagger";
import { PartnerStatus } from "@prisma/client";

export class FindAllPartnersPaginatedDto {
  @ApiPropertyOptional({
    description: "Pagination page",
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({
    description: "Pagination limit",
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: "Search by name",
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: "Start date for filtering created_at",
  })
  @IsOptional()
  @IsString()
  start_date?: string;

  @ApiPropertyOptional({
    description: "End date for filtering created_at",
  })
  @IsOptional()
  @IsString()
  end_date?: string;

  @ApiPropertyOptional({
    example: "NO_UPDATES | IMPROVED_CONTRACT | DENIED_CONTRACT | REQUESTED_COMMISSION | CHANGED_INFORMATION",
    description: "Status for filtering",
  })
  @IsOptional()
  @IsEnum(PartnerStatus)
  status?: PartnerStatus;
}
