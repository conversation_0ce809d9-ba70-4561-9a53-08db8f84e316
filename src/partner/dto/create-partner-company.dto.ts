import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsDateString, IsEmail, IsNotEmpty, IsNumber, IsString, IsUrl } from "class-validator";

export class CreatePartnerCompanyDto {
  @ApiProperty()
  @IsNumber()
  id: string;

  @ApiProperty({
    description: "Name of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: "Industry sector of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  industry_sector: string;

  @ApiProperty({
    description: "Starting date of the company",
    required: true,
  })
  @IsString()
  @IsDateString()
  starting_date: string;

  @ApiProperty({
    description: "Website of the company",
    required: true,
  })
  @IsString()
  @IsUrl()
  website: string;

  @ApiProperty({
    description: "Description of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: "Owner name of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  owner_name: string;

  @ApiProperty({
    description: "Country code of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  country_code: string;

  @ApiProperty({
    description: "Federal state of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  federal_state: string;

  @ApiProperty({
    description: "City of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({
    description: "Zip code of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  zip_code: string;

  @ApiProperty({
    description: "Street and number of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  street_and_number: string;

  @ApiPropertyOptional({
    description: "Additional address of the company",
  })
  @IsString()
  additional_address?: string;

  @ApiProperty({
    description: "Contact name of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  contact_name: string;

  @ApiProperty({
    description: "Contact email of the company",
    required: true,
  })
  @IsString()
  @IsEmail()
  contact_email: string;

  @ApiProperty({
    description: "Contact phone number of the company",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  contact_phone: string;
}
