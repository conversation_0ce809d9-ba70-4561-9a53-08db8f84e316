import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UsePipes,
  ValidationPipe,
  UploadedFiles,
  UseInterceptors,
  Req,
} from "@nestjs/common";
import { PartnerService } from "./partner.service";
import { CreatePartnerDto } from "./dto/create-partner.dto";
import { UpdatePartnerDto } from "./dto/update-partner.dto";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { FindAllPartnersPaginatedDto } from "./dto/find-all-partners-paginated.dto";
import { AnyFilesInterceptor } from "@nestjs/platform-express";
import { UpdatePartnerContractDto } from "./dto/update-partner-contract.dto";
import { GetPartnerCommissionsParamsDto } from "./dto/get-partner-commissions-params.dto";

@ApiTags("partner")
@Controller("partner")
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class PartnerController {
  constructor(private readonly partnerService: PartnerService) {}

  @Post()
  @UseInterceptors(AnyFilesInterceptor())
  create(@UploadedFiles() files: any, @Body() createPartnerDto: CreatePartnerDto, @Req() req: any) {
    const contractFile = files?.[0] as Express.Multer.File;
    const [, ...newMarketingMaterialFiles] = files ?? [];

    return this.partnerService.create({
      ...createPartnerDto,
      contract_file: contractFile,
      new_marketing_material_files: newMarketingMaterialFiles,
      coupons: createPartnerDto.coupons ? createPartnerDto.coupons.map((coupon) => Number(coupon)) : [],
      // user: {
      //   id: req.headers["x-user-id"] as string,
      //   role: req.headers["x-user-role"] as string,
      // },
    });
  }

  @Get()
  findAll(@Query("name") name: string) {
    return this.partnerService.findAll({ name });
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.partnerService.findOne(+id);
  }

  @Get("by-email/:email")
  findOneByEmail(@Param("email") email: string) {
    return this.partnerService.findOneByEmail(email);
  }

  @Get("user/:user_id")
  @ApiOperation({ summary: "Get partner by user_id" })
  findByUserId(@Param("user_id") user_id: string) {
    return this.partnerService.findByUserId(Number(user_id));
  }

  @Get("list/paginated")
  @ApiOperation({ summary: "Get paginated partners list" })
  findAllPaginated(@Query() query: FindAllPartnersPaginatedDto) {
    return this.partnerService.findAllPaginated(query);
  }

  @Patch(":id")
  @UseInterceptors(AnyFilesInterceptor())
  update(
    @Param("id") id: string,
    @Body() updatePartnerDto: UpdatePartnerDto,
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: any
  ) {
    const contractFile = files?.[0] as Express.Multer.File;
    const [, ...newMarketingMaterialFiles] = files ?? [];

    return this.partnerService.update(
      +id,
      {
        ...updatePartnerDto,
        contract_file: contractFile,
        new_marketing_material_files: newMarketingMaterialFiles,
        coupons: updatePartnerDto.coupons ? updatePartnerDto.coupons.map((coupon) => Number(coupon)) : [],
      }
      // {
      //   id: req.headers["x-user-id"] as string,
      //   role: req.headers["x-user-role"] as string,
      // }
    );
  }

  @Post("sign-contract/:id")
  signContract(@Param("id") id: string) {
    return this.partnerService.signContract(+id);
  }

  @Get(":id/contracts")
  getContracts(@Param("id") id: string) {
    return this.partnerService.getContracts(+id);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.partnerService.remove(+id);
  }

  @Get(":partner_id/coupon")
  async getPartnerCoupons(@Param("partner_id") partner_id: string) {
    return this.partnerService.getPartnerCoupons(Number(partner_id));
  }

  @Patch(":id/contracts/:contract_id")
  @UseInterceptors(AnyFilesInterceptor())
  updateContract(
    @Param("id") id: string,
    @Param("contract_id") contract_id: string,
    @Body() updatePartnerContractDto: UpdatePartnerContractDto,
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: Request
  ) {
    const contractFile = files?.[0] as Express.Multer.File;

    return this.partnerService.updateContract(
      +id,
      +contract_id,
      {
        ...updatePartnerContractDto,
        file: contractFile,
      },
      {
        id: req.headers["x-user-id"] as string,
        role: req.headers["x-user-role"] as string,
      }
    );
  }

  @Get(":id/commissions")
  @ApiOperation({ summary: "Get partner commissions" })
  getCommissions(@Param("id") id: string, @Query() query: GetPartnerCommissionsParamsDto) {
    return this.partnerService.getCommissions(+id, query);
  }
}
