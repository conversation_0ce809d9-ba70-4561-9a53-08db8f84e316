import { Test, TestingModule } from "@nestjs/testing";
import { LicensePackagingServiceService } from "./license-packaging-service.service";
import { DatabaseService } from "@/database/database.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";
import { CreateLicensePackagingServiceDto } from "./dto/create-license-packaging-service.dto";
import { UpdateLicensePackagingServiceDto } from "./dto/update-license-packaging-service.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

describe("LicensePackagingServiceService", () => {
  let service: LicensePackagingServiceService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    licensePackagingService: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LicensePackagingServiceService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<LicensePackagingServiceService>(LicensePackagingServiceService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findAll", () => {
    it("should return all license packaging services when no license_id is provided", async () => {
      const mockItems = [
        { id: 1, name: "Standard Packaging", description: "Basic packaging service", deleted_at: null },
        { id: 2, name: "Premium Packaging", description: "Premium packaging service", deleted_at: null },
      ];
      mockDatabaseService.licensePackagingService.findMany.mockResolvedValue(mockItems);

      const result = await service.findAll();

      expect(result).toEqual(mockItems);
      expect(mockDatabaseService.licensePackagingService.findMany).toHaveBeenCalledWith({
        where: {
          deleted_at: null,
        },
        include: {
          report_set: true,
          report_set_frequency: true,
          volume_reports: {
            include: {
              volume_report_items: {
                where: {
                  deleted_at: null,
                },
                include: {
                  decline: {
                    where: {
                      deleted_at: null,
                    },
                    include: {
                      decline_reasons: {
                        where: {
                          deleted_at: null,
                        },
                        include: {
                          reason: true,
                        },
                      },
                    },
                  },
                },
              },
              decline: {
                where: {
                  deleted_at: null,
                },
                include: {
                  decline_reasons: {
                    where: {
                      deleted_at: null,
                    },
                    include: {
                      reason: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          id: "asc",
        },
      });
    });

    it("should filter by license_id when provided", async () => {
      const license_id = 123;
      const mockItems = [{ id: 1, name: "Standard Packaging", license_id: 123, deleted_at: null }];
      mockDatabaseService.licensePackagingService.findMany.mockResolvedValue(mockItems);

      const result = await service.findAll(license_id);

      expect(result).toEqual(mockItems);
      expect(mockDatabaseService.licensePackagingService.findMany).toHaveBeenCalledWith({
        where: {
          deleted_at: null,
          license_id: 123,
        },
        include: {
          report_set: true,
          report_set_frequency: true,
          volume_reports: {
            include: {
              volume_report_items: {
                where: {
                  deleted_at: null,
                },
                include: {
                  decline: {
                    where: {
                      deleted_at: null,
                    },
                    include: {
                      decline_reasons: {
                        where: {
                          deleted_at: null,
                        },
                        include: {
                          reason: true,
                        },
                      },
                    },
                  },
                },
              },
              decline: {
                where: {
                  deleted_at: null,
                },
                include: {
                  decline_reasons: {
                    where: {
                      deleted_at: null,
                    },
                    include: {
                      reason: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          id: "asc",
        },
      });
    });
  });

  describe("findOne", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should return a license packaging service when valid id is provided", async () => {
      const id = 1;
      const mockItem = { id: 1, name: "Standard Packaging", deleted_at: null };
      mockDatabaseService.licensePackagingService.findUnique.mockResolvedValue(mockItem);

      jest.spyOn(service, "validatingUserPermissionLicensePackagingService").mockResolvedValue(undefined);
      const result = await service.findOne(id, user);

      expect(result).toEqual(mockItem);
      expect(mockDatabaseService.licensePackagingService.findUnique).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
        include: {
          license: true,
          report_set: true,
          report_set_frequency: true,
          volume_reports: {
            include: {
              volume_report_items: {
                where: {
                  deleted_at: null,
                },
                include: {
                  decline: {
                    where: {
                      deleted_at: null,
                    },
                    include: {
                      decline_reasons: {
                        where: {
                          deleted_at: null,
                        },
                        include: {
                          reason: true,
                        },
                      },
                    },
                  },
                },
              },
              decline: {
                where: {
                  deleted_at: null,
                },
                include: {
                  decline_reasons: {
                    where: {
                      deleted_at: null,
                    },
                    include: {
                      reason: true,
                    },
                  },
                },
              },
            },
          },
        },
      });
    });

    it("should throw BadRequestException when id is invalid", async () => {
      await expect(service.findOne(NaN, user)).rejects.toThrow(BadRequestException);
      await expect(service.findOne(null, user)).rejects.toThrow(BadRequestException);
      expect(mockDatabaseService.licensePackagingService.findUnique).not.toHaveBeenCalled();
    });

    it("should throw NotFoundException when license packaging service is not found", async () => {
      const id = 999;
      mockDatabaseService.licensePackagingService.findUnique.mockResolvedValue(null);

      await expect(service.findOne(id, user)).rejects.toThrow(NotFoundException);
      expect(mockDatabaseService.licensePackagingService.findUnique).toHaveBeenCalledWith({
        where: { id: 999, deleted_at: null },
        include: expect.any(Object),
      });
    });
  });

  describe("create", () => {
    it("should create and return a new license packaging service", async () => {
      const createDto: CreateLicensePackagingServiceDto = {
        license_id: 1,
        setup_packaging_service_id: 2,
        name: "Standard Packaging",
        description: "Basic packaging service with standard features",
      };

      const mockCreatedItem = {
        id: 1,
        ...createDto,
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };

      mockDatabaseService.licensePackagingService.create.mockResolvedValue(mockCreatedItem);

      const result = await service.create(createDto);

      expect(result).toEqual(mockCreatedItem);
      expect(mockDatabaseService.licensePackagingService.create).toHaveBeenCalledWith({
        data: {
          license_id: createDto.license_id,
          setup_packaging_service_id: createDto.setup_packaging_service_id,
          name: createDto.name,
          description: createDto.description,
          created_at: expect.any(Date),
          updated_at: expect.any(Date),
        },
      });
    });
  });

  describe("update", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should update and return the license packaging service", async () => {
      const id = 1;
      const updateDto: UpdateLicensePackagingServiceDto = {
        license_id: 2,
        setup_packaging_service_id: 3,
        name: "Updated Packaging",
        description: "Updated description",
      };

      const mockExistingItem = {
        id: 1,
        name: "Standard Packaging",
        description: "Basic packaging service",
        license_id: 1,
        setup_packaging_service_id: 2,
        deleted_at: null,
      };

      const mockUpdatedItem = {
        ...mockExistingItem,
        ...updateDto,
        updated_at: new Date(),
      };

      mockDatabaseService.licensePackagingService.findUnique.mockResolvedValue(mockExistingItem);
      mockDatabaseService.licensePackagingService.update.mockResolvedValue(mockUpdatedItem);

      jest.spyOn(service, "validatingUserPermissionLicensePackagingService").mockResolvedValue(undefined);

      const result = await service.update(id, updateDto, user);

      expect(result).toEqual(mockUpdatedItem);

      expect(mockDatabaseService.licensePackagingService.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          license_id: updateDto.license_id,
          setup_packaging_service_id: updateDto.setup_packaging_service_id,
          name: updateDto.name,
          description: updateDto.description,
          updated_at: expect.any(Date),
        },
      });
    });

    it("should throw BadRequestException when id is invalid", async () => {
      const updateDto: UpdateLicensePackagingServiceDto = {
        name: "Updated Packaging",
      };

      await expect(service.update(NaN, updateDto, user)).rejects.toThrow(BadRequestException);
      await expect(service.update(null, updateDto, user)).rejects.toThrow(BadRequestException);
      expect(mockDatabaseService.licensePackagingService.findUnique).not.toHaveBeenCalled();
      expect(mockDatabaseService.licensePackagingService.update).not.toHaveBeenCalled();
    });

    it("should throw NotFoundException when license packaging service is not found", async () => {
      const id = 999;
      const updateDto: UpdateLicensePackagingServiceDto = {
        name: "Updated Packaging",
      };

      mockDatabaseService.licensePackagingService.findUnique.mockResolvedValue(null);

      await expect(service.update(id, updateDto, user)).rejects.toThrow(NotFoundException);
      expect(mockDatabaseService.licensePackagingService.findUnique).toHaveBeenCalledWith({
        include: {
          license: {
            include: {
              contract: {
                include: {
                  customer: true,
                },
              },
            },
          },
        },
        where: { id: 999, deleted_at: null },
      });
      expect(mockDatabaseService.licensePackagingService.update).not.toHaveBeenCalled();
    });

    it("should handle partial updates", async () => {
      const id = 1;
      const updateDto: UpdateLicensePackagingServiceDto = {
        name: "Updated Packaging",
      };

      const mockExistingItem = {
        id: 1,
        name: "Standard Packaging",
        description: "Basic packaging service",
        license_id: 1,
        setup_packaging_service_id: 2,
        deleted_at: null,
      };

      const mockUpdatedItem = {
        ...mockExistingItem,
        name: updateDto.name,
        updated_at: new Date(),
      };

      mockDatabaseService.licensePackagingService.findUnique.mockResolvedValue(mockExistingItem);
      mockDatabaseService.licensePackagingService.update.mockResolvedValue(mockUpdatedItem);
      jest.spyOn(service, "validatingUserPermissionLicensePackagingService").mockResolvedValue(undefined);
      const result = await service.update(id, updateDto, user);

      expect(result).toEqual(mockUpdatedItem);
      expect(mockDatabaseService.licensePackagingService.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          license_id: undefined,
          setup_packaging_service_id: undefined,
          name: "Updated Packaging",
          description: undefined,
          updated_at: expect.any(Date),
        },
      });
    });
  });

  describe("remove", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should soft delete the license packaging service", async () => {
      const id = 1;
      const mockDeletedItem = {
        id: 1,
        deleted_at: new Date(),
      };

      mockDatabaseService.licensePackagingService.update.mockResolvedValue(mockDeletedItem);
      jest.spyOn(service, "validatingUserPermissionLicensePackagingService").mockResolvedValue(undefined);
      const result = await service.remove(id, user);

      expect(result).toEqual(mockDeletedItem);
      expect(mockDatabaseService.licensePackagingService.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          deleted_at: expect.any(Date),
        },
      });
    });
  });
});
