import { DatabaseService } from "@/database/database.service";
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { CreateLicensePackagingServiceDto } from "./dto/create-license-packaging-service.dto";
import { UpdateLicensePackagingServiceDto } from "./dto/update-license-packaging-service.dto";
import { GetLicensePackagingServicePerformanceDto } from "./dto/license-packaging-service-performance-dto";
import { GetLicensePackagingServiceTurnoverDto } from "./dto/license-packaging-service-turnover-dto";
import { Role } from "@/shared/auth/role.enum";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";

@Injectable()
export class LicensePackagingServiceService {
  constructor(private databaseService: DatabaseService) {}

  async findAll(license_id?: number) {
    return await this.databaseService.licensePackagingService.findMany({
      where: {
        deleted_at: null,
        ...(license_id && { license_id: license_id }),
      },
      include: {
        report_set: true,
        report_set_frequency: true,
        volume_reports: {
          include: {
            volume_report_items: {
              where: {
                deleted_at: null,
              },
              include: {
                decline: {
                  where: {
                    deleted_at: null,
                  },
                  include: {
                    decline_reasons: {
                      where: {
                        deleted_at: null,
                      },
                      include: {
                        reason: true,
                      },
                    },
                  },
                },
              },
            },
            decline: {
              where: {
                deleted_at: null,
              },
              include: {
                decline_reasons: {
                  where: {
                    deleted_at: null,
                  },
                  include: {
                    reason: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    });
  }

  async findOne(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicensePackagingService(id, user);

    const licensePackagingService = await this.databaseService.licensePackagingService.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        license: true,
        report_set: true,
        report_set_frequency: true,
        volume_reports: {
          include: {
            volume_report_items: {
              where: {
                deleted_at: null,
              },
              include: {
                decline: {
                  where: {
                    deleted_at: null,
                  },
                  include: {
                    decline_reasons: {
                      where: {
                        deleted_at: null,
                      },
                      include: {
                        reason: true,
                      },
                    },
                  },
                },
              },
            },
            decline: {
              where: {
                deleted_at: null,
              },
              include: {
                decline_reasons: {
                  where: {
                    deleted_at: null,
                  },
                  include: {
                    reason: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return licensePackagingService;
  }

  async getPerformance(setup_packaging_service_id: number, params: GetLicensePackagingServicePerformanceDto) {
    if (!setup_packaging_service_id || Number.isNaN(Number(setup_packaging_service_id))) {
      throw new BadRequestException("License Packaging Service ID is invalid");
    }

    const setupPackagingServices = await this.databaseService.licensePackagingService.findMany({
      where: {
        setup_packaging_service_id: Number(setup_packaging_service_id),
        deleted_at: null,
        ...(params.start_date && { created_at: { gte: params.start_date } }),
        ...(params.end_date && { created_at: { lte: params.end_date } }),
      },
      include: {
        license: {
          include: {
            price_list: true,
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
    });

    let result = {
      setup_packaging_service_id,
      name: null,
      customers_total: 0,
      revenue_total: 0,
      handling_total: 0,
      third_party_total: 0,
    };

    if (!setupPackagingServices.length) return result;

    result.name = setupPackagingServices[0].name;

    result = setupPackagingServices.reduce(
      (acc, curr) => ({
        setup_packaging_service_id: curr.setup_packaging_service_id,
        name: curr.name,
        customers_total: acc.customers_total + 1,
        revenue_total: acc.revenue_total + curr.license.price_list?.[0]?.registration_fee || 0,
        handling_total: acc.handling_total + curr.license.price_list?.[0]?.handling_fee || 0,
        third_party_total: acc.third_party_total + 0,
      }),
      result
    );

    return result;
  }

  async getTurnover(setup_packaging_service_id: number, params: GetLicensePackagingServiceTurnoverDto) {
    if (!setup_packaging_service_id || Number.isNaN(Number(setup_packaging_service_id))) {
      throw new BadRequestException("License Packaging Service ID is invalid");
    }

    if (!["MONTH", "QUARTER", "YEAR"].includes(params.group_by)) {
      throw new BadRequestException("Group by is invalid. Valid values are: MONTH, QUARTER, YEAR.");
    }

    const setupPackagingServices = await this.databaseService.licensePackagingService.findMany({
      where: { setup_packaging_service_id: Number(setup_packaging_service_id), deleted_at: null },
      include: {
        license: {
          include: {
            price_list: true,
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
    });

    if (!setupPackagingServices.length) return [];

    const currentDate = new Date();
    let periods: Date[] = [];

    if (params.group_by === "MONTH") {
      for (let i = 5; i >= 0; i--) {
        periods.push(new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1));
      }
    } else if (params.group_by === "QUARTER") {
      const currentQuarter = Math.floor(currentDate.getMonth() / 3);
      for (let i = 3; i >= 0; i--) {
        periods.push(new Date(currentDate.getFullYear(), (currentQuarter - i) * 3, 1));
      }
    } else {
      periods = [new Date(currentDate.getFullYear() - 1, 0, 1), new Date(currentDate.getFullYear(), 0, 1)];
    }

    return periods.map((period) => {
      const periodServices = setupPackagingServices.filter((service) => {
        const serviceDate = new Date(service.created_at);
        if (params.group_by === "MONTH") {
          return serviceDate.getMonth() === period.getMonth() && serviceDate.getFullYear() === period.getFullYear();
        } else if (params.group_by === "QUARTER") {
          return (
            Math.floor(serviceDate.getMonth() / 3) === Math.floor(period.getMonth() / 3) &&
            serviceDate.getFullYear() === period.getFullYear()
          );
        } else {
          return serviceDate.getFullYear() === period.getFullYear();
        }
      });

      const totalRevenue = periodServices.reduce(
        (sum, service) => sum + (service.license.price_list?.[0]?.registration_fee || 0),
        0
      );

      let periodLabel = "";
      if (params.group_by === "MONTH") {
        periodLabel = period.toLocaleString("default", { month: "long" });
      } else if (params.group_by === "QUARTER") {
        const quarter = Math.floor(period.getMonth() / 3) + 1;
        periodLabel = `Q${quarter} ${period.getFullYear()}`;
      } else {
        periodLabel = period.getFullYear().toString();
      }

      return {
        period: period.toISOString(),
        period_label: periodLabel,
        revenue: totalRevenue,
      };
    });
  }

  async getWeightReported(setup_packaging_service_id: number) {
    if (!setup_packaging_service_id || Number.isNaN(Number(setup_packaging_service_id))) {
      throw new BadRequestException("License Packaging Service ID is invalid");
    }

    const volumeReportItems = await this.databaseService.licenseVolumeReportItem.findMany({
      where: {
        setup_fraction_code: {
          not: null,
        },
        volume_report: {
          packaging_service: {
            setup_packaging_service_id: Number(setup_packaging_service_id),
            deleted_at: null,
          },
          deleted_at: null,
        },
      },
      orderBy: {
        created_at: "desc",
      },
    });

    const groupedWeights = volumeReportItems.reduce((acc, item) => {
      const code = item.setup_fraction_code;
      if (!acc[code]) {
        acc[code] = 0;
      }
      acc[code] += item.value || 0;
      return acc;
    }, {});

    const result = Object.entries(groupedWeights)
      .map(([setup_fraction_code, total_weight]) => ({
        setup_fraction_code,
        total_weight: Number(total_weight),
      }))
      .sort((a, b) => b.total_weight - a.total_weight);

    return result;
  }

  async create(data: CreateLicensePackagingServiceDto) {
    return await this.databaseService.licensePackagingService.create({
      data: {
        license_id: data.license_id,
        setup_packaging_service_id: data.setup_packaging_service_id,
        name: data.name,
        description: data.description,
        created_at: new Date(),
        updated_at: new Date(),
      },
    });
  }

  async update(id: number, data: UpdateLicensePackagingServiceDto, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicensePackagingService(id, user);

    return await this.databaseService.licensePackagingService.update({
      where: { id: Number(id) },
      data: {
        license_id: data.license_id,
        setup_packaging_service_id: data.setup_packaging_service_id,
        name: data.name,
        description: data.description,
        updated_at: new Date(),
      },
    });
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicensePackagingService(id, user);

    return await this.databaseService.licensePackagingService.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }

  async validatingUserPermissionLicensePackagingService(id: number, user: AuthenticatedUser) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("License Packaging Service ID is invalid");
    }

    const licensePackagingService = await this.databaseService.licensePackagingService.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        license: {
          include: {
            contract: {
              include: {
                customer: true,
              },
            },
          },
        },
      },
    });

    if (!licensePackagingService) {
      throw new NotFoundException("License Packaging Service not found");
    }

    const { license } = licensePackagingService;
    if (!license) {
      throw new NotFoundException("License not found");
    }
    const { contract } = license;
    if (!contract) {
      throw new NotFoundException("Contract not found");
    }
    const { customer } = contract;
    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You do not have permission to access this license packaging service");
    }
  }
}
