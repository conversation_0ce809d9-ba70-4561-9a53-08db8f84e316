import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { ApiOperation, ApiQuery } from "@nestjs/swagger";
import { LicensePackagingServiceService } from "./license-packaging-service.service";
import { CreateLicensePackagingServiceDto } from "./dto/create-license-packaging-service.dto";
import { UpdateLicensePackagingServiceDto } from "./dto/update-license-packaging-service.dto";
import { ApiTags } from "@nestjs/swagger";
import { GetLicensePackagingServiceTurnoverDto } from "./dto/license-packaging-service-turnover-dto";
import { GetLicensePackagingServicePerformanceDto } from "./dto/license-packaging-service-performance-dto";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Controller("packaging-services")
@ApiTags("packaging-services")
export class LicensePackagingServiceController {
  constructor(private readonly licensePackagingService: LicensePackagingServiceService) {}

  @Get()
  @ApiQuery({ name: "license_id", required: false, type: Number })
  @ApiOperation({ summary: "Get all packaging services" })
  findAll(@Query("license_id") license_id?: string) {
    return this.licensePackagingService.findAll(license_id ? parseInt(license_id) : undefined);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a packaging service by id" })
  findOne(@Param("id") id: number, @User() user: AuthenticatedUser) {
    return this.licensePackagingService.findOne(id, user);
  }

  @Get(":id/performance")
  @ApiOperation({ summary: "Get a packaging service performance by id" })
  performance(@Param("id") id: number, @Query() query: GetLicensePackagingServicePerformanceDto) {
    return this.licensePackagingService.getPerformance(id, query);
  }

  @Get(":id/turnover")
  @ApiOperation({ summary: "Get a packaging service turnover by id" })
  turnover(@Param("id") id: number, @Query() query: GetLicensePackagingServiceTurnoverDto) {
    return this.licensePackagingService.getTurnover(id, query);
  }

  @Get(":id/weight-reported")
  @ApiOperation({ summary: "Get a packaging service weight reported by id" })
  weightReported(@Param("id") id: number) {
    return this.licensePackagingService.getWeightReported(id);
  }

  @Post()
  @ApiOperation({ summary: "Create a packaging service" })
  create(@Body() data: CreateLicensePackagingServiceDto) {
    return this.licensePackagingService.create(data);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a packaging service" })
  update(@Param("id") id: number, @Body() data: UpdateLicensePackagingServiceDto, @User() user: AuthenticatedUser) {
    return this.licensePackagingService.update(id, data, user);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a packaging service" })
  remove(@Param("id") id: number, @User() user: AuthenticatedUser) {
    return this.licensePackagingService.remove(id, user);
  }
}
