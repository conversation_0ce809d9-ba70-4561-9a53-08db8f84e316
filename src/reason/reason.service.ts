import { DatabaseService } from "@/database/database.service";
import { HttpModuleService } from "@/http/http.service";
import { BadRequestException, Injectable } from "@nestjs/common";
import { FindAllReasonsDto } from "./dto/find-all-reasons.dto";
import { ReasonType } from "@prisma/client";

@Injectable()
export class ReasonService {
  constructor(private databaseService: DatabaseService, private httpModuleService: HttpModuleService) {}

  async findAll(query: FindAllReasonsDto) {
    if (!query.type) {
      throw new BadRequestException("Type is required");
    }

    if (!Object.values(ReasonType).includes(query.type)) {
      throw new BadRequestException("Invalid type");
    }

    const reasons = await this.databaseService.reason.findMany({
      where: {
        deleted_at: null,
        type: query.type,
      },
    });

    return reasons;
  }
}
