type Threshold = {
  title: string;
  value: number;
  fractions: Record<string, { code: string; name: string; value: number }>;
};

type Fraction = { code: string; name: string; weight: number };

export function calculateDirectLicenseNetValue(priceList: any, fractions: Fraction[]) {
  if (!priceList.thresholds.length || !fractions.length) {
    return { total: priceList.minimum_price, threshold: null };
  }

  const { basic_price, minimum_price, thresholds } = priceList;

  function calculateTotal(thresholdIndex: number): { total: number; threshold: Threshold } {
    const currentThreshold = thresholds[thresholdIndex];

    const total = fractions.reduce((acc, curr) => {
      const fractionPriceInCents = (currentThreshold.fractions[curr.code].value || 0) / 10; // Convert to cents
      const fractionWeightInKilograms = (curr.weight || 0) / 1000; // Transform weight from grams (g) to kilograms (kg)

      return acc + fractionPriceInCents * fractionWeightInKilograms;
    }, basic_price || 0);

    if (thresholds[thresholdIndex + 1] && total > thresholds[thresholdIndex + 1].value) {
      return calculateTotal(thresholdIndex + 1);
    }

    return { total, threshold: currentThreshold };
  }

  const { total, threshold } = calculateTotal(0);
  return { total: Math.max(total, minimum_price), threshold };
}
