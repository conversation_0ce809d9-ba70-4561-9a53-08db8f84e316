import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  UploadedFile,
  UseInterceptors,
  Body,
  Query,
  BadRequestException,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import { ApiTags, ApiOperation, ApiConsumes, ApiBody, ApiResponse } from "@nestjs/swagger";
import { BrokerCompanyOrdersService } from "./broker-company-orders.service";
import { Express } from "express";
import { QueryParams } from "./dto/query-params.dto";
import { UploadFileDto } from "./dto/upload-file.dto";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.BROKER_MANAGER, Role.BROKER)
@ApiTags("Broker Company Orders")
@Controller("broker-company-orders")
export class BrokerCompanyOrdersController {
  constructor(private readonly brokerCompanyOrdersService: BrokerCompanyOrdersService) {}

  @Post("upload")
  @UseInterceptors(FileInterceptor("file"))
  @ApiOperation({ summary: "Upload an Excel file with orders and specify company_id" })
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        file: { type: "string", format: "binary" },
        broker_id: { type: "string" },
      },
    },
  })
  @ApiResponse({ status: 201, description: "File uploaded and processed successfully" })
  @ApiResponse({ status: 400, description: "Invalid file format or missing company_id" })
  async uploadFile(@UploadedFile() file: Express.Multer.File, @Body() { brokerId }: UploadFileDto) {
    if (!file || !file.originalname.match(/\.(xls|xlsx)$/)) {
      throw new BadRequestException("Only Excel files are allowed!");
    }
    return await this.brokerCompanyOrdersService.processExcel(file, +brokerId);
  }

  @Get()
  @ApiOperation({ summary: "List all available orders" })
  @ApiResponse({
    status: 200,
    description: "List of orders returned successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          type: { type: "enum", enum: ["REPORT", "INITIAL_REPORT"] },
          customer_number: { type: "string" },
          company_id: { type: "number" },
          transfer_date: { type: "string", format: "date-time" },
          order_number: { type: "string" },
          year: { type: "number" },
          fractions: { type: "array", items: { type: "object" } },
          status: { type: "enum", enum: ["OPEN", "CANCELED"] },
          created_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
          updated_at: { type: "string", format: "date-time", nullable: true },
          net_value: { type: "number" },
          file_id: { type: "number" },
        },
      },
      example: [
        {
          id: 1,
          type: "REPORT",
          customer_number: "1234567890",
          company_id: 1,
          transfer_date: "2024-03-20T10:00:00Z",
          order_number: "1234567890",
          year: 2024,
          fractions: [],
          status: "OPEN",
          created_at: "2024-03-20T10:00:00Z",
          deleted_at: null,
          updated_at: "2024-03-20T10:00:00Z",
          net_value: 1000,
          file_id: 1,
        },
        {
          id: 2,
          type: "REPORT",
          customer_number: "1234567890",
          company_id: 1,
          transfer_date: "2024-03-20T10:00:00Z",
          order_number: "1234567890",
          year: 2024,
          fractions: [],
          status: "OPEN",
          created_at: "2024-03-20T10:00:00Z",
          deleted_at: null,
          updated_at: "2024-03-20T10:00:00Z",
          net_value: 1000,
          file_id: 2,
        },
      ],
    },
  })
  async findAll(@Query() query?: QueryParams) {
    return await this.brokerCompanyOrdersService.findAll(query);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get an order by ID" })
  @ApiResponse({
    status: 200,
    description: "Order details returned successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        type: { type: "enum", enum: ["REPORT", "INITIAL_REPORT"] },
        customer_number: { type: "string" },
        company_id: { type: "number" },
        transfer_date: { type: "string", format: "date-time" },
        order_number: { type: "string" },
        year: { type: "number" },
        fractions: { type: "array", items: { type: "object" } },
        status: { type: "enum", enum: ["OPEN", "CANCELED"] },
        created_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        updated_at: { type: "string", format: "date-time", nullable: true },
        net_value: { type: "number" },
        file_id: { type: "number" },
      },
      example: {
        id: 1,
        type: "REPORT",
        customer_number: "1234567890",
        company_id: 1,
        transfer_date: "2024-03-20T10:00:00Z",
        order_number: "1234567890",
        year: 2024,
        fractions: [],
        status: "OPEN",
        created_at: "2024-03-20T10:00:00Z",
        deleted_at: null,
        updated_at: "2024-03-20T10:00:00Z",
        net_value: 1000,
        file_id: 1,
      },
    },
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  async findOne(@Param("id") id: string) {
    return await this.brokerCompanyOrdersService.findOne(+id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update an order by ID" })
  @ApiBody({ type: Object })
  @ApiResponse({
    status: 200,
    description: "Order updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        type: { type: "enum", enum: ["REPORT", "INITIAL_REPORT"] },
        customer_number: { type: "string" },
        company_id: { type: "number" },
        transfer_date: { type: "string", format: "date-time" },
        order_number: { type: "string" },
        year: { type: "number" },
        fractions: { type: "array", items: { type: "object" } },
        status: { type: "enum", enum: ["OPEN", "CANCELED"] },
        created_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        updated_at: { type: "string", format: "date-time", nullable: true },
        net_value: { type: "number" },
        file_id: { type: "number" },
      },
      example: {
        id: 1,
        type: "REPORT",
        customer_number: "1234567890",
        company_id: 1,
        transfer_date: "2024-03-20T10:00:00Z",
        order_number: "1234567890",
        year: 2024,
        fractions: [],
        status: "OPEN",
        created_at: "2024-03-20T10:00:00Z",
        deleted_at: null,
        updated_at: "2024-03-20T10:00:00Z",
        net_value: 1000,
        file_id: 1,
      },
    },
  })
  async update(@Param("id") id: string, @Body() updateData: any) {
    return await this.brokerCompanyOrdersService.update(+id, updateData);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete an order by ID" })
  @ApiResponse({ status: 200, description: "Order deleted successfully" })
  async remove(@Param("id") id: string) {
    return await this.brokerCompanyOrdersService.remove(+id);
  }
}
