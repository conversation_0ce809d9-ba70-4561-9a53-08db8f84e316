import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import * as xlsx from "xlsx";
import { DatabaseService } from "../database/database.service";
import { BrokerCompanyOrderStatus, Prisma } from "@prisma/client";
import { QueryParams } from "./dto/query-params.dto";
import { GERMANY_FRACTIONS } from "./constants";
import { calculateDirectLicenseNetValue } from "./functions";

// eslint-disable-next-line @typescript-eslint/no-var-requires
const retry = require("async-retry");
import Bottleneck from "bottleneck";
import { UpdateBrokerCompanyOrderDto } from "./dto/update-broker-company-order.dto";
import { UploadDataService } from "../upload-data/upload-data.service";

const MAX_CONCURRENT_TASKS = 5;
const limiter = new Bottleneck({
  maxConcurrent: MAX_CONCURRENT_TASKS,
});

@Injectable()
export class BrokerCompanyOrdersService {
  private readonly logger = new Logger(BrokerCompanyOrdersService.name);
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly uploadDataService: UploadDataService
  ) {}

  private async getCompanyAndPriceList(registrationNumber: string, year: string) {
    const [company, priceList] = await Promise.all([
      this.databaseService.brokerCompany.findFirst({ where: { register_number: registrationNumber } }),
      this.databaseService.priceList.findFirst({
        where: { type: "DIRECT_LICENSE", condition_type_value: year },
      }),
    ]);

    if (!company) throw new NotFoundException(`Company with registration number ${registrationNumber} not found`);
    if (!priceList) throw new NotFoundException(`Price list for year ${year} not found`);

    return { company, priceList };
  }

  private async getFractions(processedOrder: any, priceList: any) {
    const fractions = [];
    for (const data in processedOrder) {
      const fraction = GERMANY_FRACTIONS.find((f) => f.name === data);
      if (fraction) {
        const priceListFraction = priceList.thresholds?.[0]?.fractions[fraction.code];
        fractions.push({
          code: fraction.code,
          name: fraction.name,
          weight: processedOrder[data] || 0,
          value: priceListFraction?.value || 0,
        });
      }
    }
    return fractions;
  }

  private async processChunkWithRetry(chunk: any[], uploadFileId: number) {
    await retry(
      async () => {
        await this.processChunk(chunk, uploadFileId);
      },
      {
        retries: 3,
        onRetry: (err: any) => this.logger.warn(`Retrying chunk due to: ${err.message}`, err),
      }
    );
  }

  private async processChunk(chunk: any[], uploadFileId: number) {
    const ordersToCreate: Prisma.BrokerCompanyOrderUncheckedCreateInput[] = [];

    await Promise.all(
      chunk.map(async (processedOrder) => {
        try {
          const { company, priceList } = await this.getCompanyAndPriceList(
            processedOrder["Registration Number"],
            String(processedOrder.Year)
          );

          const existingOrder = await this.databaseService.brokerCompanyOrder.findFirst({
            where: { company_id: company.id, year: processedOrder.Year },
          });
          if (existingOrder)
            throw new BadRequestException(`Duplicate order for ${company.id} in year ${processedOrder.Year}`);

          const [companyCount, ordersCount, fractions] = await Promise.all([
            this.databaseService.brokerCompany.count(),
            this.databaseService.brokerCompanyOrder.count(),
            this.getFractions(processedOrder, priceList),
          ]);

          const { total } = calculateDirectLicenseNetValue(priceList, fractions);

          ordersToCreate.push({
            company_id: company.id,
            year: processedOrder.Year,
            customer_number: `C-${companyCount + 1_000_001}`,
            order_number: `O-${ordersCount + 1_000_001}`,
            fractions,
            type: processedOrder.Type === "INITIAL" ? "INITIAL_REPORT" : "REPORT",
            status: BrokerCompanyOrderStatus.OPEN,
            transfer_date: new Date(),
            net_value: total,
            file_id: uploadFileId,
          });
        } catch (error) {
          this.logger.error(`Error processing order: ${error.message}`, error.stack);
        }
      })
    );

    if (ordersToCreate.length > 0) {
      this.logger.log(`Inserting ${ordersToCreate.length} orders...`);
      await this.databaseService.brokerCompanyOrder.createMany({ data: ordersToCreate });
    }
  }

  private async *streamExcelRows(buffer: Buffer) {
    const workbook = xlsx.read(buffer, { type: "buffer" });
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(sheet, { header: 1, defval: null });
    const headers = jsonData[0] as string[];
    for (let i = 1; i < jsonData.length; i++) {
      yield (jsonData[i] as any).reduce((acc: any, value: any, idx: any) => {
        acc[headers[idx]] = value || null;
        return acc;
      }, {} as Record<string, any>);
    }
  }

  async processExcel(file: Express.Multer.File, brokerId: number) {
    this.logger.log("Starting Excel file streaming process...");
    const batchSize = 100;
    const batch: any[] = [];
    try {
      const { id: fileId } = await this.uploadDataService.create(brokerId, file);
      for await (const row of this.streamExcelRows(file.buffer)) {
        batch.push(row);
        if (batch.length >= batchSize) {
          await limiter.schedule(() => this.processChunkWithRetry(batch.splice(0, batchSize), fileId));
        }
      }
      if (batch.length > 0) {
        await limiter.schedule(() => this.processChunkWithRetry(batch, fileId));
      }
      this.logger.log("Excel file streaming process completed successfully.");
      return { message: "Excel file processed successfully" };
    } catch (error) {
      this.logger.error(`Failed to process Excel file: ${error.message}`, error.stack);
      throw new InternalServerErrorException("Failed to process Excel file", error.message);
    }
  }

  async findAll(query?: QueryParams) {
    this.logger.log("Fetching broker company orders...");
    try {
      const { where, pagination } = this.buildQueryParams(query);

      const [orders, total] = await this.databaseService.$transaction([
        this.databaseService.brokerCompanyOrder.findMany({
          where,
          select: {
            id: true,
            customer_number: true,
            order_number: true,
            year: true,
            created_at: true,
            status: true,
            transfer_date: true,
            type: true,
            company: {
              select: {
                id: true,
                broker_id: true,
                name: true,
                register_number: true,
              },
            },
            fractions: true,
            deleted_at: true,
            updated_at: true,
          },
          take: pagination.limit,
          skip: pagination.skip,
          orderBy: { created_at: "desc" },
        }),
        this.databaseService.brokerCompanyOrder.count({ where }),
      ]);

      return {
        orders,
        count: total,
        current_page: pagination.page,
        pages: Math.ceil(total / pagination.limit),
      };
    } catch (error) {
      this.logger.error(`Error retrieving broker companies: ${error.message}`, error.stack);
      throw new InternalServerErrorException({
        message: "Error retrieving broker companies",
        error: error.message,
      });
    }
  }

  private buildQueryParams(query?: QueryParams) {
    const limit = Math.max(Number(query?.limit) || 10, 1);
    const page = Math.max(Number(query?.page) || 1, 1);
    const skip = (page - 1) * limit;

    const where: Prisma.BrokerCompanyOrderWhereInput = { deleted_at: null };

    if (query?.search) {
      const search = query.search.trim();
      const isNumber = !isNaN(Number(search));
      where.OR = [
        { customer_number: { contains: search, mode: "insensitive" } },
        { order_number: { contains: search, mode: "insensitive" } },
        { company: { name: { contains: search, mode: "insensitive" } } },
        ...(isNumber ? [{ year: { equals: Number(search) } }] : []),
      ];
    }

    if (query?.companyId) {
      where.company_id = Number(query.companyId);
    }

    if (query?.brokerId) {
      where.company = {
        broker_id: Number(query.brokerId),
      };
    }

    return { where, pagination: { limit, page, skip } };
  }

  public async findOne(id: number) {
    try {
      const order = await this.databaseService.brokerCompanyOrder.findUnique({ where: { id, deleted_at: null } });

      if (!order) {
        throw new NotFoundException(`Broker company order with ID ${id} not found`);
      }

      return order;
    } catch (error) {
      this.logger.error(`Error retrieving broker company with ID ${id}: ${error.message}`, error.stack);
      throw new InternalServerErrorException("Error retrieving broker company", error);
    }
  }

  async update(id: number, updateBrokerCompanyOrderDto: UpdateBrokerCompanyOrderDto) {
    try {
      return await this.databaseService.brokerCompanyOrder.update({
        where: { id, deleted_at: null },
        data: updateBrokerCompanyOrderDto,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === "P2002") {
        throw new BadRequestException("A broker company order with this unique field already exists.");
      }
      this.logger.error(`Error updating broker company order with ID ${id}: ${error.message}`, error.stack);
      throw new InternalServerErrorException("Error updating broker company order", error);
    }
  }

  async remove(id: number) {
    try {
      const existingCompanyOrder = await this.databaseService.brokerCompanyOrder.findUnique({
        where: { id, deleted_at: null },
      });

      if (!existingCompanyOrder) {
        throw new NotFoundException(`Broker company order with ID ${id} not found`);
      }

      await this.databaseService.brokerCompanyOrder.update({
        where: { id },
        data: { deleted_at: new Date() },
      });
    } catch (error) {
      this.logger.error(`Error deleting broker company order with ID ${id}: ${error.message}`, error.stack);
      throw new InternalServerErrorException("Error deleting broker company order", error);
    }
  }
}
