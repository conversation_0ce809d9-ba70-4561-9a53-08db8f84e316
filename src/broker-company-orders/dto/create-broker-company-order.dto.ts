import { IsString, <PERSON>Int, <PERSON><PERSON>rray, Is<PERSON>num, IsDateString, ValidateNested } from "class-validator";
import { Type } from "class-transformer";

export enum BrokerCompanyOrderStatus {
  OPEN = "OPEN",
  CANCELLED = "CANCELLED",
}

class FractionDto {
  @IsString()
  code: string;

  @IsString()
  name: string;

  @IsInt()
  weight: number;
}

export class CreateBrokerCompanyOrderDto {
  @IsInt()
  @Type(() => Number)
  id: number;

  @IsString()
  customer_number: string;

  @IsInt()
  company_id: number;

  @IsDateString({ strict: true })
  transfer_date: string;

  @IsString()
  order_number: string;

  @IsString()
  register_number: string;

  @IsInt()
  year: number;

  @IsEnum(BrokerCompanyOrderStatus)
  status: BrokerCompanyOrderStatus;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FractionDto)
  fractions: FractionDto[];
}
