import { Module } from "@nestjs/common";
import { HttpApiModule } from "@/http/http.module";
import { DatabaseModule } from "../database/database.module";
import { CountryFollowerController } from "./country-follower.controller";
import { CountryFollowerService } from "./country-follower.service";

@Module({
  imports: [DatabaseModule, HttpApiModule],
  controllers: [CountryFollowerController],
  providers: [CountryFollowerService],
})
export class CountryFollowerModule {}
