import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { HttpModuleService } from "../http/http.service";
import { DatabaseService } from "../database/database.service";
import { of } from "rxjs";

jest.setTimeout(30000);

describe("CountryFollowerController (e2e)", () => {
  let app: INestApplication;
  let httpModuleService: HttpModuleService;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validApiKey,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockUsers = {
    data: [
      { id: 1, name: "John Doe", email: "<EMAIL>" },
      { id: 2, name: "Jane Smith", email: "<EMAIL>" },
      { id: 3, name: "Bob Johnson", email: "<EMAIL>" },
    ],
  };

  const mockCountryFollower = {
    id: 1,
    country_id: 1,
    user_id: 1,
    user_email: "<EMAIL>",
    user_first_name: "John",
    user_last_name: "Doe",
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(HttpModuleService)
      .useValue({
        auth: jest.fn(),
        customer: jest.fn(),
      })
      .overrideProvider(DatabaseService)
      .useValue({
        countryFollower: {
          createMany: jest.fn().mockResolvedValue({ count: 2 }),
          findFirst: jest.fn().mockImplementation((params) => {
            if (params.where.country_id === 1 && params.where.user_id === 1) {
              return Promise.resolve(mockCountryFollower);
            }
            return Promise.resolve(null);
          }),
          deleteMany: jest.fn().mockResolvedValue({ count: 1 }),
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    httpModuleService = moduleFixture.get<HttpModuleService>(HttpModuleService);
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    jest.spyOn(httpModuleService, "auth").mockImplementation((config): any => {
      if (config.url === "/user" && config.method === "GET") {
        return of(mockUsers);
      }

      return of({ data: {} });
    });

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/country-followers (POST)", () => {
    const createFollowerDto = {
      country_id: 1,
      user_ids: [1, 2],
    };

    it("should create country followers when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .post("/country-followers")
        .set(authHeaders)
        .send(createFollowerDto)
        .expect(201)
        .then((response) => {
          expect(httpModuleService.auth).toHaveBeenCalled();
          expect(databaseService.countryFollower.createMany).toHaveBeenCalled();
          expect(response.body).toBeDefined();
          expect(response.body.message).toBe("Country followers created successfully");
        });
    });

    it("should create country followers when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer())
        .post("/country-followers")
        .set(adminHeaders)
        .send(createFollowerDto)
        .expect(201)
        .then(() => {
          expect(databaseService.countryFollower.createMany).toHaveBeenCalled();
        });
    });

    it("should create country followers when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer())
        .post("/country-followers")
        .set(clerkHeaders)
        .send(createFollowerDto)
        .expect(201)
        .then(() => {
          expect(databaseService.countryFollower.createMany).toHaveBeenCalled();
        });
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/country-followers")
        .set(unauthorizedHeaders)
        .send(createFollowerDto)
        .expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/country-followers")
        .set(invalidApiKeyHeaders)
        .send(createFollowerDto)
        .expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).post("/country-followers").send(createFollowerDto).expect(401);
    });

    it("should reject when country_id is missing", () => {
      const invalidDto = {
        user_ids: [1, 2],
      };

      return request(app.getHttpServer()).post("/country-followers").set(authHeaders).send(invalidDto).expect(400);
    });

    it("should reject when user_ids is missing", () => {
      const invalidDto = {
        country_id: 1,
      };

      return request(app.getHttpServer()).post("/country-followers").set(authHeaders).send(invalidDto).expect(400);
    });

    it("should reject when user_ids is empty", () => {
      const invalidDto = {
        country_id: 1,
        user_ids: [],
      };

      return request(app.getHttpServer()).post("/country-followers").set(authHeaders).send(invalidDto).expect(400);
    });
  });

  describe("/country-followers (DELETE)", () => {
    it("should delete country follower when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .delete("/country-followers")
        .set(authHeaders)
        .query({ country_id: 1, user_id: 1 })
        .expect(200)
        .then((response) => {
          expect(databaseService.countryFollower.findFirst).toHaveBeenCalledWith({
            where: { country_id: 1, user_id: 1 },
          });
          expect(databaseService.countryFollower.deleteMany).toHaveBeenCalled();
          expect(response.body).toBeDefined();
          expect(response.body.message).toBe("Country follower deleted successfully");
        });
    });

    it("should delete country follower when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer())
        .delete("/country-followers")
        .set(adminHeaders)
        .query({ country_id: 1, user_id: 1 })
        .expect(200)
        .then(() => {
          expect(databaseService.countryFollower.deleteMany).toHaveBeenCalled();
        });
    });

    it("should delete country follower when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer())
        .delete("/country-followers")
        .set(clerkHeaders)
        .query({ country_id: 1, user_id: 1 })
        .expect(200)
        .then(() => {
          expect(databaseService.countryFollower.deleteMany).toHaveBeenCalled();
        });
    });

    it("should return 404 when country follower not found", () => {
      // Mock finding no country follower for this specific query
      jest.spyOn(databaseService.countryFollower, "findFirst").mockResolvedValueOnce(null);

      return request(app.getHttpServer())
        .delete("/country-followers")
        .set(authHeaders)
        .query({ country_id: 999, user_id: 999 })
        .expect(404);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .delete("/country-followers")
        .set(unauthorizedHeaders)
        .query({ country_id: 1, user_id: 1 })
        .expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .delete("/country-followers")
        .set(invalidApiKeyHeaders)
        .query({ country_id: 1, user_id: 1 })
        .expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).delete("/country-followers").query({ country_id: 1, user_id: 1 }).expect(401);
    });

    it("should reject when country_id is missing", () => {
      return request(app.getHttpServer())
        .delete("/country-followers")
        .set(authHeaders)
        .query({ user_id: 1 })
        .expect(400);
    });

    it("should reject when user_id is missing", () => {
      return request(app.getHttpServer())
        .delete("/country-followers")
        .set(authHeaders)
        .query({ country_id: 1 })
        .expect(400);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer())
        .post("/country-followers")
        .set(systemHeaders)
        .send({ country_id: 1, user_ids: [1, 2] })
        .expect(201)
        .then(() => {
          expect(databaseService.countryFollower.createMany).toHaveBeenCalled();
        });
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer())
        .post("/country-followers")
        .set(invalidSystemHeaders)
        .send({ country_id: 1, user_ids: [1, 2] })
        .expect(401);
    });
  });
});
