import { <PERSON>, Controller, Delete, Get, Param, Post } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";
import { FractionIconService } from "./fraction-icon.service";
import { CreateFractionIconDto } from "./dto/fraction-icon-create.dto";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("FractionIcon")
@Controller("fraction-icons")
export class FractionIconController {
  constructor(private readonly fractionIconService: FractionIconService) {}

  @Post()
  @ApiOperation({ summary: "Create a new fraction icon" })
  @ApiResponse({
    status: 201,
    description: "Fraction icon created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time", nullable: true },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        file_id: { type: "string" },
        image_url: { type: "string" },
        file: {
          type: "object",
          properties: {
            id: { type: "number" },
            created_at: { type: "string", format: "date-time" },
            updated_at: { type: "string", format: "date-time", nullable: true },
            name: { type: "string" },
            extension: { type: "string" },
            size: { type: "string" },
            creator_type: { type: "string" },
            document_type: { type: "string" },
            user_id: { type: "string", nullable: true },
            original_name: { type: "string" },
            country_id: { type: "number", nullable: true },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  @ApiResponse({ status: 404, description: "File not found" })
  create(@Body() data: CreateFractionIconDto) {
    return this.fractionIconService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all fraction icons" })
  @ApiResponse({
    status: 200,
    description: "Fraction icons retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          file_id: { type: "string" },
          image_url: { type: "string" },
          file: {
            type: "object",
            properties: {
              id: { type: "number" },
              name: { type: "string" },
              extension: { type: "string" },
              size: { type: "string" },
              creator_type: { type: "string" },
              document_type: { type: "string" },
              user_id: { type: "string", nullable: true },
              original_name: { type: "string" },
              country_id: { type: "number", nullable: true },
            },
          },
        },
      },
    },
  })
  findAll() {
    return this.fractionIconService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get fraction icon by ID" })
  @ApiResponse({
    status: 200,
    description: "Fraction icon retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        file_id: { type: "string" },
        image_url: { type: "string" },
        file: {
          type: "object",
          properties: {
            id: { type: "number" },
            name: { type: "string" },
            extension: { type: "string" },
            size: { type: "string" },
            creator_type: { type: "string" },
            document_type: { type: "string" },
            user_id: { type: "string", nullable: true },
            original_name: { type: "string" },
            country_id: { type: "number", nullable: true },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Fraction icon not found" })
  findOne(@Param("id") id: string) {
    return this.fractionIconService.findOne(+id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete fraction icon by ID" })
  @ApiResponse({ status: 200, description: "Fraction icon deleted successfully" })
  @ApiResponse({ status: 404, description: "Fraction icon not found" })
  remove(@Param("id") id: string) {
    return this.fractionIconService.remove(+id);
  }
}
