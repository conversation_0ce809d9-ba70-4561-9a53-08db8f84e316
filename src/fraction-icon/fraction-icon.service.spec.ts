import { Test, TestingModule } from "@nestjs/testing";
import { FractionIconService } from "./fraction-icon.service";
import { DatabaseService } from "../database/database.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";

const mockDatabaseService = {
  files: {
    findUnique: jest.fn(),
  },
  fractionIcon: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
  },
};

describe("FractionIconService", () => {
  let service: FractionIconService;
  let databaseService: typeof mockDatabaseService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FractionIconService, { provide: DatabaseService, useValue: mockDatabaseService }],
    }).compile();

    service = module.get<FractionIconService>(FractionIconService);
    databaseService = module.get(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create a fraction icon", async () => {
      const dto = { file_id: "123" };
      const file = { id: "123", name: "test.png" };
      const fractionIcon = {
        file_id: "123",
        image_url: "https://liz-generic-files.s3.us-east-2.amazonaws.com/test.png",
        file,
      };

      databaseService.files.findUnique.mockResolvedValue(file);
      databaseService.fractionIcon.create.mockResolvedValue(fractionIcon);

      const result = await service.create(dto);
      expect(result).toEqual(fractionIcon);
    });

    it("should throw BadRequestException if file_id is missing", async () => {
      await expect(service.create({ file_id: "" })).rejects.toThrow(BadRequestException);
    });

    it("should throw NotFoundException if file is not found", async () => {
      databaseService.files.findUnique.mockResolvedValue(null);
      await expect(service.create({ file_id: "123" })).rejects.toThrow(NotFoundException);
    });
  });

  describe("findAll", () => {
    it("should return all fraction icons", async () => {
      const mockData = [{ id: 1, file_id: "123", image_url: "https://test.com/image.png", file: {} }];
      databaseService.fractionIcon.findMany.mockResolvedValue(mockData);

      const result = await service.findAll();
      expect(result).toEqual(mockData);
    });
  });

  describe("findOne", () => {
    it("should return a fraction icon", async () => {
      const mockData = { id: 1, file_id: "123", image_url: "https://test.com/image.png", file: {} };
      databaseService.fractionIcon.findUnique.mockResolvedValue(mockData);

      const result = await service.findOne(1);
      expect(result).toEqual(mockData);
    });

    it("should throw NotFoundException if fraction icon is not found", async () => {
      databaseService.fractionIcon.findUnique.mockResolvedValue(null);
      await expect(service.findOne(1)).rejects.toThrow(NotFoundException);
    });
  });

  describe("remove", () => {
    it("should mark a fraction icon as deleted", async () => {
      const mockData = { id: 1, file_id: "123", image_url: "https://test.com/image.png" };
      databaseService.fractionIcon.findUnique.mockResolvedValue(mockData);
      databaseService.fractionIcon.update.mockResolvedValue({ ...mockData, deleted_at: new Date() });

      await service.remove(1);
      expect(databaseService.fractionIcon.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { deleted_at: expect.any(Date) },
      });
    });

    it("should throw BadRequestException if id is invalid", async () => {
      await expect(service.remove(NaN)).rejects.toThrow(BadRequestException);
    });

    it("should throw NotFoundException if fraction icon is not found", async () => {
      databaseService.fractionIcon.findUnique.mockResolvedValue(null);
      await expect(service.remove(1)).rejects.toThrow(NotFoundException);
    });
  });
});
