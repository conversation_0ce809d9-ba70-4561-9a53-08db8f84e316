import { ApiProperty } from "@nestjs/swagger";

export class PartnerDiscountUsageDto {
  @ApiProperty({ description: "Partner's full name" })
  partner_name: string;

  @ApiProperty({ description: "Number of discount codes used" })
  discount_uses: number;

  @ApiProperty({ description: "Number of referral links used" })
  link_uses: number;
}

export class DiscountLinksUsageResponseDto {
  @ApiProperty({ description: "Year for which the data is shown" })
  year: number;

  @ApiProperty({
    description: "List of top 5 partners with their discount and link usage statistics",
    type: [PartnerDiscountUsageDto],
  })
  partners: PartnerDiscountUsageDto[];
}
