import { Injectable } from "@nestjs/common";
import { DatabaseService } from "@/database/database.service";
import { InviteProduct, MonthlyInviteResult, YearlyInviteResult, InviteResult } from "./types";
import { lastValueFrom } from "rxjs";
import { HttpModuleService } from "@/http/http.service";
import { Prisma } from "@prisma/client";
import { GetPartnerCouponPerformanceDto } from "./dto/get-partner-coupon-performance.dto";
import { TopPartnersResponseDto } from "./dto/top-partners.dto";
import { DiscountLinksUsageResponseDto } from "./dto/discount-links-usage.dto";

@Injectable()
export class PartnerResultsService {
  constructor(private prisma: DatabaseService, private httpModuleService: HttpModuleService) {}

  async verifyPartnerCoupon(partnerId: number, couponId: number): Promise<boolean> {
    const coupon = await this.getPartnerCoupon(partnerId, couponId);
    return !!coupon;
  }

  async getPartnerGlobalStatistics(partnerId: number, year?: number) {
    try {
      const partnerCoupons = await this.prisma.couponPartners.findMany({
        where: {
          partner_id: partnerId,
        },
        select: {
          coupon_id: true,
        },
      });

      const couponIds = partnerCoupons.map((coupon) => coupon.coupon_id);

      if (couponIds.length === 0) {
        return {
          total_acquired_clients: 0,
          total_earnings: 0,
          direct_license: 0,
          eu_license: 0,
          action_guide: 0,
        };
      }

      let dateFilter = {};
      if (year) {
        const startDate = new Date(year, 0, 1);
        const endDate = new Date(year, 11, 31, 23, 59, 59);

        dateFilter = {
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        };
      }

      const commissions = await this.prisma.commission.findMany({
        where: { coupon: { id: { in: couponIds } }, ...dateFilter },
      });

      const uniqueCustomerIds = [...new Set(commissions.map((commission) => commission.order_customer_id))];
      const totalEarnings = commissions.reduce((acc, commission) => acc + commission.commission_value, 0);

      let directLicenseSales = 0;
      let euLicenseSales = 0;
      let actionGuideSales = 0;

      for (const commission of commissions) {
        if (commission.service_type === "DIRECT_LICENSE") {
          directLicenseSales++;
        } else if (commission.service_type === "EU_LICENSE") {
          euLicenseSales++;
        } else if (commission.service_type === "ACTION_GUIDE") {
          actionGuideSales++;
        }
      }

      return {
        total_acquired_clients: uniqueCustomerIds.length,
        total_earnings: totalEarnings,
        direct_license: directLicenseSales,
        eu_license: euLicenseSales,
        action_guide: actionGuideSales,
      };
    } catch (error) {
      console.error("Error getting partner statistics:", error);
      throw error;
    }
  }

  async getPartnerCommissions(
    partnerId: number,
    options?: {
      year?: number;
      page?: number;
      pageSize?: number;
      search?: string;
      leadSources?: string[];
      products?: string[];
    }
  ) {
    try {
      const page = options?.page || 1;
      const pageSize = options?.pageSize || 12;
      const skip = (page - 1) * pageSize;

      const partnerCoupons = await this.prisma.couponPartners.findMany({
        where: {
          partner_id: partnerId,
        },
        select: {
          coupon_id: true,
        },
      });

      const couponIds = partnerCoupons.map((coupon) => coupon.coupon_id);

      if (couponIds.length === 0) {
        return {
          count: 0,
          results: [],
          totalNetTurnover: 0,
          totalCommission: 0,
        };
      }

      let dateFilter = {};
      if (options?.year) {
        const startDate = new Date(options.year, 0, 1);
        const endDate = new Date(options.year, 11, 31, 23, 59, 59);

        dateFilter = {
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        };
      }

      const couponUsesQuery = {
        where: {
          coupon_id: {
            in: couponIds,
          },
          ...dateFilter,
        },
        select: {
          id: true,
          customer_id: true,
          coupon_id: true,
          order_id: true,
          shopping_cart_id: true,
          commission: true,
          created_at: true,
        },
        orderBy: {
          created_at: "desc" as Prisma.SortOrder,
        },
      };

      // const totalCount = await this.prisma.couponUses.count({
      //   where: couponUsesQuery.where,
      // });

      const couponUses = await this.prisma.couponUses.findMany({
        ...couponUsesQuery,
        skip,
        take: pageSize,
      });

      if (couponUses.length === 0) {
        return {
          count: 0,
          results: [],
          totalNetTurnover: 0,
          totalCommission: 0,
        };
      }

      const customerIds = [...new Set(couponUses.map((use) => use.customer_id))];
      const shoppingCartIds = [
        ...new Set(couponUses.filter((use) => use.shopping_cart_id).map((use) => use.shopping_cart_id)),
      ];

      const coupons = await this.prisma.coupon.findMany({
        where: {
          id: {
            in: couponIds,
          },
        },
        select: {
          id: true,
          code: true,
          value: true,
          discount_type: true,
          commission_percentage: true,
        },
      });

      const shoppingCarts = await this.prisma.shoppingCart.findMany({
        where: {
          id: {
            in: shoppingCartIds as string[],
          },
        },
        select: {
          id: true,
          journey: true,
          total: true,
          items: {
            select: {
              service_type: true,
            },
          },
        },
      });

      const customers = await this.prisma.customer.findMany({
        where: {
          id: {
            in: customerIds,
          },
        },
        select: {
          id: true,
          first_name: true,
          last_name: true,
          email: true,
        },
      });

      const customerFirstPurchases = await this.prisma.couponUses.groupBy({
        by: ["customer_id"],
        _min: {
          created_at: true,
        },
        where: {
          customer_id: {
            in: customerIds,
          },
        },
      });

      const firstPurchaseDateMap = new Map();
      customerFirstPurchases.forEach((item) => {
        firstPurchaseDateMap.set(item.customer_id, item._min.created_at);
      });

      const results = couponUses.map((use) => {
        const coupon = coupons.find((c) => c.id === use.coupon_id);
        const shoppingCart = shoppingCarts.find((sc) => sc.id === use.shopping_cart_id);
        const customer = customers.find((c) => c.id === use.customer_id);
        const firstPurchaseDate = firstPurchaseDateMap.get(use.customer_id);
        const isFirstPurchase = firstPurchaseDate && firstPurchaseDate.getTime() === use.created_at.getTime();

        let productType = "UNKNOWN";
        if (shoppingCart?.items?.length > 0) {
          const serviceType = shoppingCart.items[0].service_type;
          productType = serviceType;
        }

        const leadSource = {
          value: shoppingCart?.journey || "UNKNOWN",
          label: this.mapJourneyToLabel(shoppingCart?.journey || "UNKNOWN"),
        };

        const amount = shoppingCart?.total ? shoppingCart.total : 0;
        const commissionPercentage = coupon?.commission_percentage || 0;
        const commissionValue = use.commission;

        return {
          id: use.id,
          commissionDate: use.created_at,
          amount: amount,
          commissionPercentage: commissionPercentage,
          commissionValue: commissionValue,
          orderId: use.order_id,
          customerName: customer ? `${customer.first_name} ${customer.last_name}` : "Unknown",
          customerEmail: customer?.email,
          leadSource: leadSource,
          firstTimePurchase: isFirstPurchase,
          productType: productType,
        };
      });

      let filteredResults = results;
      if (options?.search) {
        const searchTerm = options.search.toLowerCase();
        filteredResults = results.filter(
          (result) =>
            result.customerName.toLowerCase().includes(searchTerm) ||
            result.orderId.toString().toLowerCase().includes(searchTerm)
        );
      }

      if (options?.leadSources && options.leadSources.length > 0) {
        filteredResults = filteredResults.filter((result) => options.leadSources.includes(result.leadSource.value));
      }

      if (options?.products && options.products.length > 0 && !options.products.includes("all")) {
        filteredResults = filteredResults.filter((result) => options.products.includes(result.productType));
      }

      const totalNetTurnover = filteredResults.reduce((sum, item) => sum + item.amount, 0);
      const totalCommission = filteredResults.reduce((sum, item) => sum + item.commissionValue, 0);

      return {
        count: filteredResults.length,
        results: filteredResults,
        totalNetTurnover,
        totalCommission,
      };
    } catch (error) {
      console.error("Error getting partner commissions:", error);
      throw error;
    }
  }

  async getMonthlyInvitePerformance(inviteId: number, product: InviteProduct = "all"): Promise<MonthlyInviteResult> {
    const coupon = await this.prisma.coupon.findUnique({
      where: { id: inviteId },
      include: {
        commissions: {
          orderBy: { created_at: "asc" },
        },
      },
    });

    if (!coupon) {
      throw new Error(`Invite with ID ${inviteId} not found`);
    }

    let filteredCommissions = coupon.commissions;

    if (product !== "all") {
      const orderIds = coupon.commissions.map((commission) => commission.order_id);

      const orderDetails = await this.getOrderDetailsForMultipleOrders(orderIds);

      filteredCommissions = coupon.commissions.filter((commission) => {
        const orderDetail = orderDetails.find((order) => order.id === commission.order_id);
        return orderDetail && orderDetail.product === product;
      });
    }

    const monthlyResults: Record<string, number> = {};

    for (const commission of filteredCommissions) {
      const date = new Date(commission.created_at);
      const month = date.toLocaleString("default", { month: "long" });

      if (!monthlyResults[month]) {
        monthlyResults[month] = 0;
      }
      monthlyResults[month] += commission.commission_value;
    }

    // Convert to the expected format (values in thousands of euros)
    const results = Object.entries(monthlyResults).map(([month, value]) => ({
      month,
      value: Math.round((value / 1000) * 100) / 100, // Convert to thousands and round to 2 decimal places
    }));

    return {
      invite: {
        id: coupon.id,
        value: coupon.code || coupon.link || String(coupon.id),
        type: coupon.link ? "link" : "code",
        expiration: new Date(coupon.end_date),
        product: "all", // Default to all since we don't have product type in the coupon table
      },
      period: "m",
      product: product,
      results: results,
    };
  }

  async getPartnerInvitePerformanceResults(
    partnerId: number,
    period: "m" | "y" = "m",
    product: InviteProduct = "all"
  ): Promise<InviteResult[]> {
    const partnerCoupons = await this.prisma.couponPartners.findMany({
      where: { partner_id: partnerId },
      include: {
        coupon: true,
      },
    });

    const results = await Promise.all(
      partnerCoupons.map((partnerCoupon) => this.getInvitePerformanceResults(partnerCoupon.coupon_id, period, product))
    );

    return results;
  }

  async getInvitePerformanceResults(
    inviteId: number,
    period: "m" | "y" = "m",
    product: InviteProduct = "all"
  ): Promise<InviteResult> {
    if (period === "y") {
      return this.getYearlyInvitePerformance(inviteId, product);
    } else {
      return this.getMonthlyInvitePerformance(inviteId, product);
    }
  }

  async getYearlyInvitePerformance(inviteId: number, product: InviteProduct = "all"): Promise<YearlyInviteResult> {
    const coupon = await this.prisma.coupon.findUnique({
      where: { id: inviteId },
      include: {
        commissions: {
          orderBy: { created_at: "asc" },
        },
      },
    });

    if (!coupon) {
      throw new Error(`Invite with ID ${inviteId} not found`);
    }

    let filteredCommissions = coupon.commissions;

    if (product !== "all") {
      const orderIds = coupon.commissions.map((commission) => commission.order_id);

      const orderDetails = await this.getOrderDetailsForMultipleOrders(orderIds);

      filteredCommissions = coupon.commissions.filter((commission) => {
        const orderDetail = orderDetails.find((order) => order.id === commission.order_id);
        return orderDetail && orderDetail.product === product;
      });
    }

    const yearlyResults: Record<string, number> = {};

    for (const commission of filteredCommissions) {
      const date = new Date(commission.created_at);
      const year = date.getFullYear().toString();

      if (!yearlyResults[year]) {
        yearlyResults[year] = 0;
      }
      yearlyResults[year] += commission.commission_value;
    }

    // Convert to the expected format (values in thousands of euros)
    const results = Object.entries(yearlyResults).map(([year, value]) => ({
      year,
      value: Math.round((value / 1000) * 100) / 100, // Convert to thousands and round to 2 decimal places
    }));

    return {
      invite: {
        id: coupon.id,
        value: coupon.code || coupon.link || String(coupon.id),
        type: coupon.link ? "link" : "code",
        expiration: new Date(coupon.end_date),
        product: "all", // Default to all since we don't have product type in the coupon table
      },
      period: "y",
      product: product,
      results: results,
    };
  }

  private async getOrderDetailsForMultipleOrders(orderIds: number[]) {
    if (orderIds.length === 0) {
      return [];
    }

    try {
      const response = await lastValueFrom(
        this.httpModuleService.payments({
          url: "/payment-order",
          params: {
            orderIds: orderIds.join(","),
          },
          method: "GET",
        })
      );

      if (!response || !response.data) {
        console.error("Payment API response error: No data returned");
        throw new Error("Failed to fetch order details from payment API");
      }

      let orders = [];
      if (Array.isArray(response.data)) {
        orders = response.data;
      } else if (response.data.results && Array.isArray(response.data.results)) {
        orders = response.data.results;
      } else if (typeof response.data === "object") {
        orders = [response.data];
      }

      return orderIds.map((orderId) => {
        const order = orders.find((o) => o.id === orderId);

        if (!order || !order.items || !Array.isArray(order.items) || order.items.length === 0) {
          return { id: orderId, product: "all" as InviteProduct };
        }

        const serviceTypes = [...new Set(order.items.map((item) => item.service_type))];

        if (serviceTypes.length !== 1) {
          return { id: orderId, product: "all" as InviteProduct };
        }

        let product: InviteProduct = "all";

        switch (serviceTypes[0]) {
          case "DIRECT_LICENSE":
            product = "direct_license";
            break;
          case "EU_LICENSE":
            product = "eu_license";
            break;
          case "ACTION_GUIDE":
            product = "action_guide";
            break;
          default:
            product = "all";
        }

        return { id: orderId, product };
      });
    } catch (error) {
      console.error("Error fetching order details from payment API:", error);

      return orderIds.map((id) => ({
        id,
        product: "all" as InviteProduct,
      }));
    }
  }

  async getPartnerCoupon(partnerId: number, couponId: number) {
    return this.prisma.couponPartners.findFirst({
      where: {
        partner_id: partnerId,
        coupon_id: couponId,
      },
      include: {
        coupon: true,
      },
    });
  }

  async getPartnerCouponPerformance(partnerId: number, params?: GetPartnerCouponPerformanceDto) {
    try {
      const year = params?.year ?? new Date().getFullYear();
      const groupBy = params?.groupBy ?? "quarter";

      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 11, 31);

      const coupons = await this.prisma.coupon.findMany({
        where: {
          partners: {
            some: {
              partner_id: partnerId,
            },
          },
        },
        include: {
          commissions: {
            where: {
              affiliate_link: null,
              created_at: {
                gte: startDate,
                lte: endDate,
              },
            },
          },
        },
      });

      // Calculate commission values for each coupon
      const performanceData = coupons.map((coupon) => {
        const quarterlyData = new Array(4).fill(0);
        const yearlyData = [0];
        let totalCommission = 0;

        coupon.commissions.forEach((commission) => {
          const date = new Date(commission.created_at);
          const quarter = Math.floor(date.getMonth() / 3);
          const commissionInThousands = (commission.commission_value || 0) / 1000; // Convert to thousands of euros

          totalCommission += commissionInThousands;
          quarterlyData[quarter] += commissionInThousands;
          yearlyData[0] += commissionInThousands;
        });

        return {
          code: coupon.code,
          quarterlyData,
          yearlyData,
          totalCommission,
        };
      });

      // Sort by total commission value and take top 2
      const topTwoPerformers = performanceData.sort((a, b) => b.totalCommission - a.totalCommission).slice(0, 2);

      return {
        year,
        groupBy,
        data: topTwoPerformers.map(({ code, quarterlyData, yearlyData }) => ({
          code,
          values:
            groupBy === "quarter"
              ? quarterlyData.map((value, index) => ({
                  quarter: `Quarter ${index + 1}`,
                  value: Math.round(value * 100) / 100, // Round to 2 decimal places
                }))
              : yearlyData.map((value) => ({
                  year: year.toString(),
                  value: Math.round(value * 100) / 100, // Round to 2 decimal places
                })),
        })),
      };
    } catch (error) {
      console.error("Error getting partner coupon performance:", error);
      throw error;
    }
  }

  async getLicensePurchases(partnerId: number, year?: number) {
    try {
      const startDate = new Date(year ?? new Date().getFullYear(), 0, 1);
      const endDate = new Date(year ?? new Date().getFullYear(), 11, 31);

      const partner = await this.prisma.partner.findUnique({
        where: {
          id: partnerId,
        },
      });

      const commissions = await this.prisma.commission.findMany({
        where: {
          user_id: partner.user_id,
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: {
          created_at: "asc",
        },
        include: {
          coupon: true,
        },
      });

      let eu_license = 0;
      let direct_license = 0;
      let action_guide = 0;

      for (const commission of commissions) {
        const commissionValue = (commission.commission_value || 0) / 100000; // Convert cents to thousands of euros

        if (commission.service_type === "EU_LICENSE") {
          eu_license += commissionValue;
        }

        if (commission.service_type === "DIRECT_LICENSE") {
          direct_license += commissionValue;
        }

        if (commission.service_type === "ACTION_GUIDE") {
          action_guide += commissionValue;
        }
      }

      return {
        year,
        eu_license,
        direct_license,
        action_guide,
      };
    } catch (error) {
      console.error("Error getting license purchases:", error);
      throw error;
    }
  }

  async getRevenuePlusEarningsOfTopFivePartners(year?: number): Promise<TopPartnersResponseDto> {
    try {
      let dateFilter = {};
      if (year) {
        const startDate = new Date(year, 0, 1);
        const endDate = new Date(year, 11, 31, 23, 59, 59);

        dateFilter = {
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        };
      }

      // Get all partners with their commissions
      const partners = await this.prisma.partner.findMany({
        select: {
          id: true,
          first_name: true,
          last_name: true,
          coupons: {
            select: {
              coupon: {
                include: {
                  commissions: {
                    where: {
                      ...dateFilter,
                    },
                  },
                },
              },
            },
          },
        },
      });

      // Calculate revenue and earnings for each partner
      const partnerStats = partners.map((partner) => {
        let totalRevenue = 0;
        let totalEarnings = 0;

        partner.coupons.forEach((couponPartner) => {
          couponPartner.coupon.commissions.forEach((commission) => {
            // Revenue is the total order value before commission
            const revenue = commission.commission_value / (commission.commission_percentage / 100);
            totalRevenue += revenue;
            totalEarnings += commission.commission_value;
          });
        });

        return {
          partner_name: partner.first_name || partner.last_name,
          revenue: Math.round(totalRevenue / 100) / 10, // Convert cents to thousands of euros
          earnings: Math.round(totalEarnings / 100) / 10, // Convert cents to thousands of euros
        };
      });

      // Sort by revenue and get top 5
      const topPartners = partnerStats.sort((a, b) => b.revenue - a.revenue).slice(0, 5);

      return {
        year: year || new Date().getFullYear(),
        partners: topPartners,
      };
    } catch (error) {
      console.error("Error getting top partners:", error);
      throw error;
    }
  }

  async getDiscountLinksUsage(year?: number): Promise<DiscountLinksUsageResponseDto> {
    try {
      let dateFilter = {};
      if (year) {
        const startDate = new Date(year, 0, 1);
        const endDate = new Date(year, 11, 31, 23, 59, 59);

        dateFilter = {
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        };
      }

      // Get all partners with their coupons and coupon uses
      const partners = await this.prisma.partner.findMany({
        select: {
          id: true,
          first_name: true,
          last_name: true,
          coupons: {
            select: {
              coupon: {
                select: {
                  code: true,
                  link: true,
                  coupon_uses: {
                    where: dateFilter,
                  },
                },
              },
            },
          },
        },
      });

      // Calculate discount and link usage for each partner
      const partnerStats = partners.map((partner) => {
        let discountUses = 0;
        let linkUses = 0;

        partner.coupons.forEach((couponPartner) => {
          const coupon = couponPartner.coupon;
          const usageCount = coupon.coupon_uses.length;

          // If the coupon has a link, it's a referral link, otherwise it's a discount code
          if (coupon.link) {
            linkUses += usageCount;
          } else {
            discountUses += usageCount;
          }
        });

        return {
          partner_name: partner.first_name || partner.last_name,
          discount_uses: discountUses,
          link_uses: linkUses,
        };
      });

      // Sort by total uses (discount + links) and get top 5
      const topPartners = partnerStats
        .sort((a, b) => b.discount_uses + b.link_uses - (a.discount_uses + a.link_uses))
        .slice(0, 5);

      return {
        year: year || new Date().getFullYear(),
        partners: topPartners,
      };
    } catch (error) {
      console.error("Error getting discount and links usage:", error);
      throw error;
    }
  }

  private mapJourneyToLabel(journey: string): string {
    const journeyMap = {
      LONG: "Full Journey",
      DIRECT_LICENSE: "Direct License",
      QUICK_LICENSE: "Quick License",
      QUICK_ACTION_GUIDE: "Quick Action Guide",
    };

    return journeyMap[journey] || journey;
  }
}
