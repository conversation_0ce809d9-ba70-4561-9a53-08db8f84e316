import { <PERSON>, Get, Param, Query, NotFoundException, BadRequestException, ParseIntPipe } from "@nestjs/common";
import { PartnerResultsService } from "./partner-results.service";
import { ApiTags, ApiOperation, ApiParam, ApiQuery, ApiResponse } from "@nestjs/swagger";
import { InviteProduct } from "./types";
import { GetPartnerCouponPerformanceDto } from "./dto/get-partner-coupon-performance.dto";
import { TopPartnersResponseDto } from "./dto/top-partners.dto";
import { DiscountLinksUsageResponseDto } from "./dto/discount-links-usage.dto";

@ApiTags("partner-results")
@Controller("partner-results")
export class PartnerResultsController {
  constructor(private readonly partnerResultsService: PartnerResultsService) {}

  @Get(":partnerId/performance")
  @ApiOperation({ summary: "Get performance results for all partner invites" })
  @ApiParam({ name: "partnerId", description: "ID of the partner" })
  @ApiQuery({ name: "period", enum: ["m", "y"], required: false, description: "Period: monthly or yearly" })
  @ApiQuery({ name: "product", enum: ["all", "direct_license", "eu_license", "action_guide"], required: false })
  @ApiResponse({ status: 200, description: "Returns performance results for all partner invites" })
  async getPartnerPerformance(
    @Param("partnerId", ParseIntPipe) partnerId: number,
    @Query("period") period: "m" | "y" = "m",
    @Query("product") product: InviteProduct = "all"
  ) {
    try {
      return await this.partnerResultsService.getPartnerInvitePerformanceResults(partnerId, period, product);
    } catch (error) {
      throw new BadRequestException(`Failed to fetch partner performance: ${error.message}`);
    }
  }

  @Get(":partnerId/invite/:inviteId/performance")
  @ApiOperation({ summary: "Get performance results for a specific invite" })
  @ApiParam({ name: "partnerId", description: "ID of the partner" })
  @ApiParam({ name: "inviteId", description: "ID of the invite (coupon)" })
  @ApiQuery({ name: "period", enum: ["m", "y"], required: false, description: "Period: monthly or yearly" })
  @ApiQuery({ name: "product", enum: ["all", "direct-license", "eu-license", "action-guide"], required: false })
  @ApiResponse({ status: 200, description: "Returns performance results for the specified invite" })
  async getInvitePerformance(
    @Param("partnerId", ParseIntPipe) partnerId: number,
    @Param("inviteId", ParseIntPipe) inviteId: number,
    @Query("period") period: "m" | "y" = "m",
    @Query("product") product: InviteProduct = "all"
  ) {
    const belongs = await this.partnerResultsService.verifyPartnerCoupon(partnerId, inviteId);
    if (!belongs) {
      throw new NotFoundException(`Invite with ID ${inviteId} not found for partner ${partnerId}`);
    }
    try {
      return await this.partnerResultsService.getInvitePerformanceResults(inviteId, period, product);
    } catch (error) {
      throw new BadRequestException(`Failed to fetch invite performance: ${error.message}`);
    }
  }

  @Get(":partner_id/statistics")
  async getPartnerGlobalStatistics(@Param("partner_id") partner_id: string, @Query("year") year?: string) {
    return await this.partnerResultsService.getPartnerGlobalStatistics(
      Number(partner_id),
      year ? Number(year) : undefined
    );
  }

  @Get(":partner_id/commissions")
  async getPartnerCommissions(
    @Param("partner_id") partner_id: string,
    @Query("year") year?: number,
    @Query("page") page?: number,
    @Query("pageSize") pageSize?: number,
    @Query("search") search?: string,
    @Query("leadSources") leadSources?: string,
    @Query("products") products?: string
  ) {
    const leadSourcesArray = leadSources ? leadSources.split(",") : undefined;
    const productsArray = products ? products.split(",") : undefined;

    return await this.partnerResultsService.getPartnerCommissions(Number(partner_id), {
      year,
      page: page ? Number(page) : 1,
      pageSize: pageSize ? Number(pageSize) : 12,
      search,
      leadSources: leadSourcesArray,
      products: productsArray,
    });
  }

  @Get(":partner_id/coupon-performance")
  @ApiOperation({ summary: "Get partner coupon performance data for visualization" })
  async getPartnerCouponPerformance(
    @Param("partner_id") partner_id: string,
    @Query() params?: GetPartnerCouponPerformanceDto
  ) {
    return this.partnerResultsService.getPartnerCouponPerformance(Number(partner_id), params);
  }

  @Get(":partner_id/license-purchases")
  @ApiOperation({ summary: "Get partner's license purchases data over time" })
  async getLicensePurchases(@Param("partner_id", ParseIntPipe) partnerId: number, @Query("year") year?: number) {
    return this.partnerResultsService.getLicensePurchases(partnerId, year);
  }

  @Get("revenue-plus-earnings")
  @ApiOperation({ summary: "Get revenue and earnings for top 5 partners" })
  @ApiQuery({ name: "year", type: Number, required: false, description: "Filter by year" })
  @ApiResponse({
    status: 200,
    description: "Returns revenue and earnings for top 5 partners",
    type: TopPartnersResponseDto,
  })
  async getRevenuePlusEarningsOfTopFivePartners(@Query("year") year?: number): Promise<TopPartnersResponseDto> {
    return this.partnerResultsService.getRevenuePlusEarningsOfTopFivePartners(year);
  }

  @Get("discount-links-used")
  @ApiOperation({ summary: "Get top 5 partners by discount codes and referral links usage" })
  @ApiQuery({ name: "year", type: Number, required: false, description: "Filter by year" })
  @ApiResponse({
    status: 200,
    description: "Returns top 5 partners by discount codes and referral links usage",
    type: DiscountLinksUsageResponseDto,
  })
  async getDiscountLinksUsage(@Query("year") year?: number): Promise<DiscountLinksUsageResponseDto> {
    return this.partnerResultsService.getDiscountLinksUsage(year);
  }
}
