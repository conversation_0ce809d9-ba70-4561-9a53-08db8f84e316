import { ApiProperty } from "@nestjs/swagger";

export class CreateRepresentativeTierDto {
  @ApiProperty({
    required: true,
    description: "Name of the representative tier",
  })
  name: string;

  @ApiProperty({
    required: true,
    description: "Price of the representative tier",
  })
  price: number;

  @ApiProperty({
    required: true,
    description: "ID of the country",
  })
  country_id: number;
}
