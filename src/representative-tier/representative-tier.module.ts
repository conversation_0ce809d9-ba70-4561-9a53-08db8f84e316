import { Module } from "@nestjs/common";
import { DatabaseModule } from "../database/database.module";
import { RepresentativeTierController } from "./representative-tier.controller";
import { RepresentativeTierService } from "./representative-tier.service";

@Module({
  imports: [DatabaseModule],
  controllers: [RepresentativeTierController],
  providers: [RepresentativeTierService],
})
export class RepresentativeTierModule {}
