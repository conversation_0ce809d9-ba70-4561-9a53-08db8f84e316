import { HttpService } from "@nestjs/axios";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { AxiosResponse } from "axios";
import { Observable } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { MethodsRequest } from "./http.interface";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ROLE } from "@/shared/auth/const";
import { Role } from "@/shared/auth/role.enum";

interface IParams {
  [key: string]: any;
}

@Injectable()
export class HttpModuleService {
  constructor(private httpService: HttpService) {}

  admin({
    url,
    params,
    method,
  }: {
    url: string;
    params: IParams;
    method: "get" | "post" | "patch" | "delete";
  }): Observable<AxiosResponse<any>> {
    if (!url || !method) {
      throw new HttpException("Invalid input", HttpStatus.BAD_REQUEST);
    }
    return this.httpService
      .request({
        url: `${process.env.ADMIN_API_URL}${url}`,
        method: method,
        [method === "post" || method === "patch" ? "data" : "params"]: params,
        headers: {
          [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
          [HEADER_USER_ROLE]: Role.SYSTEM,
        },
      })
      .pipe(
        map((response: AxiosResponse) => response),

        catchError((error) => {
          throw new HttpException(error.response.data, error.response.status);
        })
      );
  }

  auth({
    url,
    params,
    method,
  }: {
    url: string;
    params: IParams;
    method: MethodsRequest;
  }): Observable<AxiosResponse<any>> {
    if (!url || !method) {
      throw new HttpException("Invalid input", HttpStatus.BAD_REQUEST);
    }

    return this.httpService
      .request({
        url: `${process.env.AUTH_API_URL}${url}`,
        method: method,
        [method === "POST" || method === "PUT" || method === "PATCH" ? "data" : "params"]: params,
        headers: {
          [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
          [HEADER_USER_ROLE]: Role.SYSTEM,
        },
      })
      .pipe(
        map((response: AxiosResponse) => response),

        catchError((error) => {
          console.log(error);
          throw new HttpException(error.response.data, error.response.status);
        })
      );
  }

  customer({
    url,
    params,
    method,
  }: {
    url: string;
    params: IParams;
    method: MethodsRequest;
  }): Observable<AxiosResponse<any>> {
    if (!url || !method) {
      throw new HttpException("Invalid input", HttpStatus.BAD_REQUEST);
    }

    if (!process.env.CUSTOMER_API_URL) {
      throw new HttpException("Customer API URL is not set", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return this.httpService
      .request({
        url: `${process.env.CUSTOMER_API_URL}${url}`,
        method: method,
        [method === "POST" || method === "PUT" || method === "PATCH" ? "data" : "params"]: params,
        headers: {
          [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
          [HEADER_USER_ROLE]: Role.SYSTEM,
        },
      })
      .pipe(
        map((response: AxiosResponse) => response),

        catchError((error) => {
          console.log(error);
          throw new HttpException(error.response.data, error.response.status);
        })
      );
  }
}
