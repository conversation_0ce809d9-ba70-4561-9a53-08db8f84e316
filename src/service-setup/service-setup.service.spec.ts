import { Test, TestingModule } from "@nestjs/testing";
import { ServiceSetupService } from "./service-setup.service";
import { DatabaseService } from "../database/database.service";
import { NotFoundException, BadRequestException } from "@nestjs/common";
import { CommitmentAnswer, SubmitCommitmentDto } from "./dto/submit-commitment.dto";

describe("ServiceSetupService", () => {
  let service: ServiceSetupService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    country: {
      findUnique: jest.fn(),
    },
    packagingService: {
      findMany: jest.fn(),
    },
    reportSet: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    reportSetFrequency: {
      findMany: jest.fn(),
    },
    criteria: {
      findMany: jest.fn(),
    },
    representativeTier: {
      findMany: jest.fn(),
    },
    otherCost: {
      findMany: jest.fn(),
    },
    countryPriceList: {
      findMany: jest.fn(),
    },
    requiredInformation: {
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ServiceSetupService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<ServiceSetupService>(ServiceSetupService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findServiceSetup", () => {
    it("should return the service setup for a valid country", async () => {
      const mockCountry = {
        id: 1,
        code: "BR",
        name: "Brasil",
        packaging_services: [],
        country_price_lists: [],
        representative_tiers: [],
        required_informations: [],
        other_costs: [],
      };

      mockDatabaseService.country.findUnique.mockResolvedValue(mockCountry);

      const result = await service.findServiceSetup("BR");

      expect(result).toBeDefined();
      expect(result.code).toBe("BR");
      expect(databaseService.country.findUnique).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { code: "BR" },
        })
      );
    });

    it("should throw NotFoundException when the country is not found", async () => {
      mockDatabaseService.country.findUnique.mockResolvedValue(null);

      await expect(service.findServiceSetup("XX")).rejects.toThrow(NotFoundException);
    });

    it("should correctly transform the serviceSetup object", async () => {
      const mockCountry = {
        id: 1,
        code: "BR",
        name: "Brasil",
        packaging_services: [
          {
            id: 1,
            name: "Service 1",
            report_set_frequencies: [
              {
                id: 1,
                frequency: JSON.stringify({ type: "monthly" }),
              },
            ],
            report_sets: [
              {
                id: 1,
                fractions: [{ id: 1 }],
                columns: [{ id: 1 }],
                price_lists: [{ id: 1 }],
              },
            ],
          },
        ],
        country_price_lists: [{ id: 1 }],
        representative_tiers: [{ id: 1 }],
        required_informations: [{ id: 1, file: { id: 1 } }],
        other_costs: [{ id: 1 }],
      };

      mockDatabaseService.country.findUnique.mockResolvedValue(mockCountry);

      const result = await service.findServiceSetup("BR");

      expect(result).toEqual({
        ...mockCountry,
        packaging_services: [
          {
            ...mockCountry.packaging_services[0],
            report_set_frequencies: [
              {
                id: 1,
                frequency: { type: "monthly" },
              },
            ],
            report_sets: [
              {
                id: 1,
                fractions: [{ id: 1 }],
                columns: [{ id: 1 }],
                price_lists: [{ id: 1 }],
              },
            ],
          },
        ],
        country_price_lists: [{ id: 1 }],
        representative_tiers: [{ id: 1 }],
        required_informations: [{ id: 1, file: { id: 1 } }],
        other_costs: [{ id: 1 }],
      });
    });

    it("should keep frequency as null when there is no data", async () => {
      const mockCountry = {
        id: 1,
        code: "BR",
        name: "Brasil",
        packaging_services: [
          {
            id: 1,
            name: "Service 1",
            report_set_frequencies: [
              {
                id: 1,
                frequency: null,
              },
            ],
            report_sets: [],
          },
        ],
        country_price_lists: [],
        representative_tiers: [],
        required_informations: [],
        other_costs: [],
      };

      mockDatabaseService.country.findUnique.mockResolvedValue(mockCountry);

      const result = await service.findServiceSetup("BR");

      expect(result.packaging_services[0].report_set_frequencies[0].frequency).toBeNull();
    });

    it("should preserve the complete structure of the serviceSetup object", async () => {
      const mockCountry = {
        id: 1,
        code: "BR",
        name: "Brasil",
        packaging_services: [],
        country_price_lists: [{ id: 1 }],
        representative_tiers: [{ id: 1 }],
        required_informations: [
          {
            id: 1,
            file: { id: 1 },
          },
        ],
        other_costs: [{ id: 1 }],
      };

      mockDatabaseService.country.findUnique.mockResolvedValue(mockCountry);

      const result = await service.findServiceSetup("BR");

      expect(result).toEqual({
        ...mockCountry,
        packaging_services: [],
        country_price_lists: [{ id: 1 }],
        representative_tiers: [{ id: 1 }],
        required_informations: [
          {
            id: 1,
            file: { id: 1 },
          },
        ],
        other_costs: [{ id: 1 }],
      });
    });
  });

  describe("findServiceSetupRepresentativeTiers", () => {
    it("should return representative tiers with criteria flags", async () => {
      const mockRepresentativeTiers = [
        {
          id: 1,
          name: "Tier 1",
          country: {
            criterias: [{ id: 1, type: "REPRESENTATIVE_TIER" }],
          },
        },
      ];

      mockDatabaseService.representativeTier = {
        findMany: jest.fn().mockResolvedValue(mockRepresentativeTiers),
      };

      const result = await service.findServiceSetupRepresentativeTiers("BR");

      expect(result[0].has_criteria).toBe(true);
    });
  });

  describe("findServiceSetupOtherCosts", () => {
    it("should return other costs with criteria flags", async () => {
      const mockOtherCosts = [
        {
          id: 1,
          name: "Cost 1",
          country: {
            criterias: [{ id: 1, type: "OTHER_COST" }],
          },
        },
      ];

      mockDatabaseService.otherCost = {
        findMany: jest.fn().mockResolvedValue(mockOtherCosts),
      };

      const result = await service.findServiceSetupOtherCosts("BR");

      expect(result[0].has_criteria).toBe(true);
    });
  });

  describe("findServiceSetupPriceLists", () => {
    it("should return the country's price lists", async () => {
      const mockPriceLists = [
        {
          id: 1,
          price_list: {
            id: 1,
            name: "Price List 1",
          },
        },
      ];

      mockDatabaseService.countryPriceList = {
        findMany: jest.fn().mockResolvedValue(mockPriceLists),
      };

      const result = await service.findServiceSetupPriceLists("BR");

      expect(result).toEqual(mockPriceLists);
    });
  });

  describe("findServiceSetupRequiredInformations", () => {
    it("should return required information with criteria flags", async () => {
      const mockRequiredInformations = [
        {
          id: 1,
          name: "Info 1",
          file: { id: 1 },
          criterias: [{ id: 1, type: "REQUIRED_INFORMATION" }],
        },
      ];

      mockDatabaseService.requiredInformation = {
        findMany: jest.fn().mockResolvedValue(mockRequiredInformations),
      };

      const result = await service.findServiceSetupRequiredInformations("BR");

      expect(result[0].has_criteria).toBe(true);
    });
  });

  describe("getServiceSetupStatus", () => {
    it("should return incomplete status when there are no packaging services", async () => {
      const mockCountry = {
        packaging_services: [],
        country_price_lists: [],
      };

      mockDatabaseService.country.findUnique.mockResolvedValue(mockCountry);

      const result = await service.getServiceSetupStatus("BR");

      expect(result).toEqual({
        completed: false,
        message: "Packaging services are empty",
      });
    });
  });

  describe("findServiceSetupCommitment", () => {
    it("should return commitment criteria for a valid country", async () => {
      const mockCriterias = [
        { id: 1, type: "PACKAGING_SERVICE" },
        { id: 2, type: "REPORT_SET" },
      ];

      mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

      const result = await service.findServiceSetupCommitment("BR");

      expect(result).toEqual(mockCriterias);
      expect(databaseService.criteria.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            mode: "COMMITMENT",
            country: { code: "BR" },
            deleted_at: null,
          },
        })
      );
    });

    it("should throw NotFoundException when there are no commitment criteria", async () => {
      mockDatabaseService.criteria.findMany.mockResolvedValue([]);

      await expect(service.findServiceSetupCommitment("BR")).rejects.toThrow(NotFoundException);
    });
  });

  describe("findServiceSetupReportFrequencies", () => {
    it("should return report frequencies with criteria flags", async () => {
      const mockReportFrequencies = [
        {
          id: 1,
          frequency: JSON.stringify({ type: "monthly" }),
          packaging_service: {
            criterias: [{ id: 1, type: "REPORT_FREQUENCY" }],
          },
        },
      ];

      mockDatabaseService.reportSetFrequency = {
        findMany: jest.fn().mockResolvedValue(mockReportFrequencies),
      };

      const result = await service.findServiceSetupReportFrequencies("BR");

      expect(result[0].has_criteria).toBe(true);
      expect(result[0].frequency).toEqual({ type: "monthly" });
    });

    it("should handle null frequency", async () => {
      const mockReportFrequencies = [
        {
          id: 1,
          frequency: null,
          packaging_service: {
            criterias: [],
          },
        },
      ];

      mockDatabaseService.reportSetFrequency = {
        findMany: jest.fn().mockResolvedValue(mockReportFrequencies),
      };

      const result = await service.findServiceSetupReportFrequencies("BR");

      expect(result[0].frequency).toBeNull();
    });
  });

  describe("findServiceSetupPackagingServices", () => {
    it("should return packaging services with criteria flags", async () => {
      const mockPackagingServices = [
        {
          id: 1,
          name: "Service 1",
          criterias: [
            { id: 1, type: "PACKAGING_SERVICE" },
            { id: 2, type: "REPORT_SET" },
            { id: 3, type: "REPORT_FREQUENCY" },
          ],
        },
      ];

      mockDatabaseService.packagingService.findMany.mockResolvedValue(mockPackagingServices);

      const result = await service.findServiceSetupPackagingServices("BR");

      expect(result[0].has_criteria).toBe(true);
      expect(result[0].has_report_set_criteria).toBe(true);
      expect(result[0].has_report_frequency_criteria).toBe(true);
    });
  });

  describe("findServiceSetupReportSets", () => {
    it("should return report sets with criteria flag", async () => {
      const mockReportSets = [
        {
          id: 1,
          name: "Report Set 1",
          packaging_service: {
            criterias: [{ id: 1, type: "REPORT_SET" }],
          },
        },
      ];

      mockDatabaseService.reportSet.findMany.mockResolvedValue(mockReportSets);

      const result = await service.findServiceSetupReportSets("BR");

      expect(result[0].has_criteria).toBe(true);
    });
  });

  describe("findServiceSetupReportSet", () => {
    it("should return a specific report set", async () => {
      const mockReportSet = {
        id: 1,
        name: "Report Set 1",
        packaging_service: {
          criterias: [{ id: 1, type: "REPORT_SET" }],
        },
        sheet_file: { id: 1 },
        fractions: [],
        columns: [],
        price_lists: [],
      };

      mockDatabaseService.reportSet.findUnique.mockResolvedValue(mockReportSet);

      const result = await service.findServiceSetupReportSet("BR", 1);

      expect(result.has_criteria).toBe(true);
      expect(result.id).toBe(1);
    });

    it("should throw NotFoundException when report set is not found", async () => {
      mockDatabaseService.reportSet.findUnique.mockResolvedValue(null);

      await expect(service.findServiceSetupReportSet("BR", 1)).rejects.toThrow(NotFoundException);
    });
  });

  describe("submitServiceSetupCommitment", () => {
    const mockCommitmentData = {
      year: 2024,
      commitment: [
        { id: 1, answer: "OBLIGED" },
        { id: 2, answer: "1" },
      ],
    };

    describe("criteria validations", () => {
      const mockCountry = {
        id: 1,
        code: "BR",
        name: "Brasil",
        flag_url: "flag.png",
        authorize_representative_obligated: false,
        other_costs_obligated: false,
        packaging_services: [],
        country_price_lists: [
          {
            price_list: { condition_type_value: "2024" },
          },
        ],
      };

      beforeEach(() => {
        mockDatabaseService.country.findUnique.mockResolvedValue(mockCountry);
      });

      it("should throw BadRequestException when commitment is not an array", async () => {
        const commitmentData = {
          year: 2024,
          commitment: "not an array" as unknown as CommitmentAnswer[],
        };

        await expect(service.submitServiceSetupCommitment("BR", commitmentData)).rejects.toThrow(
          new BadRequestException("Commitment answers array is required")
        );
      });

      it("should throw BadRequestException when commitment is null", async () => {
        const commitmentData: SubmitCommitmentDto = {
          year: 2024,
          commitment: null as unknown as CommitmentAnswer[],
        };

        await expect(service.submitServiceSetupCommitment("BR", commitmentData)).rejects.toThrow(
          new BadRequestException("Commitment answers array is required")
        );
      });

      it("should throw BadRequestException when commitment is undefined", async () => {
        const commitmentData: SubmitCommitmentDto = {
          year: 2024,
          commitment: undefined as unknown as CommitmentAnswer[],
        };

        await expect(service.submitServiceSetupCommitment("BR", commitmentData)).rejects.toThrow(
          new BadRequestException("Commitment answers array is required")
        );
      });

      it("should throw NotFoundException when commitment is an empty array", async () => {
        const commitmentData: SubmitCommitmentDto = {
          year: 2024,
          commitment: [],
        };

        mockDatabaseService.criteria.findMany.mockResolvedValue([]);

        await expect(service.submitServiceSetupCommitment("BR", commitmentData)).rejects.toThrow(
          new NotFoundException("Commitment is empty")
        );
      });

      it("should throw BadRequestException when an answer is missing", async () => {
        const mockCriterias = [
          {
            id: 1,
            type: "PACKAGING_SERVICE",
            options: [{ value: "OBLIGED" }],
          },
        ];

        mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

        const commitmentData = {
          year: 2024,
          commitment: [],
        };

        await expect(service.submitServiceSetupCommitment("BR", commitmentData)).rejects.toThrow(
          new BadRequestException("Missing answer for criteria 1")
        );
      });

      it("should throw BadRequestException when an answer is empty", async () => {
        const mockCriterias = [
          {
            id: 1,
            type: "PACKAGING_SERVICE",
            options: [{ value: "OBLIGED" }],
          },
        ];

        mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

        const commitmentData = {
          year: 2024,
          commitment: [{ id: 1, answer: "" }],
        };

        await expect(service.submitServiceSetupCommitment("BR", commitmentData)).rejects.toThrow(
          new BadRequestException("Commitment answers are invalid")
        );
      });

      it("should throw BadRequestException when an answer is invalid", async () => {
        const mockCriterias = [
          {
            id: 1,
            type: "PACKAGING_SERVICE",
            options: [{ value: "OBLIGED" }],
          },
        ];

        mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

        const commitmentData = {
          year: 2024,
          commitment: [{ id: 1, answer: "INVALID_OPTION" }],
        };

        await expect(service.submitServiceSetupCommitment("BR", commitmentData)).rejects.toThrow(
          new BadRequestException("Invalid answer for criteria 1")
        );
      });

      it("should accept a valid answer", async () => {
        const mockCriterias = [
          {
            id: 1,
            type: "PACKAGING_SERVICE",
            options: [{ value: "OBLIGED" }],
          },
        ];

        mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

        const commitmentData = {
          year: 2024,
          commitment: [{ id: 1, answer: "OBLIGED" }],
        };

        const result = await service.submitServiceSetupCommitment("BR", commitmentData);
        expect(result).toBeDefined();
      });

      it("should validate multiple criteria answers", async () => {
        const mockCriterias = [
          {
            id: 1,
            type: "PACKAGING_SERVICE",
            options: [{ value: "OBLIGED" }],
          },
          {
            id: 2,
            type: "REPORT_SET",
            options: [{ value: "1" }],
          },
        ];

        mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

        const commitmentData = {
          year: 2024,
          commitment: [{ id: 1, answer: "OBLIGED" }],
        };

        await expect(service.submitServiceSetupCommitment("BR", commitmentData)).rejects.toThrow(
          new BadRequestException("Missing answer for criteria 2")
        );
      });

      it("should validate all answers before processing", async () => {
        const mockCriterias = [
          {
            id: 1,
            type: "PACKAGING_SERVICE",
            options: [{ value: "OBLIGED" }],
          },
          {
            id: 2,
            type: "REPORT_SET",
            options: [{ value: "1" }],
          },
        ];

        mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

        const commitmentData = {
          year: 2024,
          commitment: [
            { id: 1, answer: "OBLIGED" },
            { id: 2, answer: "INVALID_OPTION" },
          ],
        };

        await expect(service.submitServiceSetupCommitment("BR", commitmentData)).rejects.toThrow(
          new BadRequestException("Invalid answer for criteria 2")
        );
      });
    });

    it("should process different types of criteria", async () => {
      const mockCountry = {
        id: 1,
        code: "BR",
        name: "Brasil",
        flag_url: "flag.png",
        authorize_representative_obligated: false,
        other_costs_obligated: false,
        packaging_services: [
          {
            id: 1,
            name: "Service 1",
            report_sets: [{ id: 1 }],
            report_set_frequencies: [{ id: 1, frequency: JSON.stringify({ type: "monthly" }) }],
          },
        ],
        country_price_lists: [
          {
            price_list: { id: 1, condition_type_value: "2024" },
          },
        ],
        representative_tiers: [{ id: 1 }],
        required_informations: [{ id: 1 }],
        other_costs: [{ id: 1 }],
      };

      const mockCriterias = [
        {
          id: 1,
          type: "PACKAGING_SERVICE",
          packaging_service_id: 1,
          options: [{ value: "OBLIGED" }],
        },
        {
          id: 2,
          type: "REPORT_SET",
          packaging_service_id: 1,
          options: [{ value: "1" }],
        },
        {
          id: 3,
          type: "REPORT_FREQUENCY",
          packaging_service_id: 1,
          options: [{ value: "1" }],
        },
        {
          id: 4,
          type: "AUTHORIZE_REPRESENTATIVE",
          options: [{ value: "OBLIGED" }],
        },
        {
          id: 5,
          type: "REPRESENTATIVE_TIER",
          options: [{ value: "1" }],
        },
        {
          id: 6,
          type: "OTHER_COST",
          options: [{ value: "1" }],
        },
        {
          id: 7,
          type: "REQUIRED_INFORMATION",
          options: [{ value: "1" }],
        },
      ];

      const commitmentData = {
        year: 2024,
        commitment: mockCriterias.map((c) => ({
          id: c.id,
          answer: c.options[0].value,
        })),
      };

      mockDatabaseService.country.findUnique.mockResolvedValue(mockCountry);
      mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

      const result = await service.submitServiceSetupCommitment("BR", commitmentData);

      expect(result.year).toBe("2024");
      expect(result.setup.packaging_services[0].obliged).toBe(true);
      expect(result.setup.authorize_representative_obligated).toBe(true);
      expect(result.setup.representative_tier).toBeDefined();
      expect(result.setup.other_costs).toBeDefined();
      expect(result.setup.required_informations).toBeDefined();
    });

    it("should validate commitment answers", async () => {
      const mockCountry = {
        id: 1,
        packaging_services: [],
        country_price_lists: [],
      };

      const mockCriterias = [
        {
          id: 1,
          options: [{ value: "VALID" }],
        },
      ];

      mockDatabaseService.country.findUnique.mockResolvedValue(mockCountry);
      mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

      const invalidCommitment = {
        year: 2024,
        commitment: [{ id: 1, answer: "INVALID" }],
      };

      await expect(service.submitServiceSetupCommitment("BR", invalidCommitment)).rejects.toThrow(TypeError);
    });

    it("should process commitment successfully", async () => {
      const mockCountry = {
        id: 1,
        code: "BR",
        name: "Brasil",
        flag_url: "flag.png",
        authorize_representative_obligated: false,
        other_costs_obligated: false,
        packaging_services: [
          {
            id: 1,
            name: "Service 1",
            report_sets: [{ id: 1 }],
            report_set_frequencies: [
              {
                id: 1,
                frequency: JSON.stringify({ type: "monthly" }),
              },
            ],
          },
        ],
        country_price_lists: [
          {
            price_list: {
              condition_type_value: "2024",
              id: 1,
            },
          },
        ],
        representative_tiers: [],
        required_informations: [],
        other_costs: [],
      };

      const mockCriterias = [
        {
          id: 1,
          type: "PACKAGING_SERVICE",
          packaging_service_id: 1,
          options: [{ value: "OBLIGED" }],
        },
        {
          id: 2,
          type: "REPORT_SET",
          packaging_service_id: 1,
          options: [{ value: "1" }],
        },
      ];

      mockDatabaseService.country.findUnique.mockResolvedValue(mockCountry);
      mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

      const result = await service.submitServiceSetupCommitment("BR", mockCommitmentData);

      expect(result.year).toBe("2024");
      expect(result.setup.packaging_services[0].obliged).toBe(true);
    });
  });

  describe("getServiceSetupStatus", () => {
    it("should validate report sets in packaging services", async () => {
      const mockCountry = {
        packaging_services: [
          {
            id: 1,
            report_sets: [],
            report_set_frequencies: [{ id: 1 }],
            criterias: [],
          },
        ],
        country_price_lists: [{ id: 1 }],
      };

      mockDatabaseService.country.findUnique.mockResolvedValue(mockCountry);

      const result = await service.getServiceSetupStatus("BR");

      expect(result).toEqual({
        completed: false,
        message: "There are packaging services without report sets",
      });
    });
  });

  describe("findServiceSetupCriterias", () => {
    it("should return criteria filtered by type and country", async () => {
      const mockCriterias = [{ id: 1, type: "PACKAGING_SERVICE", options: [] }];

      mockDatabaseService.criteria.findMany.mockResolvedValue(mockCriterias);

      const result = await service.findServiceSetupCriterias({
        countryCode: "BR",
        type: "PACKAGING_SERVICE",
      });

      expect(result).toEqual(mockCriterias);
      expect(databaseService.criteria.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            type: "PACKAGING_SERVICE",
            country: { code: "BR" },
          }),
        })
      );
    });

    it("should include packagingServiceId filter when provided", async () => {
      await service.findServiceSetupCriterias({
        countryCode: "BR",
        type: "PACKAGING_SERVICE",
        packagingServiceId: 1,
      });

      expect(databaseService.criteria.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            packaging_service_id: 1,
          }),
        })
      );
    });
  });
});
