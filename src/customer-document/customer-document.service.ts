import { DatabaseService } from "@/database/database.service";
import { ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { CreateCustomerDocumentDto } from "./dto/create-customer-document.dto";
import { UpdateCustomerDocumentDto } from "./dto/update-customer-document.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

@Injectable()
export class CustomerDocumentService {
  constructor(private databaseService: DatabaseService) {}
  db_url = process.env.DATABASE_URL;
  create(data: CreateCustomerDocumentDto) {
    return this.databaseService.customerDocument.create({
      data: {
        ...data,
      },
    });
  }

  findAll() {
    return this.databaseService.customerDocument.findMany();
  }

  async findOne(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionCustomerDocument(id, user);

    return await this.databaseService.customerDocument.findUnique({
      where: { id },
    });
  }

  async update(id: number, data: UpdateCustomerDocumentDto, user: AuthenticatedUser) {
    await this.validatingUserPermissionCustomerDocument(id, user);

    return this.databaseService.customerDocument.update({
      where: { id },
      data: {
        ...data,
      },
    });
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionCustomerDocument(id, user);

    return this.databaseService.customerDocument.delete({
      where: { id },
    });
  }

  async validatingUserPermissionCustomerDocument(id: number, user: AuthenticatedUser) {
    const customerDocument = await this.databaseService.customerDocument.findUnique({
      where: { id },
      include: {
        customer: true,
      },
    });

    if (!customerDocument) {
      throw new NotFoundException("Customer document not found");
    }

    const { customer } = customerDocument;
    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You do not have permission to access this customer document");
    }
  }
}
