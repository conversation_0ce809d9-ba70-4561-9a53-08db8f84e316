import { DatabaseService } from "@/database/database.service";
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { CreateContractDto } from "./dto/create-contract.dto";
import { UpdateContractDto } from "./dto/update-contract.dto";
import { Prisma } from "@prisma/client";
import { FindAllContractDto } from "./dto/find-all-contract.dto";
import { generatePdf } from "@/shared/utils/generate-pdf";
import * as path from "path";
import { FileService } from "@/file/file.service";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

@Injectable()
export class ContractService {
  constructor(private databaseService: DatabaseService, private fileService: FileService) {}

  async findAll(query: FindAllContractDto) {
    const where = {
      deleted_at: null,
      status: { not: "TERMINATED" },
      ...(query.customer_id && { customer_id: Number(query.customer_id) }),
      ...(query.status && { status: query.status }),
      ...(query.type && { type: query.type }),
    };

    const queryInclude: Prisma.ContractInclude = {};

    if (query.customer_id) {
      queryInclude.general_informations = {
        where: {
          deleted_at: null,
        },
      };
      queryInclude.action_guides = { where: { deleted_at: null }, include: { termination: true } };
      queryInclude.licenses = {
        where: { deleted_at: null },
        include: {
          files: {
            where: {
              deleted_at: null,
            },
          },
          termination: {
            where: {
              deleted_at: null,
            },
            include: {
              files: {
                where: {
                  deleted_at: null,
                },
              },
            },
          },
          price_list: true,
        },
      };
      queryInclude.files = {
        where: { deleted_at: null },
      };
      queryInclude.termination = {
        include: {
          files: {
            where: {
              deleted_at: null,
            },
          },
        },
      };
    }

    const contracts = await this.databaseService.contract.findMany({
      where,
      include: queryInclude,
      orderBy: {
        created_at: "desc",
      },
    });

    return contracts;
  }

  async findOne(id: number, tx?: Prisma.TransactionClient) {
    const contract = await (tx || this.databaseService).contract.findUnique({
      where: { id: Number(id), deleted_at: null, status: { not: "TERMINATED" } },
      include: {
        customer: {
          include: {
            companies: {
              include: {
                address: true,
              },
            },
          },
        },
        action_guides: { where: { deleted_at: null } },
        licenses: {
          where: { deleted_at: null },
          include: {
            files: true,
            termination: true,
            price_list: true,
            packaging_services: true,
          },
        },
        general_informations: {
          where: {
            deleted_at: null,
          },
        },
        files: {
          where: {
            deleted_at: null,
          },
        },
        termination: {
          include: {
            files: {
              where: {
                deleted_at: null,
              },
            },
          },
        },
      },
    });

    return contract;
  }

  async create(data: CreateContractDto) {
    return await this.databaseService.contract.create({
      data: {
        customer_id: data.customer_id,
        type: data.type,
        title: data.title,
        start_date: data.start_date,
        end_date: new Date(new Date(data.start_date || new Date()).getFullYear(), 11, 31),
      },
    });
  }

  async update(id: number, data: UpdateContractDto, user: AuthenticatedUser) {
    await this.validatingUserPermissionContracts(id, user);

    return await this.databaseService.contract.update({
      where: { id: Number(id) },
      data: {
        type: data.type,
        title: data.title,
        start_date: data.start_date,
        updated_at: new Date(),
      },
    });
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionContracts(id, user);

    return await this.databaseService.contract.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }

  async generateContractPdf(contractId: number, tx?: Prisma.TransactionClient) {
    try {
      const contract = await this.findOne(contractId, tx);

      switch (contract.type) {
        case "EU_LICENSE": {
          const contractPdf = await generatePdf({
            templateFile: "contract.hbs",
            fileName: "contract-eu.pdf",
            data: {
              company_name: contract.customer.companies[0]?.name || "---",
              address: contract.customer.companies[0]?.address.address_line || "---",
              zip_code: contract.customer.companies[0]?.address.zip_code || "---",
              country_name: contract.customer.companies[0]?.address.country_code || "---",
              email: contract.customer.email || "---",
              vat_id: contract.customer.companies[0]?.vat || "---",
              tax_id: contract.customer.companies[0]?.tin || "---",
              supplementary: "---",
            },
            footerTemplate: `
          <div style="padding: 0 80px; width: calc(100% - 160px);">
            <div style="font-family: Arial, sans-serif; display: flex; justify-content: space-between; align-items: center; color: #002652;">
              <div style="text-align: left; font-size: 14px;">
                <p style="margin: 0">Köln, den ${new Date().toLocaleDateString()}</p>
                <p style="margin: 0">Cologne, ${new Date().toLocaleDateString()}</p>
              </div>
              <div style="text-align: right; font-size: 16px;">
                <p style="margin: 0">UCE EPR v24-01</p>
              </div>
            </div>
          </div>
        `,
            headerTemplate: `<div style="display: none;"></div>`,
            displayHeaderFooter: true,
            margin: {
              top: 48,
              right: 80,
              bottom: 75,
              left: 80,
            },
          });

          const contractFile = await this.fileService.uploadFile(
            {
              user: {
                id: String(contract.customer.user_id),
                role: "customer",
              },
              contract_id: contractId,
              type: "CONTRACT",
            },
            contractPdf,
            tx
          );

          console.log(`http://localhost:4000/files/${contractFile.id}`);
          return { pdf: contractPdf, file: contractFile };
        }

        default: {
          throw new BadRequestException("Contract pdf not implemented");
        }
      }
    } catch (error) {
      console.log(error);
      console.error("Error generating PDF:", error);
      throw error;
    }
  }

  async generateServiceOverviewPdf(contractId: number, tx?: Prisma.TransactionClient) {
    try {
      const contract = await this.findOne(contractId, tx);

      switch (contract.type) {
        case "EU_LICENSE": {
          const rows = contract.licenses.map((license) => ({
            country_name: license.country_name,
            obligation: license.packaging_services.map((service) => service.name).join(", "),
            start_year: license.year,
            end_year: license.year + 1,
            handling_fee: (license.price_list[0].handling_fee / 100).toFixed(3),
            registration_fee: (license.price_list[0].registration_fee / 100).toFixed(3),
          }));

          const serviceOverviewPdf = await generatePdf({
            templateFile: "service-overview.hbs",
            fileName: "service-overview.pdf",
            data: {
              rows,
            },
            margin: {
              top: 48,
              right: 80,
              bottom: 75,
              left: 80,
            },
          });

          const serviceOverviewFile = contract.files.find((file) => file.original_name === "service-overview.pdf");

          if (serviceOverviewFile) {
            await this.fileService.deleteFile(serviceOverviewFile.id, tx);
          }

          await this.fileService.uploadFile(
            {
              user: {
                id: String(contract.customer.user_id),
                role: "customer",
              },
              contract_id: contractId,
              type: "CONTRACT",
            },
            serviceOverviewPdf,
            tx
          );

          return serviceOverviewPdf;
        }

        default: {
          throw new BadRequestException("Contract pdf not implemented");
        }
      }
    } catch (error) {
      console.log(error);
      console.error("Error generating PDF:", error);
      throw error;
    }
  }

  async validatingUserPermissionContracts(id: number, user: AuthenticatedUser) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("Contract ID is invalid");
    }

    const contract = await this.databaseService.contract.findUnique({
      where: {
        deleted_at: null,
        id: Number(id),
      },
      include: {
        customer: true,
      },
    });

    if (!contract) {
      throw new NotFoundException("Contract not found");
    }

    const { customer } = contract;
    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You do not have permission to access this license contract");
    }
  }
}
