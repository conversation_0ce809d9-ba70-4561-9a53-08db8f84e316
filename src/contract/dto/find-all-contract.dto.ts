import { ApiProperty } from "@nestjs/swagger";
import { ContractStatus, ContractType } from "@prisma/client";

export class FindAllContractDto {
  @ApiProperty({
    description: "The customer id of the contract",
    example: 1,
  })
  customer_id?: number;

  @ApiProperty({
    description: "The type of the contract",
    enum: ContractType,
    example: ContractType.EU_LICENSE,
  })
  type?: ContractType;

  @ApiProperty({
    description: "The status of the contract",
    enum: ContractStatus,
    example: ContractStatus.ACTIVE,
  })
  status?: ContractStatus;
}
