import { Modu<PERSON> } from "@nestjs/common";
import { <PERSON>rokerDataController } from "./broker-data.controller";
import { DatabaseModule } from "../database/database.module";
import { CreatorService, FinderService, IndexerService, RemoverService, UpdaterService } from "./services";
import { BrokerRepository } from "./repositories";
import { HttpApiModule } from "../http/http.module";
import { ConfigModule } from "@nestjs/config";

@Module({
  imports: [DatabaseModule, HttpApiModule, ConfigModule],
  controllers: [BrokerDataController],
  providers: [FinderService, CreatorService, RemoverService, UpdaterService, IndexerService, BrokerRepository],
})
export class BrokerDataModule {}
