import { Body, Controller, Delete, Get, Param, Post, Put } from "@nestjs/common";
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { CompanyService } from "./company.service";
import { CreateCompanyDto } from "./dto/create-company.dto";
import { UpdateCompanyDto } from "./dto/update-company.dto";
import { VatIdDto } from "./dto/vatId.dto";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@ApiTags("company")
@Controller("company")
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  @Get(":id")
  @ApiOperation({ summary: "Get company  by id" })
  @ApiResponse({ status: 200, description: "The company  details" })
  findOne(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.companyService.findOne(Number(id), user);
  }

  @Get()
  @ApiOperation({ summary: "Get all companies" })
  @ApiResponse({ status: 200, description: "List companies" })
  findAll() {
    return this.companyService.findAll();
  }

  @Get("partner/:partnerId")
  @ApiOperation({ summary: "Get company by partner id" })
  @ApiResponse({ status: 200, description: "The company  details" })
  findOneByPartner(@Param("partnerId") partnerId: string, @User() user: AuthenticatedUser) {
    return this.companyService.findOneByPartner(+partnerId, user);
  }

  @Post()
  @ApiOperation({ summary: "Create company" })
  @ApiResponse({
    status: 201,
    description: "The company has been successfully created.",
  })
  @ApiBody({ type: CreateCompanyDto })
  create(@Body() data: CreateCompanyDto) {
    return this.companyService.create(data);
  }

  @Get("crm/:id")
  @ApiOperation({ summary: "Get customer monday by id" })
  @ApiResponse({ status: 200, description: "The customer monday details" })
  findOneCustomerMondayById(@Param("id") id: string) {
    return this.companyService.findOneCustomerMondayById(Number(id));
  }

  @Put(":id")
  @ApiOperation({ summary: "Update company by id" })
  @ApiResponse({
    status: 200,
    description: "The company has been successfully updated.",
  })
  @ApiBody({ type: UpdateCompanyDto })
  update(@Param("id") id: string, @Body() data: UpdateCompanyDto, @User() user: AuthenticatedUser) {
    return this.companyService.update(Number(id), data, user);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete company  by id" })
  @ApiResponse({
    status: 200,
    description: "The company  has been successfully deleted.",
  })
  remove(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.companyService.remove(Number(id), user);
  }

  @Post("validate-vat")
  @ApiOperation({ summary: "Get company  by vatId" })
  @ApiResponse({ status: 200, description: "The company  details" })
  findByVatId(@Body() vatIdDTO: VatIdDto) {
    return this.companyService.findByVatId(vatIdDTO);
  }
}
