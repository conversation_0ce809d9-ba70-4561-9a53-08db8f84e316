import { Test, TestingModule } from "@nestjs/testing";
import { CompanyService } from "./company.service";
import { DatabaseService } from "@/database/database.service";
import { CustomerIoService } from "@/customer-io/customer-io.service";
import { MondayService } from "@/integration/monday.service";
import { BadRequestException, ConflictException, HttpException, NotFoundException } from "@nestjs/common";
import { CreateCompanyDto } from "./dto/create-company.dto";
import axios from "axios";
import { VatIdDto } from "./dto/vatId.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

jest.mock("axios");
const mockedAxios = axios as jest.Mocked<typeof axios>;
global.fetch = jest.fn();

describe("CompanyService", () => {
  let service: CompanyService;
  let databaseService: DatabaseService;
  let customerIoService: CustomerIoService;
  let mondayService: MondayService;

  const mockDatabaseService = {
    company: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    companyEmail: {
      createMany: jest.fn(),
      deleteMany: jest.fn(),
    },
    companyContact: {
      create: jest.fn(),
      deleteMany: jest.fn(),
    },
    customerPhone: {
      createMany: jest.fn(),
    },
  };

  const mockCustomerIoService = {
    updateCompanyByCustomerId: jest.fn(),
  };

  const mockMondayService = {
    updateBoardAccountsMonday: jest.fn(),
  };

  beforeAll(() => {
    jest.spyOn(console, "error").mockImplementation(() => {});
    jest.spyOn(console, "log").mockImplementation(() => {});
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CompanyService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
        {
          provide: CustomerIoService,
          useValue: mockCustomerIoService,
        },
        {
          provide: MondayService,
          useValue: mockMondayService,
        },
      ],
    }).compile();

    service = module.get<CompanyService>(CompanyService);
    databaseService = module.get<DatabaseService>(DatabaseService);
    customerIoService = module.get<CustomerIoService>(CustomerIoService);
    mondayService = module.get<MondayService>(MondayService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findAll", () => {
    it("should return all companies", async () => {
      const mockCompanies = [
        { id: 1, name: "Company 1" },
        { id: 2, name: "Company 2" },
      ];
      mockDatabaseService.company.findMany.mockResolvedValue(mockCompanies);

      const result = await service.findAll();
      expect(result).toEqual(mockCompanies);
    });
  });

  describe("findOne", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should return a company when found", async () => {
      const mockCompany = {
        id: 1,
        name: "Test Company",
        emails: [],
        address: {},
      };
      mockDatabaseService.company.findUnique.mockResolvedValue(mockCompany);

      jest.spyOn(service, "validatingUserPermissionCompany").mockResolvedValue(undefined);

      const result = await service.findOne(1, user);
      expect(result).toEqual(mockCompany);
    });
  });

  describe("findOneByName", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should return a company when found by name", async () => {
      const mockCompany = {
        id: 1,
        name: "Test Company",
        address: {},
      };
      mockDatabaseService.company.findFirst.mockResolvedValue(mockCompany);

      jest.spyOn(service, "validatingUserPermissionCompany").mockResolvedValue(undefined);

      const result = await service.findOneByName("Test Company", user);
      expect(result).toEqual(mockCompany);
    });
  });

  describe("findByLucid", () => {
    it("should return a company when found by LUCID", async () => {
      const mockCompany = { id: 1, lucid: "LUCID123" };
      mockDatabaseService.company.findFirst.mockResolvedValue(mockCompany);

      const result = await service.findByLucid("LUCID123");
      expect(result).toEqual(mockCompany);
    });
  });

  describe("addCustomerMailsAndPhones", () => {
    it("should add emails and phones successfully", async () => {
      const dto = {
        customer_phones: ["*********"],
        company_emails: ["<EMAIL>"],
        company_id: 1,
        customer_id: 1,
      };

      await service.addCustomerMailsAndPhones(dto);

      expect(mockDatabaseService.companyEmail.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            company_id: 1,
            email: "<EMAIL>",
          }),
        ]),
        skipDuplicates: true,
      });

      expect(mockDatabaseService.customerPhone.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            customer_id: 1,
            phone_number: "*********",
          }),
        ]),
        skipDuplicates: true,
      });
    });

    it("should handle errors when adding emails and phones", async () => {
      const dto = {
        customer_phones: ["*********"],
        company_emails: ["<EMAIL>"],
        company_id: 1,
        customer_id: 1,
      };

      mockDatabaseService.companyEmail.createMany.mockRejectedValue(new Error("Database error"));

      await expect(service.addCustomerMailsAndPhones(dto)).rejects.toThrow(BadRequestException);
    });
  });

  describe("update", () => {
    const mockExistingCompany = {
      id: 1,
      name: "Old Company",
      vat: "OLD_VAT",
      customer: {
        id: 1,
        name: "New Contact",
        email: "<EMAIL>",
      },
    };

    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    beforeEach(() => {
      mockDatabaseService.company.findUnique.mockResolvedValue(mockExistingCompany);
    });

    it("should update company with new emails", async () => {
      const updateDto = {
        name: "Updated Company",
        emails: ["<EMAIL>"],
      };

      const mockUpdatedCompany = {
        ...mockExistingCompany,
        ...updateDto,
        emails: [{ email: "<EMAIL>" }],
      };

      mockDatabaseService.companyEmail.deleteMany.mockResolvedValue({ count: 1 });
      mockDatabaseService.companyEmail.createMany.mockResolvedValue({ count: 1 });
      mockDatabaseService.company.update.mockResolvedValue(mockUpdatedCompany);

      const result = await service.update(1, updateDto, user);

      expect(mockDatabaseService.companyEmail.deleteMany).toHaveBeenCalledWith({
        where: { company_id: 1 },
      });
      expect(mockDatabaseService.companyEmail.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            company_id: 1,
            email: "<EMAIL>",
          }),
        ]),
        skipDuplicates: true,
      });
      expect(result).toEqual(mockUpdatedCompany);
    });

    it("should handle database error when updating emails", async () => {
      const updateDto = {
        name: "Updated Company",
        emails: ["<EMAIL>"],
      };

      mockDatabaseService.companyEmail.deleteMany.mockRejectedValue(new Error("Database error"));

      await expect(async () => {
        await service.update(1, updateDto, user);
      }).rejects.toThrow();
    });

    it("should update company with new contact", async () => {
      const updateDto = {
        id: 1,
        name: "Updated Company",
        contact: {
          name: "New Contact",
          email: "<EMAIL>",
          customer: {
            id: 1,
          },
        },
      };
      const user: AuthenticatedUser = {
        email: "<EMAIL>",
        id: "1",
        role: Role.ADMIN,
      };
      const mockUpdatedCompany = { ...mockExistingCompany, ...updateDto };
      mockDatabaseService.company.update.mockResolvedValue(mockUpdatedCompany);

      const result = await service.update(1, updateDto, user);

      expect(mockDatabaseService.companyContact.deleteMany).toHaveBeenCalled();
      expect(mockDatabaseService.companyContact.create).toHaveBeenCalled();
      expect(result).toEqual(mockUpdatedCompany);
    });
  });

  describe("findByVatId", () => {
    beforeEach(() => {
      process.env.MASTER_VAT_ID = "MASTER123";
    });

    it("should throw error for invalid country code", async () => {
      const vatIdDto: VatIdDto = {
        vat_id: "X",
        country_code: "XX",
        company_name: "Test",
        company_zipcode: "12345",
        company_city: "Test City",
        company_street: "Test Street",
      };

      await expect(service.findByVatId(vatIdDto)).rejects.toThrow(HttpException);
    });

    it("should validate master VAT ID", async () => {
      const vatIdDto: VatIdDto = {
        vat_id: "MASTER123",
        country_code: "DE",
        company_name: "Test",
        company_zipcode: "12345",
        company_city: "Test City",
        company_street: "Test Street",
      };

      const result = await service.findByVatId(vatIdDto);
      expect(result.valid).toBe(true);
    });

    it("should check country availability", async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () =>
          Promise.resolve({
            countries: [{ countryCode: "DE", availability: "Available" }],
          }),
      });

      const result = await service.checkCountryAvailability("DE");
      expect(result.availability).toBe("Available");
    });
  });

  describe("checkVatIdIsNotGermany", () => {
    const mockVatIdDTO: VatIdDto = {
      vat_id: "DE*********",
      country_code: "DE",
      company_name: "Test Company",
      company_zipcode: "12345",
      company_city: "Berlin",
      company_street: "Test Street",
    };

    beforeEach(() => {
      (global.fetch as jest.Mock).mockReset();
    });

    it("should validate German VAT ID successfully", async () => {
      const mockResponse = `<?xml version="1.0" encoding="UTF-8"?>
            <params>
                <param>
                    <value>
                        <array>
                            <data>
                                <value>
                                    <string>_text</string>
                                </value>
                                <value>
                                    <string>_text</string>
                                </value>
                            </data>
                        </array>
                    </value>
                </param>
                <param>
                    <value>
                        <array>
                            <data>
                                <value>
                                    <string>ErrorCode</string>
                                </value>
                                <value>
                                    <string>200</string>
                                </value>
                            </data>
                        </array>
                    </value>
                </param>
            </params>`;

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        text: () => Promise.resolve(mockResponse),
      });

      const result = await service.checkVatIdIsNotGermany(mockVatIdDTO);

      expect(result).toEqual({
        message: expect.any(String),
        valid: true,
        ...mockVatIdDTO,
      });
    });

    it("should throw error when ErrorCode is not found in response", async () => {
      const mockResponse = `<?xml version="1.0" encoding="UTF-8"?>
            <params>
                <param>
                    <value>
                        <array>
                            <data>
                                <value>
                                    <string>OtherCode</string>
                                </value>
                                <value>
                                    <string>200</string>
                                </value>
                            </data>
                        </array>
                    </value>
                </param>
            </params>`;

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        text: () => Promise.resolve(mockResponse),
      });

      await expect(service.checkVatIdIsNotGermany(mockVatIdDTO)).rejects.toThrow(
        "convertResponseToJson.params.param.map is not a function"
      );
    });

    it("should throw error for invalid VAT ID", async () => {
      const mockResponse = `<?xml version="1.0" encoding="UTF-8"?>
            <params>
                <param>
                    <value>
                        <array>
                            <data>
                                <value>
                                    <string>_text</string>
                                </value>
                                <value>
                                    <string>_text</string>
                                </value>
                            </data>
                        </array>
                    </value>
                </param>
                <param>
                    <value>
                        <array>
                            <data>
                                <value>
                                    <string>ErrorCode</string>
                                </value>
                                <value>
                                    <string>400</string>
                                </value>
                            </data>
                        </array>
                    </value>
                </param>
            </params>`;

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        text: () => Promise.resolve(mockResponse),
      });

      await expect(service.checkVatIdIsNotGermany(mockVatIdDTO)).rejects.toThrow(HttpException);
    });

    it("should handle network errors", async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error("Network error"));

      await expect(service.checkVatIdIsNotGermany(mockVatIdDTO)).rejects.toThrow();
    });

    it("should construct URL with correct parameters", async () => {
      const mockResponse = `<?xml version="1.0" encoding="UTF-8"?>
            <params>
                <param>
                    <value>
                        <array>
                            <data>
                                <value>
                                    <string>_text</string>
                                </value>
                                <value>
                                    <string>_text</string>
                                </value>
                            </data>
                        </array>
                    </value>
                </param>
                <param>
                    <value>
                        <array>
                            <data>
                                <value>
                                    <string>ErrorCode</string>
                                </value>
                                <value>
                                    <string>200</string>
                                </value>
                            </data>
                        </array>
                    </value>
                </param>
            </params>`;

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        text: () => Promise.resolve(mockResponse),
      });

      await service.checkVatIdIsNotGermany(mockVatIdDTO);

      const expectedParams = {
        UstId_1: "DE257906838",
        UstId_2: mockVatIdDTO.vat_id,
        Firmenname: mockVatIdDTO.company_name,
        Ort: mockVatIdDTO.company_city,
        PLZ: mockVatIdDTO.company_zipcode,
        Strasse: mockVatIdDTO.company_street,
      };

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining(new URLSearchParams(expectedParams).toString())
      );
    });
  });
});
