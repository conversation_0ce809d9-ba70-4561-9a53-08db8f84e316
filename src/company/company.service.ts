import { CustomerIoService } from "@/customer-io/customer-io.service";
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { Company } from "@prisma/client";
import axios from "axios";
import { DatabaseService } from "@/database/database.service";
import { AddCustomerMailsAndPhones } from "./dto/add-mail-phones.dto";
import { CreateCompanyDto } from "./dto/create-company.dto";
import { UpdateCompanyDto } from "./dto/update-company.dto";
import { VatIdDto } from "./dto/vatId.dto";
import { MondayService } from "@/integration/monday.service";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ROLE } from "@/shared/auth/const";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";
import { HttpModuleService } from "@/http/http.service";
import { lastValueFrom } from "rxjs";
import { validateForeignVat, validateGermanVat } from "@/shared/utils/vat-validation";

@Injectable()
export class CompanyService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly customerIoService: CustomerIoService,
    private readonly mondayService: MondayService,
    private readonly httpModuleService: HttpModuleService
  ) {}

  async findByDocument(vat?: string, tin?: string) {
    let company: Company | null = null;

    if (vat) {
      company = await this.databaseService.company.findFirst({
        where: {
          vat,
        },
        include: {
          address: true,
          emails: true,
          billing: true,
          contacts: true,
        },
      });
    }

    if (tin) {
      company = await this.databaseService.company.findFirst({
        where: {
          tin,
        },
        include: {
          address: true,
          emails: true,
          billing: true,
          contacts: true,
        },
      });
    }

    return company;
  }

  async findByLucid(lucid: string) {
    const company = await this.databaseService.company.findFirst({
      where: {
        lucid,
      },
      include: {
        address: true,
        emails: true,
        billing: true,
        contacts: true,
      },
    });

    return company;
  }

  async addCustomerMailsAndPhones(addCustomerMailsAndPhones: AddCustomerMailsAndPhones) {
    try {
      const companyEmails = addCustomerMailsAndPhones.company_emails.map((email) => {
        return {
          company_id: addCustomerMailsAndPhones.company_id,
          email: email,
          created_at: new Date(),
          updated_at: new Date(),
        };
      });

      await this.databaseService.companyEmail.createMany({
        data: companyEmails,
        skipDuplicates: true,
      });

      const customerPhones = addCustomerMailsAndPhones.customer_phones.map((phone) => {
        return {
          phone_number: phone,
          customer_id: addCustomerMailsAndPhones.customer_id,
          phone_type: "PHONE",
          created_at: new Date(),
          updated_at: new Date(),
        };
      });

      await this.databaseService.customerPhone.createMany({
        data: customerPhones,
        skipDuplicates: true,
      });
    } catch (error) {
      console.error("CompanyService/add customer phones and company emails - ", error);
      throw new BadRequestException("An error occurred on the server. Please contact support.");
    }
  }

  findAll() {
    return this.databaseService.company.findMany();
  }

  async findOne(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionCompany(id, user);

    return await this.databaseService.company.findUnique({
      where: { id },
      include: {
        address: true,
        emails: true,
        billing: true,
        contacts: true,
      },
    });
  }

  async findOneByPartner(partner_id: number, user: AuthenticatedUser) {
    const company = await this.databaseService.company.findFirst({
      where: { partner_id, deleted_at: null },
      include: {
        address: true,
        emails: true,
        billing: true,
        contacts: true,
      },
    });

    if (!company) throw new NotFoundException("Company not found");

    await this.validatingUserPermissionCompany(company.id, user);

    return company;
  }

  async findOneCustomerMondayById(id: number) {
    try {
      const baseUrl = process.env.CRM_API_URL;

      const crmUrl = `${baseUrl}/customers/${id}`;
      await axios.get(crmUrl, {
        headers: {
          [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
          [HEADER_USER_ROLE]: Role.SYSTEM,
        },
      });
    } catch (error) {
      console.error("CompanyService/findOneCustomerMondayById - ", error);
      // throw new BadRequestException("An error occurred on the server. Please contact support.");
    }
  }

  async findOneByName(name: string, user: AuthenticatedUser) {
    const company = await this.databaseService.company.findFirst({
      where: { name },
      include: {
        address: true,
        emails: true,
        billing: true,
        contacts: true,
      },
    });

    if (!company) throw new NotFoundException("Company not found");

    await this.validatingUserPermissionCompany(company.id, user);

    return company;
  }

  async create(data: CreateCompanyDto) {
    if (data.vat && data.tin) {
      throw new BadRequestException("A company can not have both vat and tax number");
    }

    const documentAlreadyUsed = await this.findByDocument(data.vat, data.tin);

    if (documentAlreadyUsed) {
      throw new ConflictException("Document already used by another company");
    }

    if (data.lucid) {
      const companyWihLucidAlreadyExists = await this.findByLucid(data.lucid);

      if (companyWihLucidAlreadyExists) {
        throw new ConflictException("LUCID number is already in use");
      }
    }

    const relation = await (async () => {
      if (!data.customer_id && !data.partner_id) {
        throw new BadRequestException("Customer or partner is required");
      }

      if (data.customer_id) {
        const customer = await this.databaseService.customer.findUnique({
          where: {
            id: data.customer_id,
          },
        });

        if (!customer) {
          throw new NotFoundException("Customer not found");
        }

        return customer;
      }

      if (data.partner_id) {
        const partner = await this.databaseService.partner.findUnique({
          where: {
            id: data.partner_id,
          },
        });

        if (!partner) {
          throw new NotFoundException("Partner not found");
        }

        return partner;
      }
    })();

    const companyBilling = {
      full_name: data.billing?.full_name || `${relation.first_name} ${relation.last_name}`,
      country_code: data.billing?.country_code || data.address.country_code,
      country_name: data.billing?.country_name || data.address.country_code,
      company_name: data.billing?.company_name || data.name,
      street_and_number: data.billing?.street_and_number || data.address.street_and_number,
      city: data.billing?.city || data.address.city,
      zip_code: data.billing?.zip_code || data.address.zip_code,
      is_custom: !!data.billing,
    };

    const createdCompany = await this.databaseService.company.create({
      data: {
        name: data.name,
        description: data.description,
        tin: data.tin || null,
        vat: data.vat || null,
        lucid: data.lucid || null,
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
        emails: {
          createMany: {
            data: (data.emails || []).map((email) => ({
              email,
              created_at: new Date(),
              updated_at: new Date(),
            })),
            skipDuplicates: true,
          },
        },
        contacts: {
          create: {
            ...data.contact,
            created_at: new Date(),
            updated_at: new Date(),
          },
        },
        address: {
          create: {
            ...data.address,
            created_at: new Date(),
            updated_at: new Date(),
          },
        },
        ...(data.customer_id && {
          customer: {
            connect: {
              id: data.customer_id,
            },
          },
        }),
        ...(data.partner_id && {
          partner: {
            connect: {
              id: data.partner_id,
            },
          },
        }),
        billing: { create: companyBilling },
      },
      include: {
        address: true,
        emails: true,
        billing: true,
        contacts: true,
      },
    });

    if (createdCompany.customer_id) {
      // TODO Verify company creation in customer.io
      await this.customerIoService.updateCompanyByCustomerId(createdCompany.customer_id);

      this.mondayService.updateBoardAccountsMonday(createdCompany, createdCompany.customer_id);
    }

    return createdCompany;
  }

  async update(id: number, data: UpdateCompanyDto, user: AuthenticatedUser) {
    await this.validatingUserPermissionCompany(id, user);

    return await this.databaseService.$transaction(async (tx) => {
      const company = await tx.company.findUnique({
        where: { id },
      });

      if (data.vat && data.tin) {
        throw new BadRequestException("A company can not have both vat and tax number");
      }

      const documentAlreadyUsed = await this.findByDocument(data.vat, data.tin);

      if (documentAlreadyUsed && documentAlreadyUsed.id !== company.id) {
        throw new ConflictException("Document already used by another company");
      }

      if (data.emails && Array.isArray(data.emails)) {
        const companyEmails = data.emails.map((email) => {
          return {
            company_id: company.id,
            email: email,
            created_at: new Date(),
            updated_at: new Date(),
          };
        });

        await tx.companyEmail.deleteMany({
          where: {
            company_id: company.id,
          },
        });

        await tx.companyEmail.createMany({
          data: companyEmails,
          skipDuplicates: true,
        });
      }

      if (data.contact) {
        await tx.companyContact.deleteMany({
          where: {
            company_id: company.id,
          },
        });

        await tx.companyContact.create({
          data: {
            ...data.contact,
            company_id: company.id,
            created_at: new Date(),
            updated_at: new Date(),
          },
        });
      }

      if (data.billing) {
        await tx.companyBilling.update({
          where: { company_id: company.id },
          data: { ...data.billing, is_custom: true },
        });
      }

      delete data.contact;
      delete data.emails;

      const updatedCompany = await tx.company.update({
        where: { id },
        data: {
          name: data.name,
          description: data.description,
          tin: data.tin,
          vat: data.vat,
          lucid: data.lucid,
          starting: data.starting,
          website: data.website,
          updated_at: new Date(),
          deleted_at: null,
          address: {
            update: {
              ...data.address,
              updated_at: new Date(),
            },
          },
        },
        include: {
          address: true,
          emails: true,
          billing: true,
          contacts: true,
        },
      });

      if (updatedCompany.customer_id) {
        // TODO Verify company update in customer.io
        await this.customerIoService.updateCompanyByCustomerId(updatedCompany.customer_id);
      }

      this.mondayService.updateBoardAccountsMonday(updatedCompany, updatedCompany.customer_id);

      try {
        await lastValueFrom(
          this.httpModuleService.admin({
            url: "/emails/send-message",
            params: {
              transactional_message_id: "27",
              identifiers: {
                email: user.email,
              },
              to: user.email,
              from: "Lizenzero <<EMAIL>>",
              subject: "Company information changed",
            },
            method: "post",
          })
        ).catch((error) => {
          console.error(error?.response?.data);
        });
      } catch (error) {
        console.error(error);
      }

      return updatedCompany;
    });
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionCompany(id, user);

    return this.databaseService.company.delete({
      where: { id },
    });
  }

  async findByVatId(data: VatIdDto) {
    if (!data.vat_id || typeof data.vat_id !== "string" || data.vat_id.length < 2) {
      return {
        is_valid: false,
        data,
        error: "INVALID",
      };
    }

    const vatId = data.vat_id.trim().toUpperCase();

    const vatCountryCode = vatId.substring(0, 2);

    if (/\d/.test(vatCountryCode)) {
      return {
        is_valid: false,
        data,
        error: "INVALID",
      };
    }

    if (vatCountryCode !== data.country_code) {
      return {
        is_valid: false,
        data,
        error: "INVALID",
      };
    }

    if (process.env.MASTER_VAT_ID && vatId === process.env.MASTER_VAT_ID) {
      return {
        message: "VALID",
        is_valid: true,
        data,
      };
    }

    if (vatCountryCode === "DE") return validateGermanVat(data);

    return validateForeignVat(data);
  }

  async validatingUserPermissionCompany(id: number, user: AuthenticatedUser) {
    const company = await this.databaseService.company.findUnique({
      where: { id },
      include: {
        customer: true,
        address: true,
        emails: true,
        billing: true,
        contacts: true,
      },
    });

    if (!company) throw new NotFoundException("Company not found");

    const { customer } = company;
    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You do not have permission to access this company");
    }
  }
}
