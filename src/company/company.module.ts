import { Module } from "@nestjs/common";
import { CompanyService } from "./company.service";
import { CompanyController } from "./company.controller";
import { CustomerService } from "@/customer/customer.service";
import { HttpApiModule } from "@/http/http.module";
import { CustomerIoModule } from "@/customer-io/customer-io.module";
import { CustomerInviteTokenModule } from "@/customer-invite-token/customer-invite-token.module";
import { MondayService } from "@/integration/monday.service";
import { CouponModule } from "@/coupon/coupon.module";

@Module({
  imports: [HttpApiModule, CustomerIoModule, CustomerInviteTokenModule, CouponModule],
  providers: [CompanyService, CustomerService, MondayService],
  controllers: [CompanyController],
  exports: [CompanyService],
})
export class CompanyModule {}
