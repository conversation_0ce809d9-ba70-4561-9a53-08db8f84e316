import { Test, TestingModule } from "@nestjs/testing";
import { CustomerCommitmentService } from "./customer-commitment.service";
import { DatabaseService } from "@/database/database.service";
import { BadRequestException, InternalServerErrorException, NotFoundException } from "@nestjs/common";
import axios from "axios";
import { CreateCustomerCommitmentDto } from "./dto/create-customer-commitment.dto";
import { FindCustomerCommitmentDto } from "./dto/find-customer-commitment.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

jest.mock("axios");
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe("CustomerCommitmentService", () => {
  let service: CustomerCommitmentService;
  let databaseService: DatabaseService;

  beforeAll(() => {
    jest.spyOn(console, "error").mockImplementation(() => {});
    jest.spyOn(console, "log").mockImplementation(() => {});
  });

  const mockDatabaseService = {
    customerCommitment: {
      upsert: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomerCommitmentService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<CustomerCommitmentService>(CustomerCommitmentService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    const mockCreateDto: CreateCustomerCommitmentDto = {
      customer_email: "<EMAIL>",
      country_code: "US",
      year: 2024,
      commitment_answers: [
        { criteria_id: 1, answer: "YES" },
        { criteria_id: 2, answer: "NO" },
      ],
    };

    const mockApiResponse = {
      year: 2024,
      commitment: [
        { id: 1, answer: "YES" },
        { id: 2, answer: "NO" },
      ],
      setup: {
        packaging_services: [{ obliged: true }],
      },
    };

    it("should throw BadRequestException when year is missing", async () => {
      const invalidDto = { ...mockCreateDto, year: null };
      await expect(async () => {
        await service.create(invalidDto);
      }).rejects.toThrow(BadRequestException);
    });

    it("should throw BadRequestException when commitment answers are invalid", async () => {
      const invalidDto = { ...mockCreateDto, commitment_answers: [] };
      await expect(async () => {
        await service.create(invalidDto);
      }).rejects.toThrow(BadRequestException);
    });

    it("should handle admin API errors", async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 500,
          data: { message: "Failed to submit customer commitment" },
        },
      });

      await expect(async () => {
        await service.create(mockCreateDto);
      }).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe("findAll", () => {
    const mockQuery: FindCustomerCommitmentDto = {
      customer_email: "<EMAIL>",
      year: 2024,
      country_code: "US",
    };

    it("should return all customer commitments", async () => {
      const mockCommitments = [
        { id: 1, customer_email: "<EMAIL>" },
        { id: 2, customer_email: "<EMAIL>" },
      ];

      mockDatabaseService.customerCommitment.findMany.mockResolvedValue(mockCommitments);

      const result = await service.findAll(mockQuery);

      expect(result).toEqual(mockCommitments);
      expect(mockDatabaseService.customerCommitment.findMany).toHaveBeenCalledWith({
        where: {
          deleted_at: null,
          customer_email: mockQuery.customer_email,
          year: mockQuery.year,
          country_code: mockQuery.country_code,
        },
      });
    });

    it("should throw BadRequestException when customer email is missing", async () => {
      const invalidQuery = { ...mockQuery, customer_email: null };
      await expect(async () => {
        await service.findAll(invalidQuery);
      }).rejects.toThrow(BadRequestException);
    });

    it("should throw BadRequestException when year is invalid", async () => {
      const invalidQuery = { ...mockQuery, year: NaN };
      await expect(async () => {
        await service.findAll(invalidQuery);
      }).rejects.toThrow(BadRequestException);
    });
  });

  describe("findOne", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should return a customer commitment when found", async () => {
      const mockCommitment = { id: 1, customer_email: "<EMAIL>" };
      mockDatabaseService.customerCommitment.findUnique.mockResolvedValue(mockCommitment);

      jest.spyOn(service, "validatingUserPermissionCustomerCommitment").mockResolvedValue(undefined);

      const result = await service.findOne(1, user);

      expect(result).toEqual(mockCommitment);
      expect(mockDatabaseService.customerCommitment.findUnique).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
      });
    });

    it("should throw NotFoundException when commitment not found", async () => {
      mockDatabaseService.customerCommitment.findUnique.mockResolvedValue(null);

      await expect(async () => {
        await service.findOne(999, user);
      }).rejects.toThrow(NotFoundException);
    });
  });

  describe("update", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should update a customer commitment", async () => {
      const updateDto = {
        customer_email: "<EMAIL>",
        commitment: { some: "data" },
      };

      const mockUpdatedCommitment = {
        id: 1,
        ...updateDto,
      };

      mockDatabaseService.customerCommitment.update.mockResolvedValue(mockUpdatedCommitment);

      jest.spyOn(service, "validatingUserPermissionCustomerCommitment").mockResolvedValue(undefined);

      const result = await service.update(1, updateDto, user);

      expect(result).toEqual(mockUpdatedCommitment);
      expect(mockDatabaseService.customerCommitment.update).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
        data: updateDto,
      });
    });
  });

  describe("remove", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should soft delete a customer commitment", async () => {
      const mockCustomerCommitment = {
        id: 1,
        customer: {
          user_id: 1,
        },
      };

      const mockDeletedCommitment = {
        id: 1,
        deleted_at: expect.any(Date),
      };

      mockDatabaseService.customerCommitment.findUnique.mockResolvedValue(mockCustomerCommitment);
      mockDatabaseService.customerCommitment.update.mockResolvedValue(mockDeletedCommitment);

      const result = await service.remove(1, user);

      expect(result.deleted_at).toBeDefined();
      expect(mockDatabaseService.customerCommitment.update).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
        data: { deleted_at: expect.any(Date) },
      });
    });
  });
});
