import { ApiProperty } from "@nestjs/swagger";

export class FindCustomerCommitmentDto {
  @ApiProperty({
    description: "The customer email",
    example: "<EMAIL>",
    required: false,
  })
  customer_email: string;

  @ApiProperty({
    description: "The year",
    example: 2025,
  })
  year: number;

  @ApiProperty({
    description: "The country code",
    example: "ID",
  })
  country_code?: string;
}
