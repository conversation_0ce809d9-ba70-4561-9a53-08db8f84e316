import { ApiProperty } from "@nestjs/swagger";

export class CreateCustomerCommitmentDto {
  @ApiProperty({
    description: "The customer email",
    example: "<EMAIL>",
    required: false,
  })
  customer_email?: string;

  @ApiProperty({
    description: "The country code",
    example: "ID",
  })
  country_code: string;

  @ApiProperty({
    description: "The year",
    example: 2025,
  })
  year: number;

  @ApiProperty({
    description: "The shopping cart id",
    example: "123",
    required: false,
  })
  shopping_cart_id?: string;

  @ApiProperty({
    description: "The answers",
    example: [
      {
        criteria_id: 1,
        answer: "OBLIGED",
      },
      {
        criteria_id: 2,
        answer: "REQUEST",
      },
    ],
  })
  commitment_answers: CommitmentAnswer[];
}

export interface CommitmentAnswer {
  criteria_id: number;
  answer: string;
  to_answer?: string;
}
