import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { CreateCertificateDto } from "./dto/create-certificate.dto";
import { UpdateCertificateDto } from "./dto/update-certificate.dto";
import { CertificateService } from "./certificate.service";
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from "@nestjs/swagger";
import { CreateCertificatePdfDto } from "./dto/create-certificate-pdf.dto";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";
import { CertificateStatus } from "@prisma/client";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@Controller("certificates")
@ApiTags("certificates")
export class CertificateController {
  constructor(private readonly certificateService: CertificateService) {}

  @Get()
  @ApiOperation({ summary: "Get all certificates" })
  @ApiResponse({
    status: 200,
    description: "Certificates fetched successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        license_id: { type: "number" },
        status: { type: "enum", enum: Object.values(CertificateStatus) },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time" },
      },
    },
    example: {
      id: 1,
      name: "Certificate 1",
      license_id: 1,
      status: CertificateStatus.AVAILABLE,
      created_at: new Date(),
      updated_at: new Date(),
      deleted_at: null,
    },
  })
  @ApiResponse({ status: 400, description: "License ID is invalid" })
  @ApiQuery({ name: "license_id", required: false, type: Number })
  findAll(@Query("license_id") license_id?: string) {
    return this.certificateService.findAll(license_id ? parseInt(license_id) : undefined);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a certificate by id" })
  @ApiResponse({
    status: 200,
    description: "Certificate fetched successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        license_id: { type: "number" },
        status: { type: "enum", enum: Object.values(CertificateStatus) },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time" },
      },
    },
    example: {
      id: 1,
      name: "Certificate 1",
      license_id: 1,
      status: CertificateStatus.AVAILABLE,
      created_at: new Date(),
      updated_at: new Date(),
      deleted_at: null,
    },
  })
  @ApiResponse({ status: 400, description: "Certificate ID is invalid" })
  @ApiResponse({ status: 404, description: "Certificate not found" })
  findOne(@Param("id") id: number) {
    return this.certificateService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: "Create a certificate" })
  @ApiResponse({
    status: 201,
    description: "Certificate created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        license_id: { type: "number" },
        status: { type: "enum", enum: Object.values(CertificateStatus) },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time" },
      },
    },
    example: {
      id: 1,
      name: "Certificate 1",
      license_id: 1,
      status: CertificateStatus.AVAILABLE,
      created_at: new Date(),
      updated_at: new Date(),
      deleted_at: null,
    },
  })
  @ApiResponse({ status: 404, description: "License not found" })
  @ApiResponse({ status: 400, description: "Invalid request body" })
  create(@Body() data: CreateCertificateDto) {
    return this.certificateService.create(data);
  }

  @Post("pdf")
  @ApiOperation({ summary: "Create a certificate pdf" })
  @ApiResponse({
    status: 200,
    description: "Certificate pdf created successfully",
    schema: {
      type: "object",
      properties: {
        filename: { type: "string" },
        mimetype: { type: "string" },
        size: { type: "number" },
        path: { type: "string" },
      },
    },
    example: {
      filename: "certificate.pdf",
      mimetype: "application/pdf",
      size: 1000,
      path: "path/to/certificate.pdf",
    },
  })
  @ApiResponse({ status: 500, description: "Failed to generate certificate pdf" })
  generateSavePdf(@Body() data: CreateCertificatePdfDto) {
    return this.certificateService.generateSavePdf(data);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a certificate" })
  @ApiResponse({
    status: 200,
    description: "Certificate updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        license_id: { type: "number" },
        status: { type: "enum", enum: Object.values(CertificateStatus) },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time" },
      },
    },
    example: {
      id: 1,
      name: "Certificate 1",
      license_id: 1,
      status: CertificateStatus.AVAILABLE,
      created_at: new Date(),
      updated_at: new Date(),
      deleted_at: null,
    },
  })
  @ApiResponse({ status: 404, description: "Certificate not found" })
  update(@Param("id") id: number, @Body() data: UpdateCertificateDto) {
    return this.certificateService.update(id, data);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a certificate" })
  @ApiResponse({
    status: 200,
    description: "Certificate deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Certificate not found" })
  @ApiResponse({ status: 400, description: "Certificate ID is invalid" })
  remove(@Param("id") id: number) {
    return this.certificateService.remove(id);
  }
}
