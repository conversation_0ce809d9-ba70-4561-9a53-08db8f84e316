import { ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsDateString, IsN<PERSON>ber, IsOptional, IsString, Min } from "class-validator";

export class FindAllClustersPaginatedDto {
  @ApiPropertyOptional({
    description: "Pagination page",
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({
    description: "Pagination limit",
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: "Filter by name",
    example: "Cluster 1",
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: "Filter by start date",
    example: "2025-01-01",
  })
  @IsOptional()
  @IsDateString()
  start_date?: string;

  @ApiPropertyOptional({
    description: "Filter by end date",
    example: "2025-01-01",
  })
  @IsOptional()
  @IsDateString()
  end_date?: string;
}
