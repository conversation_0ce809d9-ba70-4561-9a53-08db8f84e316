import { Body, Controller, Delete, Get, Param, Post } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { CreateReportSetColumnFractionDto } from "./dto/report-set-column-fraction-create.dto";
import { ReportSetColumnFractionsService } from "./report-set-column-fractions.service";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("ReportSetColumnFractions")
@Controller("report-set-column-fractions")
export class ReportSetColumnFractionsController {
  constructor(private readonly reportSetColumnFractionsService: ReportSetColumnFractionsService) {}

  @Post()
  @ApiOperation({ summary: "Create a new report set column fraction" })
  @ApiResponse({
    status: 201,
    description: "Report set column fraction created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        column_code: { type: "string" },
        fraction_code: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
  })
  @ApiResponse({ status: 400, description: "Invalid report set column fraction data" })
  @ApiResponse({ status: 404, description: "Report set column not found" })
  @ApiResponse({ status: 404, description: "Report set fraction not found" })
  create(@Body() data: CreateReportSetColumnFractionDto) {
    return this.reportSetColumnFractionsService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all report set column fractions" })
  @ApiResponse({
    status: 200,
    description: "Report set column fractions retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          column_code: { type: "string" },
          fraction_code: { type: "string" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
        },
      },
    },
  })
  findAll() {
    return this.reportSetColumnFractionsService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get report set column fraction by ID" })
  @ApiResponse({
    status: 200,
    description: "Report set column fraction retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        column_code: { type: "string" },
        fraction_code: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Report set column fraction not found" })
  @ApiResponse({ status: 400, description: "Invalid report set column fraction ID" })
  findOne(@Param("id") id: string) {
    return this.reportSetColumnFractionsService.findOne(+id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete report set column fraction by ID" })
  @ApiResponse({ status: 200, description: "Report set column fraction deleted successfully" })
  @ApiResponse({ status: 404, description: "Report set column fraction not found" })
  @ApiResponse({ status: 400, description: "Invalid report set column fraction ID" })
  remove(@Param("id") id: string) {
    return this.reportSetColumnFractionsService.remove(+id);
  }
}
