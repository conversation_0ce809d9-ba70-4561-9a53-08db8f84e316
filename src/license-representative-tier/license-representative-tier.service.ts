import { DatabaseService } from "@/database/database.service";
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { CreateLicenseRepresentativeTierDto } from "./dto/create-license-representative-tier.dto";
import { UpdateLicenseRepresentativeTierDto } from "./dto/update-license-representative-tier.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

@Injectable()
export class LicenseRepresentativeTierService {
  constructor(private databaseService: DatabaseService) {}

  async findAll(license_id?: number) {
    return await this.databaseService.licenseRepresentativeTier.findMany({
      where: {
        deleted_at: null,
        ...(license_id && { license_id: license_id }),
      },
      include: {
        license: true,
      },
      orderBy: {
        id: "desc",
      },
    });
  }

  async findOne(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicenseRepresentativeTier(id, user);

    const licenseRepresentativeTier = await this.databaseService.licenseRepresentativeTier.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        license: true,
      },
    });

    return licenseRepresentativeTier;
  }

  async create(data: CreateLicenseRepresentativeTierDto) {
    await this.databaseService.licenseRepresentativeTier.updateMany({
      where: {
        license_id: data.license_id,
      },
      data: {
        deleted_at: new Date(),
      },
    });

    const representativeTier = await this.databaseService.licenseRepresentativeTier.create({
      data: {
        license_id: data.license_id,
        setup_representative_tier_id: data.setup_representative_tier_id,
        price: data.price,
        name: data.name,
        created_at: new Date(),
        updated_at: new Date(),
      },
      include: {
        license: {
          include: {
            contract: {
              include: {
                customer: true,
              },
            },
          },
        },
      },
    });

    return representativeTier;
  }

  async update(id: number, data: UpdateLicenseRepresentativeTierDto, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicenseRepresentativeTier(id, user);

    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("License Third Party Invoice ID is invalid");
    }

    const licenseRepresentativeTier = await this.databaseService.licenseRepresentativeTier.findUnique({
      where: { id: Number(id), deleted_at: null },
    });

    if (!licenseRepresentativeTier) {
      throw new NotFoundException("License Representative Tier not found");
    }

    return await this.databaseService.licenseRepresentativeTier.update({
      where: { id: Number(id) },
      data: {
        setup_representative_tier_id: data.setup_representative_tier_id,
        price: data.price,
        name: data.name,
        updated_at: new Date(),
      },
    });
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicenseRepresentativeTier(id, user);

    return await this.databaseService.licenseRepresentativeTier.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }

  async validatingUserPermissionLicenseRepresentativeTier(id: number, user: AuthenticatedUser) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("License Third Party Invoice ID is invalid");
    }

    const licenseRepresentativeTier = await this.databaseService.licenseRepresentativeTier.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        license: {
          include: {
            contract: {
              include: {
                customer: true,
              },
            },
          },
        },
      },
    });

    if (!licenseRepresentativeTier) {
      throw new NotFoundException("License Representative Tier not found");
    }

    const { license } = licenseRepresentativeTier;
    if (!license) {
      throw new NotFoundException("License not found");
    }
    const { contract } = license;
    if (!contract) {
      throw new NotFoundException("Contract not found");
    }
    const { customer } = contract;
    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You do not have permission to access this license representative tier");
    }
  }
}
