import { ApiProperty } from "@nestjs/swagger";
import { CriteriaMode, CriteriaType, CriteriaInputType, CriteriaCalculatorType } from "@prisma/client";

export class CreateCriteriaDto {
  @ApiProperty({
    required: true,
    enum: CriteriaMode,
    description: "Mode of the criteria",
  })
  mode: CriteriaMode;

  @ApiProperty({
    required: true,
    enum: CriteriaType,
    description: "Type of the criteria",
  })
  type: CriteriaType;

  @ApiProperty({
    required: false,
    description: "Title of the criteria",
  })
  title?: string;

  @ApiProperty({
    required: false,
    description: "Help text for the criteria",
  })
  help_text?: string;

  @ApiProperty({
    required: false,
    enum: CriteriaInputType,
    description: "Input type of the criteria",
  })
  input_type?: CriteriaInputType;

  @ApiProperty({
    required: false,
    enum: CriteriaCalculatorType,
    description: "Calculator type of the criteria",
  })
  calculator_type?: CriteriaCalculatorType;

  @ApiProperty({
    required: true,
    description: "ID of the country",
  })
  country_id: number;

  @ApiProperty({
    required: false,
    description: "ID of the packaging service",
  })
  packaging_service_id?: number;

  @ApiProperty({
    required: false,
    description: "ID of the required information",
  })
  required_information_id?: number;

  @ApiProperty({
    required: false,
    description: "Options of the criteria",
  })
  options?: { criteria_id: number; option_value: string; option_to_value: string | null; value: string }[];
}
