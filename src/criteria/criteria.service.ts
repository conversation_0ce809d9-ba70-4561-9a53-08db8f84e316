import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "../database/database.service";
import { CreateCriteriaDto } from "./dto/criteria-create.dto";
import { UpdateCriteriaDto } from "./dto/criteria-update.dto";

@Injectable()
export class CriteriaService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create({ options, ...data }: CreateCriteriaDto) {
    return this.databaseService.$transaction(async (tx) => {
      const createdCriteria = await tx.criteria.create({
        data,
        include: { options: true },
      });

      if (options && !!options.length) {
        await tx.criteriaOption.createMany({
          data: options.map((option) => ({
            ...option,
            criteria_id: createdCriteria.id,
          })),
        });
      }

      return createdCriteria;
    });
  }

  async findAll() {
    return this.databaseService.criteria.findMany({
      where: { deleted_at: null },
    });
  }

  async findOne(id: number) {
    const criteria = await this.databaseService.criteria.findUnique({
      where: { id, deleted_at: null },
    });

    if (!criteria) {
      throw new NotFoundException("Criteria not found");
    }

    return criteria;
  }

  async update(id: number, { options, ...data }: UpdateCriteriaDto) {
    const criteria = await this.databaseService.criteria.findUnique({
      where: { id },
    });

    if (!criteria) throw new NotFoundException("Criteria not found");

    return this.databaseService.$transaction(async (prisma) => {
      const updatedCriteria = await prisma.criteria.update({
        where: { id },
        data,
        include: { options: true },
      });

      if (options && options.length > 0) {
        await prisma.criteriaOption.deleteMany({ where: { criteria_id: updatedCriteria.id } });

        await prisma.criteriaOption.createMany({
          data: options.map((option) => ({ ...option, criteria_id: updatedCriteria.id })),
        });
      }

      return updatedCriteria;
    });
  }

  async remove(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid criteria ID");

    const criteria = await this.databaseService.criteria.findUnique({
      where: { id },
    });

    if (!criteria) {
      throw new NotFoundException("Criteria not found");
    }

    await this.databaseService.criteria.update({
      where: { id },
      data: { deleted_at: new Date() },
    });
  }
}
