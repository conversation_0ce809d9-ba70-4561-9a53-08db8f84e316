import { Body, Controller, Post } from "@nestjs/common";
import { ForgotPasswordMailDto } from "./dto/forgot-password.dto";
import { MailService } from "./mail.service";
import { SendEmailRequestOptions } from "customerio-node/dist/lib/api/requests";
import { ApiTags } from "@nestjs/swagger";
import { PublicRoute } from "@/shared/auth/public-route.decorator";

@ApiTags("Emails")
@Controller("emails")
export class MailController {
  constructor(private readonly mailService: MailService) {}

  @PublicRoute()
  @Post("forgot-password")
  async sendForgotPasswordEmail(@Body() data: ForgotPasswordMailDto): Promise<void> {
    await this.mailService.sendForgotPasswordMail(data);
  }

  @PublicRoute()
  @Post("email-verification")
  async sendEmailVerification(@Body() data: ForgotPasswordMailDto): Promise<void> {
    await this.mailService.sendEmailVerificationMail(data);
  }

  @PublicRoute()
  @Post("send-magic-link")
  async sendMagicLink(@Body() data: ForgotPasswordMailDto): Promise<void> {
    await this.mailService.sendMagicLink(data);
  }

  @PublicRoute()
  @Post("send-message")
  async sendMessage(@Body() customerIoDto: SendEmailRequestOptions): Promise<void> {
    await this.mailService.sendMessage(customerIoDto);
  }
}
