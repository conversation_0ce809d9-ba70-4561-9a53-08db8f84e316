import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  BadRequestException,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common";
import { BrokerCompaniesService } from "./broker-companies.service";
import { CreateBrokerCompanyDto } from "./dto/create-broker-company.dto";
import { UpdateBrokerCompanyDto } from "./dto/update-broker-company.dto";
import { FindAllQueryDto } from "./dto/find-all-query.dto";
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiConsumes } from "@nestjs/swagger";
import { UploadFileDto } from "../broker-company-orders/dto/upload-file.dto";
import { FileInterceptor } from "@nestjs/platform-express";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.BROKER_MANAGER, Role.BROKER)
@ApiTags("Broker Companies")
@Controller("broker-companies")
export class BrokerCompaniesController {
  constructor(private readonly brokerCompaniesService: BrokerCompaniesService) {}

  @Post()
  @ApiOperation({ summary: "Create a new broker company" })
  @ApiResponse({
    status: 201,
    description: "Broker company successfully created.",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        broker_id: { type: "number" },
        register_number: { type: "string" },
        vat: { type: "string", nullable: true },
        tax: { type: "string", nullable: true },
        country_code: { type: "string" },
        address_number: { type: "string" },
        address_street: { type: "string" },
        city: { type: "string" },
        contact_name: { type: "string" },
        contact_email: { type: "string" },
        phone_number: { type: "string" },
        file_id: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time", nullable: true },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
      example: {
        id: 1,
        name: "Broker Company 1",
        broker_id: 1,
        register_number: "1234567890",
        vat: "EXAMPLE_VAT",
        tax: null,
        country_code: "IT",
        address_number: "123",
        address_street: "Main St",
        city: "New York",
        contact_name: "John Doe",
        contact_email: "<EMAIL>",
        phone_number: "+1234567890",
        file_id: 1,
        created_at: "2024-03-20T10:00:00Z",
        updated_at: "2024-03-20T10:00:00Z",
        deleted_at: null,
      },
    },
  })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  async create(@Body() createBrokerCompanyDto: CreateBrokerCompanyDto) {
    return await this.brokerCompaniesService.create(createBrokerCompanyDto);
  }

  @Post("upload")
  @UseInterceptors(FileInterceptor("file"))
  @ApiOperation({ summary: "Upload an Excel file with companies and specify brokerId" })
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        file: { type: "string", format: "binary" },
        brokerId: { type: "string" },
      },
    },
  })
  @ApiResponse({ status: 201, description: "File uploaded and processed successfully" })
  @ApiResponse({ status: 400, description: "Invalid file format or missing company_id" })
  async uploadFile(@UploadedFile() file: Express.Multer.File, @Body() { brokerId }: UploadFileDto) {
    if (!file || !file.originalname.match(/\.(xls|xlsx)$/)) {
      throw new BadRequestException("Only Excel files are allowed!");
    }
    return await this.brokerCompaniesService.processExcel(file, +brokerId);
  }

  @Get()
  @ApiOperation({
    summary: "Retrieve all broker companies with pagination and search",
  })
  @ApiResponse({
    status: 200,
    description: "List of broker companies",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          name: { type: "string" },
          broker_id: { type: "number" },
          register_number: { type: "string" },
          vat: { type: "string", nullable: true },
          tax: { type: "string", nullable: true },
          country_code: { type: "string" },
          address_number: { type: "string" },
          address_street: { type: "string" },
          city: { type: "string" },
          contact_name: { type: "string" },
          contact_email: { type: "string" },
          phone_number: { type: "string" },
          file_id: { type: "number" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time", nullable: true },
          deleted_at: { type: "string", format: "date-time", nullable: true },
        },
      },
      example: [
        {
          id: 1,
          name: "Broker Company 1",
          broker_id: 1,
          register_number: "1234567890",
          vat: "EXAMPLE_VAT",
          tax: null,
          country_code: "IT",
          address_number: "123",
          address_street: "Main St",
          city: "New York",
          contact_name: "John Doe",
          contact_email: "<EMAIL>",
          phone_number: "+1234567890",
          file_id: 1,
          created_at: "2024-03-20T10:00:00Z",
          updated_at: "2024-03-20T10:00:00Z",
          deleted_at: null,
        },
        {
          id: 2,
          name: "Broker Company 2",
          broker_id: 2,
          register_number: "1234567890",
          vat: "EXAMPLE_VAT",
          tax: null,
          country_code: "IT",
          address_number: "123",
          address_street: "Main St",
          city: "New York",
          contact_name: "John Doe",
          contact_email: "<EMAIL>",
          phone_number: "+1234567890",
          file_id: 2,
          created_at: "2024-03-20T10:00:00Z",
          updated_at: "2024-03-20T10:00:00Z",
          deleted_at: null,
        },
      ],
    },
  })
  async findAll(@Query() query: FindAllQueryDto) {
    return await this.brokerCompaniesService.findAll(query);
  }

  @Get(":id")
  @ApiOperation({ summary: "Retrieve a single broker company by ID" })
  @ApiParam({ name: "id", type: Number, description: "Broker company ID" })
  @ApiResponse({
    status: 200,
    description: "Broker company found",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        broker_id: { type: "number" },
        register_number: { type: "string" },
        vat: { type: "string", nullable: true },
        tax: { type: "string", nullable: true },
        country_code: { type: "string" },
        address_number: { type: "string" },
        address_street: { type: "string" },
        city: { type: "string" },
        contact_name: { type: "string" },
        contact_email: { type: "string" },
        phone_number: { type: "string" },
        file_id: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time", nullable: true },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
      example: {
        id: 1,
        name: "Broker Company 1",
        broker_id: 1,
        register_number: "1234567890",
        vat: "EXAMPLE_VAT",
        tax: null,
        country_code: "IT",
        address_number: "123",
        address_street: "Main St",
        city: "New York",
        contact_name: "John Doe",
        contact_email: "<EMAIL>",
        phone_number: "+1234567890",
        file_id: 1,
        created_at: "2024-03-20T10:00:00Z",
        updated_at: "2024-03-20T10:00:00Z",
        deleted_at: null,
      },
    },
  })
  @ApiResponse({ status: 404, description: "Broker company not found" })
  async findOne(@Param("id") id: number) {
    return await this.brokerCompaniesService.findOne(+id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update a broker company by ID" })
  @ApiParam({ name: "id", type: Number, description: "Broker company ID" })
  @ApiResponse({
    status: 200,
    description: "Broker company updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        broker_id: { type: "number" },
        register_number: { type: "string" },
        vat: { type: "string", nullable: true },
        tax: { type: "string", nullable: true },
        country_code: { type: "string" },
        address_number: { type: "string" },
        address_street: { type: "string" },
        city: { type: "string" },
        contact_name: { type: "string" },
        contact_email: { type: "string" },
        phone_number: { type: "string" },
        file_id: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time", nullable: true },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
      example: {
        id: 1,
        name: "Broker Company 1",
        broker_id: 1,
        register_number: "1234567890",
        vat: "EXAMPLE_VAT",
        tax: null,
        country_code: "IT",
        address_number: "123",
        address_street: "Main St",
        city: "New York",
        contact_name: "John Doe",
        contact_email: "<EMAIL>",
        phone_number: "+1234567890",
        file_id: 1,
        created_at: "2024-03-20T10:00:00Z",
        updated_at: "2024-03-20T10:00:00Z",
        deleted_at: null,
      },
    },
  })
  @ApiResponse({ status: 404, description: "Broker company not found" })
  async update(@Param("id") id: number, @Body() updateBrokerCompanyDto: UpdateBrokerCompanyDto) {
    return await this.brokerCompaniesService.update(+id, updateBrokerCompanyDto);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Soft delete a broker company by ID" })
  @ApiParam({ name: "id", type: Number, description: "Broker company ID" })
  @ApiResponse({ status: 200, description: "Broker company soft deleted" })
  @ApiResponse({ status: 404, description: "Broker company not found" })
  async remove(@Param("id") id: number) {
    return await this.brokerCompaniesService.remove(+id);
  }
}
