import Status from "@/components/modules/country/components/task-status";
import { ClaimManagement } from "@/lib/api/claim-management/types";
import { formatCurrency } from "@/utils/format-currency";

import { EditCircle, KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { createColumnHelper } from "@tanstack/react-table";
import { Dispatch, SetStateAction } from "react";
import { InvoiceActionsDropdown } from "./claim-management-actions";
import { ClaimManagementModalState } from "@/components/modules/customers/customer-profile/claim-management";
interface UseClaimManagementColumnProps {
  setModalState: Dispatch<SetStateAction<ClaimManagementModalState>>;
}

export function useClaimManagementCustomerColumn({ setModalState }: UseClaimManagementColumnProps) {
  const columnHelper = createColumnHelper<ClaimManagement>();

  return [
    columnHelper.display({
      id: "invoiceId",
      cell: (info) => {
        const invoiceId = info.row.original.invoiceId;
        return (
          <div className="flex items-center gap-3">
            <KeyboardArrowDown className="size-5 fill-support-blue" />
            <div className="text-paragraph-regular text-primary">#{invoiceId ?? " "}</div>
          </div>
        );
      },
      header: "Invoice ID",
    }),
    columnHelper.accessor("customerName", {
      header: "Customer Name",
      cell: (info) => info.getValue(),
    }),
    columnHelper.accessor("totalAmount", {
      header: "Total Amount",
      cell: (info) => formatCurrency(Number(info.getValue()) || 0),
    }),
    columnHelper.accessor("dateIssued", {
      header: "Date Issued",
      cell: (info) => info.getValue(),
    }),
    columnHelper.display({
      id: "dueDate",
      cell: (info) => {
        const dueDate = info.row.original.dueDate;
        return (
          <div className="flex items-center gap-3">
            <div className="text-paragraph-regular text-primary">{dueDate}</div>
            <EditCircle className="size-5 fill-support-blue mb-2" />
          </div>
        );
      },
      header: "Due Date",
    }),
    columnHelper.accessor("paymentMethod", {
      header: "Payment method",
      cell: (info) => info.getValue(),
    }),
    columnHelper.accessor("type", {
      header: "Type",
      cell: (info) => info.getValue(),
    }),
    columnHelper.accessor("status", {
      header: "Status",
      cell: (info) => {
        const status = info.getValue();
        return <Status status={status} />;
      },
    }),
    columnHelper.display({
      id: "actions",
      cell: (info) => {
        const rowData = info.row.original;
        return <InvoiceActionsDropdown rowData={rowData} setModalState={setModalState} />;
      },
    }),
  ];
}
