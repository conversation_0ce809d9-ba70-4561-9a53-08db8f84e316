import { ClaimManagementModalState } from "@/components/modules/customers/customer-profile/claim-management";
import { Dropdown, DropdownContent, DropdownItem, DropdownTrigger } from "@/components/ui/table-options";
import { ClaimManagement } from "@/lib/api/claim-management/types";
import { File } from "@arthursenno/lizenzero-ui-react/Icon";
import { Dispatch, SetStateAction } from "react";
import { MdCancel, MdRemoveRedEye } from "react-icons/md";
import { useTranslations } from "next-intl";
interface InvoiceActionsDropdownProps {
  rowData: ClaimManagement;
  setModalState: Dispatch<SetStateAction<ClaimManagementModalState>>;
}

export const InvoiceActionsDropdown: React.FC<InvoiceActionsDropdownProps> = ({ rowData, setModalState }) => {
  const t = useTranslations("ClaimManagementActions");
  return (
    <Dropdown>
      <DropdownTrigger />
      <DropdownContent>
        <DropdownItem onClick={() => setModalState({ isOpen: true, type: "view", data: rowData })}>
          <MdRemoveRedEye className="size-5" />
          <span className="mt-1.5">{t("view")}</span>
        </DropdownItem>
        <DropdownItem onClick={() => setModalState({ isOpen: true, type: "print", data: rowData })}>
          <File className="size-5 fill-primary" />
          <span className="mt-1.5">{t("print")}</span>
        </DropdownItem>
        <DropdownItem onClick={() => setModalState({ isOpen: true, type: "refund", data: rowData })}>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M19.1666 6.66732L19.1666 15.0007C19.1666 15.9173 18.4166 16.6673 17.4999 16.6673L4.16658 16.6673C3.70825 16.6673 3.33325 16.2923 3.33325 15.834C3.33325 15.3757 3.70825 15.0007 4.16658 15.0007L17.4999 15.0007L17.4999 6.66732C17.4999 6.20898 17.8749 5.83398 18.3333 5.83398C18.7916 5.83398 19.1666 6.20898 19.1666 6.66732ZM3.33325 13.334C1.94992 13.334 0.833252 12.2173 0.833252 10.834L0.833252 5.83398C0.833252 4.45065 1.94992 3.33398 3.33325 3.33398L13.3333 3.33398C14.7166 3.33398 15.8333 4.45065 15.8333 5.83398L15.8333 11.6673C15.8333 12.584 15.0833 13.334 14.1666 13.334L3.33325 13.334ZM5.83325 8.33398C5.83325 9.71732 6.94992 10.834 8.33325 10.834C9.71658 10.834 10.8333 9.71732 10.8333 8.33398C10.8333 6.95065 9.71658 5.83398 8.33325 5.83398C6.94992 5.83398 5.83325 6.95065 5.83325 8.33398Z"
              fill="#002652"
            />
          </svg>
          <span className="mt-1.5">{t("refund")}</span>
        </DropdownItem>
        <DropdownItem onClick={() => setModalState({ isOpen: true, type: "cancel", data: rowData })}>
          <MdCancel className="size-5" />
          <span className="mt-1.5">
            {t("cancel")} <br /> {t("invoice")}
          </span>
        </DropdownItem>
        <DropdownItem onClick={() => setModalState({ isOpen: true, type: "reissue", data: rowData })}>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M15.5418 6.95898L13.2168 9.28398C12.9501 9.55065 13.1334 10.0007 13.5084 10.0007H15.0001C15.0001 12.759 12.7584 15.0007 10.0001 15.0007C9.34177 15.0007 8.70011 14.8757 8.12511 14.634C7.82511 14.509 7.48344 14.6007 7.25844 14.8257C6.83344 15.2507 6.98344 15.9673 7.54177 16.1923C8.30011 16.5007 9.13344 16.6673 10.0001 16.6673C13.6834 16.6673 16.6668 13.684 16.6668 10.0007H18.1584C18.5334 10.0007 18.7168 9.55065 18.4501 9.29232L16.1251 6.96732C15.9668 6.80065 15.7001 6.80065 15.5418 6.95898ZM5.00011 10.0007C5.00011 7.24232 7.24177 5.00065 10.0001 5.00065C10.6584 5.00065 11.3001 5.12565 11.8751 5.36732C12.1751 5.49232 12.5168 5.40065 12.7418 5.17565C13.1668 4.75065 13.0168 4.03398 12.4584 3.80898C11.7001 3.50065 10.8668 3.33398 10.0001 3.33398C6.31677 3.33398 3.33344 6.31732 3.33344 10.0007H1.84177C1.46677 10.0007 1.28344 10.4507 1.55011 10.709L3.87511 13.034C4.04177 13.2007 4.30011 13.2007 4.46677 13.034L6.79177 10.709C7.05011 10.4507 6.86677 10.0007 6.49177 10.0007H5.00011Z"
              fill="#002652"
            />
          </svg>
          <span className="mt-1.5">{t("reissue")}</span>
        </DropdownItem>
        <DropdownItem onClick={() => setModalState({ isOpen: true, type: "details", data: rowData })}>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M3.33333 15H6.66667C7.125 15 7.5 14.625 7.5 14.1667C7.5 13.7083 7.125 13.3333 6.66667 13.3333H3.33333C2.875 13.3333 2.5 13.7083 2.5 14.1667C2.5 14.625 2.875 15 3.33333 15ZM2.5 5.83333C2.5 6.29167 2.875 6.66667 3.33333 6.66667L16.6667 6.66667C17.125 6.66667 17.5 6.29167 17.5 5.83333C17.5 5.375 17.125 5 16.6667 5L3.33333 5C2.875 5 2.5 5.375 2.5 5.83333ZM3.33333 10.8333L11.6667 10.8333C12.125 10.8333 12.5 10.4583 12.5 10C12.5 9.54167 12.125 9.16667 11.6667 9.16667L3.33333 9.16667C2.875 9.16667 2.5 9.54167 2.5 10C2.5 10.4583 2.875 10.8333 3.33333 10.8333Z"
              fill="#002652"
            />
          </svg>
          <span className="mt-1.5">{t("details")}</span>
        </DropdownItem>
      </DropdownContent>
    </Dropdown>
  );
};
