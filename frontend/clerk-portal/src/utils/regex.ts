export const EMAIL_REGEX =
  /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;

export const SPECIAL_CHARS_REGEX = /^[^\-+*/.,!@#$%&*()_]+$/;

export const SPECIAL_CHARS_NUMERIC_REGEX = /^[^\d\-+*/.,!@#$%&*()_]+$/;

export const ONLY_ALPHANUMERIC_REGEX = /^[a-zA-Z0-9]+$/;

export const ZIP_CODE_REGEX = /^[0-9a-zA-Z-]+$/;

export const CONTAINS_LETTER_REGEX = /[a-zA-Z]/;

export const CONTAINS_NUMBER_REGEX = /[0-9]/;

export const CONTAINS_NON_ALPHANUMERIC_REGEX = /[^a-zA-Z0-9]/;

export const ADDRESS_REGEX = /^[^\+*/!@#$%&*()_]+$/;

export const SPECIAL_CHARS_COMPANY_NAME_REGEX = /^[^\+*/,!@#$%*()_]+$/;
