interface DownloadFileParams {
  buffer: Buffer;
  fileName: string;
}

export async function downloadFile({ buffer, fileName }: DownloadFileParams) {
  try {
    const blob = new Blob([buffer]);

    const url = window.URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.download = fileName;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    window.URL.revokeObjectURL(url);
  } catch (error) {}
}
