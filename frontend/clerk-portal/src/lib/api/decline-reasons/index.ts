import { api } from "@/lib/api";

import { DeclineReason, type DeclineReasonType } from "./types";

interface GetDeclineReasonsParams {
  type: DeclineReasonType;
}

export async function getDeclineReasons(params: GetDeclineReasonsParams) {
  const response = await api.get<DeclineReason[]>(`/customer/decline-reasons`, { params });

  if (response.status !== 200 || !response.data) {
    throw new Error("Failed to fetch decline reasons");
  }

  return response.data;
}
