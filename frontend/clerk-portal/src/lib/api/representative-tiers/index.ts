import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { RepresentativeTier, RepresentativeTierDto } from "./types";

export async function getRepresentativeTiers({ countryCode }: { countryCode: string }) {
  try {
    const response = await api.get<RepresentativeTier[]>(ApiEndpoints.representativeTiers.get(countryCode));

    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
}

export async function createRepresentativeTiers(data: RepresentativeTierDto) {
  try {
    const response = await api.post<RepresentativeTier[]>(ApiEndpoints.representativeTiers.post, data);

    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
}
