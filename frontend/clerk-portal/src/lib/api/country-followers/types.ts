export interface ICountryFollower {
  id: number;
  country_id: number;
  user_id: number;
  user_email: string;
  user_first_name: string;
  user_last_name: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface CreateCountryFollowerRequest {
  country_id: number;
  user_ids: number[];
}

export interface DeleteCountryFollowerRequest {
  country_id: number;
  user_id: number;
}
