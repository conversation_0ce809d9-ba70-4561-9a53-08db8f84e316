import { api } from "@/lib/api";

import { ApiEndpoints } from "../endpoints";
import { CreateCountryFollowerRequest, DeleteCountryFollowerRequest } from "./types";

export const createCountryFollower = async (requestData: CreateCountryFollowerRequest) => {
  const { data } = await api.post(ApiEndpoints.countryFollowers.create, requestData);
  return data;
};

export const deleteCountryFollower = async (requestParams: DeleteCountryFollowerRequest) => {
  const { data } = await api.delete(ApiEndpoints.countryFollowers.delete, {
    params: {
      country_id: requestParams.country_id,
      user_id: requestParams.user_id,
    },
  });
  return data;
};
