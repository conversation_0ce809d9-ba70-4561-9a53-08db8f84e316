import { PublishedCountry } from "@/hooks/use-liberated-countries";

export interface OtherCost {
  id: number;
  name: string;
  price: number;
  country_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  country: PublishedCountry;
  has_criteria: boolean;
}

export interface OtherCostDto {
  license_id: number;
  setup_other_cost_id: number;
  name: string;
  price: number;
}
