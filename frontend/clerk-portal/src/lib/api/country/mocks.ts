import { ICountry, MondayTasks } from "./types";

export const countries: ICountry[] = [
  // {
  //   id: "1a2b3c4d5e6f7g8h9i",
  //   name: "Germany",
  //   code: "DE",
  //   licensed_customers: 150,
  //   unlicensed_customers: 30,
  //   open_tasks: 7,
  //   representative: ["Hans Ford"],
  // },
  // {
  //   id: "2a3b4c5d6e7f8g9h0i",
  //   name: "Switzerland",
  //   code: "CH",
  //   licensed_customers: 120,
  //   unlicensed_customers: 25,
  //   open_tasks: 6,
  //   representative: null,
  // },
  // {
  //   id: "3a4b5c6d7e8f9g0h1i",
  //   name: "Great Britain",
  //   code: "GB",
  //   licensed_customers: 200,
  //   unlicensed_customers: 40,
  //   open_tasks: 10,
  //   representative: ["<PERSON>", "<PERSON>"],
  // },
  // {
  //   id: "4a5b6c7d8e9f0g1h2i",
  //   name: "Italy",
  //   code: "IT",
  //   licensed_customers: 180,
  //   unlicensed_customers: 35,
  //   open_tasks: 8,
  //   representative: ["<PERSON>"],
  // },
  // {
  //   id: "5a6b7c8d9e0f1g2h3i",
  //   name: "Netherlands",
  //   code: "NL",
  //   licensed_customers: 130,
  //   unlicensed_customers: 28,
  //   open_tasks: 5,
  //   representative: ["Maarten Visser"],
  // },
  // {
  //   id: "6a7b8c9d0e1f2g3h4i",
  //   name: "Austria",
  //   code: "AT",
  //   licensed_customers: 110,
  //   unlicensed_customers: 20,
  //   open_tasks: 4,
  //   representative: ["Max Gruber", "Jane Smith"],
  // },
  // {
  //   id: "7a8b9c0d1e2f3g4h5i",
  //   name: "Spain",
  //   code: "ES",
  //   licensed_customers: 170,
  //   unlicensed_customers: 33,
  //   open_tasks: 9,
  //   representative: ["Carlos García"],
  // },
  // {
  //   id: "8a9b0c1d2e3f4g5h6i",
  //   name: "Belgium",
  //   code: "BE",
  //   licensed_customers: 140,
  //   unlicensed_customers: 26,
  //   open_tasks: 6,
  //   representative: null,
  // },
  // {
  //   id: "9a0b1c2d3e4f5g6h7i",
  //   name: "Bulgaria",
  //   code: "BG",
  //   licensed_customers: 90,
  //   unlicensed_customers: 15,
  //   open_tasks: 3,
  //   representative: ["Ivan Ivanov"],
  // },
  // {
  //   id: "0a1b2c3d4e5f6g7h8i",
  //   name: "Greece",
  //   code: "GR",
  //   licensed_customers: 100,
  //   unlicensed_customers: 18,
  //   open_tasks: 5,
  //   representative: ["Nikos Papadopoulos"],
  // },
  // {
  //   id: "1a2b3c4d5e6f7g8h9j",
  //   name: "Ireland",
  //   code: "IE",
  //   licensed_customers: 160,
  //   unlicensed_customers: 30,
  //   open_tasks: 7,
  //   representative: ["Liam O'Connor"],
  // },
  // {
  //   id: "2a3b4c5d6e7f8g9h0j",
  //   name: "Croatia",
  //   code: "HR",
  //   licensed_customers: 80,
  //   unlicensed_customers: 12,
  //   open_tasks: 4,
  //   representative: ["Ivan Horvat"],
  // },
];

export const mondayTasks: MondayTasks[] = [
  {
    id: 1,
    customerId: "#029231",
    documentName: "Document Name",
    type: "Registration",
    isDone: false,
    status: "Open task",
    taskProgress: "Registration in progress",
  },
  {
    id: 2,
    customerId: "#029231",
    documentName: "Document Name",
    type: "Registration",
    isDone: false,
    status: "Open task",
    taskProgress: "Registration in progress",
  },
  {
    id: 3,
    customerId: "#029231",
    documentName: "Document Name",
    type: "Registration",
    isDone: false,
    status: "Open task",
    taskProgress: "Registration in progress",
  },
  {
    id: 4,
    customerId: "#029231",
    documentName: "Volume Report - Q01",
    type: "Volume Reports",
    isDone: true,
    status: "Complete",
    taskProgress: "Complete",
  },
  {
    id: 5,
    customerId: "#029231",
    documentName: "Volume Report - Q02",
    type: "Volume Reports",
    isDone: false,
    status: "Pending",
    taskProgress: "Pending - response from dual system",
  },
  {
    id: 6,
    customerId: "#029231",
    documentName: "Volume Report - Q03",
    type: "Volume Reports",
    isDone: false,
    status: "Open task",
    taskProgress: "In progress",
  },
  {
    id: 7,
    customerId: "#029231",
    documentName: "Volume Report - Q04",
    type: "Volume Reports",
    isDone: false,
    status: "Open task",
    taskProgress: "Outstanding",
  },
  {
    id: 8,
    customerId: "#029231",
    documentName: "Invoice name",
    type: "Invoice",
    isDone: false,
    status: "Pending",
    taskProgress: "Unprocessed Invoice",
  },
  {
    id: 9,
    customerId: "#029231",
    documentName: "Invoice name",
    type: "Invoice",
    isDone: false,
    status: "Open task",
    taskProgress: "Invoice Outstanding",
  },
  {
    id: 10,
    customerId: "#029231",
    documentName: "Invoice name",
    type: "Invoice",
    isDone: true,
    status: "Complete",
    taskProgress: "Unprocessed Dunning reminder",
  },
  {
    id: 11,
    customerId: "#029231",
    documentName: "Invoice name",
    type: "Invoice",
    isDone: false,
    status: "Pending",
    taskProgress: "Invoice in Dunning Procedure",
  },
  {
    id: 12,
    customerId: "#029231",
    documentName: "Eu License - All Countries",
    type: "Terminations",
    isDone: true,
    status: "Pending",
    taskProgress: "Pending with dual system",
  },
  {
    id: 13,
    customerId: "#029231",
    documentName: "Direct License",
    type: "Terminations",
    isDone: false,
    status: "Open task",
    taskProgress: "In progress",
  },
];
