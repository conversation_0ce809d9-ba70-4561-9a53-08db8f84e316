import { api } from "@/lib/api";
import { delay } from "@/utils/delay";

import { ApiEndpoints } from "../endpoints";
import type { GetCountriesOverviewParams, ICountry } from "./types";
import { countries, mondayTasks } from "./mocks";

export const getLiberatedCountries = async () => {
  try {
    return await api.get(`/countries`);
  } catch (err) {
    return err;
  }
};

export const getCountries = async () => {
  const response = await api.get<ICountry[]>(ApiEndpoints.country.getAll);

  if (response.status !== 200 || !response.data) {
    throw new Error("Failed to fetch countries");
  }

  return response.data || [];
};

export const getCountriesOverview = async (params: GetCountriesOverviewParams) => {
  const response = await api.get<ICountry[]>(ApiEndpoints.country.getOverview, { params });

  if (response.status !== 200 || !response.data) {
    throw new Error("Failed to fetch countries overview");
  }

  return response.data || [];
};

export const getCountryById = async (id: string | number) => {
  try {
    await delay();
    return countries.find((country) => country.id === id) || null;
  } catch (error) {
    return null;
  }
};

export const getTasks = async () => {
  try {
    await delay();
    return mondayTasks;
  } catch (error) {
    return null;
  }
};

export const getCountryByCode = async (code: string) => {
  const response = await api.get<ICountry>(ApiEndpoints.country.getByCode(code));

  if (response.status !== 200 || !response.data) {
    throw new Error("Failed to fetch country by code");
  }

  return response.data;
};

export const getCountryOverview = async (code: string) => {
  const response = await api.get<ICountry>(ApiEndpoints.country.getOverviewByCode(code));

  if (response.status !== 200 || !response.data) {
    throw new Error("Failed to fetch country overview");
  }

  return response.data;
};
