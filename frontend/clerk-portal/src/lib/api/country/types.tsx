import type { ICountryFollower } from "../country-followers/types";

export interface ICountry {
  id: number;
  name: string;
  code: string;
  flag_url: string;
  followers: ICountryFollower[];
  licensed_customer_count: number;
  unlicensed_customer_count: number;
  tasks: number;
}

export interface MondayTasks {
  id: number;
  customerId: string;
  documentName: string;
  type: "Registration" | "Volume Reports" | "Invoice" | "Terminations";
  taskProgress: string;
  status: "Complete" | "Pending" | "Open task";
  isDone: boolean;
}

export interface GetCountriesOverviewParams {
  search?: string;
}
