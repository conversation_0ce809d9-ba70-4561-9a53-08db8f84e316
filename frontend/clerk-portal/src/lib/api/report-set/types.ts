export type FullReportSet = ReportSet & {
  packaging_service: {
    id: number;
    name: string;
  };
  fractions: (ReportSetFraction & {
    children: (ReportSetFraction & {
      children: ReportSetFraction[];
    })[];
  })[];
  columns: (ReportSetColumn & {
    children: (ReportSetColumn & {
      fractions: ReportSetColumnFraction[];
    })[];
  })[];
};

export interface ReportSet {
  id: number;
  name: string;
  mode: ReportSetMode;
  type: ReportSetType;
  packaging_service_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export type ReportSetType = "FRACTIONS" | "CATEGORIES";

export type ReportSetMode = "ON_PLATAFORM" | "BY_EXCEL";

export type CreateReportSet = Omit<ReportSet, "id" | "created_at" | "updated_at" | "deleted_at">;

export type UpdateReportSet = Omit<
  ReportSet,
  "id" | "packaging_service_id" | "mode" | "type" | "created_at" | "updated_at" | "deleted_at"
> & {
  fractions?: Pick<ReportSetFraction, "id" | "name" | "description" | "icon" | "is_active">[];
  columns?: Pick<ReportSetColumn, "id" | "name" | "description" | "unit_type">[];
};

export interface ReportSetFraction {
  id: number;
  name: string;
  description: string;
  icon: string;
  is_active: boolean;
  report_set_id: number;
  parent_id: number | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export type CreateReportSetFraction = Omit<ReportSetFraction, "id" | "created_at" | "updated_at" | "deleted_at">;

export type UpdateReportSetFraction = Partial<
  Omit<ReportSetFraction, "id" | "report_set_id" | "created_at" | "updated_at" | "deleted_at">
>;

export interface ReportSetFrequency {
  id: number;
  rhythm: ReportSetRhythm;
  frequency: Frequency | null;
  packaging_service_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export type ReportSetRhythm = "ANNUALLY" | "MONTHLY" | "QUARTERLY";

export type CreateReportSetFrequency = Omit<ReportSetFrequency, "id" | "created_at" | "updated_at" | "deleted_at">;

export type UpdateReportSetFrequency = Partial<
  Omit<ReportSetFrequency, "id" | "packaging_service_id" | "created_at" | "updated_at" | "deleted_at">
>;

export type Frequency = AnnuallyFrequency | MonthlyFrequency | QuarterlyFrequency;

export interface AnnuallyFrequency {
  deadline: {
    day: number;
    month: string;
  };
  open: {
    day: number;
    month: string;
  };
}

export interface QuarterlyFrequency {
  deadline: {
    option: string;
    weekDay: string;
  };
  open: {
    option: string;
    weekDay: string;
  };
}

export interface MonthlyFrequency {
  deadline: {
    day: number;
  };
  open: {
    day: number;
  };
}

export interface ReportSetFraction {
  id: number;
  parent_id: number | null;
  code: string;
  parent_code: string | null;
  name: string;
  description: string;
  icon: string;
  fraction_icon_id: number;
  fraction_icon: {
    id: number;
    image_url: string;
  };
  is_active: boolean;
  report_set_id: number;
  level: number;
  order: number;
  has_second_level: boolean;
  has_third_level: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  children: ReportSetFraction[];
}

export interface ReportSetColumn {
  id: number;
  parent_id: number | null;
  code: string;
  parent_code: string | null;
  name: string;
  description: string;
  unit_type: ReportSetColumnUnitType;
  report_set_id: number;
  level: number;
  order: number;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  children: ReportSetColumn[];
  fractions: ReportSetColumnFraction[];
}

export interface ReportSetColumnFraction {
  id: number;
  column_code: string;
  fraction_code: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export type ReportSetColumnUnitType = "KG" | "UNITS" | "EACH";

export interface LicenseReportSetDto {
  setup_report_set_id?: number;
  license_packaging_service_id?: number;
}

export type LicenseReportSetWithId = LicenseReportSetDto & { id: string };
