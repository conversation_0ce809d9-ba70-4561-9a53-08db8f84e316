import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { VolumeReport, VolumeReportColumn, VolumeReportFraction } from "./types";

type CreateVolumeReportParams = {
  license_packaging_service_id: number;
  interval: string;
  year: number;
  report_table: {
    fractions: VolumeReportFraction[];
    columns: VolumeReportColumn[];
  };
};

export async function createVolumeReport(params: CreateVolumeReportParams) {
  const response = await api.post<VolumeReport>(ApiEndpoints.volumeReport.create, {
    ...params,
    status: "DONE",
  });

  return response.data;
}

interface UpdateVolumeReportParams {
  status: "DONE" | "IN_PROGRESS";
}

export async function updateVolumeReport(id: number, data: UpdateVolumeReportParams) {
  const response = await api.put<VolumeReport>(ApiEndpoints.volumeReport.update(id), data);

  return response.data;
}
