import { api } from "@/lib/api";
import { ContractType } from "../contracts/types";
import { ApiEndpoints } from "../endpoints";
import type {
  CustomerDataPayload,
  ListCustomer,
  UpdateCustomerCompanyInfoPayload,
  UpdateCustomerContactInfoPayload,
} from "./types";

interface GetCustomersParams {
  page?: number;
  limit?: number;
  search?: string;
  service_type?: ContractType;
  order?: "ASC" | "DESC" | "LAST_MODIFIED" | "FIRST_MODIFIED";
  status?: "ACTIVE" | "TERMINATED";
  country_code?: string;
}

interface GetCustomersResponse {
  count: number;
  current_page: number;
  pages: number;
  customers: ListCustomer[];
}

export async function getCustomers(params: GetCustomersParams) {
  const response = await api.get<GetCustomersResponse>(ApiEndpoints.customers.getAll, { params });

  if (response.status !== 200 || !response.data) {
    throw new Error("Failed to fetch customers");
  }

  return response.data;
}

export async function getCustomerById(id: number) {
  const response = await api.get<ListCustomer>(ApiEndpoints.customers.getById(id));

  return response.data;
}

export const updateCustomerContactInfo = async (payload: UpdateCustomerContactInfoPayload) => {
  const firstName = payload.fullName.split(" ")[0];
  const lastName = payload.fullName.split(" ")[1];

  try {
    return await api.put(`/customer/customer/${payload.customerId}`, {
      first_name: firstName,
      last_name: lastName,
      // email: payload?.emails?.[0],
      email: payload.email,
      phones: payload.phones,
    });
  } catch (error) {
    return error;
  }
};

export const updateCustomerCompanyInfo = async (
  companyId: number | string,
  payload: UpdateCustomerCompanyInfoPayload
) => {
  try {
    const res = await api.put(`/customer/company/${companyId}`, payload);
    return res;
  } catch (error: any) {
    return error;
  }
};

export const createCustomerWithcountries = async (payload: CustomerDataPayload) => {
  try {
    return await api.post(`/customer/customer/customer-with-countries`, payload);
  } catch (err) {
    return err;
  }
};
