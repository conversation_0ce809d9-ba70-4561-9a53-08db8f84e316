import { Certificate } from "../certificates/types";
import { Contract } from "../contracts/types";
import { PackagingService } from "../packaging-services/types";
import { RequiredInformation } from "../required-information/types";
import { ServiceNextStep } from "../service-next-steps/types";
import { ThirdPartyInvoice } from "../third-party-invoice/types";
import { Termination } from "../termination/types";
import { VolumeReport } from "../volume-report/types";
import { UploadedFile } from "../file/types";
import { RepresentativeTier } from "../representative-tiers/types";
export type License = {
  id: number;
  contract_id: number;
  registration_number: string;
  registration_status: LicenseRegistrationStatus;
  clerk_control_status: LicenseClerkControlStatus;
  contract_status: LicenseContractStatus;
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  year: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  start_date: string;
  end_date: string | null;
  termination: Termination | null;
  files: UploadedFile[];
  certificates: Certificate[];
};

export type FullLicense = License & {
  packaging_services: (PackagingService & { volume_reports: VolumeReport[] })[];
  required_informations: RequiredInformation[];
  third_party_invoices: ThirdPartyInvoice[];
  next_steps: ServiceNextStep[];
  contract: Contract & { files: File[] };
  certificates: Certificate[];
  termination: Termination;
  price_list: LicensePriceList[];
  pendencies: LicensePendency[];
  files: UploadedFile[];
  other_costs: OtherCost[];
  representative_tiers: RepresentativeTier[];
};

export type LicenseRegistrationStatus = "PENDING" | "IN_REVIEW" | "REGISTRATION" | "DONE";

export type LicenseClerkControlStatus = "PENDING" | "DONE";

export type LicenseContractStatus = "ACTIVE" | "TERMINATION_PROCESS" | "TERMINATED";

export interface LicensePendency {
  type: "REQUIRED_INFORMATIONS" | "VOLUME_REPORTS" | "INVOICES";
  label: string;
}

export interface OtherCost {
  id: number;
  name: string;
  setup_other_cost_id: number;
  price: number;
}

type BasePriceList = {
  id: string;
  name: string;
  service_type: "EU_LICENSE" | "DIRECT_LICENSE" | "ACTION_GUIDE";
  description: string;
  condition_type: "LICENSE_YEAR";
  condition_type_value: string;
  start_date: string;
  end_date: string;
};

export type LicensePriceList = BasePriceList & {
  service_type: "EU_LICENSE";
  basic_price: number;
  minimum_price: number;
  registration_fee: number;
  handling_fee: number;
  variable_handling_fee: number;
};

export type ActionGuidePriceList = BasePriceList & {
  service_type: "ACTION_GUIDE";
  price: number;
};
