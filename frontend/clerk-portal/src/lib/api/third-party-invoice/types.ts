import { License } from "../license/types";

export interface ThirdPartyInvoice {
  id: number;
  title: string;
  price: number;
  issued_at: string;
  due_date: string;
  status: ThirdPartyInvoiceStatus;
  issuer: LicenseThirdPartyInvoiceIssuer;
  license: License;
  license_id: number;
  file_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface CreateThirdPartyInvoiceDTO {
  title: string;
  price: number;
  issued_at: string;
  due_date: string;
  status: ThirdPartyInvoiceStatus;
  issuer: LicenseThirdPartyInvoiceIssuer;
  license_id: number;
  file_id: number;
}

export type ThirdPartyInvoiceStatus = "OPEN" | "PAYED" | "UNPROCESSED" | "CANCELLED";

export type LicenseThirdPartyInvoiceIssuer = "THIRD_PARTY_DUAL_SYSTEM" | "OTHER_THIRD_PARTY";
