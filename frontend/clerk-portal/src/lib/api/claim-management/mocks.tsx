import { ClaimManagement } from "./types";

export const claims: ClaimManagement[] = [
  {
    invoiceId: 101,
    customerName: "<PERSON>",
    totalAmount: "1200.50",
    dateIssued: "2024.09.01",
    dueDate: "2024.09.15",
    paymentMethod: "Credit Card",
    type: "Credit Invoice",
    status: "Open",
    info: "Admin processing",
    openBalance: "500.00",
    claimDate: "2024.09.12",
    otherInfo1: "Lorem ipsum dolor sit amet",
    otherInfo2: "Consectetur adipiscing elit",
  },
  {
    invoiceId: 102,
    customerName: "<PERSON>",
    totalAmount: "850",
    dateIssued: "2024.08.25",
    dueDate: "2024.09.10",
    paymentMethod: "Credit Card",
    type: "Credit Invoice",
    status: "Payed",
  },
  {
    invoiceId: 103,
    customerName: "Acme Corp",
    totalAmount: "3000.00",
    dateIssued: "2024.09.05",
    dueDate: "2024.09.20",
    paymentMethod: "Bank Transfer",
    type: "Invoice",
    status: "Open",
    info: "Priority customer",
    openBalance: "1500.00",
    claimDate: "2024.09.15",
  },
  {
    invoiceId: 104,
    customerName: "Global Industries",
    totalAmount: "1500.75",
    dateIssued: "2024.07.15",
    dueDate: "2024.08.01",
    paymentMethod: "PayPal",
    type: "Invoice",
    status: "Payed",
  },
  {
    invoiceId: 105,
    customerName: "Tech Solutions",
    totalAmount: "500",
    dateIssued: "2024.09.10",
    dueDate: "2024.09.25",
    paymentMethod: "Bank Transfer",
    type: "Invoice",
    status: "Open",
    otherInfo1: "Pending additional details",
  },
];
