import { ApiEndpoints } from "../endpoints";
import { api } from "..";
import { PackagingService } from "./types";

type GetPackagingServicesByParams = {
  license_id: number;
  year?: number;
};

export async function getPackagingServices(params: GetPackagingServicesByParams) {
  const response = await api.get<PackagingService[]>(ApiEndpoints.packagingServices.getAll, { params });
  return response.data;
}

export async function getPackagingService(id: number) {
  const response = await api.get<PackagingService>(ApiEndpoints.packagingServices.findById(id));
  return response.data;
}
