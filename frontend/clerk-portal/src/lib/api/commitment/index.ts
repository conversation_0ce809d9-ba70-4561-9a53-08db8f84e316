import { api } from "..";
import { Commitment, ServiceSetupResult } from "./types";

export async function getCommitment(countryCode: string) {
  try {
    const normalizedCountryCode = countryCode.toUpperCase();

    const commitmentResponse = await api.get<Commitment>(`/admin/service-setups/${normalizedCountryCode}/commitment`);

    if (commitmentResponse.status !== 200) throw commitmentResponse;

    const commitment = commitmentResponse.data;

    return { success: true, data: commitment } as const;
  } catch (err: any) {
    const errorMessage = err?.response?.data?.message || err?.message || "Unknown error occurred";
    return { success: false, error: errorMessage as string } as const;
  }
}

export interface SubmitCommitmentParams {
  customer_email?: string;
  country_code: string;
  year: number;
  commitment_answers: { criteria_id: number; answer: string }[];
}

export interface CustomerCommitment {
  id: number;
  customer_email?: string;
  country_code: string;
  year: number;
  commitment: Commitment;
  service_setup: ServiceSetupResult;
  is_license_required: boolean;
  customer?: {
    id: number;
  };
}

export async function submitCommitment(data: SubmitCommitmentParams) {
  try {
    const serviceSetupResultResponse = await api.post<CustomerCommitment>(`/customer/customer-commitments`, data);

    if (serviceSetupResultResponse.status !== 200 && serviceSetupResultResponse.status !== 201)
      throw serviceSetupResultResponse;

    const result = serviceSetupResultResponse.data;

    return { success: true, data: result } as const;
  } catch (err: any) {
    const errorMessage = err?.response?.data?.message || err?.message || "Unknown error occurred";
    return { success: false, error: errorMessage as string } as const;
  }
}

export async function getCustomerCommitment(customerCommitmentId: number) {
  try {
    const customerCommitmentResponse = await api.get<CustomerCommitment>(
      `/customer/customer-commitments/${customerCommitmentId}`
    );

    if (customerCommitmentResponse.status !== 200) throw customerCommitmentResponse;

    const customerCommitment = customerCommitmentResponse.data;

    return { success: true, data: customerCommitment } as const;
  } catch (err: any) {
    const errorMessage = err?.response?.data?.message || err?.message || "Unknown error occurred";
    return { success: false, error: errorMessage as string } as const;
  }
}
