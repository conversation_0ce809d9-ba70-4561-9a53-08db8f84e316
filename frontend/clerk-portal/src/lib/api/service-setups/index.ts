import { api } from "@/lib/api";
import { ApiEndpoints } from "../endpoints";

type GetServiceSetupInformationsParams = {
  country_code: string;
};

export const getServiceSetupRequiredInformations = async (params: GetServiceSetupInformationsParams) => {
  const countryCode = params.country_code;
  const response = await api.get<any[]>(ApiEndpoints.serviceSetups.getRequiredInformations(countryCode), { params });

  if (response.status !== 200 || !response.data) {
    throw new Error("Failed to fetch service setup required informations");
  }

  return response.data;
};
