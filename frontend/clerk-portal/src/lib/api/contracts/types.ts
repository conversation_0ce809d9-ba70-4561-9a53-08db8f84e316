import { UploadedFile } from "../file/types";
import { License } from "../license/types";

export interface Contract {
  id: number;
  customer_id: number;
  type: ContractType;
  status: ContractStatus;
  title: string;
  start_date: Date;
  end_date: Date;
  termination_id: number | null;
  termination_date: Date | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  licenses: License[];
  action_guides: ActionGuide[];
  files: UploadedFile[];
  termination: Termination | null;
}

export type ContractType = "EU_LICENSE" | "DIRECT_LICENSE" | "ACTION_GUIDE";

export type ContractStatus = "ACTIVE" | "TERMINATION_PROCESS" | "TERMINATED";

export type ActionGuide = {
  id: number;
  contract_id: number;
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  contract_status: ContractStatus;
  termination: Termination | null;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
};

export type Termination = {
  id: number;
  created_at: string;
  completed_at: string;
  requested_at: string;
  status: TerminationStatus;
  year: string;
};

export type TerminationStatus = "REQUESTED" | "PENDING" | "COMPLETED";
