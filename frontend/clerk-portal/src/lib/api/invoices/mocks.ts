import type { ITransaction } from "./types";

export const transactions: ITransaction[] = [
  {
    id: "fc20385a-4bc5-4e66-88cb-deda4a261f13",
    companyName: "Lautner CO.",
    externalReference: "585424",
    transactionAmount: -300.0,
    remainingTransactionAmount: null,
    iban: "20.12.2023",
    transactionDate: "2024-12-30",
    clientId: "928234",
    status: "unassigned",
  },
  {
    id: "7ad92f63-9df6-4d9e-ae65-b531d816c12b",
    companyName: "Skyline Industries",
    externalReference: "295832",
    transactionAmount: 1200.0,
    remainingTransactionAmount: 800.0,
    iban: "15.11.2023",
    transactionDate: "2023-11-15",
    clientId: "928234",
    status: "unassigned",
  },
  {
    id: "9f4bce01-08f7-45b4-8b1c-23c77a3d17d2",
    companyName: "TechHub Corp",
    externalReference: "431527",
    transactionAmount: -500.0,
    remainingTransactionAmount: null,
    iban: "25.10.2023",
    transactionDate: "2023-10-25",
    clientId: "928234",
    status: "unassigned",
  },
  {
    id: "5e8b1a4d-b347-4c47-8260-865d0c3a7f61",
    companyName: "Solaris Solutions",
    externalReference: "789654",
    transactionAmount: 2500.0,
    remainingTransactionAmount: null,
    iban: "20.09.2023",
    transactionDate: "2023-09-20",
    clientId: "928234",
    status: "assigned",
  },
];
