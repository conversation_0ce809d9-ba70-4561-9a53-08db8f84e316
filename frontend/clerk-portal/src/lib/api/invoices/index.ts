// import { api } from "@/lib/api";
import { delay } from "@/utils/delay";
import { transactions } from "./mocks";
import { api } from "..";
import { GetInvoicesParams } from "./types";
import { ApiEndpoints } from "../endpoints";

// import { ApiEndpoints } from "../endpoints";
// import type { InvoiceBalance, ITransaction } from "./types";

export const getInvoiceBalance = async () => {
  try {
    // const res = await api.get<InvoiceBalance>(ApiEndpoints.invoices.balance);
    // return res.data;

    await delay(); // simulate slow network
    return { totalReceived: 6_000_000, totalAssigned: 5_100_000, totalUnassigned: 900_000 };
  } catch (error) {
    return null;
  }
};

export const getTransactions = async () => {
  try {
    // const res = await api.get<ITransaction[]>(ApiEndpoints.invoices.getTransactions);
    // return res.data;

    await delay(); // simulate slow network
    return transactions;
  } catch (error) {
    return null;
  }
};

export async function getInvoices(params: GetInvoicesParams) {
  const { customerId, startDate, endDate, orderId } = params;

  try {
    const params = {
      ...(customerId && { customerId }),
      ...(startDate && { startDate }),
      ...(endDate && { endDate }),
      ...(orderId && { orderId }),
    };

    const res = await api.get(ApiEndpoints.invoices.get, { params });

    if (res.status !== 200 && res.status !== 201) throw res;

    return { success: true, data: res.data } as const;
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message;
    return { success: false, error: errorMessage };
  }
}
