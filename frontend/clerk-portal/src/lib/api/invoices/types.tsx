import { Currency } from "../purchase/types";

export interface InvoiceBalance {
  totalReceived: number;
  totalAssigned: number;
  totalUnassigned: number;
}

export type TransactionStatus = "assigned" | "unassigned" | "refund";

export interface ITransaction {
  id: string;
  companyName: string;
  externalReference: string;
  transactionAmount: number;
  remainingTransactionAmount: number | null;
  iban: string;
  transactionDate: string;
  clientId: string;
  status: TransactionStatus;
}

export enum RefundType {
  DUNNING_CHARGE = "DUNNING_CHARGE",
  OTHER = "OTHER",
}

export enum PaymentMethod {
  BANK_TRANSFER = "BANK_TRANSFER",
  OTHER = "OTHER",
}

export interface Invoice {
  id: number;
  payment_customer_id: string;
  amount: number;
  currency: Currency;
  status: InvoiceStatus;
  company_name: string;
  company_country: string;
  city: string;
  street: string;
  post_code: string;
  vat_id: string;
  tax_id: string;
}

export enum InvoiceStatus {
  OPEN = "OPEN",
  PAID = "PAID",
  CANCELLED = "CANCELLED",
  REFUND = "REFUND",
}

export interface GetInvoicesParams {
  customerId?: string;
  startDate?: string;
  endDate?: string;
  orderId?: string;
}
