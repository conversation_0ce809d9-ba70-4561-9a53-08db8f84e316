interface CountryOffer {
  countryCode: string;
  expenses: ExpenseData[];
}

interface ExpenseData {
  feeName: string;
  price: number;
}

interface OfferItens {
  categoryName: string;
  countriesOffers?: CountryOffer[];
}

export interface IOffer {
  id?: string;
  name?: string;
  offerItems?: OfferItens[];
  status?: string;
  paymentCondition?: string;
  total?: number;
  file?: File;
  isDraft?: boolean;
}
