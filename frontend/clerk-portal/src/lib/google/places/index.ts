// The GooglePlaces api is only for server side code
import "server-only";
import {
  AddressComponent,
  GetPlaceSuggestionsParams,
  PlaceDetails,
  PlaceSuggestion,
  PlaceSuggestionsResponse,
  Result,
} from "./types";
import { MOCK_PLACE_SUGGESTIONS, MOCK_PLACE_DETAILS } from "./mock";

const GOOGLE_AUTOCOMPLETE_API_KEY = "AIzaSyDyK5_A20SP5-kCN7Z9PqpTIUwwvWnYExQ";
const GOOGLE_AUTOCOMPLETE_API_URL = "https://maps.googleapis.com/maps/api/place";

class GooglePlaces {
  private apiKey: string;
  private apiUrl: string;

  constructor() {
    this.apiKey = GOOGLE_AUTOCOMPLETE_API_KEY;
    this.apiUrl = GOOGLE_AUTOCOMPLETE_API_URL;
  }

  async getPlaceSuggestions(
    params: GetPlaceSuggestionsParams,
    env: "development" | "production" = "development"
  ): Promise<Result<PlaceSuggestion[]>> {
    if (!params.countryCode || !params.addressLine) {
      return { success: false, error: "Missing required parameters" };
    }

    if (env === "development") {
      const mockPlaceSuggestions = MOCK_PLACE_SUGGESTIONS;

      return { success: true, data: mockPlaceSuggestions };
    }

    const queryParams = new URLSearchParams();

    queryParams.set("input", params.addressLine);
    queryParams.set("types", "address");
    queryParams.set("components", `country:${params.countryCode}`);
    queryParams.set("key", this.apiKey);

    try {
      const response = await fetch(`${this.apiUrl}/autocomplete/json?${queryParams.toString()}`);

      const data = (await response.json()) as PlaceSuggestionsResponse;

      let addresses: PlaceSuggestion[] = [];

      if (data.predictions) {
        addresses = data.predictions.map((p) => ({ formattedAddress: p.description, placeId: p.place_id }));
      }

      return { success: true, data: addresses };
    } catch (error: any) {
      return { success: false, error: error.message || "Error autocompleting address" };
    }
  }

  async getPlaceDetails(
    placeId?: string,
    env: "development" | "production" = "development"
  ): Promise<Result<PlaceDetails>> {
    if (!placeId) return { success: false, error: "Missing placeId" };

    if (env === "development") {
      const mockPlaceDetails = MOCK_PLACE_DETAILS[placeId];

      if (!mockPlaceDetails) return { success: false, error: "Mock place details not found" };

      return { success: true, data: mockPlaceDetails };
    }

    const queryParams = new URLSearchParams();

    queryParams.set("place_id", placeId);
    queryParams.set("key", this.apiKey);
    queryParams.set("fields", "address_component,formatted_address");

    try {
      const response = await fetch(`${this.apiUrl}/details/json?${queryParams.toString()}`);

      const data = await response.json();

      if (data.status !== "OK" || !data.result) {
        return { success: false, error: "Unable to fetch address details" };
      }

      const { address_components, formatted_address } = data.result;

      const placeDetails: PlaceDetails = {
        formattedAddress: formatted_address,
        streetNumber: null,
        route: null,
        postalCode: null,
        city: null,
        country: null,
      };

      const addressComponents = address_components as AddressComponent[];

      placeDetails.city = (() => {
        const locality = addressComponents.find((component) => component.types.includes("locality"));

        if (locality) return locality.long_name;

        const mostGranularAdministrativeAreaLevel = addressComponents.find((component) =>
          component.types.some((type: string) => type.startsWith("administrative_area_level_"))
        );

        return mostGranularAdministrativeAreaLevel?.long_name || null;
      })();

      addressComponents.forEach((component: any) => {
        if (component.types.includes("street_number")) {
          placeDetails.streetNumber = component.long_name;
        }
        if (component.types.includes("route")) {
          placeDetails.route = component.long_name;
        }
        if (component.types.includes("postal_code")) {
          placeDetails.postalCode = component.long_name;
        }
        if (component.types.includes("country")) {
          placeDetails.country = component.long_name;
        }
      });

      return { success: true, data: placeDetails };
    } catch (error: any) {
      return { success: false, error: error.message || "Error fetching address details" };
    }
  }
}

export const googlePlaces = new GooglePlaces();
