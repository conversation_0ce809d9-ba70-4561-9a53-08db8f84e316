"use client";

import { LoadingScreen } from "@/components/_common/loading-screen";
import { tokenManager } from "@/lib/next-auth/local-token-manager";
import { signOut, useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import { ReactNode, useEffect } from "react";

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const session = useSession();
  const pathname = usePathname();

  useEffect(() => {
    if (session.status === "loading") return;

    if (session.status === "unauthenticated") {
      tokenManager.setAccessToken(null);

      if (!pathname.includes("auth")) {
        signOut({ redirect: true, callbackUrl: "/auth/login" });
      }

      return;
    }

    tokenManager.setAccessToken(session.data!.user.access_token);
  }, [session.status]);

  if (session.status === "loading") return <LoadingScreen />;

  return children;
}
