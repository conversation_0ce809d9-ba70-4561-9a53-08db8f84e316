"use client";

import { ReactQueryProvider } from "@/lib/react-query";
import { SessionProvider } from "next-auth/react";
import { SnackbarProvider } from "notistack";
import { ReactNode } from "react";
import { AuthProvider } from "./auth-provider";

interface ProvidersProps {
  children: ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <SessionProvider>
      <ReactQueryProvider>
        <SnackbarProvider autoHideDuration={3000}>
          <AuthProvider>{children}</AuthProvider>
        </SnackbarProvider>
      </ReactQueryProvider>
    </SessionProvider>
  );
}
