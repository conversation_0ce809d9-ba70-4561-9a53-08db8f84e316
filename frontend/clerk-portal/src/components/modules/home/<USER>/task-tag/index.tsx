import { cn } from "@/lib/utils";

enum TagType {
  REPORT = "report",
  DOCS = "docs_and_info",
  COUNTRY = "country",
}

interface TaskTagProps {
  tag: { key: string; label: string };
}

export function TaskTag({ tag }: TaskTagProps) {
  const tagKey = tag.key as TagType;

  const tagColor = {
    [TagType.REPORT]: "bg-tonal-dark-blue-90 text-tonal-blue-40",
    [TagType.DOCS]: "bg-tonal-dark-green-80 text-tonal-dark-green-30",
    [TagType.COUNTRY]: "bg-tonal-dark-blue-90 text-tonal-blue-40",
  };

  return (
    <div
      className={cn("rounded-lg py-1 px-4 text-xs font-medium text-center", {
        [tagColor[tagKey]]: true,
      })}
    >
      <span className="pt-1 block leading-none">{tag.label}</span>
    </div>
  );
}
