import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Select } from "@arthursenno/lizenzero-ui-react/Select";
import { useState } from "react";
import Divider from "@/components/_common/divider";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Search } from "@arthursenno/lizenzero-ui-react/Icon";
import { useCustomerLicense } from "./use-customer-license";
import { formatCurrency } from "@/utils/format-currency";
import { useQuery, useMutation } from "@tanstack/react-query";
import { getReportSets, updateReportSets } from "@/lib/api/report-set";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Delete } from "@arthursenno/lizenzero-ui-react/Icon";
import { But<PERSON> } from "@arthursenno/lizenzero-ui-react/Button";
import { createRepresentativeTiers, getRepresentativeTiers } from "@/lib/api/representative-tiers";
import { createOtherCosts, deleteOtherCosts, getOtherCosts } from "@/lib/api/other-costs";
import { RepresentativeTierDto } from "@/lib/api/representative-tiers/types";
import { queryClient } from "@/lib/react-query";
import { enqueueSnackbar } from "notistack";
import { OtherCostDto } from "@/lib/api/other-costs/types";
import { Loading } from "@/components/_common/loading";
import { LicenseReportSetDto, LicenseReportSetWithId } from "@/lib/api/report-set/types";
import { useTranslations } from "next-intl";

type selectedValueType = "FRACTION" | "OTHER_COST" | "DELETE_OTHER_COST" | "REPRESENTATIVE";

export function CustomerCountryPriceList() {
  const t = useTranslations("CustomerCountryPriceList");
  const c = useTranslations("common");
  const { license, refetch } = useCustomerLicense();

  const [isOpenConfirmModal, setIsOpenConfirmModal] = useState(false);
  const [selectedValue, setSelectedValue] = useState<{
    type: selectedValueType;
    [packageId: number | string]: number | string;
  } | null>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [searchValueCost, setSearchValueCost] = useState("");

  const { data: pricesData } = useQuery({
    queryKey: ["prices-data", license?.country_code],
    queryFn: async () => {
      try {
        if (!license?.country_code) return null;

        const [reportSet, representativeTiers, otherCosts] = await Promise.all([
          getReportSets({ countryCode: license?.country_code }),
          getRepresentativeTiers({ countryCode: license?.country_code }),
          getOtherCosts({ countryCode: license?.country_code }),
        ]);

        return {
          reportSet,
          representativeTiers,
          otherCosts,
        };
      } catch (error) {
        console.error(error);
        throw error;
      }
    },
  });

  const { mutateAsync: mutateAsyncRepresentativeTier, isPending: isPendingRepresentativeTier } = useMutation({
    mutationFn: async (data: RepresentativeTierDto) => {
      await createRepresentativeTiers(data);
    },
    onSuccess: () => {
      enqueueSnackbar(t("dataSaved"), { variant: "success" });
    },
    onError: () => {
      enqueueSnackbar(t("errorSavingData"), { variant: "error" });
    },
  });

  const { mutateAsync: mutateAsyncOtherCost, isPending: isPendingOtherCost } = useMutation({
    mutationFn: async (data: OtherCostDto) => {
      await createOtherCosts(data);
    },
    onSuccess: () => {
      enqueueSnackbar(t("dataSaved"), { variant: "success" });
    },
    onError: () => {
      enqueueSnackbar(t("errorSavingData"), { variant: "error" });
    },
  });

  const {
    mutateAsync: mutateAsyncDeleteOtherCost,
    isPending: isPendingDeleteOtherCost,
    variables: variablesDeleteOtherCost,
  } = useMutation({
    mutationFn: async (otherCostsId: string | number) => {
      await deleteOtherCosts(otherCostsId);
    },
    onSuccess: () => {
      enqueueSnackbar("Data deleted successfully", { variant: "success" });
    },
    onError: () => {
      enqueueSnackbar("Error deleting data", { variant: "error" });
    },
  });

  const { mutateAsync: mutateAsyncUpdateReportSets, isPending: isPendingUpdateReportSets } = useMutation({
    mutationFn: async ({ id, ...data }: LicenseReportSetWithId) => {
      await updateReportSets(id, data);
    },
    onSuccess: () => {
      enqueueSnackbar("Data deleted successfully", { variant: "success" });
    },
    onError: () => {
      enqueueSnackbar("Error deleting data", { variant: "error" });
    },
  });

  const handleSave = async () => {
    if (selectedValue?.type === "REPRESENTATIVE") {
      const representativeTierSelect = pricesData?.representativeTiers.find(
        (item) => item.id === Number(selectedValue?.["REPRESENTATIVE"])
      );

      if (!representativeTierSelect || !license?.country_code) return;

      await mutateAsyncRepresentativeTier({
        license_id: license.id,
        setup_representative_tier_id: representativeTierSelect.id,
        name: representativeTierSelect.name,
        price: representativeTierSelect.price,
      });
    }

    if (selectedValue?.type === "OTHER_COST") {
      const otherCostsSelect = pricesData?.otherCosts.find((item) => item.id === Number(selectedValue?.["OTHER_COST"]));

      if (!otherCostsSelect || !license?.id) return;
      await mutateAsyncOtherCost({
        license_id: license.id,
        setup_other_cost_id: otherCostsSelect.id,
        name: otherCostsSelect.name,
        price: otherCostsSelect.price,
      });
    }

    if (selectedValue?.type === "DELETE_OTHER_COST" && selectedValue?.["DELETE_OTHER_COST"]) {
      await mutateAsyncDeleteOtherCost(selectedValue["DELETE_OTHER_COST"]);
    }

    if (selectedValue?.type === "FRACTION") {
      const fractionId = Object.keys(selectedValue).filter((key) => key !== "type")?.[0];

      if (!fractionId || !selectedValue?.[fractionId]) return;

      await mutateAsyncUpdateReportSets({
        id: fractionId,
        setup_report_set_id: Number(selectedValue?.[fractionId]),
      });
    }

    refetch();
    handleChangeModal(false);
  };

  const handleChangeModal = (value: boolean) => {
    setIsOpenConfirmModal(value);
    setSelectedValue(null);
  };

  const handleSelectNewValue = (key: number | string, value: number | string, type: selectedValueType) => {
    setIsOpenConfirmModal(true);
    setSelectedValue({ [key]: value, type });
  };

  if (!license) return null;

  const priceList = license?.price_list?.[0];

  const isDirectLicensing = license?.country_code === "DE";

  const fractionData = (() => {
    const options: {
      [packageName: string]: { label: string; value: string }[];
    } = {};
    const selectedValue: { [packageName: string]: string } = {};

    if (pricesData?.reportSet) {
      license?.packaging_services.forEach((service) => {
        const selectedPackaging =
          pricesData.reportSet.filter((item) => item.packaging_service_id === service.setup_packaging_service_id) || [];

        options[service.report_set.id] =
          selectedPackaging?.map((item) => ({ label: item.name, value: item.id.toString() })) || [];

        selectedValue[service.report_set.id] = service?.report_set?.setup_report_set_id?.toString();
      });
    }

    return {
      options,
      selectedValue,
    };
  })();

  if (isDirectLicensing) {
    return (
      <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
        <div className="flex flex-row items-center justify-between">
          <p className="text-title-3 font-bold text-[#183362]">{t("priceList")}</p>
        </div>
        <div className="mt-6 w-full flex flex-col gap-3">
          <div className="bg-white w-full h-auto rounded-xl p-4">
            <Select
              label={priceList?.name ?? ""}
              onChange={console.log}
              options={
                [
                  { label: t("standard2024"), value: "standard2024" },
                  { label: t("standard2023"), value: "standard2023" },
                  { label: t("standard2022"), value: "standard2022" },
                ] as unknown as []
              }
              value=""
            />
            <p className="text-tonal-dark-cream-50 font-small-paragraph-regular text-sm mt-4">
              {t("set")}:{" "}
              {priceList?.end_date ? new Date(priceList.end_date).toLocaleDateString().replaceAll("/", ".") : "-"}
            </p>
          </div>
        </div>
      </div>
    );
  }

  const filteredOtherCosts = pricesData?.otherCosts?.filter((otherCost) => {
    if (license.other_costs.some((item) => item.setup_other_cost_id === otherCost.id)) return false;

    return JSON.stringify(otherCost).toLowerCase().includes(searchValueCost.toLowerCase());
  });

  const isLoading =
    isPendingRepresentativeTier || isPendingOtherCost || isPendingDeleteOtherCost || isPendingUpdateReportSets;

  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{t("priceList")}</p>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        <div className="bg-white w-full h-auto rounded-xl p-4">
          <Tabs defaultValue={priceList?.condition_type_value}>
            <TabsList>
              {[priceList!]?.map((list) => (
                <TabsTrigger key={list.id} value={list.condition_type_value} className="text-[#808FA9]">
                  {list.condition_type_value}
                </TabsTrigger>
              ))}
            </TabsList>

            {[priceList].map((list) => (
              <TabsContent key={list.id} value={list.condition_type_value!}>
                <section className="flex flex-col gap-4">
                  <div className="flex flex-col gap-3">
                    <p className="text-primary font-bold text-sm">{t("priceList")}</p>

                    <div className="flex w-full">
                      <div className="flex-1">
                        <p className="text-primary text-small-paragraph-regular">{t("registrationFee")}</p>
                        <p className="text-tonal-dark-cream-30 text-small-paragraph-regular">
                          {formatCurrency(list.registration_fee || 0)}
                        </p>
                      </div>
                      <div className="flex-1">
                        <p className="text-primary text-small-paragraph-regular">{t("handlingFee")}</p>
                        <p className="text-tonal-dark-cream-30 text-small-paragraph-regular">
                          {formatCurrency(list.handling_fee || 0)}
                        </p>
                      </div>
                    </div>
                    <div className="flex w-full">
                      <div className="flex-1">
                        <p className="text-primary text-small-paragraph-regular">{t("variableHandlingFee")}</p>
                        <p className="text-tonal-dark-cream-30 text-small-paragraph-regular">
                          {list?.variable_handling_fee}%
                        </p>
                      </div>
                    </div>
                  </div>
                  <Divider initialMarginDisabled />
                  <section className="flex flex-col gap-4">
                    {license.packaging_services.map((service) => (
                      <Select
                        key={service.id}
                        label={`Fraction Set (${service.name})`}
                        placeholder={"Select Fraction Set"}
                        value={fractionData.selectedValue?.[service.report_set.id]}
                        options={(fractionData.options?.[service.report_set.id] as []) || []}
                        onChange={(e) => handleSelectNewValue(service.report_set.id, e.target.value, "FRACTION")}
                      />
                    ))}
                  </section>
                  <Divider initialMarginDisabled />
                  <section>
                    <p className="text-primary text-paragraph-regular font-bold mb-4">{t("authorizeRepresentative")}</p>
                    <div className="flex flex-col gap-4">
                      <Select
                        label={t("authorizeRepresentative")}
                        placeholder={t("selectRepresentative")}
                        options={
                          (pricesData?.representativeTiers?.map((item) => ({
                            label: item.name,
                            value: item.id.toString(),
                          })) as []) || []
                        }
                        value={license?.representative_tiers?.[0]?.setup_representative_tier_id.toString() || ""}
                        onChange={(e) => handleSelectNewValue("REPRESENTATIVE", e.target.value, "REPRESENTATIVE")}
                      />
                      <div>
                        <p className="text-primary text-small-paragraph-regular">{t("thirdpartyCosts")}</p>
                        <div className="w-full relative">
                          <Input
                            placeholder={t("addCosts")}
                            leftIcon={<Search className="size-6 fill-primary" />}
                            onFocus={() => setIsFocused(true)}
                            onBlur={() =>
                              setTimeout(() => {
                                setIsFocused(false);
                              }, 100)
                            }
                            onChange={(e: any) => setSearchValueCost(e.target.value)}
                          />
                          {isFocused && (
                            <div className="w-full flex flex-col gap-1 absolute mt-1 left-0 bg-white rounded-xl p-4 shadow-elevation-02-1">
                              {filteredOtherCosts?.map((item) => (
                                <button
                                  className="flex gap-2 justify-between py-2"
                                  key={item.id}
                                  type="button"
                                  onClick={() => {
                                    setIsFocused(false);
                                    handleSelectNewValue("OTHER_COST", item.id, "OTHER_COST");
                                  }}
                                >
                                  <p className="text-primary">{item.name}</p>
                                  <p className="text-primary">{formatCurrency(item.price)}</p>
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2 flex-col mt-5">
                          {license.other_costs.map((item) => (
                            <div className="flex gap-2 justify-between" key={item.id}>
                              <p className="text-tonal-dark-cream-30 text-small-paragraph-regular">{item.name}</p>
                              <div className="flex items-center gap-2">
                                <p className="text-tonal-dark-cream-30 text-small-paragraph-regular">
                                  {formatCurrency(item.price)}
                                </p>
                                {variablesDeleteOtherCost === item.id ? (
                                  <Loading size={20} />
                                ) : (
                                  <Delete
                                    className="fill-tonal-dark-cream-30 cursor-pointer flex-none"
                                    height={20}
                                    width={20}
                                    onClick={() =>
                                      !(variablesDeleteOtherCost === item.id) &&
                                      handleSelectNewValue("DELETE_OTHER_COST", item.id, "DELETE_OTHER_COST")
                                    }
                                  />
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </section>
                </section>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </div>
      <AlertDialog open={isOpenConfirmModal} onOpenChange={handleChangeModal}>
        <AlertDialogTrigger asChild></AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("areYouSure")} {selectedValue?.["DELETE_OTHER_COST"] ? t("delete") : t("saveChanges")}
            </AlertDialogTitle>
          </AlertDialogHeader>

          <AlertDialogFooter>
            {!isLoading && (
              <Button color="dark-blue" variant="outlined" size="medium" onClick={() => handleChangeModal(false)}>
                Back
              </Button>
            )}
            <Button color="dark-blue" variant="filled" size="medium" onClick={handleSave} disabled={isLoading}>
              {isLoading ? c("saving") : c("save")}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
