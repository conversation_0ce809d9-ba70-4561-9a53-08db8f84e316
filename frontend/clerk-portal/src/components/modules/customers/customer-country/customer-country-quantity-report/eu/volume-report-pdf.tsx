import { PackagingService } from "@/lib/api/packaging-services/types";
import { VolumeReport } from "@/lib/api/volume-report/types";
import { formatWeight } from "@/utils/format-weight";
import { Document, Page, Text, View, StyleSheet, Font, Svg, Circle, Path } from "@react-pdf/renderer";

export const VolumeReportPDF = ({
  volumeReport,
  packagingServiceName,
  packagingService,
}: {
  volumeReport: VolumeReport;
  packagingServiceName: string;
  packagingService?: PackagingService;
}) => {
  Font.register({
    family: "CentraNo2",
    format: "truetype",
    fonts: [
      { src: "/fonts/CentraNo2/Thin.ttf", fontWeight: 100 },
      { src: "/fonts/CentraNo2/Light.ttf", fontWeight: 300 },
      { src: "/fonts/CentraNo2/Book.ttf", fontWeight: 400 },
      { src: "/fonts/CentraNo2/Medium.ttf", fontWeight: 500 },
      { src: "/fonts/CentraNo2/Extrabold.ttf", fontWeight: 700 },
      { src: "/fonts/CentraNo2/Black.ttf", fontWeight: 800 },
    ],
  });

  const styles = StyleSheet.create({
    page: {
      fontFamily: "CentraNo2",
      flexDirection: "column",
      gap: "20px",
      padding: "20px",
    },
    title: {
      fontWeight: 500,
      color: "#183362",
      fontSize: "20px",
      lineHeight: "28px",
    },
    reportFrequencyContainer: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
    },
    reportFrequency: {
      color: "#73706e",
      fontWeight: "medium",
      fontSize: "14px",
    },
    reportFrequencyValue: {
      color: "#183362",
      fontSize: "12px",
      fontWeight: "medium",
    },
    reportFrequencyInterval: {
      color: "#009dd3",
      fontWeight: "bold",
      fontSize: "16px",
      lineHeight: "24px",
    },
    table: {
      width: "100%",
      borderRadius: 20,
      overflow: "hidden",
    },
    headerRow: {
      flexDirection: "row",
      backgroundColor: "#F7F5F2",
    },
    headerCell: {
      padding: 10,
      color: "#183362",
      fontSize: 12,
      flex: 1,
      fontWeight: 400,
    },
    fractionCell: {
      width: 160,
      padding: 10,
      fontWeight: 400,
    },
    firstLevelFraction: {
      backgroundColor: "#A9C8FF",
      flexDirection: "row",
    },
    secondLevelFraction: {
      backgroundColor: "#CCE4FF",
      flexDirection: "row",
    },
    thirdLevelFraction: {
      backgroundColor: "#F7F5F2",
      flexDirection: "row",
    },
    fractionName: {
      fontSize: 12,
      color: "#183362",
    },
    dataCell: {
      padding: 10,
      flex: 1,
      fontSize: 12,
      color: "#183362",
      textAlign: "left",
      fontWeight: 300,
      flexDirection: "row",
      alignItems: "center",
      gap: 4,
    },
    errorCell: {
      backgroundColor: "#FCEAE8",
    },
    icon: {
      width: 12,
      height: 12,
    },
  });

  const CheckCircleIcon = () => (
    <Svg style={styles.icon} viewBox="0 0 24 24">
      <Circle cx="12" cy="12" r="10" fill="#66A73F" />
      <Path d="M9 12l2 2l4-4" stroke="white" strokeWidth="2" fill="none" />
    </Svg>
  );

  const ErrorIcon = () => (
    <Svg style={styles.icon} viewBox="0 0 24 24">
      <Circle cx="12" cy="12" r="10" fill="#D92D20" />
      <Path d="M12 8v4m0 4h.01" stroke="white" strokeWidth="2" strokeLinecap="round" />
    </Svg>
  );

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.title}>{packagingServiceName}</Text>
        <View style={styles.reportFrequencyContainer}>
          <Text style={styles.reportFrequency}>
            Report frequency:{" "}
            <Text style={styles.reportFrequencyValue}>
              {packagingService?.report_set_frequency.rhythm.toLowerCase().replace(/^\w/, (c) => c.toUpperCase())}
            </Text>
          </Text>
          <Text style={styles.reportFrequencyInterval}>{volumeReport.interval}</Text>
        </View>
        <View style={styles.table}>
          <View style={styles.headerRow}>
            <View style={styles.fractionCell} />
            {volumeReport?.report_table?.columns?.map((column) => (
              <Text key={column.name} style={styles.headerCell}>
                {column.name}
              </Text>
            ))}
          </View>
          {volumeReport?.report_table?.fractions?.map((firstLevelFraction) => (
            <View key={firstLevelFraction.name}>
              <View style={styles.firstLevelFraction}>
                <Text style={[styles.fractionCell, styles.fractionName]}>{firstLevelFraction.name}</Text>
                {volumeReport?.report_table?.columns?.map((column) => (
                  <View key={column.name} style={styles.dataCell} />
                ))}
              </View>
              {firstLevelFraction.children?.map((secondLevelFraction) => (
                <View key={secondLevelFraction.name}>
                  <View style={styles.secondLevelFraction}>
                    <Text style={[styles.fractionCell, styles.fractionName]}>{secondLevelFraction.name}</Text>
                    {volumeReport?.report_table?.columns?.map((column) => (
                      <View key={column.name} style={{ flex: 1, flexDirection: "row" }}>
                        {column.children?.map((item) => (
                          <Text key={item.name} style={styles.dataCell}>
                            {item.name}
                          </Text>
                        ))}
                      </View>
                    ))}
                  </View>
                  {secondLevelFraction.children?.map((thirdLevelFraction) => (
                    <View key={thirdLevelFraction.name} style={styles.thirdLevelFraction}>
                      <Text style={[styles.fractionCell, styles.fractionName]}>{thirdLevelFraction.name}</Text>
                      {volumeReport?.report_table?.columns?.map((column) => (
                        <View key={column.name} style={{ flex: 1, flexDirection: "row" }}>
                          {column.children?.map((secondLevelColumn) => {
                            const reportItem = volumeReport.volume_report_items.find(
                              (item) =>
                                item.setup_fraction_id === thirdLevelFraction.id &&
                                item.setup_column_id === secondLevelColumn.id
                            );
                            const value = reportItem?.value || 0;
                            const hasErrors = reportItem?.errors && reportItem.errors.length > 0;

                            return (
                              <View key={secondLevelColumn.id} style={{ flex: 1, flexDirection: "row" }}>
                                <View style={[styles.dataCell, ...(hasErrors ? [styles.errorCell] : [])]}>
                                  <Text>{value ? formatWeight(value) : "-- kg"}</Text>
                                  {hasErrors && <ErrorIcon />}
                                  {!hasErrors && value > 0 && <CheckCircleIcon />}
                                </View>
                              </View>
                            );
                          })}
                        </View>
                      ))}
                    </View>
                  ))}
                </View>
              ))}
            </View>
          ))}
        </View>
      </Page>
    </Document>
  );
};
