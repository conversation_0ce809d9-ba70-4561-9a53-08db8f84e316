"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { FullLicense } from "@/lib/api/license/types";
import { getPackagingServices } from "@/lib/api/packaging-services";
import { VolumeReport } from "@/lib/api/volume-report/types";
import { cn } from "@/lib/utils";
import { GERMANY_FRACTIONS } from "@/mocks/germany";
import { exportLucidXML } from "@/utils/export-lucid-file";
import { formatWeight } from "@/utils/format-weight";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { useTranslations } from "next-intl";

interface DirectLicenseReportingTableProps {
  licenses: FullLicense[];
  licenseYear: string;
}

export function DirectLicenseReportingTable({ licenseYear, licenses = [] }: DirectLicenseReportingTableProps) {
  const t = useTranslations("QuantityReportItem");
  const tXml = useTranslations("DirectLicenseReportingTable");
  const tVolumeReporting = useTranslations("volumeReporting");
  const license = licenses.find((l) => l.year === Number(licenseYear));

  const { data: volumeReport, isLoading: isLoadingVolumeReport } = useQuery<VolumeReport | null, Error>({
    queryKey: ["license-volumes", { license_id: license?.id }],
    queryFn: async () => {
      const packagingServices = await getPackagingServices({ license_id: Number(license?.id) });

      const packagingService = packagingServices[0];

      if (!packagingService) return null;

      const volumeReport = packagingService.volume_reports[0];

      if (!volumeReport) return null;

      return volumeReport;
    },
    enabled: !!license,
  });

  const fractions: { code: string; name: string; value: number }[] = (() => {
    return GERMANY_FRACTIONS.map((fraction) => ({
      code: fraction.code,
      name: fraction.name,
      value:
        volumeReport?.volume_report_items.find((item) => String(item.setup_fraction_code) === fraction.code)?.value ||
        0,
    }));
  })();

  const totalWeight = fractions.reduce((acc, fraction) => acc + fraction.value, 0);

  function handleDownloadVolumesXML() {
    if (!volumeReport) return;

    exportLucidXML(volumeReport);

    enqueueSnackbar(tXml("exportedXml"), { variant: "success" });
  }

  if (isLoadingVolumeReport || !volumeReport)
    return (
      <div className="space-y-5">
        <div className="p-4 space-y-4 bg-white rounded-md">
          <div className="pb-2 border-b border-tonal-dark-cream-80">
            <Skeleton className="h-6 w-72" />
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-20" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-20" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-40" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-24" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-36" />
            </div>
          </div>
          <div>
            <div className="flex items-center justify-between border-b-2 border-tonal-dark-cream-80 p-1 pb-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-20" />
            </div>
            {[1, 2, 3, 4, 5].map((_, index) => (
              <div key={index} className="flex items-center justify-between p-1 border-b border-tonal-dark-cream-80">
                <Skeleton className="h-4 w-40" />
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
          </div>
        </div>
        <div className="md:hidden">
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="hidden md:block">
          <Skeleton className="h-12 w-80 ml-auto" />
        </div>
      </div>
    );

  return (
    <div className="space-y-5">
      <div className={cn("p-4 text-primary space-y-4 bg-white rounded-md")}>
        <p className="font-bold pb-2 border-b border-tonal-dark-cream-80">
          {t("licenseYear")} {licenseYear}
        </p>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <p className="font-bold w-32 text-sm">{t("licenseYear")}</p>
            <p>{licenseYear}</p>
          </div>
          <div className="flex items-center gap-2">
            <p className="font-bold w-32 text-sm">{tVolumeReporting("orderNumber")}</p>
            <p>000000</p>
          </div>
          <div className="flex items-center gap-2">
            <p className="font-bold w-32 text-sm">{tVolumeReporting("timeAndDate")}</p>
            <p>{new Date(volumeReport.created_at).toLocaleString()}</p>
          </div>
          <div className="flex items-center gap-2">
            <p className="font-bold w-32 text-sm">{tVolumeReporting("amount")}</p>
            <p>{formatWeight(totalWeight)}</p>
          </div>
          <div className="flex items-center gap-2">
            <p className="font-bold w-32 text-sm">{tVolumeReporting("order")}</p>
            <p>License Contract {licenseYear}</p>
          </div>
        </div>
        <div>
          <div className="flex items-center justify-between border-b-2 border-tonal-dark-cream-80 p-1 pb-2">
            <p className="font-bold">{tVolumeReporting("typeOfMaterial")}</p>
            <p className="font-bold">{tVolumeReporting("volume")}</p>
          </div>
          {fractions.map((fraction, fractionIndex) => (
            <div
              key={fraction.code}
              data-last={fractionIndex === fractions.length - 1}
              className="flex items-center justify-between data-[last=false]:border-b data-[last=false]:border-tonal-dark-cream-80 p-1"
            >
              <p className="font-bold text-sm">{fraction.name}:</p>
              <p>{formatWeight(fraction.value)}</p>
            </div>
          ))}
        </div>
      </div>
      <Button
        onClick={handleDownloadVolumesXML}
        variant="filled"
        color="yellow"
        size="small"
        leadingIcon={<Download />}
        className="md:hidden w-full mt-6"
      >
        {tXml("downloadCurrent")} {licenseYear}
      </Button>
      <Button
        onClick={handleDownloadVolumesXML}
        variant="filled"
        color="yellow"
        size="medium"
        leadingIcon={<Download />}
        className="hidden md:flex ml-auto mt-8"
      >
        {tXml("download")} {licenseYear}
      </Button>
    </div>
  );
}
