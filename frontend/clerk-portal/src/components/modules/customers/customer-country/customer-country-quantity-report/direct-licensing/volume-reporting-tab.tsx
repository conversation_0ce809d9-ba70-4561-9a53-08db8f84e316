import { VolumeReport } from "@/lib/api/volume-report/types";
import { useCustomerLicense } from "../../use-customer-license";
import Divider from "@/components/_common/divider";
import { Download } from "@arthursenno/lizenzero-ui-react/Icon";
import { CgSpinnerAlt } from "react-icons/cg";
import { initializeVolumeReportEntries } from "./quantity-report-item";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Fragment } from "react";
import { formatWeight } from "@/utils/format-weight";
import { useTranslations } from "next-intl";

export const VolumeReportingTab = ({ volumeReport }: { volumeReport?: VolumeReport }) => {
  const t = useTranslations("volumeReporting");
  const c = useTranslations("common");
  const { license, isLoading: isLoadingLicense } = useCustomerLicense();

  if (!volumeReport || isLoadingLicense) {
    return (
      <div className="flex justify-center items-center w-full">
        <CgSpinnerAlt className="fill-primary text-primary animate-spin size-8" />
      </div>
    );
  }

  const entries = initializeVolumeReportEntries(volumeReport);

  return (
    <div className="flex w-full gap-x-10">
      <div className="bg-white shadow-md rounded-md p-6 w-full">
        <h1 className="text-lg font-semibold text-primary">{t("title")}</h1>
        <Divider initialMarginDisabled />
        <div className="grid grid-cols-1 gap-y-4 mt-4 text-sm">
          <div>
            <span className="font-medium text-primary">{t("licenseYear")}</span>
            <span className="ml-2 text-[#512552]">{license?.year}</span>
          </div>
          <div>
            <span className="font-medium text-primary">{t("orderNumber")}:</span>
            <span className="ml-2 text-[#512552]">{license?.contract_id}</span>
          </div>
          <div>
            <span className="font-medium text-primary">{t("timeAndDate")}:</span>
            <span className="ml-2 text-[#512552]">
              {license?.start_date ? new Date(license.start_date).toLocaleDateString().replaceAll("/", ".") : "N/A"}{" "}
              {license?.start_date ? new Date(license.start_date).toLocaleTimeString() : ""}
            </span>
          </div>
          <div>
            <span className="font-medium text-primary">{t("amount")}:</span>
            <span className="ml-2 text-[#512552]">[TODO]</span>
          </div>
          <div>
            <span className="font-medium text-primary">{t("order")}:</span>
            <span className="ml-2 text-[#512552]">{license?.contract?.title}</span>
          </div>
        </div>

        <div className="grid grid-cols-2 justify-between items-center mt-4">
          <p className="font-medium text-primary">{t("typeOfMaterial")}</p>
          <p className="font-medium text-primary ml-auto">{t("volume")}</p>
          {entries.map((entry) => (
            <Fragment key={entry.key}>
              <p className="text-primary mt-2">{entry.name}</p>
              <p className="text-primary ml-auto">{formatWeight(entry.value || 0)}</p>
            </Fragment>
          ))}
        </div>
      </div>
      <Button
        variant="filled"
        color="yellow"
        size="medium"
        className="h-10 max-w-52 mt-auto"
        onClick={() => {}}
        leadingIcon={<Download />}
      >
        {c("download")}
      </Button>
    </div>
  );
};
