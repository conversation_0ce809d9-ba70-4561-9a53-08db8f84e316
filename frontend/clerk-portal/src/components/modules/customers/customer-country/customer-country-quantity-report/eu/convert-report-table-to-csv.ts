import { VolumeReport } from "@/lib/api/volume-report/types";
import { formatWeight } from "@/utils/format-weight";

export const convertReportTableToCSV = (volumeReport: VolumeReport) => {
  const escapeComma = (text: string) => {
    return text.includes(",") ? `"${text}"` : text;
  };

  const volumeReportItems = volumeReport.volume_report_items;

  const mainHeaders = [""];
  const subHeaders = [""];

  volumeReport.report_table.columns.forEach((column) => {
    if (column.children && column.children.length > 0) {
      mainHeaders.push(escapeComma(column.name), ...Array(column.children.length - 1).fill(""));
      column.children.forEach((child) => {
        subHeaders.push(escapeComma(child.name));
      });
    } else {
      mainHeaders.push(escapeComma(column.name));
      subHeaders.push("");
    }
  });

  const csvRows = [mainHeaders.join(","), subHeaders.join(",")];

  volumeReport.report_table.fractions.forEach((firstLevelFraction) => {
    const firstLevelRow = [escapeComma(firstLevelFraction.name), ...Array(mainHeaders.length - 1).fill("")];
    csvRows.push(firstLevelRow.join(","));

    firstLevelFraction.children?.forEach((secondLevelFraction) => {
      const secondLevelRow = [escapeComma(secondLevelFraction.name), ...Array(mainHeaders.length - 1).fill("")];
      csvRows.push(secondLevelRow.join(","));

      secondLevelFraction.children?.forEach((thirdLevelFraction) => {
        const thirdLevelRow = [escapeComma(thirdLevelFraction.name)];
        volumeReport.report_table.columns.forEach((column) => {
          if (column.children && column.children.length > 0) {
            column.children.forEach((childColumn) => {
              const value = volumeReportItems.find(
                (item) => item.setup_fraction_id === thirdLevelFraction.id && item.setup_column_id === childColumn.id
              )?.value;
              thirdLevelRow.push(value ? formatWeight(value) : "-- kg");
            });
          } else {
            thirdLevelRow.push("");
          }
        });
        csvRows.push(thirdLevelRow.join(","));
      });
    });
  });

  return csvRows.join("\n");
};
