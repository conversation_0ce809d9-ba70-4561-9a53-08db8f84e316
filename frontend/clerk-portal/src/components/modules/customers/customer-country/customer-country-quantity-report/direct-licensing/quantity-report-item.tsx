import { getPackagingService } from "@/lib/api/packaging-services";
import { GERMANY_FRACTIONS } from "@/lib/api/volume-report/mocks";
import { ReportingEnum, VolumeReport } from "@/lib/api/volume-report/types";
import { cn } from "@/lib/utils";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { CgSpinnerAlt } from "react-icons/cg";
import { bulkUpdateVolumeReportItems, createVolumeReportItems } from "@/lib/api/volume-report-item";
import { Loader2Icon } from "lucide-react";
import { VolumeReportEntry } from "./volume-report-pdf";
import { VolumeReportingTab } from "./volume-reporting-tab";
import { DirectLicenseReportingTable } from "./direct-license-reporting-table";
import { FullLicense } from "@/lib/api/license/types";
import { FractionInput } from "@/components/ui/fraction-input";
import { formatWeight } from "@/utils/format-weight";
import { useTranslations } from "next-intl";

type Props = {
  packagingServiceId: number;
  year: string;
  setYear: (year: string) => void;
  setVolumeReportPDF: (entries: VolumeReportEntry[]) => void;
  licenses: FullLicense[];
};

const initialReportingTypes = [
  ReportingEnum.INTRA_YEAR,
  ReportingEnum.VOLUME_REPORTING,
  ReportingEnum.END_OF_YEAR,
  ReportingEnum.INITIAL_PLANNED,
  ReportingEnum.BALANCE,
];

export const initializeVolumeReportEntries = (volumeReport: VolumeReport): VolumeReportEntry[] => {
  return GERMANY_FRACTIONS.map((fraction) => {
    const volumeReportItem = volumeReport.volume_report_items.find((item) => item.setup_fraction_id === fraction.id);

    return {
      key: fraction.id,
      name: fraction.name,
      value: volumeReportItem?.value || 0,
      price: 0,
      volumeReportItemId: volumeReportItem?.id,
    };
  });
};

// Define the form data type outside of the hook for proper typing
type DirectLicenseDeclareVolumesFormData = {
  fractions: {
    key: number;
    name: string;
    value?: number;
    price?: number;
    volumeReportItemId?: number;
  }[];
};

function useDirectLicenseSchema() {
  const t = useTranslations("QuantityReportItem");

  const directLicenseDeclareVolumesFormSchema = z.object({
    fractions: z.array(
      z.object({
        key: z.number(),
        name: z.string(),
        value: z
          .number()
          .gte(0, { message: t("minimumWeight") })
          .optional(),
        price: z
          .number()
          .gte(0, { message: t("minimumPrice") })
          .optional(),
        volumeReportItemId: z.number().optional(),
      })
    ),
  });

  return {
    directLicenseDeclareVolumesFormSchema,
  };
}

export function QuantityReportItem({ packagingServiceId, year, setVolumeReportPDF, licenses }: Props) {
  const t = useTranslations("QuantityReportItem");
  const c = useTranslations("common");

  const [selectedTab, setSelectedTab] = useState<ReportingEnum>(initialReportingTypes[0]);

  const { data } = useQuery({
    queryKey: ["packaging-service", packagingServiceId],
    queryFn: async () => {
      const packagingServiceDetails = await getPackagingService(packagingServiceId);

      const volumeReport =
        packagingServiceDetails.volume_reports.findLast((vol) => vol.year === Number(year)) ||
        packagingServiceDetails.volume_reports[0];

      if (!volumeReport) return;

      const entries = initializeVolumeReportEntries(volumeReport);
      setVolumeReportPDF(entries);

      return { packagingServiceDetails, volumeReport };
    },
  });

  const volumeReport = data?.volumeReport;

  return (
    <div className="flex flex-col gap-5">
      <div className="flex items-center gap-6">
        {initialReportingTypes.map((typeData) => (
          <Button
            color="dark-blue"
            variant="filled"
            size="small"
            onClick={() => setSelectedTab(typeData)}
            key={typeData}
            className={cn(
              "rounded-2xl hover:text-white",
              `${typeData !== selectedTab && "bg-tonal-dark-blue-96 text-primary"}`
            )}
          >
            {typeData}
          </Button>
        ))}
      </div>
      {selectedTab === ReportingEnum.VOLUME_REPORTING ? (
        <DirectLicenseReportingTable licenses={licenses || []} licenseYear={year} />
      ) : selectedTab === ReportingEnum.BALANCE ? (
        <div className="w-full flex flex-col my-5 gap-5">
          <div className="flex flex-col gap-2">
            {/* TODO: Get exceeded amount payed */}
            <p className="text-large-paragraph-bold font-bold text-primary flex items-center gap-3">
              {t("exceededAmountPayed")}: <span className="font-normal">€ 0.00</span>
            </p>
            <p className="text-small-paragraph-regular text-tonal-dark-cream-40">
              {t("purchaseMadeIn")}:{" "}
              {volumeReport?.created_at
                ? new Date(volumeReport?.created_at).toLocaleDateString().replaceAll("/", ".")
                : ""}
            </p>
          </div>
          <div className="w-full flex flex-col">
            {/* TODO: Get refunded amount */}
            <p className="text-large-paragraph-bold font-bold text-primary flex items-center gap-3">
              {t("amountRefunded")}: <span className="font-normal">€ 0.00</span>
            </p>
            <p className="text-small-paragraph-regular text-tonal-dark-cream-40">
              {t("purchaseMadeIn")}:{" "}
              {volumeReport?.created_at
                ? new Date(volumeReport?.created_at).toLocaleDateString().replaceAll("/", ".")
                : ""}
            </p>
          </div>
        </div>
      ) : (
        <DirectLicensingReportTable
          volumeReport={volumeReport}
          showExceededAmountPayed={selectedTab === ReportingEnum.END_OF_YEAR}
        />
      )}
    </div>
  );
}

type DirectLicensingReportTableProps = {
  volumeReport?: VolumeReport;
  showExceededAmountPayed?: boolean;
};

const DirectLicensingReportTable = ({ volumeReport, showExceededAmountPayed }: DirectLicensingReportTableProps) => {
  const t = useTranslations("QuantityReportItem");
  const c = useTranslations("common");
  const { directLicenseDeclareVolumesFormSchema } = useDirectLicenseSchema();

  const {
    control,
    reset,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<DirectLicenseDeclareVolumesFormData>({
    resolver: zodResolver(directLicenseDeclareVolumesFormSchema),
  });
  const fractions = useWatch({ control, name: "fractions" }) || [];
  const [isEditing, setIsEditing] = useState(false);
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!volumeReport) return;
    if (!!fractions.length) return;
    const entries = initializeVolumeReportEntries(volumeReport);
    reset({ fractions: entries });
  }, [volumeReport?.id, fractions.length, volumeReport, reset]);

  const { mutate, isPending } = useMutation({
    mutationFn: async () => {
      if (!volumeReport) return;

      const itemsToCreate = fractions.filter((fraction) => !fraction.volumeReportItemId);
      const itemsToUpdate = fractions.filter((fraction) => !!fraction.volumeReportItemId);

      if (!itemsToCreate.length && !itemsToUpdate.length) return;

      if (itemsToCreate.length) {
        await createVolumeReportItems(
          itemsToCreate.map(
            (item) =>
              ({
                license_volume_report_id: volumeReport.id,
                setup_column_id: 1,
                setup_fraction_id: Number(item.key),
                value: item.value as number,
              }) as any
          )
        );
      }

      if (itemsToUpdate.length) {
        await bulkUpdateVolumeReportItems(
          itemsToUpdate.map((item) => ({
            license_volume_report_id: volumeReport.id,
            value: item.value as number,
            volume_report_item_id: item.volumeReportItemId as number,
          }))
        );
      }
    },
    onSuccess: () => {
      setIsEditing(false);
      queryClient.invalidateQueries({
        queryKey: ["packaging-service"],
      });
    },
    onError: (error) => {
      console.error(error);
      setError("fractions", { message: t("errorOcurred") });
    },
  });

  if (!fractions.length || !volumeReport) {
    return (
      <div className="flex justify-center items-center w-full">
        <CgSpinnerAlt className="fill-primary text-primary animate-spin size-8" />
      </div>
    );
  }

  return (
    <div className="w-full flex gap-5">
      <form className="w-full max-w-xl" onSubmit={handleSubmit(() => mutate())}>
        {!!fractions.length && (
          <div className="flex flex-col w-full rounded-2xl">
            <div className="grid grid-cols-3 items-center w-full bg-surface-03 rounded-t-2xl">
              <div className="p-4"></div>
              <div className="text-primary font-sm font-normal p-4 text-left">{t("currentVolume")}</div>
              <div className="text-primary font-sm font-normal p-4 text-left flex items-center gap-2">
                {t("newVolume")}
                {!isEditing && (
                  <Button
                    type="button"
                    variant="text"
                    color="light-blue"
                    size="small"
                    onClick={() => setIsEditing(!isEditing)}
                  >
                    {c("edit")}
                  </Button>
                )}
              </div>
            </div>
            <div className="flex flex-col">
              {fractions.map((fraction, fractionIndex) => (
                <div
                  key={`fraction-${fractionIndex}`}
                  className={cn(
                    "grid grid-cols-3 items-center border-y border-y-white",
                    fractionIndex === 0 && "border-t-2",
                    fractionIndex === fractions.length - 1 && "border-b-0"
                  )}
                >
                  <div
                    className={cn(
                      "bg-surface-01 h-full flex items-center",
                      fractionIndex === fractions.length - 1 && "rounded-bl-2xl"
                    )}
                  >
                    <p className="text-sm md:text-base text-primary m-4 truncate max-w-[200px]" title={fraction.name}>
                      {fraction.name}
                    </p>
                  </div>
                  <div className="p-4 bg-surface-02 flex items-center justify-start">
                    <div className="flex items-center gap-2">
                      <span className="text-paragraph-bold text-primary font-bold">
                        {formatWeight(
                          volumeReport.volume_report_items.find(
                            (item) => item.setup_fraction_id === Number(fraction.key)
                          )?.value || 0
                        )}
                      </span>
                      <span className="text-paragraph-bold text-primary font-bold">kg</span>
                    </div>
                  </div>
                  <div className="p-4 bg-white h-full flex items-center justify-start">
                    {isEditing ? (
                      <div className="flex items-center gap-2 w-full">
                        <Controller
                          key={fraction.key}
                          name={`fractions.${fractionIndex}.value`}
                          control={control}
                          render={({ field }) => (
                            <FractionInput
                              type="weight"
                              {...field}
                              disabled={isPending}
                              value={field.value}
                              data-invalid={!!errors && !!errors.fractions && !!errors.fractions[fractionIndex]}
                              onChange={(value) => field.onChange(value)}
                              className="p-3 flex-1"
                            />
                          )}
                        />
                        <span className="font-paragraph-regular text-primary">kg</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <span className="font-paragraph-regular text-primary">{formatWeight(fraction.value || 0)}</span>
                        {fraction.value && <span className="font-paragraph-regular text-primary">kg</span>}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {isEditing && (
          <div className="grid grid-cols-2 gap-2 shadow-elevation-04-1 rounded-2xl p-2 bg-white mt-2 max-w-52 h-14 justify-self-end">
            <Button
              color="yellow"
              size="iconSmall"
              variant="filled"
              leadingIcon={isPending ? <Loader2Icon className="animate-spin" /> : <CheckCircle />}
              onClick={() => {}}
              className="gap-2 h-10"
              disabled={isPending}
            >
              <span className="mt-1">{c("save")}</span>
            </Button>
            <Button
              variant="text"
              size="small"
              color="gray"
              leadingIcon={undefined}
              trailingIcon={undefined}
              onClick={() => {
                if (!volumeReport) return;
                const entries = initializeVolumeReportEntries(volumeReport);
                reset({ fractions: entries });
                setIsEditing(false);
              }}
              className="mr-1 hover:underline"
            >
              Cancel
            </Button>
          </div>
        )}
      </form>
      {showExceededAmountPayed && (
        <div className="flex flex-col">
          {/* TODO: Get exceeded amount payed */}
          <p className="text-large-paragraph-bold font-bold text-primary mt-5 flex items-center gap-3">
            {t("exceededAmountPayed")}: <span className="font-normal">€ 0.00</span>
          </p>
          <p className="text-small-paragraph-regular text-tonal-dark-cream-40 mt-2">
            {t("purchaseMadeIn")}: {new Date(volumeReport.created_at).toLocaleDateString().replaceAll("/", ".")}
          </p>
        </div>
      )}
    </div>
  );
};
