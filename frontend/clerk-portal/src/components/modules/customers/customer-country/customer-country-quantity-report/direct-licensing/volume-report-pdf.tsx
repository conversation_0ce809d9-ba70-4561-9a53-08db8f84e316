import { formatWeight } from "@/utils/format-weight";
import { Document, Font, Page, StyleSheet, View, Text } from "@react-pdf/renderer";
import { useTranslations } from "next-intl";

export type VolumeReportEntry = {
  key: number;
  name: string;
  value: number;
  price: number;
  volumeReportItemId?: number;
};

export const VolumeReportPDF = ({ entries }: { entries: VolumeReportEntry[] }) => {
  const t = useTranslations("volumeReporting");
  const c = useTranslations("common");
  Font.register({
    family: "CentraNo2",
    format: "truetype",
    fonts: [
      { src: "/fonts/CentraNo2/Thin.ttf", fontWeight: 100 },
      { src: "/fonts/CentraNo2/Light.ttf", fontWeight: 300 },
      { src: "/fonts/CentraNo2/Book.ttf", fontWeight: 400 },
      { src: "/fonts/CentraNo2/Medium.ttf", fontWeight: 500 },
      { src: "/fonts/CentraNo2/Extrabold.ttf", fontWeight: 700 },
      { src: "/fonts/CentraNo2/Black.ttf", fontWeight: 800 },
    ],
  });

  const styles = StyleSheet.create({
    page: {
      fontFamily: "CentraNo2",
      padding: 20,
    },
    table: {
      width: "100%",
      borderRadius: 8,
      marginTop: 20,
    },
    headerRow: {
      flexDirection: "row",
      backgroundColor: "#F7F5F2",
      borderTopLeftRadius: 8,
      borderTopRightRadius: 8,
    },
    row: {
      flexDirection: "row",
      borderBottomWidth: 1,
      borderBottomColor: "#F7F5F2",
      backgroundColor: "#F7F5F2",
    },
    fractionCell: {
      flex: 1,
      padding: 16,
      backgroundColor: "#F7F5F2",
    },
    currentVolumeCell: {
      flex: 1,
      padding: 16,
      backgroundColor: "#F7F5F2",
    },
    newVolumeCell: {
      flex: 1,
      padding: 16,
      backgroundColor: "#F7F5F2",
    },
    headerText: {
      fontSize: 12,
      color: "#183362",
      fontWeight: "normal",
    },
    cellText: {
      fontSize: 12,
      color: "#183362",
    },
    fractionName: {
      fontSize: 12,
      color: "#183362",
      maxWidth: 200,
    },
  });

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.table}>
          <View style={styles.headerRow}>
            <View style={styles.fractionCell}>
              <Text style={styles.headerText}></Text>
            </View>
            <View style={styles.currentVolumeCell}>
              <Text style={styles.headerText}>{t("currentVolume")}</Text>
            </View>
            <View style={styles.newVolumeCell}>
              <Text style={styles.headerText}>{t("newVolume")}</Text>
            </View>
          </View>
          {entries.map((entry) => (
            <View key={entry.key} style={styles.row}>
              <View style={styles.fractionCell}>
                <Text style={styles.fractionName}>{entry.name}</Text>
              </View>
              <View style={styles.currentVolumeCell}>
                <Text style={styles.cellText}>{formatWeight(entry.value || 0)}</Text>
              </View>
              <View style={styles.newVolumeCell}>
                <Text style={styles.cellText}>{formatWeight(entry.value || 0)}</Text>
              </View>
            </View>
          ))}
        </View>
      </Page>
    </Document>
  );
};
