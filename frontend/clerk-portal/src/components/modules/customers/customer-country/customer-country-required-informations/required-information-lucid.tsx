import { StatusBadge, StatusBadgeVariant } from "@/components/ui/status-badge";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { FileCopy, Help } from "@arthursenno/lizenzero-ui-react/Icon";
import { enqueueSnackbar } from "notistack";
import { RequiredInformationIcon } from "./required-information-icon";
import { useTranslations } from "next-intl";

interface RequiredInformationItemProps {
  lucid?: string;
}

export function RequiredInformationLucid({ lucid }: RequiredInformationItemProps) {
  const t = useTranslations("RequiredInformationLucid");
  async function handleCopyAnswer(answer: string) {
    await navigator.clipboard.writeText(answer || "");
    enqueueSnackbar(t("answerCopied"), { variant: "success" });
  }

  const requiredInformationInfo = {
    label: lucid ? t("submitted") : t("waiting"),
    variant: lucid ? t("success") : t("warning"),
  };

  return (
    <div className="flex flex-col gap-4 md:gap-2 my-5">
      <div className="flex flex-col md:flex-row items-center justify-between w-full gap-2">
        <div className="flex items-center gap-2 w-full md:w-auto">
          <Help className="size-6 fill-[#808FA9]" />
          <RequiredInformationIcon requiredInformationType={"NUMBER"} />
          <p className="text-paragraph-regular font-medium text-tonal-dark-cream-20">{t("lucidNumber")}</p>
        </div>
        <StatusBadge
          variant={requiredInformationInfo?.variant as StatusBadgeVariant}
          label={requiredInformationInfo?.label}
        />
      </div>
      {!!lucid && (
        <div className="flex items-center gap-2 md:pr-5">
          <div className="hidden md:block w-6"></div>
          <div className="w-full max-w-full md:max-w-80 flex items-center border-[1px] border-tonal-dark-cream-80 rounded-full p-2 gap-4">
            <p className="text-primary text-sm text-left pl-2 text-nowrap whitespace-nowrap flex-1 overflow-hidden">
              {lucid}
            </p>
            <Button
              color="yellow"
              size="iconXSmall"
              variant="filled"
              onClick={() => handleCopyAnswer(lucid!)}
              trailingIcon={<FileCopy className="fill-primary" />}
            />
          </div>
        </div>
      )}
    </div>
  );
}
