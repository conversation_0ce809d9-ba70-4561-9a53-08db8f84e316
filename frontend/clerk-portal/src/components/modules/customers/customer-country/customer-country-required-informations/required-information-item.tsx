import { StatusBadge, StatusBadgeProps } from "@/components/ui/status-badge";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { deleteCustomerFile, downloadCustomerFile } from "@/lib/api/file";
import { updateRequiredInformation, updateRequiredInformationStatus } from "@/lib/api/required-information";
import { RequiredInformation } from "@/lib/api/required-information/types";
import { queryClient } from "@/lib/react-query";
import { downloadFile } from "@/utils/download-file";
import { UserTypes } from "@/utils/user";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Delete, Download, File, FileCopy, Help } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import { CgSpinnerAlt } from "react-icons/cg";
import { RequiredInformationIcon } from "./required-information-icon";
import { DeclineRequiredInformationModal } from "./required-information-decline-modal";
import { useTranslations } from "next-intl";

interface RequiredInformationItemProps {
  information: RequiredInformation;
}

export function getRequiredInformationLabel(information: RequiredInformation): StatusBadgeProps {
  if (information.status === "OPEN") {
    if (information.type === "DOCUMENT") {
      return {
        label: "Waiting Signature",
        variant: "warning",
      };
    }

    if (information.type === "IMAGE") {
      return {
        label: "Waiting image",
        variant: "warning",
      };
    }

    if (information.type === "NUMBER" || information.type === "TEXT") {
      return {
        label: "Awaiting answer",
        variant: "warning",
      };
    }

    if (information.type === "FILE") {
      return {
        label: "Waiting file",
        variant: "warning",
      };
    }
  }

  if (information.status === "APPROVED") {
    return {
      label: "Accepted",
      variant: "success",
    };
  }

  if (information.status === "DECLINED") {
    return {
      label: "Declined",
      variant: "error",
    };
  }

  return {
    label: "default",
    variant: "neutral",
  };
}

export function RequiredInformationItem({ information }: RequiredInformationItemProps) {
  const t = useTranslations("RequiredInformationItem");
  const authSession = useSession();
  const userId = authSession?.data?.user.id;

  const { changeParam } = useQueryFilter(["document-id"]);

  const handleOpenDeclineModal = () => {
    changeParam("document-id", information.id.toString());
  };

  const approveInformationMutation = useMutation({
    mutationFn: async (id: number) => {
      await updateRequiredInformationStatus(id, "APPROVED");
    },
  });

  function handleApproveRequiredInformation(id: number) {
    approveInformationMutation.mutate(id, {
      onSuccess: () => {
        if (information.license_id) {
          queryClient.invalidateQueries({
            queryKey: ["required-informations", information.license_id],
          });
        }

        if (information.contract_id) {
          queryClient.invalidateQueries({
            queryKey: ["general-informations", information.contract_id],
          });
        }

        enqueueSnackbar(t("documentApproved"), { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar(t("documentApprovedFailed"), { variant: "error" });
      },
    });
  }

  const downloadFileMutation = useMutation({
    mutationFn: async (fileId: string) => {
      if (!userId) return;
      return downloadCustomerFile({
        file_id: fileId,
        user_id: Number(userId),
        user_role: UserTypes.CLERK,
      });
    },
  });

  const deleteFileMutation = useMutation({
    mutationFn: async (fileId: string) => {
      await deleteCustomerFile(fileId);

      if (!information.answer_files?.filter((file) => file.id !== fileId).length) {
        await updateRequiredInformationStatus(information.id, "OPEN");
      }
    },
  });

  const deleteAnswerMutation = useMutation({
    mutationFn: async () => {
      await updateRequiredInformation(information.id, { answer: null, status: "OPEN" });
    },
  });

  async function handleDownloadFile(fileId: string) {
    downloadFileMutation.mutate(fileId, {
      onSuccess: (file) => {
        downloadFile({ buffer: file, fileName: `file.${file.type.split("/")[1]}` });

        enqueueSnackbar(t("fileDownloaded"), { variant: "default" });
      },
      onError: () => {
        enqueueSnackbar(t("fileDownloadFailed"), { variant: "default" });
      },
    });
  }

  async function handleDeleteFile(fileId: string) {
    if (!userId) return;

    deleteFileMutation.mutate(fileId, {
      onSuccess: () => {
        if (information.license_id) {
          queryClient.invalidateQueries({
            queryKey: ["required-informations", information.license_id],
          });
        }

        if (information.contract_id) {
          queryClient.invalidateQueries({
            queryKey: ["general-informations", information.contract_id],
          });
        }

        enqueueSnackbar(t("deleteFile"), { variant: "success" });
      },
      onError: (error) => {
        enqueueSnackbar(t("deleteFileFailed"), { variant: "error" });
      },
    });
  }

  async function handleDeleteAnswer() {
    deleteAnswerMutation.mutate(undefined, {
      onSuccess: () => {
        if (information.license_id) {
          queryClient.invalidateQueries({
            queryKey: ["required-informations", information.license_id],
          });
        }

        if (information.contract_id) {
          queryClient.invalidateQueries({
            queryKey: ["general-informations", information.contract_id],
          });
        }

        enqueueSnackbar(t("deleteAnswer"), { variant: "success" });
      },
      onError: (error) => {
        enqueueSnackbar(t("deleteAnswerFailed"), { variant: "error" });
      },
    });
  }

  async function handleCopyAnswer(answer: string) {
    await navigator.clipboard.writeText(answer || "");
    enqueueSnackbar(t("copyAnswer"), { variant: "success" });
  }

  const requiredInformationInfo = getRequiredInformationLabel(information);

  const isDone = information.status === "DONE";

  return (
    <div className="flex flex-col gap-4 md:gap-2 my-5">
      <div className="flex flex-col md:flex-row items-center justify-between w-full gap-2">
        <div className="flex items-center gap-2 w-full md:w-auto">
          <Help className="size-6 fill-[#808FA9]" />
          <RequiredInformationIcon requiredInformationType={information.type} />
          <p className="text-paragraph-regular font-medium text-tonal-dark-cream-20">{information.name}</p>
        </div>
        {isDone && (
          <div className="flex items-center gap-2 w-full md:w-auto">
            <Button
              color="red"
              variant="outlined"
              size="small"
              className="w-full md:w-auto"
              onClick={(e) => {
                e.stopPropagation();
                handleOpenDeclineModal();
              }}
              disabled={approveInformationMutation.isPending}
            >
              Decline
            </Button>
            <Button
              color="dark-blue"
              variant="filled"
              size="small"
              className="w-full md:w-auto"
              onClick={() => handleApproveRequiredInformation(information.id)}
              disabled={approveInformationMutation.isPending}
            >
              Approve
            </Button>
          </div>
        )}
        {!isDone && <StatusBadge variant={requiredInformationInfo?.variant} label={requiredInformationInfo?.label} />}
      </div>
      {!!information.answer_files.length && (
        <div className="md:mt-4 w-full md:w-auto">
          {information.answer_files.map((file) => (
            <div key={file.id} className="w-full grid grid-cols-3 place-content-end md:pr-5">
              <div key={file.id} className="flex items-center gap-2 md:pl-9 justify-self-start">
                <File className="size-5 md:size-7 fill-primary" />
                <p className="text-small-paragraph-regular text-primary underline w-16 md:w-64 overflow-hidden text-ellipsis">
                  {file.original_name}
                </p>
              </div>
              <div className="flex items-center justify-end gap-6">
                <p className="text-small-paragraph-regular text-tonal-dark-blue-10 justify-self-center">
                  {new Date(information.created_at).toLocaleDateString().replaceAll("/", ".")}
                </p>
              </div>
              <div className="flex items-center justify-end gap-2 md:gap-6">
                <Button
                  color="dark-blue"
                  size="iconSmall"
                  variant="text"
                  onClick={() => handleDeleteFile(file.id)}
                  leadingIcon={
                    deleteFileMutation.isPending ? (
                      <CgSpinnerAlt className="animate-spin" />
                    ) : (
                      <Delete className="fill-primary" />
                    )
                  }
                  disabled={deleteFileMutation.isPending}
                />
                <Button
                  color="dark-blue"
                  size="iconSmall"
                  variant="text"
                  onClick={() => handleDownloadFile(file.id)}
                  leadingIcon={
                    downloadFileMutation.isPending ? (
                      <CgSpinnerAlt className="animate-spin" />
                    ) : (
                      <Download className="fill-support-blue" />
                    )
                  }
                  disabled={downloadFileMutation.isPending}
                />
              </div>
            </div>
          ))}
        </div>
      )}
      {!!information.answer && (
        <div className="flex items-center gap-2 md:pr-5">
          <div className="hidden md:block w-6"></div>
          <div className="w-full max-w-full md:max-w-80 flex items-center border-[1px] border-tonal-dark-cream-80 rounded-full p-2 gap-4">
            <p className="text-primary text-sm text-left pl-2 text-nowrap whitespace-nowrap flex-1 overflow-hidden">
              {information.answer}
            </p>
            <Button
              color="yellow"
              size="iconXSmall"
              variant="filled"
              onClick={() => handleCopyAnswer(information.answer!)}
              trailingIcon={<FileCopy className="fill-primary" />}
            />
          </div>
          <div className="flex-1 flex justify-end">
            <Button
              color="dark-blue"
              size="iconSmall"
              variant="text"
              onClick={() => handleDeleteAnswer()}
              leadingIcon={
                deleteAnswerMutation.isPending ? (
                  <CgSpinnerAlt className="animate-spin" />
                ) : (
                  <Delete className="fill-primary" />
                )
              }
              disabled={deleteAnswerMutation.isPending}
            />
          </div>
        </div>
      )}
      {/* {
        !!information?.answer_files?.length && (
          <div className="pl-9 pr-4">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1">
                <AccordionTrigger>
                  <p className="text-tonal-dark-cream-10">Upload history</p>
                </AccordionTrigger>
                <AccordionContent>
                  {information.answer_files.map((answerFile) => (
                    <div className="flex items-center justify-between" key={answerFile.id}>
                      <div className="flex items-center gap-2 mt-3">
                        <File className="size-6 fill-tonal-dark-cream-30" />
                        <p className="text-small-paragraph-regular text-tonal-dark-cream-20 underline">
                          {answerFile.name}.{answerFile.extension}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <p className="text-small-paragraph-regular text-tonal-dark-blue-10 mt-2">
                          {new Date(answerFile.created_at).toLocaleDateString().replaceAll("/", ".")}
                        </p>
                        <Download className="size-6 fill-support-blue" />
                      </div>
                    </div>
                  ))}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        )
      } */}
      <DeclineRequiredInformationModal information={information} />
    </div>
  );
}
