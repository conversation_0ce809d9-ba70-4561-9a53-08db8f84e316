import { useParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { getLicense } from "@/lib/api/license";
import { useCustomer } from "../customer-profile/use-customer";

export function useCustomerLicense() {
  const { customer } = useCustomer();
  const params = useParams();

  const licenseId = (() => {
    const contracts = customer?.contracts.filter((contract) => contract.type !== "ACTION_GUIDE");

    if (!contracts) return null;

    for (const contract of contracts) {
      const license = contract.licenses.find((license) => license.country_code === params.countryCode);

      if (!license) continue;

      return license.id;
    }

    return null;
  })();

  const {
    data: license,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["licenses", licenseId],
    queryFn: () => getLicense(licenseId!),
    enabled: !!licenseId,
  });

  return {
    licenseId,
    license: license || null,
    isLoading,
    refetch,
  };
}
