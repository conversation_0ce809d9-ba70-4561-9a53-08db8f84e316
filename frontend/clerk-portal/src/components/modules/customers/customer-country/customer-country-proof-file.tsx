"use client";

import { deleteCustomerFile, downloadCustomerFile, uploadFile } from "@/lib/api/file";
import { UploadedFile } from "@/lib/api/file/types";
import { queryClient } from "@/lib/react-query";
import { downloadFile } from "@/utils/download-file";
import { UserTypes } from "@/utils/user";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Delete, Download, File, Upload } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import { CgSpinnerAlt } from "react-icons/cg";
import { useCustomerLicense } from "./use-customer-license";
import { useTranslations } from "next-intl";
interface CustomFile extends File {
  id: string;
}

export function CustomerCountryProofFile() {
  const t = useTranslations("CustomerCountry");
  const session = useSession();

  const { license } = useCustomerLicense();

  const downloadFileMutation = useMutation({
    mutationFn: async (file: UploadedFile) => {
      const downloadedFile = await downloadCustomerFile({
        user_id: 0,
        user_role: UserTypes.CLERK,
        file_id: file.id,
      });

      downloadFile({ buffer: downloadedFile, fileName: file.original_name });
    },
  });

  const deleteFileMutation = useMutation({
    mutationFn: (file: UploadedFile) => deleteCustomerFile(file.id),
  });

  const uploadFileMutation = useMutation({
    mutationFn: (file: File) =>
      uploadFile({
        file,
        type: "LICENSE_PROOF_OF_REGISTRATION",
        user_id: String(session.data?.user?.id),
        license_id: license?.id,
      }),
  });

  async function handleDownloadFile(file: UploadedFile) {
    downloadFileMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar(t("fileDownloaded"), { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar(t("errorDownloadingFile"), { variant: "error" });
      },
    });
  }

  async function handleDeleteFile(file: UploadedFile) {
    deleteFileMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar(t("fileDeletedSuccessfully"), { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["licenses", license?.id] });
      },
      onError: () => {
        enqueueSnackbar(t("errorDeletingFile"), { variant: "error" });
      },
    });
  }

  async function handleUploadProofOfRegistrationFile(file: File) {
    if (!session.data?.user) return;
    if (!license) return;

    uploadFileMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar(t("fileUploadedSuccessfully"), { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["licenses", license?.id] });
      },
      onError: () => {
        enqueueSnackbar(t("errorUploadingFile"), { variant: "error" });
      },
    });
  }

  const licenseFile = license?.files.find((file) => file.type === "LICENSE_PROOF_OF_REGISTRATION");

  return (
    <div className="w-full flex flex-col gap-2">
      <p className="text-tonal-dark-cream-30 text-small-paragraph-regular">{t("proofOfRegistration")}</p>
      {!licenseFile ? (
        <div>
          <input
            type="file"
            id="upload-document"
            className="hidden"
            onChange={(e) => e.target.files?.[0] && handleUploadProofOfRegistrationFile(e.target.files?.[0])}
            disabled={uploadFileMutation.isPending}
          />
          <Button
            variant="text"
            color="light-blue"
            size="small"
            leadingIcon={uploadFileMutation.isPending ? <CgSpinnerAlt className="animate-spin" /> : <Upload />}
            onClick={() => document.getElementById("upload-document")?.click()}
            disabled={uploadFileMutation.isPending}
          >
            {t("uploadDocument")}
          </Button>
        </div>
      ) : (
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <File className="size-6 fill-primary" />
            <p className="text-primary text-paragraph-regular">{licenseFile.original_name}</p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="text"
              color="light-blue"
              size="iconSmall"
              leadingIcon={
                downloadFileMutation.isPending && downloadFileMutation.variables?.id === licenseFile.id ? (
                  <CgSpinnerAlt className="size-4 animate-spin" />
                ) : (
                  <Download />
                )
              }
              onClick={() => handleDownloadFile(licenseFile)}
            />
            <Button
              variant="text"
              color="dark-blue"
              size="iconSmall"
              leadingIcon={
                deleteFileMutation.isPending && deleteFileMutation.variables?.id === licenseFile.id ? (
                  <CgSpinnerAlt className="size-4 animate-spin" />
                ) : (
                  <Delete className="fill-primary" />
                )
              }
              onClick={() => handleDeleteFile(licenseFile)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
