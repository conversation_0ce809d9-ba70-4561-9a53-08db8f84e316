"use client";

import UploadInput from "@/components/_common/fileUploader";
import { CustomRadio } from "@/components/_common/forms/customRadio/custom-radio";
import { FractionInput } from "@/components/ui/fraction-input";
import { uploadFile } from "@/lib/api/file";
import { FileType } from "@/lib/api/file/types";
import { createThirdPartyInvoice } from "@/lib/api/third-party-invoice";
import { ThirdPartyInvoiceStatus } from "@/lib/api/third-party-invoice/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useCustomerLicense } from "../use-customer-license";
import { useTranslations } from "next-intl";

export interface AddThirdPartyInvoiceModalProps {
  isOpen: boolean;
  setOpen: (open: boolean) => void;
  refetch: () => void;
}

export function AddThirdPartyInvoiceModal({ isOpen, setOpen, refetch }: AddThirdPartyInvoiceModalProps) {
  const t = useTranslations("AddThirdPartyInvoiceModal");
  const c = useTranslations("common");
  const session = useSession();
  const { license } = useCustomerLicense();
  const [File, setFile] = useState<File | undefined>();
  const [FileError, setFileError] = useState<boolean | undefined>();
  const [invoiceIssuer, setInvoiceIssuer] = useState<"THIRD_PARTY_DUAL_SYSTEM" | "OTHER_THIRD_PARTY">(
    "THIRD_PARTY_DUAL_SYSTEM"
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
    reset,
  } = useForm();

  const handleOpen = () => {
    setOpen(!isOpen);
  };

  const handleAddInvoice = async (data: any) => {
    if (!session.data?.user) return;
    if (!license) return;
    if (!File) {
      setFileError(true);
      return;
    }

    setIsSubmitting(true);

    const uploadData = {
      file: File,
      type: "THIRD_PARTY_INVOICE" as FileType,
      user_id: String(session.data.user.id),
      license_id: license.id,
    };

    const uploadedFile = await uploadFile(uploadData);

    if (uploadedFile) {
      const invoiceData = {
        title: File.name,
        price: data.price,
        issued_at: new Date().toISOString(),
        due_date: new Date(data.due_date).toISOString(),
        status: "OPEN" as ThirdPartyInvoiceStatus,
        issuer: invoiceIssuer,
        license_id: license.id,
        file_id: Number(uploadedFile.id),
      };

      const createdInvoice = await createThirdPartyInvoice(invoiceData);

      if (createdInvoice) {
        enqueueSnackbar(t("invoiceCreated"), { variant: "success" });
        refetch();
        reset();
        setFile(undefined);
        setIsSubmitting(false);
        setOpen(!isOpen);
      } else {
        enqueueSnackbar(t("failedToCreateInvoice"), { variant: "error" });
      }
    } else {
      enqueueSnackbar(t("failedToUpload"), { variant: "error" });
    }
  };

  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={handleOpen}
    >
      <div className="p-5 max-h-[75vh] overflow-auto ">
        <div className="flex flex-row w-full justify-between">
          <div className="flex flex-col  gap-2 justify-start">
            <p className="text-[28px] text-primary font-bold">{t("addInvoice")}</p>
          </div>
          <div className="flex justify-end">
            <button onClick={handleOpen}>
              <Clear className="size-6 fill-primary" />
            </button>
          </div>
        </div>

        <div className="mt-2">
          <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("uploadInvoiceDocument")}</p>
        </div>

        <div className="mt-4">
          <UploadInput acceptedFileTypes={["pdf", "doc", "jpg", "png"]} file={File} setFile={setFile} />
          {FileError && <p className="text-paragraph-regular text-error">{t("pleaseUploadDocument")}</p>}
        </div>

        <div className="flex flex-col mt-6">
          <p className="text-paragraph-regular text-primary mb-4">{t("invoiceIssuer")}</p>

          <div className="space-y-3">
            <CustomRadio
              label={t("thirdPartyDualSystem")}
              checked={invoiceIssuer === "THIRD_PARTY_DUAL_SYSTEM"}
              onChange={() => setInvoiceIssuer("THIRD_PARTY_DUAL_SYSTEM")}
            />
            <CustomRadio
              label={t("otherThirdParty")}
              checked={invoiceIssuer === "OTHER_THIRD_PARTY"}
              onChange={() => setInvoiceIssuer("OTHER_THIRD_PARTY")}
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-3 mt-6">
          <Controller
            name="price"
            control={control}
            rules={{ required: "Price is required", min: { value: 0.01, message: t("priceMustBe") } }}
            render={({ field }) => (
              <FractionInput
                label={t("invoiceAmount")}
                placeholder="€ 00"
                type="currency"
                onChange={(value) => field.onChange({ target: { value } })}
                value={field.value}
                error={errors.price ? String(errors.price.message) : undefined}
              />
            )}
          />
          <div className="w-[60%] flex flex-col gap-1">
            <Controller
              name="due_date"
              control={control}
              rules={{
                required: t("dueDateRequired"),
                validate: {
                  notEmpty: (value) => value !== "00-00-0000" || "Invalid date",
                  notPast: (value) => new Date(value) >= new Date() || "Due date cannot be in the past",
                },
              }}
              render={({ field }) => (
                <Input
                  label={t("invoiceDueDate")}
                  type="date"
                  placeholder={t("selectDueDate")}
                  errorMessage={errors.due_date ? String(errors.due_date.message) : undefined}
                  variant={errors.due_date ? "error" : "enabled"}
                  {...field}
                />
              )}
            />
          </div>
        </div>

        <div className=" flex justify-end mt-12">
          <Button
            color="dark-blue"
            variant="filled"
            size="medium"
            onClick={handleSubmit(handleAddInvoice)}
            disabled={isSubmitting}
          >
            {isSubmitting ? t("creatingInvoice") : t("addInvoice")}
          </Button>
        </div>
      </div>
    </Modal>
  );
}
