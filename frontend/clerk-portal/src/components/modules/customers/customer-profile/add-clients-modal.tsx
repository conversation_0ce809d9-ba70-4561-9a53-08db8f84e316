import UploadInput from "@/components/_common/fileUploader";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, East } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useState } from "react";
import * as XLSX from "xlsx";
import { AddNewClientFormSchemaData } from "../add-new-client-form/add-new-client-form-provider";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import { useInfoAddClient } from "../add-client-context";

interface IModalCompletedProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const AddClientsModal = ({ open, setOpen }: IModalCompletedProps) => {
  const t = useTranslations("AddNewClient");
  const c = useTranslations("common");
  const { setInfoClientsList } = useInfoAddClient();

  const router = useRouter();

  const [file, setFile] = useState<File | undefined>();

  const handleOpen = () => setOpen(!open);

  const addClient = () => {
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      if (!e.target) return;
      const data = new Uint8Array(e.target.result as ArrayBuffer);
      const workbook = XLSX.read(data, { type: "array" });
      const customers = XLSX.utils.sheet_to_json<AddNewClientFormSchemaData>(workbook.Sheets["Main Customer Data"]);
      const emails = XLSX.utils.sheet_to_json<{ companyName: string; email: string }>(workbook.Sheets["Emails"]);
      const countries = XLSX.utils.sheet_to_json<{ companyName: string; country: string }>(
        workbook.Sheets["Countries"]
      );
      const phones = XLSX.utils.sheet_to_json<{ companyName: string; phone: string }>(workbook.Sheets["Phones"]);

      const processedClients = customers.map((customer) => {
        return {
          ...customer,
          Emails: emails.filter((e) => e.companyName === customer.companyName).map((e) => e.email),
          Countries: countries.filter((c) => c.companyName === customer.companyName).map((c) => c.country),
          Phones: phones.filter((p) => p.companyName === customer.companyName).map((p) => p.phone),
        };
      });

      setInfoClientsList(processedClients);
      router.push(`/customers`);
    };
    reader.readAsArrayBuffer(file);
  };

  return (
    <Modal
      open={open}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={handleOpen}
    >
      <div className="p-5 max-h-[75vh] overflow-auto">
        <div className="flex flex-row w-full justify-between">
          <div className="flex flex-col gap-2 justify-start">
            <p className="text-[28px] text-primary font-bold">{t("newClients")}</p>
          </div>
          <div className="flex justify-end">
            <button onClick={handleOpen}>
              <Clear className="size-6 fill-primary" />
            </button>
          </div>
        </div>
        <div className="mt-2">
          <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("information")}</p>
        </div>
        <div className="my-4">
          <UploadInput acceptedFileTypes={["xlsx", "xls"]} file={file} setFile={setFile} />
        </div>
        <div className="flex justify-end">
          <Button trailingIcon={<East />} color="yellow" variant="filled" size="medium" onClick={addClient}>
            {c("add")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
