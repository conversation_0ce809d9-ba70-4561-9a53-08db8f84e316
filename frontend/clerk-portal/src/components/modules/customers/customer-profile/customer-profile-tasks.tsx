import Divider from "@/components/_common/divider";
import { CheckCircle, CheckCircleOutline, Launch } from "@arthursenno/lizenzero-ui-react/Icon";
import Image from "next/image";
import React from "react";
import Status, { StatusType } from "@/components/modules/country/components/task-status";
import StatusBadge from "@/components/modules/country/components/task-type";
import { FilterTasks } from "@/components/modules/country/components/task-type-filter";
import { getTasks } from "@/lib/api/home";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { TaskGroup, TaskGroupItem } from "@/lib/api/home/<USER>";
import { cn } from "@/lib/utils";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { useTranslations } from "next-intl";

const isTaskGroupItemComplete = (taskGroupItem: TaskGroupItem) => {
  return taskGroupItem?.column_values?.find((item) => item?.column?.title === "Status")?.text === "Complete";
};

const getTaskGroupCompletedCount = (taskGroup: TaskGroup[]) => {
  return taskGroup.reduce((acc, group) => {
    return acc + group?.items_page?.items?.filter((item) => isTaskGroupItemComplete(item))?.length;
  }, 0);
};

const getTaskGroupTotalCount = (taskGroup: TaskGroup[]) => {
  return taskGroup.reduce((acc, group) => {
    return acc + group?.items_page?.items?.length;
  }, 0);
};

const getTaskGroupItems = (taskGroup: TaskGroup[]) => {
  return taskGroup.reduce((acc, group) => {
    return acc.concat(group?.items_page?.items);
  }, [] as TaskGroupItem[]);
};

export function CustomerProfileTasks({ customerId, countryCode }: { customerId: number; countryCode?: string }) {
  const t = useTranslations("Monday");
  const searchParams = useSearchParams();
  const typeParam = searchParams.get("type");

  const { data: tasks, isLoading } = useQuery({
    queryKey: ["monday-tasks", customerId, countryCode],
    queryFn: () => getTasks(customerId, countryCode),
    // TODO: Monday API give us a timeout of 20 seconds, so we need to set a stale time to avoid empty responses
    staleTime: 30000,
  });

  const registrationData = {
    count: getTaskGroupTotalCount(tasks?.registrations?.[0]?.groups ?? []) ?? 0,
    tasks: getTaskGroupItems(tasks?.registrations?.[0]?.groups ?? []),
    completedCount: getTaskGroupCompletedCount(tasks?.registrations?.[0]?.groups ?? []) ?? 0,
  };
  const volumeReportsData = {
    count: getTaskGroupTotalCount(tasks?.volumeReports?.[0]?.groups ?? []) ?? 0,
    tasks: getTaskGroupItems(tasks?.volumeReports?.[0]?.groups ?? []),
    completedCount: getTaskGroupCompletedCount(tasks?.volumeReports?.[0]?.groups ?? []) ?? 0,
  };
  const thirdPartyInvoicesData = {
    count: getTaskGroupTotalCount(tasks?.thirdPartyInvoices?.[0]?.groups ?? []) ?? 0,
    tasks: getTaskGroupItems(tasks?.thirdPartyInvoices?.[0]?.groups ?? []),
    completedCount: getTaskGroupCompletedCount(tasks?.thirdPartyInvoices?.[0]?.groups ?? []) ?? 0,
  };
  const terminationsData = {
    count: getTaskGroupTotalCount(tasks?.terminations?.[0]?.groups ?? []) ?? 0,
    tasks: getTaskGroupItems(tasks?.terminations?.[0]?.groups ?? []),
    completedCount: getTaskGroupCompletedCount(tasks?.terminations?.[0]?.groups ?? []) ?? 0,
  };

  const allData = {
    count: registrationData.count + volumeReportsData.count + thirdPartyInvoicesData.count + terminationsData.count,
    tasks: [
      ...registrationData.tasks,
      ...volumeReportsData.tasks,
      ...thirdPartyInvoicesData.tasks,
      ...terminationsData.tasks,
    ],
    completedCount:
      registrationData.completedCount +
      volumeReportsData.completedCount +
      thirdPartyInvoicesData.completedCount +
      terminationsData.completedCount,
  };

  const taskCounts = {
    ALL: allData.count,
    REGISTRATION: registrationData.count,
    VOLUME_REPORTS: volumeReportsData.count,
    THIRD_PARTY_INVOICES: thirdPartyInvoicesData.count,
    TERMINATIONS: terminationsData.count,
  };

  const filteredTasks = (() => {
    if (typeParam === "ALL") {
      return allData.tasks;
    }
    if (typeParam === "REGISTRATION") {
      return registrationData.tasks;
    }
    if (typeParam === "VOLUME_REPORTS") {
      return volumeReportsData.tasks;
    }
    if (typeParam === "THIRD_PARTY_INVOICES") {
      return thirdPartyInvoicesData.tasks;
    }
    if (typeParam === "TERMINATIONS") {
      return terminationsData.tasks;
    }
    return allData.tasks;
  })();

  const getCustomerId = (taskGroupItem: TaskGroupItem) => {
    const customerId = taskGroupItem?.column_values?.find((item) => item.column.title === "Customer ID")?.text;
    return customerId ? `${t("customer")} #${customerId}` : "-";
  };

  const getStatus = (taskGroupItem: TaskGroupItem) => {
    return taskGroupItem?.column_values?.find((item) => item.column.title === "Status")?.text ?? "Open";
  };

  const getProgress = (taskGroupItem: TaskGroupItem) => {
    return taskGroupItem?.column_values?.find((item) => item.column.title === "Stage")?.text ?? "-";
  };

  if (isLoading) {
    return <Skeleton className={cn("h-[610px] w-auto md:w-[704px] rounded-3xl")} />;
  }

  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <div className="flex items-center justify-between gap-4">
          <div className="w-[40px] h-[40px] border-[1px] border-solid border-[#ECECEC] rounded-xl flex justify-center items-center bg-white">
            <Image src={`/assets/images/LogoMonday.svg`} alt={"Logo Monday"} width={30} height={19} />
          </div>
          <p className="text-tonal-dark-blue-10 text-title-3 font-bold">{t("name")}</p>
        </div>
        <Link href={"https://lizenzero.monday.com/"}>
          <Launch className="size-6 fill-[#009DD3]" />
        </Link>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        <div className="flex items-center justify-between">
          <FilterTasks taskCounts={taskCounts} />
          <div className="flex items-center gap-1">
            <p className="text-small-paragraph-regular text-tonal-dark-cream-20">{t("tasksDone")}:</p>
            <p className="text-small-paragraph-regular text-tonal-dark-cream-20">
              <span className="text-small-paragraph-regular text-tonal-dark-cream-20 font-bold">
                {allData.completedCount}
              </span>{" "}
              / {typeParam === "ALL" && `${taskCounts.ALL}`}
              {typeParam === "REGISTRATION" && `${taskCounts.REGISTRATION}`}
              {typeParam === "VOLUME_REPORTS" && `${taskCounts.VOLUME_REPORTS}`}
              {typeParam === "THIRD_PARTY_INVOICES" && `${taskCounts.THIRD_PARTY_INVOICES}`}
              {typeParam === "TERMINATIONS" && `${taskCounts.TERMINATIONS}`}
              {!typeParam && `${taskCounts.ALL}`}
            </p>
          </div>
        </div>
        <div className="">
          <div className={cn("p-6 overflow-auto max-h-[480px] h-full bg-white rounded-xl")}>
            {filteredTasks && filteredTasks.length > 0 ? (
              filteredTasks.map((task) => (
                <React.Fragment key={task.id}>
                  <div className="p-2 flex items-center justify-between">
                    <div className="flex flex-col gap-0.5">
                      <p className="text-support-blue text-small-paragraph-regular underline">{getCustomerId(task)}</p>
                      <p className="font-bold text-paragraph-regular text-tonal-dark-cream-20">{task.name}</p>
                      <p className="text-small-paragraph-regular text-tonal-dark-cream-50">{getProgress(task)}</p>
                    </div>
                    <div className="flex items-center justify-between  gap-6">
                      <div className="flex flex-col justify-start items-start gap-2">
                        <Status status={getStatus(task) as StatusType} />
                        {/* TODO: Replace with actual task type */}
                        {/* <StatusBadge status={task.type} /> */}
                        <StatusBadge status="Registration" />
                      </div>
                      <div className="flex justify-end">
                        {isTaskGroupItemComplete(task) ? (
                          <CheckCircle className="size-8 fill-success" />
                        ) : (
                          <CheckCircleOutline className="size-8 fill-tonal-dark-cream-40" />
                        )}
                      </div>
                    </div>
                  </div>
                  <Divider initialMarginDisabled className="my-1" />
                </React.Fragment>
              ))
            ) : (
              <p className="text-small-paragraph-regular text-tonal-dark-cream-50 text-center mt-6">
                {t("noTasksFound")}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
