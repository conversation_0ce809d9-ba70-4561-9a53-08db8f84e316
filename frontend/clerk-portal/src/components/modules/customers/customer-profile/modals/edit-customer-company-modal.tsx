"use client";

import Image from "next/image";
import { use<PERSON>arams } from "next/navigation";
import { useEffect, useState } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { CgSpinnerAlt } from "react-icons/cg";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";

import { CountrySelect } from "@/components/_common/country-select";
import { cn } from "@/lib/utils";
import { COUNTRIES } from "@/utils/countries";
import { ADDRESS_REGEX, SPECIAL_CHARS_COMPANY_NAME_REGEX, ZIP_CODE_REGEX } from "@/utils/regex";

import { AutocompleteAddressInput } from "@/components/_common/forms/address-input";
import { RightCheckIcon } from "@/components/ui/right-check-icon";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { AddressSuggestionDetails } from "@/lib/api/address-suggestions";
import { validateVatId } from "@/lib/api/company";
import { useTranslations } from "next-intl";
import { useCustomer } from "../use-customer";
import { updateCustomerCompanyInfo } from "@/lib/api/customer";
import { enqueueSnackbar } from "notistack";

function useEditCustomerCompanyModal() {
  const c = useTranslations("common");
  const t = useTranslations("EditCustomerCompanyModal");

  const editCompanyInfoFormSchema = z.object({
    name: z.string().min(1, t("required")).regex(SPECIAL_CHARS_COMPANY_NAME_REGEX, t("noSpecial")),
    country: z.string().min(1, t("required")),
    // federalState: z.string().min(1, "Required field"),
    city: z
      .string()
      .min(1, { message: t("validCity") })
      .regex(ADDRESS_REGEX, t("noSpecial")),
    zipCode: z
      .string()
      .min(1, { message: t("validZipCode") })
      .regex(ZIP_CODE_REGEX, t("noSpecial")),
    streetAndNumber: z
      .string()
      .min(1, { message: t("validStreetAndNumber") })
      .regex(ADDRESS_REGEX, t("noSpecial")),
    additionalAddressLine: z.string().regex(ADDRESS_REGEX, t("noSpecial")).optional().or(z.literal("")),
    vat: z.string().optional(),
    addressLine: z
      .string({
        required_error: t("validAddress"),
        invalid_type_error: t("validAddress"),
      })
      .min(1, { message: t("validAddress") })
      .regex(ADDRESS_REGEX, t("noSpecial")),
  });

  return {
    c,
    t,
    editCompanyInfoFormSchema,
  };
}

type EditCompanyInfoForm = z.infer<ReturnType<typeof useEditCustomerCompanyModal>["editCompanyInfoFormSchema"]>;

const countrySelectOptions = COUNTRIES.map((c) => ({ label: c.name, value: c.code, flag: c.flag }));

export function EditCustomerCompanyModal({ onSubmit }: { onSubmit?: () => void }) {
  const { c, t, editCompanyInfoFormSchema } = useEditCustomerCompanyModal();
  const params = useParams();

  const customerId = params.customerId;
  const { paramValues, deleteParam } = useQueryFilter(["edit-company"]);

  const isOpen = paramValues["edit-company"] === "true";

  const { customer } = useCustomer();

  const customerCompany = customer?.companies?.[0];

  const [isSuccess, setIsSuccess] = useState(false);
  const [zipCodeMask, setZipCodeMask] = useState("");
  const [loadingValidatVat, setLoadingValidatVat] = useState(false);
  const [isFocusedVat, setIsFocusedVat] = useState(false);
  const [isValidVat, setIsValidVat] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    getValues,
    setValue,
    clearErrors,
    setError,
    resetField,
    handleSubmit,
    watch,
    formState: { errors },
    control,
    formState: { isSubmitted },
    reset,
  } = useForm<EditCompanyInfoForm>({
    resolver: zodResolver(editCompanyInfoFormSchema),
    defaultValues: {
      name: customerCompany?.name ?? "",
      country: customerCompany?.address.country_code ?? "",
      city: customerCompany?.address.city ?? "",
      zipCode: customerCompany?.address.zip_code ?? "",
      streetAndNumber: customerCompany?.address.street_and_number ?? "",
      additionalAddressLine: customerCompany?.address.additional_address ?? "",
      vat: customerCompany?.vat ?? "",
      addressLine: customerCompany?.address?.address_line ?? "",
    },
  });

  useEffect(() => {
    reset({
      name: customerCompany?.name ?? "",
      country: customerCompany?.address.country_code ?? "",
      city: customerCompany?.address.city ?? "",
      zipCode: customerCompany?.address.zip_code ?? "",
      streetAndNumber: customerCompany?.address.street_and_number ?? "",
      additionalAddressLine: customerCompany?.address.additional_address ?? "",
      vat: customerCompany?.vat ?? "",
      addressLine: customerCompany?.address?.address_line ?? "",
    });
  }, [customerCompany, reset]);

  useEffect(() => {
    if (!isOpen) {
      setIsSuccess(false);
      reset();
    }
  }, [isOpen, reset]);

  function handleCloseModal() {
    deleteParam("edit-company");
  }

  function handleOnOpenChange(open: boolean) {
    if (!open) handleCloseModal();
  }

  function handleSelectCountry(countryCode: string) {
    setValue("city", "");
    setValue("zipCode", "");
    setValue("streetAndNumber", "");
    setValue("additionalAddressLine", "");
    setValue("addressLine", "");

    if (!countryCode) return;

    const foundCountry = COUNTRIES.find((c) => c.code === countryCode);

    if (!foundCountry) return;

    setZipCodeMask(foundCountry.address.zipCodeMask);
  }

  function handleSelectAddress(address: AddressSuggestionDetails, paymentType?: boolean) {
    const data: {
      addressLine: string;
      city: string | undefined;
      zipCode: string | undefined;
      streetAndNumber: string | undefined;
    } = {
      addressLine: address.formattedAddress,
      city: address.city || undefined,
      zipCode: address.postalCode || undefined,
      streetAndNumber: undefined,
    };

    if (address.route) {
      const streetAndNumber = [address.route];

      if (address.streetNumber) streetAndNumber.push(address.streetNumber);

      data.streetAndNumber = streetAndNumber.join(", ");
    }

    if (data.addressLine) setValue("addressLine", data.addressLine);
    if (data.city) setValue("city", data.city);
    if (data.zipCode) setValue("zipCode", data.zipCode);
    if (data.streetAndNumber) setValue("streetAndNumber", data.streetAndNumber);
  }

  async function handleSelectVatId(vatId: string) {
    setIsValidVat(false);
    if (loadingValidatVat || !vatId) return clearErrors("vat");

    setLoadingValidatVat(true);

    const formattedVatId = vatId.replace(/\s/g, "").trim();

    const vatValidationResponse = await validateVatId({
      vat_id: formattedVatId,
      country_code: watch("country").toUpperCase(),
      company_zipcode: watch("zipCode"),
      company_city: watch("city"),
      company_street: watch("streetAndNumber"),
    });

    if (!vatValidationResponse.valid) {
      setError("vat", { type: "manual", message: vatValidationResponse.code || "Invalid VAT ID" });
      setLoadingValidatVat(false);
      return;
    }

    clearErrors("zipCode");
    clearErrors("vat");
    setValue("zipCode", watch("zipCode"));
    setValue("vat", formattedVatId);
    setLoadingValidatVat(false);
    setIsValidVat(true);
  }

  async function handleOnSubmit(values: EditCompanyInfoForm) {
    if (!customerId) return;
    if (!customerCompany?.id) return;
    setIsLoading(true);

    if (values.vat) {
      const vatValidationResponse = await validateVatId({
        vat_id: values.vat,
        country_code: values.country,
        company_zipcode: values.zipCode,
        company_city: values.city,
        company_street: values.streetAndNumber,
      });
      setIsLoading(false);

      if (!vatValidationResponse.valid)
        return setError("vat", { type: "manual", message: vatValidationResponse.code || "Invalid VAT ID" });
    }

    try {
      const payload = {
        name: values.name,
        country: values.country,
        city: values.city,
        zipCode: values.zipCode,
        streetAndNumber: values.streetAndNumber,
        additionalAddressLine: values.additionalAddressLine,
        customerId: Number(customerId),
        address: {
          country_code: values.country,
          address_line: values.addressLine,
          city: values.city,
          zip_code: values.zipCode,
          street_and_number: values.streetAndNumber,
          additional_address: values.additionalAddressLine || "",
        },
        ...(() => {
          if (values.vat) {
            return {
              vat: values.vat,
              tin: null,
            };
          }

          return {
            vat: null,
          };
        })(),
      };

      setIsLoading(true);
      const companyResponse: any = await updateCustomerCompanyInfo(customerCompany.id, payload);
      if (!companyResponse.data) {
        const statusRes = companyResponse?.response?.status;
        const msgRes = companyResponse?.response?.data?.message?.toLowerCase();
        const errorIsDocument = msgRes?.includes(`vat`) || msgRes?.includes(`document`) || msgRes?.includes(`tax`);

        if (statusRes === 409 && errorIsDocument) {
          if (values.vat) {
            setError("vat", {
              type: "manual",
              message: t("vatinUse"),
            });
          }
        }

        enqueueSnackbar(t("error"), { variant: "error" });
        return;
      }

      onSubmit && onSubmit();

      setIsSuccess(true);
      setIsLoading(false);
    } catch (error) {
      setIsSuccess(false);
    }
  }

  return (
    <Modal
      open={isOpen}
      data-success={isSuccess}
      onOpenChange={handleOnOpenChange}
      className={cn(
        "z-50 !rounded-[52px] w-full max-w-[672px] !py-8 !px-9 overflow-hidden !max-h-[90vh]",
        isSuccess ? `h-[35vh]` : `h-[90vh]`
      )}
    >
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-end">
          <Clear onClick={handleCloseModal} className="w-8 h-8 fill-primary hover:opacity-75 cursor-pointer" />
        </div>
        {!isSuccess && (
          <div className="flex flex-col flex-1 overflow-y-auto pb-8">
            <div className="mb-10 space-y-4">
              <h3 className="font-bold text-[1.75rem] text-primary">{t("companyInformation")}</h3>
              <p className="text-tonal-dark-cream-20">{t("editCompanyInfo")}</p>
            </div>
            <div className="w-full h-full pl-1 pr-5 pb-8">
              <form onSubmit={handleSubmit(handleOnSubmit)} className="space-y-8">
                <Controller
                  control={control}
                  name="name"
                  render={({ field, fieldState: { error } }) => {
                    const fieldValue = getValues("name");
                    const hasRightIcon =
                      !error && fieldValue && editCompanyInfoFormSchema.shape.name.safeParse(fieldValue).success;
                    return (
                      <Input
                        label={t("companyNameRequired")}
                        placeholder={t("companyNamePlaceholder")}
                        variant={error && "error"}
                        errorMessage={error && error.message}
                        rightIcon={hasRightIcon && <RightCheckIcon />}
                        {...field}
                      />
                    );
                  }}
                />

                <div className="grid items-start gap-6">
                  <Controller
                    control={control}
                    name="country"
                    render={({ field, fieldState: { error } }) => (
                      <CountrySelect
                        label={t("countryRequired")}
                        isValid={!error}
                        errorMessage={error && error.message}
                        opts={countrySelectOptions}
                        value={field.value}
                        onChange={(value) => {
                          field.onChange(value);
                          handleSelectCountry(value);
                        }}
                      />
                    )}
                  />
                </div>

                <AutocompleteAddressInput
                  key={`customer-company`}
                  defaultValue={watch("addressLine")}
                  countryCode={watch("country")}
                  onChangeAddressLine={(addressLine) => {
                    clearErrors("city");
                    clearErrors("zipCode");
                    clearErrors("streetAndNumber");

                    if (!addressLine) {
                      setError("addressLine", { message: t("validAddress") });
                    } else {
                      clearErrors("addressLine");
                    }
                  }}
                  onSelectAddress={handleSelectAddress}
                  isError={!!errors.addressLine}
                  errorMessage={errors.addressLine?.message}
                />

                <div className="grid md:grid-cols-2 items-start gap-6">
                  <Controller
                    control={control}
                    name="city"
                    render={({ field, fieldState: { error } }) => {
                      const fieldValue = getValues("city");
                      const hasRightIcon =
                        !error && fieldValue && editCompanyInfoFormSchema.shape.city.safeParse(fieldValue).success;
                      return (
                        <Input
                          label={t("cityRequired")}
                          placeholder={t("cityPlaceholder")}
                          variant={error && "error"}
                          errorMessage={error && error.message}
                          rightIcon={hasRightIcon && <RightCheckIcon />}
                          {...field}
                        />
                      );
                    }}
                  />
                  <Controller
                    control={control}
                    name="zipCode"
                    render={({ field, fieldState: { error } }) => {
                      const fieldValue = getValues("zipCode");
                      const hasRightIcon =
                        !error && fieldValue && editCompanyInfoFormSchema.shape.zipCode.safeParse(fieldValue).success;

                      return (
                        <Input
                          label={t("zipCodeRequired")}
                          placeholder={t("zipCodePlaceholder")}
                          variant={error && "error"}
                          errorMessage={error && error.message}
                          rightIcon={hasRightIcon && <RightCheckIcon />}
                          {...field}
                        />
                      );

                      // return (
                      //   <ZipCodeInput
                      //     label="ZIP Code *"
                      //     mask={zipCodeMask}
                      //     isInvalid={!!error}
                      //     errorMessage={error && error.message}
                      //     {...field}
                      //   />
                      // );
                    }}
                  />
                </div>

                <Controller
                  control={control}
                  name="streetAndNumber"
                  render={({ field, fieldState: { error } }) => {
                    const fieldValue = getValues("streetAndNumber");
                    const hasRightIcon =
                      !error &&
                      fieldValue &&
                      editCompanyInfoFormSchema.shape.streetAndNumber.safeParse(fieldValue).success;
                    return (
                      <Input
                        label={t("streetAndNumberRequired")}
                        placeholder={t("streetAndNumberPlaceholder")}
                        variant={error && "error"}
                        errorMessage={error && error.message}
                        rightIcon={hasRightIcon && <RightCheckIcon />}
                        {...field}
                      />
                    );
                  }}
                />

                <Controller
                  control={control}
                  name="additionalAddressLine"
                  render={({ field, fieldState: { error } }) => {
                    const fieldValue = getValues("additionalAddressLine");
                    const hasRightIcon =
                      !error &&
                      fieldValue &&
                      editCompanyInfoFormSchema.shape.additionalAddressLine.safeParse(fieldValue).success;
                    return (
                      <Input
                        label={t("additionalAddressLine")}
                        placeholder={t("additionalAddressLine")}
                        variant={error && "error"}
                        errorMessage={error && error.message}
                        rightIcon={hasRightIcon && <RightCheckIcon />}
                        {...field}
                      />
                    );
                  }}
                />

                <Input
                  label={t("vat")}
                  placeholder={t("vat")}
                  variant={errors?.vat && "error"}
                  rightIcon={
                    (loadingValidatVat && <CgSpinnerAlt size={20} className="animate-spin text-primary" />) ||
                    (isValidVat && !isFocusedVat && getValues("vat") && <RightCheckIcon />)
                  }
                  onFocus={() => setIsFocusedVat(true)}
                  errorMessage={errors?.vat?.message}
                  {...register("vat", {
                    onBlur: (event) => {
                      setIsFocusedVat(false);
                      handleSelectVatId(event?.target?.value);
                    },
                  })}
                />

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    variant="filled"
                    color="yellow"
                    size="medium"
                    disabled={isLoading || loadingValidatVat}
                    className="sm:min-w-52"
                  >
                    {isLoading ? c("saving") : c("save")} <KeyboardArrowRight className="size-5 fill-on-tertiary" />
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
        {isSuccess && (
          <div className="space-y-10 pt-8">
            <div className="flex items-center gap-2">
              <Image alt="Leaf seal" src="/assets/images/leaf-seal.svg" width={35} height={36} />
              <h3 className="mt-1.5 flex-1 font-bold text-[1.75rem] text-primary">{t("informationUpdated")}</h3>
            </div>
            <div className="flex justify-end">
              <Button type="button" color="yellow" variant="filled" size="medium" onClick={handleCloseModal}>
                {c("close")}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
