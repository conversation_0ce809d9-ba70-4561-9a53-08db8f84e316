"use client";

import { CountrySelect } from "@/components/_common/country-select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ClaimManagement } from "@/lib/api/claim-management/types";
import { COUNTRIES } from "@/utils/countries";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle, Clear, Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Control, Controller, UseFormHandleSubmit, UseFormSetValue, useForm } from "react-hook-form";
import { z } from "zod";
import { useTranslations } from "next-intl";

function useActionModal() {
  const t = useTranslations("ActionModal");
  const c = useTranslations("common");

  const reissueFormSchema = z.object({
    invoiceId: z.number().optional(),
    companyName: z.string().min(1, t("companyNameRequired")),
    additionalInfo: z.string().optional(),
    country: z.string().min(1, t("countryRequired")),
    city: z.string().min(1, t("cityRequired")),
    streetAndNumber: z.string().optional(),
    additionalAddressLine: z.string().optional(),
    zipCode: z.string().min(1, t("zipCodeRequired")),
    federalState: z.string().min(1, t("federalStateRequired")),
    vatNumber: z.string().optional(),
    tinNumber: z.string().optional(),
  });

  return {
    t,
    c,
    reissueFormSchema,
  };
}

type ReIssueInvoiceFormData = z.infer<ReturnType<typeof useActionModal>["reissueFormSchema"]>;

interface FormStepProps {
  control: Control<ReIssueInvoiceFormData>;
  handleSubmit: UseFormHandleSubmit<ReIssueInvoiceFormData>;
  errors: Record<string, any>;
  onNextStep: (data: ReIssueInvoiceFormData) => void;
  setValue: UseFormSetValue<ReIssueInvoiceFormData>;
  data: ClaimManagement | undefined;
}

const FormStep = ({ control, handleSubmit, errors, onNextStep, setValue, data }: FormStepProps) => {
  const { t, c } = useActionModal();
  const [tinOrVat, setTinOrVat] = useState("VAT");

  const countrySelectOptions = COUNTRIES.map((c) => ({ ...c, label: c.name, value: c.code, flag: c.flag }));

  const [isCountrySelected, setIsCountrySelected] = useState(false);

  useEffect(() => {
    setValue("invoiceId", data?.invoiceId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSelectCountry = (country: { code: string }) => {
    setIsCountrySelected(!!country);
  };

  const onChangeVatTin = () => {
    if (tinOrVat === "VAT") {
      setTinOrVat("TIN");
      setValue("vatNumber", "");
    } else {
      setTinOrVat("VAT");
      setValue("tinNumber", "");
    }
  };

  return (
    <form onSubmit={handleSubmit(onNextStep)} className="flex flex-col gap-6 mt-4">
      <p className="text-[#808FA9] font-light text-sm mb-3">*{t("mandatoryFields")}</p>

      <Controller
        control={control}
        name="companyName"
        render={({ field }) => (
          <Input
            label={t("companyName") + "*"}
            placeholder={t("companyName")}
            rightIcon={
              !errors.companyName &&
              field.value && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
            }
            errorMessage={errors.companyName?.message}
            {...field}
          />
        )}
      />

      <div className="flex gap-3">
        <Controller
          control={control}
          name="streetAndNumber"
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label={t("streetAndNumber")}
              placeholder={t("streetAndNumber")}
              rightIcon={
                !error && field.value && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
              }
              errorMessage={error?.message}
              variant={error ? "error" : undefined}
            />
          )}
        />

        <Controller
          control={control}
          name="additionalAddressLine"
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label={t("additionalAddressLine")}
              placeholder={t("additionalAddressLine")}
              rightIcon={
                !error && field.value && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
              }
              errorMessage={error?.message}
              variant={error ? "error" : undefined}
            />
          )}
        />
      </div>
      <div className="flex gap-3">
        <Controller
          control={control}
          name="zipCode"
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label={t("zipCode") + "*"}
              placeholder={t("zipCode")}
              rightIcon={
                !error && field.value && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
              }
              errorMessage={error?.message}
              variant={error ? "error" : undefined}
            />
          )}
        />

        <Controller
          control={control}
          name="city"
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label={t("city") + "*"}
              placeholder={t("city")}
              rightIcon={
                !error && field.value && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
              }
              errorMessage={error?.message}
              variant={error ? "error" : undefined}
            />
          )}
        />
      </div>
      <div className="flex gap-3">
        <div className="w-full">
          <Controller
            control={control}
            name="country"
            render={({ field, fieldState: { error } }) => (
              <CountrySelect
                label={t("country") + "*"}
                isValid={!error}
                errorMessage={error && error.message}
                opts={countrySelectOptions}
                value={field.value}
                onChange={(value) => {
                  field.onChange(value);
                  const selectedCountry = countrySelectOptions.find((c) => c.value === value);
                  if (selectedCountry) {
                    handleSelectCountry({ code: selectedCountry.value });
                  }
                }}
              />
            )}
          />
        </div>
        <div className="w-full">
          <Controller
            control={control}
            name="federalState"
            render={({ field, fieldState: { error } }) => (
              <Input
                {...field}
                label={t("federalState") + "*"}
                placeholder={t("federalState")}
                rightIcon={
                  !error && field.value && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
                }
                errorMessage={error?.message}
                variant={error ? "error" : undefined}
                enabled={isCountrySelected}
              />
            )}
          />
        </div>
      </div>

      <div className="">
        <div className="">
          <RadioGroup value={tinOrVat} onValueChange={() => onChangeVatTin()} className="flex w-full">
            <div className="w-full">
              <div className="flex items-center gap-2 w-full">
                <RadioGroupItem value="VAT" className={tinOrVat === "VAT" ? "bg-white border-primary" : ""} />
                <label className={tinOrVat === "VAT" ? "text-primary font-semibold" : "text-primary"}>{t("vat")}</label>
              </div>
              <Controller
                control={control}
                name="vatNumber"
                render={({ field, fieldState: { error } }) => (
                  <Input
                    {...field}
                    placeholder={t("vatNumber")}
                    rightIcon={
                      !error && field.value && tinOrVat === "VAT" ? (
                        <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
                      ) : null
                    }
                    errorMessage={error?.message}
                    variant={error ? "error" : undefined}
                    enabled={tinOrVat === "VAT"}
                  />
                )}
              />
            </div>
            <div className="flex items-center gap-2 w-full">
              <div className="w-full">
                <div className="flex items-center gap-2 w-full">
                  <RadioGroupItem value="TIN" className={tinOrVat === "TIN" ? "bg-white border-primary" : ""} />
                  <label className={tinOrVat === "TIN" ? "text-primary font-semibold" : "text-primary"}>
                    {t("tin")}
                  </label>
                </div>
                <Controller
                  control={control}
                  name="tinNumber"
                  render={({ field, fieldState: { error } }) => (
                    <Input
                      {...field}
                      placeholder={t("tinNumber")}
                      rightIcon={
                        !error && field.value && tinOrVat === "TIN" ? (
                          <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
                        ) : null
                      }
                      errorMessage={error?.message}
                      variant={error ? "error" : undefined}
                      enabled={tinOrVat === "TIN"}
                    />
                  )}
                />
              </div>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="flex justify-end">
        <Button type="submit" color="dark-blue" variant="filled" size="medium">
          {c("continue")}
        </Button>
      </div>
    </form>
  );
};

interface ConfirmationStepProps {
  formData: ReIssueInvoiceFormData;
  onConfirm: () => void;
  onBack: () => void;
  loading: boolean;
}

const ConfirmationStep = ({ formData, onConfirm, onBack, loading }: ConfirmationStepProps) => {
  const { t, c } = useActionModal();
  return (
    <div className="flex flex-col gap-6 mt-4">
      <p className="text-paragraph-regular text-tonal-dark-cream-20">
        {t("changeInformationPre")} #{formData.invoiceId} {t("changeInformationPost")} and a new one will be created.
      </p>

      <div className="flex justify-end gap-4">
        <Button
          type="button"
          color="dark-blue"
          variant="outlined"
          size="small"
          onClick={onBack}
          className="px-10"
          disabled={loading}
        >
          {c("back")}
        </Button>
        <Button type="button" color="yellow" variant="filled" size="small" onClick={onConfirm} disabled={loading}>
          {loading ? c("loading") : t("generateInvoice")}
        </Button>
      </div>
    </div>
  );
};

export interface ClaimManagementReissueModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: ClaimManagement;
}

export function ClaimManagementReissueInvoiceModal({ isOpen, onClose, data }: ClaimManagementReissueModalProps) {
  const { t, c, reissueFormSchema } = useActionModal();
  const [step, setStep] = useState<1 | 2 | 3>(1);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ReIssueInvoiceFormData | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<ReIssueInvoiceFormData>({
    defaultValues: {
      companyName: "",
      additionalInfo: "",
      country: "",
      city: "",
      streetAndNumber: "",
      additionalAddressLine: "",
      zipCode: "",
      federalState: "",
      vatNumber: "",
      tinNumber: "",
      invoiceId: data?.invoiceId,
    },
    resolver: zodResolver(reissueFormSchema),
  });

  const handleClose = () => {
    reset();
    onClose();
    setStep(1);
    setFormData(null);
  };

  const handleNextStep = (data: ReIssueInvoiceFormData) => {
    setFormData(data);
    setStep(2);
  };

  const handleBack = () => {
    setStep(1);
  };

  const handleConfirm = async () => {
    if (formData) {
      try {
        setLoading(true);
        // TODO: Integrate with API
        const res = await new Promise((resolve) => {
          setTimeout(() => {
            resolve(true);
          }, 2000);
        });
        if (!res) throw new Error(t("unableToProcessReissue"));
        setStep(3);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={handleClose}
    >
      <div className="p-5 max-h-[75vh] overflow-auto">
        <div className="flex justify-end w-full">
          <button onClick={handleClose}>
            <Clear className="size-6 fill-primary" />
          </button>
        </div>
        {step === 3 ? null : (
          <div className="text-[28px] text-primary font-bold">
            <p>{c("areYouSure")}</p>
          </div>
        )}

        {step === 1 && (
          <FormStep
            control={control}
            handleSubmit={handleSubmit}
            errors={errors}
            onNextStep={handleNextStep}
            setValue={setValue}
            data={data}
          />
        )}

        {step === 2 && formData && (
          <ConfirmationStep formData={formData} onConfirm={handleConfirm} onBack={handleBack} loading={loading} />
        )}

        {step === 3 && (
          <div className="flex flex-col gap-6 mt-4">
            <div className="flex flex-row gap-4 items-center">
              <Plant className="size-8 fill-success" />
              <p className="text-title-2 text-primary font-bold mt-2">{t("invoiceGeneratedSuccessfully")}</p>
            </div>
            <div className="flex justify-end">
              <Button type="button" color="dark-blue" variant="filled" size="medium" onClick={handleClose}>
                {c("close")}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
