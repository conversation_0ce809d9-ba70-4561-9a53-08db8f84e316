"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { AutocompleteAddressInput } from "@/components/_common/forms/address-input";
import { CustomRadio } from "@/components/_common/forms/customRadio/custom-radio";
import { Combobox } from "@/components/ui/combobox";
import { AddressSuggestionDetails } from "@/lib/api/address-suggestions";
import { ClaimManagement } from "@/lib/api/claim-management/types";
import { validateVatId } from "@/lib/api/company";
import { COUNTRIES, EU_COUNTRY_CODES } from "@/utils/countries";
import { ADDRESS_REGEX, SPECIAL_CHARS_REGEX, ZIP_CODE_REGEX } from "@/utils/regex";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Check, Clear, Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { CgSpinnerAlt } from "react-icons/cg";
import { z } from "zod";
import { useTranslations } from "next-intl";

function useReissueModal() {
  const t = useTranslations("ReissueModal");

  const reissueFormSchema = z.object({
    companyName: z.string().regex(SPECIAL_CHARS_REGEX, t("specialCharacters")).min(1),
    countryCode: z.string().min(1),
    addressLine: z
      .string({
        required_error: t("validAddress"),
        invalid_type_error: t("validAddress"),
      })
      .min(1, { message: t("validAddress") })
      .regex(ADDRESS_REGEX, t("specialCharacters")),
    city: z
      .string({
        required_error: t("validCity"),
        invalid_type_error: t("validCity"),
      })
      .min(1, { message: t("validCity") })
      .regex(ADDRESS_REGEX, t("specialCharacters")),
    zipCode: z
      .string({
        required_error: t("validZip"),
        invalid_type_error: t("validZip"),
      })
      .min(1, { message: t("validZip") })
      .regex(ZIP_CODE_REGEX, t("specialCharacters")),
    streetAndNumber: z
      .string({
        required_error: t("validStreetAndNumber"),
        invalid_type_error: t("validStreetAndNumber"),
      })
      .min(1, {
        message: t("validStreetAndNumber"),
      })
      .regex(ADDRESS_REGEX, t("specialCharacters")),
    additionalAddressLine: z.string().regex(ADDRESS_REGEX, t("specialCharacters")).optional(),
    documentType: z.enum(["TAX", "VAT"]),
    vatId: z.string().optional(),
    taxNumber: z
      .string()
      .min(1, t("required"))
      .regex(/^[a-zA-Z0-9ßÀ-ÿ\s]+$/, t("specialCharacters"))
      .optional(),
  });

  return {
    t,
    reissueFormSchema,
  };
}

type ReissueFormData = z.infer<ReturnType<typeof useReissueModal>["reissueFormSchema"]>;

interface ConfirmationStepProps {
  onConfirm: () => void;
  loading: boolean;
  handleBack: () => void;
  invoiceId: number;
}

const ConfirmationStep = ({ onConfirm, loading, handleBack, invoiceId }: ConfirmationStepProps) => {
  const { t } = useReissueModal();

  return (
    <div className="flex flex-col gap-6 mt-4">
      <p className="text-[28px] text-primary font-bold">{t("areYouSure")}</p>

      <p className="text-paragraph-regular text-tonal-dark-cream-20">
        {t("byChanging")} <span className="text-primary font-bold">#{invoiceId}</span> {t("autoCancel")}
      </p>

      <div className="flex flex-row gap-6 justify-end">
        <Button onClick={handleBack} variant="outlined" color="dark-blue" size="medium" disabled={loading}>
          {loading ? t("loading") : t("back")}
        </Button>
        <Button type="button" color="yellow" variant="filled" onClick={onConfirm} size="medium" disabled={loading}>
          {loading ? t("loading") : t("generateInvoice")}
        </Button>
      </div>
    </div>
  );
};

export interface ClaimManagementRefundModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: ClaimManagement;
}

export function ClaimManagementReissueModal({ isOpen, onClose, data }: ClaimManagementRefundModalProps) {
  const { t, reissueFormSchema } = useReissueModal();
  const [step, setStep] = useState<1 | 2 | 3>(1);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ReissueFormData | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    resetField,
    setValue,
    getValues,
    clearErrors,
    setError,
  } = useForm<ReissueFormData>({
    resolver: zodResolver(reissueFormSchema),
  });

  const handleClose = () => {
    reset();
    onClose();
    setStep(1);
    setFormData(null);
  };

  const handleNextStep = (data: ReissueFormData) => {
    setFormData(data);
    setStep(2);
  };

  const handleBack = () => {
    setStep(1);
  };

  const handleConfirm = async () => {
    if (formData) {
      try {
        setLoading(true);
        // TODO: Integrate with API
        const res = await new Promise((resolve) => {
          setTimeout(() => {
            resolve(true);
          }, 2000);
        });
        if (!res) throw new Error("Unable to process re-issue");
        setStep(3);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    }
  };

  const documentTypeWatch = watch(`documentType`);
  const countryCodeWatch = watch(`countryCode`);
  const companyNameWatch = watch(`companyName`);
  const zipCodeWatch = watch(`zipCode`);
  const cityWatch = watch(`city`);
  const streetAndNumberWatch = watch(`streetAndNumber`);
  const addressLineWatch = watch(`addressLine`);

  const [vatInfoMessage, setVatInfoMessage] = useState<string | null>(null);
  const [loadingValidatVat, setLoadingValidatVat] = useState(false);
  const [addressInputKey, setAddressInputKey] = useState(0);

  function setDocumentType(type: "VAT" | "TAX") {
    if (loadingValidatVat) return;
    if (type === "VAT") {
      resetField("taxNumber");
      setValue("documentType", "VAT");
      setValue("taxNumber", undefined);

      clearErrors("taxNumber");
    }
    if (type === "TAX") {
      resetField("vatId");
      setValue("documentType", "TAX");
      setValue("vatId", undefined);
      setVatInfoMessage("");
      clearErrors("vatId");
    }

    clearErrors(`documentType`);
  }

  function handleSelectCountry(country: { value: string; label: string } | null) {
    setValue("city", "");
    setValue("zipCode", "");
    setValue("streetAndNumber", "");
    setValue("addressLine", "");
    setValue("countryCode", "");

    setAddressInputKey((prevKey) => prevKey + 1);

    if (!country) return;

    const countryName = country.value;

    const foundCountry = COUNTRIES.find((c) => c.name === countryName);

    if (!foundCountry) return;

    const isEuCountry = EU_COUNTRY_CODES.find((code) => code === foundCountry.code);

    setValue("countryCode", foundCountry.code);
    clearErrors(`countryCode`);

    const documentType = isEuCountry ? "VAT" : "TAX";

    setDocumentType(documentType);
  }

  function handleSelectAddress(address: AddressSuggestionDetails) {
    const data = {
      addressLine: address.formattedAddress,
      city: address.city || undefined,
      zipCode: address.postalCode || undefined,
      streetAndNumber: ``,
    };

    if (address.route) {
      const streetAndNumber = [address.route];

      if (address.streetNumber) streetAndNumber.push(address.streetNumber);

      data.streetAndNumber = streetAndNumber.join(", ");
    }

    if (data.addressLine) setValue("addressLine", data.addressLine);
    if (data.city) setValue("city", data.city);
    if (data.zipCode) setValue("zipCode", data.zipCode);
    if (data.streetAndNumber) setValue("streetAndNumber", data.streetAndNumber);
  }

  async function handleSelectVatId(vatId: string) {
    if (documentTypeWatch !== `VAT` || loadingValidatVat) return;

    setLoadingValidatVat(true);

    const formattedVatId = vatId.replace(/\s/g, "").trim();

    const vatValidationResponse = await validateVatId({
      vat_id: formattedVatId,
      country_code: countryCodeWatch.toUpperCase(),
      company_name: companyNameWatch,
      company_zipcode: zipCodeWatch,
      company_city: cityWatch,
      company_street: streetAndNumberWatch,
    });

    if (!vatValidationResponse.valid && documentTypeWatch === "VAT") {
      setError("vatId", { type: "manual", message: vatValidationResponse.code || "Invalid VAT ID" });
      setLoadingValidatVat(false);
      return;
    }

    clearErrors("zipCode");
    setValue("zipCode", zipCodeWatch);
    setValue("vatId", formattedVatId);
    setLoadingValidatVat(false);
  }

  const selectCountryInfo = COUNTRIES.find((c) => c.code === countryCodeWatch);

  const isTaxAndVatEnabled = !!selectCountryInfo?.name && !!zipCodeWatch && !!cityWatch && !!streetAndNumberWatch;

  return (
    <Modal
      open={isOpen}
      className="z-[50] w-full"
      style={{
        maxWidth: "600px",
        borderRadius: "52px",
        maxHeight: "100vh",
        backgroundColor: "#F0F0EF",
      }}
      onOpenChange={handleClose}
      classNameOverlay="z-[40]"
    >
      <div className="p-5 max-h-[75vh] overflow-auto">
        <div className="flex justify-end w-full">
          <button onClick={handleClose}>
            <Clear className="size-6 fill-primary" />
          </button>
        </div>

        {step === 1 && (
          <form onSubmit={handleSubmit(handleNextStep)} className="flex flex-col gap-6">
            <div>
              <p className="text-[28px] text-primary font-bold">Re-issue invoice</p>
              <span className="text-sm text-on-surface-01">* Mandatory fields</span>
            </div>

            <Input
              label={t("companyName")}
              placeholder={t("companyName")}
              {...register("companyName")}
              errorMessage={errors.companyName?.message}
              rightIcon={
                !errors.companyName &&
                getValues("companyName") && <Check width={20} height={20} className="fill-tonal-green-40" />
              }
              variant={errors.companyName && "error"}
            />

            <div className="grid md:grid-cols-2 w-full gap-8">
              <div className="space-y-2 relative z-[60]">
                <p className="text-primary">{t("country")}</p>
                {/* TODO: Fix combobox popover content to show inside the modal */}
                <Combobox
                  items={COUNTRIES.map((c) => ({ label: c.name, value: c.name, flag: c.flag }))}
                  placeholder={t("country")}
                  emptyText={t("noCountry")}
                  searchText={t("searchCountry")}
                  value={selectCountryInfo?.name}
                  onSelect={handleSelectCountry}
                  invalid={!!errors.countryCode}
                  renderItem={(item) => (
                    <div className="flex items-center gap-3 text-primary">
                      <CountryIcon country={{ name: item.label, flag: item.flag }} className="size-6" />
                      {item.label}
                    </div>
                  )}
                />
                {!!errors.countryCode && (
                  <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                    <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                      {errors.countryCode.message}
                    </span>
                  </div>
                )}
              </div>
              <AutocompleteAddressInput
                key={addressInputKey}
                defaultValue={addressLineWatch}
                countryCode={countryCodeWatch}
                onChangeAddressLine={(addressLine) => {
                  clearErrors("city");
                  clearErrors("zipCode");
                  clearErrors("streetAndNumber");

                  if (!addressLine) {
                    setError("addressLine", { message: t("validAddress") });
                  } else {
                    clearErrors("addressLine");
                  }
                }}
                onSelectAddress={handleSelectAddress}
                isError={!!errors.addressLine}
                errorMessage={errors.addressLine?.message}
              />
            </div>

            <div className="grid md:grid-cols-2 w-full gap-8">
              <Input
                label={t("city") + "*"}
                placeholder={t("city")}
                rightIcon={
                  !errors.city && getValues("city") && <Check width={20} height={20} className="fill-tonal-green-40" />
                }
                errorMessage={errors.city?.message}
                {...register("city")}
                variant={errors.city && "error"}
                enabled={!!countryCodeWatch && !!addressLineWatch}
              />
              <Input
                label={t("zipCode") + "*"}
                placeholder={t("zipCode")}
                errorMessage={errors.zipCode?.message}
                {...register("zipCode")}
                rightIcon={
                  !errors.zipCode &&
                  getValues("zipCode") && <Check width={20} height={20} className="fill-tonal-green-40" />
                }
                variant={errors.zipCode && "error"}
                enabled={!!countryCodeWatch && !!addressLineWatch}
              />
            </div>

            <div className="grid md:grid-cols-2 w-full gap-8">
              <Input
                label={t("streetAndNumber")}
                placeholder={t("streetAndNumber")}
                rightIcon={
                  !errors.streetAndNumber &&
                  getValues("streetAndNumber") && <Check width={20} height={20} className="fill-tonal-green-40" />
                }
                errorMessage={errors.streetAndNumber?.message}
                {...register("streetAndNumber")}
                variant={errors.streetAndNumber && "error"}
                enabled={!!countryCodeWatch && !!addressLineWatch}
              />
              <Input
                label={t("additionalAddress")}
                placeholder={t("additionalAddress")}
                errorMessage={errors.additionalAddressLine?.message}
                rightIcon={
                  !errors.additionalAddressLine &&
                  getValues("additionalAddressLine") && <Check width={20} height={20} className="fill-tonal-green-40" />
                }
                {...register("additionalAddressLine", {
                  setValueAs: (value) => {
                    return value === "" ? undefined : value;
                  },
                })}
                variant={errors.additionalAddressLine && "error"}
              />
            </div>

            <div className="grid md:grid-cols-2 w-full gap-8">
              <div>
                <CustomRadio
                  checked={documentTypeWatch === "VAT"}
                  label={"VAT ID"}
                  onChange={() => setDocumentType("VAT")}
                  disabled={!isTaxAndVatEnabled}
                />
                <Input
                  label=""
                  placeholder={t("vatId")}
                  enabled={isTaxAndVatEnabled && documentTypeWatch === "VAT"}
                  variant={
                    documentTypeWatch === "VAT" && errors.vatId
                      ? "error"
                      : documentTypeWatch !== "VAT"
                        ? "disabled"
                        : "enabled"
                  }
                  rightIcon={
                    (loadingValidatVat && <CgSpinnerAlt size={20} className="animate-spin text-primary" />) ||
                    (!errors.vatId && getValues("vatId") && (
                      <Check width={20} height={20} className="fill-tonal-green-40" />
                    ))
                  }
                  errorMessage={documentTypeWatch === "VAT" && errors.vatId?.message}
                  {...register("vatId", {
                    onBlur: (e) => handleSelectVatId(e.target.value),
                  })}
                />
              </div>
              <div>
                <CustomRadio
                  checked={documentTypeWatch === "TAX"}
                  onChange={() => setDocumentType("TAX")}
                  label={"TAX Number"}
                  disabled={!isTaxAndVatEnabled}
                />
                <Input
                  label=""
                  placeholder={t("taxNumber")}
                  errorMessage={documentTypeWatch === "TAX" && errors.taxNumber?.message}
                  enabled={isTaxAndVatEnabled && documentTypeWatch === "TAX"}
                  variant={
                    documentTypeWatch === "TAX" && errors.taxNumber
                      ? "error"
                      : documentTypeWatch !== "TAX"
                        ? "disabled"
                        : "enabled"
                  }
                  rightIcon={
                    !errors.taxNumber &&
                    getValues("taxNumber") && <Check width={20} height={20} className="fill-tonal-green-40" />
                  }
                  {...register("taxNumber")}
                />
                {vatInfoMessage && <p className="text-success text-sm mt-2">{vatInfoMessage}</p>}
              </div>
            </div>

            <div className="flex justify-end">
              <Button type="submit" color="dark-blue" variant="filled" size="medium">
                {t("continue")}
              </Button>
            </div>
          </form>
        )}

        {step === 2 && formData && (
          <ConfirmationStep
            invoiceId={data?.invoiceId || 0}
            onConfirm={handleConfirm}
            loading={loading}
            handleBack={handleBack}
          />
        )}

        {step === 3 && (
          <div className="flex flex-col gap-6 mt-4">
            <div className="flex flex-row gap-4 items-center">
              <Plant className="size-8 fill-success" />
              <p className="text-title-2 text-primary font-bold mt-2">{t("invoiceGenerated")}</p>
            </div>
            <div className="flex justify-end">
              <Button type="button" color="yellow" variant="filled" size="medium" onClick={handleClose}>
                {t("close")}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
