import { createColumnHelper } from "@tanstack/react-table";
import { Launch, Elipse } from "@arthursenno/lizenzero-ui-react/Icon";
import { CountryIcon } from "@/components/_common/country-icon";
import Link from "next/link";
import { ListCustomer } from "@/lib/api/customer/types";
import { ContactInformationModal } from "../customer-profile/contact-information-modal";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useTranslations } from "next-intl";

export function getServiceType(contract: ListCustomer["contracts"][number]) {
  if (contract.type === "EU_LICENSE") return "License Service";

  if (contract.type === "ACTION_GUIDE") return "Action Guide";

  if (contract.type === "DIRECT_LICENSE") return "Direct License";

  return "";
}

export function getStatusLabel(status: "ACTIVE" | "TERMINATED" | "TERMINATION_PROCESS") {
  if (status === "ACTIVE") return "Active";
  if (status === "TERMINATED") return "Terminated";
  if (status === "TERMINATION_PROCESS") return "Termination Process";
  return "";
}

export function useCustomersTableColumns() {
  const t = useTranslations("CustomerProfileHeader");
  const c = useTranslations("common");
  const columnHelper = createColumnHelper<ListCustomer>();

  const { changeParam } = useQueryFilter(["customer_id"]);

  function handleSelectCustomer(customerId: number) {
    changeParam("customer_id", customerId.toString());
  }

  return [
    columnHelper.display({
      id: "company",
      cell: (info) => {
        const company = info.row.original.companies[0];
        return (
          <div className="flex flex-col gap-1">
            <Link href={`/customers/${info.row.original.id}`} className="block flex-none">
              <div className="text-sm text-blue-600 hover:underline">{company?.name ?? " "}</div>
            </Link>
            <div className="text-xs font-medium text-tonal-dark-cream-50">#{company?.id ?? " "}</div>
          </div>
        );
      },
      header: t("company"),
    }),
    columnHelper.display({
      id: "country",
      cell: (info) => {
        const countries = info.row.original.contracts.reduce(
          (acc, contract) => {
            const licensesSort = contract.licenses.sort((a, b) => b.contract_status.localeCompare(a.contract_status));

            licensesSort.forEach((license) => {
              if (acc.find((country) => country.country_code === license.country_code)) return;
              acc.push({
                country_code: license.country_code,
                country_name: license.country_name,
                country_flag: license.country_flag,
              });
            });

            contract.action_guides.forEach((actionGuide) => {
              if (acc.find((country) => country.country_code === actionGuide.country_code)) return;

              acc.push({
                country_code: actionGuide.country_code,
                country_name: actionGuide.country_name,
                country_flag: actionGuide.country_flag,
              });
            });

            return acc;
          },
          [] as { country_code: string; country_name: string; country_flag: string }[]
        );

        return (
          <div className="flex gap-2 pl-4">
            {countries?.slice(0, 2).map((country) => (
              <div key={country.country_code}>
                <CountryIcon country={{ name: country.country_name, flag: country.country_flag }} />
              </div>
            ))}
            {countries && countries?.length > 2 && (
              <div className="size-6 flex items-center justify-center rounded-full bg-secondary text-primary text-xs font-medium border-[1.5px] border-primary">
                +{countries.length - 2}
              </div>
            )}
          </div>
        );
      },
      header: t("country"),
    }),
    columnHelper.display({
      id: "contact",
      cell: (info) => (
        <div className="flex flex-col gap-1 pl-4">
          <div className="flex items-center gap-2 text-sm">
            <span className="mt-0.5">{`${info.row.original.first_name} ${info.row.original.last_name}`}</span>
            <button
              type="button"
              className="p-px rounded-full hover:bg-support-blue/15"
              onClick={() => handleSelectCustomer(info.row.original.id)}
            >
              <Launch className="fill-support-blue w-4 h-4" />
            </button>
          </div>
          <div className="text-xs font-medium text-tonal-dark-cream-50">{info.row.original.email ?? ""}</div>
          <ContactInformationModal customer={info.row.original} />
        </div>
      ),
      header: "Contact",
    }),
    columnHelper.display({
      id: "licenseYear",
      // TODO : add license year from data
      cell: () => <span className="text-primary text-sm pl-4">2024</span>,
      header: t("licenseYear"),
    }),
    columnHelper.display({
      id: "termination",
      cell: (info) => {
        const contracts = info.row.original.contracts;

        const termination = (() => {
          for (const contract of contracts) {
            if (contract.termination) return contract.termination;

            if (contract.licenses) {
              for (const license of contract.licenses) {
                if (license.termination) return license.termination;
              }
            }

            if (contract.action_guides) {
              for (const actionGuide of contract.action_guides) {
                if (actionGuide.termination) return actionGuide.termination;
              }
            }
          }

          return null;
        })();

        const terminationLabel = termination
          ? new Date(termination.created_at).toLocaleDateString()
          : "Automatic renewal";

        return <span className="text-primary text-sm pl-4">{terminationLabel}</span>;
      },
      header: t("terminationDate"),
    }),
    columnHelper.display({
      id: "serviceType",
      cell: (info) => (
        <div className="flex flex-col gap-1 pl-4">
          {info.row.original.contracts.map((contract) => (
            <span key={contract.id} className="text-primary text-sm text-nowrap">
              {getServiceType(contract)}
            </span>
          ))}
        </div>
      ),
      header: t("serviceType"),
    }),
    columnHelper.display({
      id: "customerStatus",
      cell: (info) => {
        const contracts = info.row.original.contracts;

        const status = (() => {
          for (const contract of contracts) {
            if (contract.status !== "ACTIVE") return contract.status;

            if (contract.licenses) {
              for (const license of contract.licenses) {
                if (license.contract_status !== "ACTIVE") return license.contract_status;
              }
            }

            if (contract.action_guides) {
              for (const actionGuide of contract.action_guides) {
                if (actionGuide.contract_status !== "ACTIVE") return actionGuide.contract_status;
              }
            }
          }

          return "ACTIVE";
        })();

        const statusLabel = getStatusLabel(status);

        return (
          <div
            data-status={status}
            className="text-sm group flex items-center gap-1 pl-4 text-tonal-dark-cream-30 data-[status=ACTIVE]:text-success"
          >
            <Elipse className="flex-none fill-tonal-dark-cream-30 group-data-[status=ACTIVE]:fill-success size-2" />
            <p className="mt-0.5 font-bold text-tonal-dark-cream-30 group-data-[status=ACTIVE]:text-success">
              {statusLabel}
            </p>
          </div>
        );
      },
      header: t("customerStatus"),
    }),
  ];
}
