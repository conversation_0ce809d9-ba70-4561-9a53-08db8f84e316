// status-filter.tsx
import React from "react";
import { Dropdown } from "@/components/_common/dropdown";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { FilterAlt, KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { cn } from "@/lib/utils";

export interface StatusFilterType {
  label: string;
  value: string;
}

export interface StatusFilterProps {
  filters: StatusFilterType[];
  showFilterIcon?: boolean;
  triggerClassName?: string;
}

function StatusFilter({ filters, showFilterIcon = true, triggerClassName }: StatusFilterProps) {
  const { paramValues, changeParams } = useQueryFilter(["status"]);

  const currentStatus = paramValues.status || filters[0].value;
  const currentStatusFilter = filters.find((filter) => filter.value === currentStatus) || filters[0];

  const handleStatusChange = (statusFilter: StatusFilterType) => {
    changeParams({
      page: "1",
      status: statusFilter.value,
    });
  };

  return (
    <Dropdown
      trigger={
        <button className={cn("flex items-center gap-2 text-support-blue font-bold text-base", triggerClassName)}>
          {showFilterIcon && <FilterAlt width={20} height={20} className="fill-support-blue" />}
          <span>{currentStatusFilter?.label}</span>
          <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
        </button>
      }
    >
      {filters.map((statusFilter, idx) => (
        <DropdownMenu.Item
          key={idx}
          className="group py-5 px-4 text-base focus:outline-none cursor-pointer hover:bg-surface-02"
          onClick={() => handleStatusChange(statusFilter)}
          style={{
            color: statusFilter.value === currentStatus ? "#002652" : "#242423",
            fontWeight: statusFilter.value === currentStatus ? "bold" : "normal",
          }}
        >
          {statusFilter.label}
        </DropdownMenu.Item>
      ))}
    </Dropdown>
  );
}

export default StatusFilter;
