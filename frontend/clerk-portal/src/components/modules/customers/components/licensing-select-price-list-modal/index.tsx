"use client";

import { usePathname, useRouter } from "@/i18n/navigation";
import { PriceList } from "@/lib/api/commitment/types";
import { getPriceList as getPriceListApi } from "@/lib/api/price-list";
import { cn } from "@/lib/utils";
import { formatCurrency } from "@/utils/format-currency";
import { formatDate } from "@/utils/format-date";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, RadioSelected, RadioUnselected } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useParams, useSearchParams } from "next/navigation";
import { enqueueSnackbar } from "notistack";
import { useEffect, useState } from "react";
import { BiLoader } from "react-icons/bi";

type Step = "form" | "confirm" | "success";

interface LicensingSelectPriceListModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialPriceListSelected: PriceList | null;
  onPriceListSelected: (priceList: PriceList | null) => void;
  onConfirm: () => Promise<{ message: string; success: boolean }>;
  isLoadingConfirm?: boolean;
}

export function LicensingSelectPriceListModal({
  isOpen,
  onClose,
  initialPriceListSelected,
  onPriceListSelected,
  onConfirm,
  isLoadingConfirm,
}: LicensingSelectPriceListModalProps) {
  const session = useSession();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const t = useTranslations("LicensingSelectPriceListModal");
  const c = useTranslations("common");
  const [priceList, setPriceList] = useState<PriceList[]>([]);
  const [priceListSelected, setPriceListSelected] = useState<PriceList | null>(initialPriceListSelected || null);
  const [isLoading, setIsLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [step, setStep] = useState<Step>("form");

  const isModalOpen = isOpen;

  const params = useParams<{ customerId: string }>();
  const customerId = params.customerId;

  useEffect(() => {
    if (!isModalOpen) setStep("form");
  }, [isModalOpen]);

  function handleCloseModal() {
    onClose();
  }

  function handleCloseSuccessModal() {
    handleCloseModal();

    router.push(`/customers/${customerId}`);
  }

  function handleOnOpenChange(open: boolean) {
    if (!open) handleCloseModal();
  }

  const getPriceList = async () => {
    if (!session.data?.user?.id) return;

    try {
      setIsLoading(true);
      const res = await getPriceListApi(session.data?.user?.access_token, {
        service_type: "EU_LICENSE",
      });
      setPriceList(res.filter((priceList: PriceList) => priceList.handling_fee));
    } catch (error) {
      enqueueSnackbar(t("errorFetchingPriceList"), { variant: "error" });
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (step === "form" && isModalOpen) {
      getPriceList();
    }
  }, [step, isModalOpen]);

  const filteredPriceList = priceList.filter((priceList) =>
    JSON.stringify(priceList).toLowerCase().includes(search.toLowerCase())
  );

  const handleContinue = () => {
    if (!priceListSelected) {
      enqueueSnackbar(t("pleaseSelectPriceList"), { variant: "error" });
      return;
    }

    setStep("confirm");
    onPriceListSelected(priceListSelected);
  };

  const handleConfirm = async () => {
    const res = await onConfirm();

    if (!res.success) return enqueueSnackbar(res.message, { variant: "error" });

    setStep("success");
  };

  const stepForm = (
    <>
      <div className="mb-10 space-y-4">
        <h3 className="font-bold text-[1.75rem] text-primary">{t("selectPriceList")}</h3>
        <p className="text-tonal-dark-cream-20">{t("selectPriceListDescription")}</p>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center gap-1 md:gap-2 px-2 py-3 border-[1px] border-tonal-dark-cream-80 rounded-md">
          <BiLoader className="fill-primary animate-spin" />
          <p className="text-center text-primary">{t("loadingPriceList")}</p>
        </div>
      ) : (
        <form className="flex flex-col gap-10">
          <Input placeholder={t("search")} value={search} onChange={(e: any) => setSearch(e.target.value)} />

          <div className="h-[300px] overflow-auto">
            <ul className="space-y-4">
              {filteredPriceList.map((priceList, idx) => (
                <li key={idx} className="flex items-start gap-2 py-4">
                  <label className="flex flex-1 items-center justify-between">
                    <input
                      type="radio"
                      name={`item-price-${priceList.id}`}
                      onChange={() => setPriceListSelected(priceList)}
                      value={priceList.id}
                      checked={priceListSelected?.id === priceList.id}
                      className="hidden peer"
                    />
                    <div
                      className="flex items-center gap-4 group"
                      data-checked={priceListSelected?.id === priceList.id}
                    >
                      <RadioSelected className="hidden size-5 fill-primary group-data-[checked=true]:block" />
                      <RadioUnselected className="block cursor-pointer group-data-[checked=true]:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error" />
                      <div className="flex flex-col gap-1 px-2">
                        <span className="text-sm text-[#002652]">{priceList.name}</span>
                        <strong className="text-[#656773] text-xs font-medium">
                          Created in: {formatDate(priceList.created_at)}
                        </strong>
                      </div>
                    </div>

                    {/* <div className="text-sm text-primary px-2">A</div> */}
                    <div className="text-sm text-primary px-2">{formatCurrency(priceList.basic_price || 0)}</div>

                    <div className="flex flex-col px-2">
                      <span className="text-sm text-[#002652]">{priceList?.type?.replaceAll(`_`, ` `)}</span>
                      {/* <span className="text-sm text-[#808FA9]">B2B Pack.</span> */}
                    </div>
                  </label>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex items-center justify-end gap-6 w-full">
            <Button
              type="button"
              color="dark-blue"
              variant="outlined"
              size="medium"
              onClick={handleCloseModal}
              className="w-full md:max-w-[200px]"
            >
              {c("cancel")}
            </Button>
            <Button
              type="button"
              color="yellow"
              variant="filled"
              size="medium"
              className="w-full md:max-w-[200px]"
              onClick={handleContinue}
            >
              {c("continue")}
            </Button>
          </div>
        </form>
      )}
    </>
  );
  const stepConfirm = (
    <>
      <div className="mb-10 space-y-4">
        <h3 className="font-bold text-[1.75rem] text-primary">{t("confirmAction")}</h3>
        <p className="text-tonal-dark-cream-20">
          {t("addingNew")}
          <br />
          <strong>{formatCurrency(priceListSelected?.basic_price || 0)}</strong> {t("toTheCustomer")}
          <br />
          {t("invoiceWillBeGenerated")}
          <br /> <br />
          {t("pleaseConfirm")}
        </p>
      </div>

      <div className="flex items-center justify-end gap-6 w-full">
        <Button type="button" color="dark-blue" variant="outlined" size="medium" onClick={() => setStep("form")}>
          {c("back")}
        </Button>
        <Button
          type="button"
          color="yellow"
          variant="filled"
          size="medium"
          onClick={handleConfirm}
          disabled={isLoadingConfirm}
        >
          {isLoadingConfirm ? c("loading") : c("confirm")}
        </Button>
      </div>
    </>
  );
  const stepSuccess = (
    <div className="space-y-10 pt-8">
      <div className="flex items-center gap-2">
        <Image alt="Leaf seal" src="/assets/images/leaf-seal.svg" width={35} height={36} />
        <h3 className="mt-1.5 flex-1 font-bold text-[1.75rem] text-primary">{t("addedWithSuccess")}</h3>
      </div>
      <div className="flex justify-end">
        <Button type="button" color="yellow" variant="filled" size="medium" onClick={handleCloseSuccessModal}>
          {c("close")}
        </Button>
      </div>
    </div>
  );

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className={cn("z-50 !rounded-[52px] w-full max-w-[672px] !py-8 !px-9")}
    >
      <div className="flex items-center justify-end">
        <Clear onClick={handleCloseModal} className="w-8 h-8 fill-primary hover:opacity-75 cursor-pointer" />
      </div>
      {step === "form" && stepForm}
      {step === "confirm" && stepConfirm}
      {step === "success" && stepSuccess}
    </Modal>
  );
}
