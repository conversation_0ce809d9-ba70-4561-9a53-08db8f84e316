import Image from "next/image";
import { useTranslations } from "next-intl";
import { formatDate } from "@/utils/format-date";

export function DownloadOfferCard() {
  const t = useTranslations("common");
  return (
    <div className="relative flex flex-col gap-6 py-10 pl-7 pr-10 w-full rounded-4xl bg-white shadow-[0px_2px_6px_rgba(0,0,0,0.1),_0px_1px_2px_rgba(0,0,0,0.25)]">
      <h3 className="text-primary text-2xl font-bold">{t("downloadOffer")}</h3>

      <div className="flex items-end gap-1 py-3 mr-16 border-b border-tonal-dark-cream-80">
        <Image src="/assets/images/xlsx.svg" alt="Download offer" width={48} height={48} />

        <div className="flex flex-1 items-start justify-between">
          <div className="flex flex-col">
            <strong className="font-bold text-sm text-tonal-dark-cream-20">{t("clientOffer")}</strong>
            <span className="text-sm text-tonal-dark-cream-50">{formatDate(new Date())}</span>
          </div>
          {/* Download */}
          <button type="button" className="text-primary">
            {t("download")}
          </button>
        </div>
      </div>

      {/* <div className="absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2 bg-white w-12 h-12 rounded-full flex items-center justify-center shadow-[0px_2px_6px_rgba(0,0,0,0.1),_0px_1px_2px_rgba(0,0,0,0.25)]">
        <div className="bg-tertiary w-4 h-4 rounded-full text-primary">a</div>
      </div> */}
    </div>
  );
}
