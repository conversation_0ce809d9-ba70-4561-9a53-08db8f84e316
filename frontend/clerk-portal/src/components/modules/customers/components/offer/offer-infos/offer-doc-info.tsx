"use client";

import { StatusType } from "@/components/modules/country/components/task-type";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import {
  CheckBox,
  CheckBoxOutlineBlank,
  KeyboardArrowRight,
  KeyboardArrowUp,
} from "@arthursenno/lizenzero-ui-react/Icon";
import { OfferDocSelec, OfferDocSelecProps } from "./offer-doc-select";
import { OfferTitleStatus } from "./offer-title-status";
import { useState } from "react";
import { OfferModalSend } from "../components/offer-modal-send";
import { useTranslations } from "next-intl";

export interface OfferDocInfoProps extends Omit<OfferDocSelecProps, `status`> {
  onClickEdit?: () => void;
  onClickSend?: () => void;
  onClickCancel?: () => void;
  title?: string;
  offerStatus?: StatusType;
  uploadType?: boolean;
}

export const OfferDocInfo = ({
  onClickSend = () => {},
  onClickCancel = () => {},
  offerStatus,
  title,
  showDownload,
  docFile,
  uploadType,
}: OfferDocInfoProps) => {
  const offerAccepted = offerStatus === `Accepted`;

  const [checked, setChecked] = useState(false);
  const [isOpenModalSend, setIsOpenModalSend] = useState(false);
  const t = useTranslations("OfferModalEdit");
  const c = useTranslations("common");

  return (
    <div>
      <div className="flex">
        <OfferTitleStatus
          title={title || (uploadType && `Upload offer`) || `Offer ${offerAccepted ? `Accepted` : `status`}`}
          status={(!offerAccepted && offerStatus) || undefined}
        />
        <Button color="light-blue" size="small" variant="text" className="!font-normal" onClick={onClickCancel}>
          {c("cancel")}
        </Button>
      </div>

      <OfferDocSelec showDownload={showDownload || offerAccepted} docFile={docFile} />

      {uploadType && (
        <>
          <div className="flex items-center gap-2 mt-6">
            <label className="cursor-pointer pt-1">
              <input type="checkbox" className="hidden" checked={checked} onClick={() => setChecked((curr) => !curr)} />
              {checked ? (
                <CheckBoxOutlineBlank className="text-on-tertiary size-5" />
              ) : (
                <CheckBox className="text-on-tertiary size-5" />
              )}
            </label>
            <p className="text-on-tertiary">{t("sendOfferAsPdf")}</p>
          </div>

          <div className="w-full flex justify-end mt-6">
            <Button
              color="yellow"
              variant="filled"
              size="medium"
              trailingIcon={<KeyboardArrowRight className="size-5 fill-primary" />}
              onClick={() => setIsOpenModalSend(true)}
            >
              {t("sendOfferToClient")}
            </Button>
          </div>
          <OfferModalSend isOpen={isOpenModalSend} setIsOpen={setIsOpenModalSend} onClickSend={onClickSend} />
        </>
      )}

      {offerStatus === `Declined` && (
        <div className="flex justify-end mt-5">
          <div className="flex flex-col justify-between w-2/4 gap-6 h-full">
            <Button
              color="yellow"
              variant="filled"
              size="medium"
              trailingIcon={<KeyboardArrowRight className="size-5 fill-primary" />}
              onClick={() => setIsOpenModalSend(true)}
            >
              {t("editOffer")}
            </Button>
            <Button color="dark-blue" variant="outlined" size="medium">
              {t("uploadOffer")}
            </Button>
          </div>
        </div>
      )}

      {offerAccepted && (
        <details className="group mt-6">
          <summary className="flex justify-center items-center">
            <p className="text-[#808FA9] font-bold text-sm">{t("showOfferHistory")}</p>
            <KeyboardArrowUp className="flex-none size-7 fill-[#808FA9] group-open:-rotate-180 transition-all duration-300" />
          </summary>
          <div className="flex flex-col gap-2">
            <OfferDocSelec status={`OfferDenied`} />
            <OfferDocSelec status={`OfferDenied`} />
            <OfferDocSelec status={`Accepted`} />
          </div>
        </details>
      )}
    </div>
  );
};
