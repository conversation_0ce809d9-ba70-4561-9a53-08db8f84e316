import { CountryIcon } from "@/components/_common/country-icon";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { Dispatch, SetStateAction } from "react";
import { OfferCommitment } from "./offer-commitment";
import { OfferPackaging } from "./offer-packaging";

interface OfferModalEditCountryProps {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  country: { name: string; flag: string };
}

export const OfferModalEditCountry = ({ isOpen, setIsOpen, country }: OfferModalEditCountryProps) => {
  const handleCloseModal = () => setIsOpen(false);

  return (
    <Modal
      open={isOpen}
      onOpenChange={handleCloseModal}
      className="z-50 bg-surface-02"
      style={{ width: `100vw`, height: `100vh`, maxWidth: `100vw`, maxHeight: `100vh` }}
    >
      <div className="flex items-center justify-end">
        <Clear
          onClick={handleCloseModal}
          className="w-8 h-8 fill-primary hover:opacity-75 cursor-pointer bg-white rounded-full"
        />
      </div>
      <div className="h-[90vh] w-full overflow-auto">
        <div className="flex flex-col justify-start items-center">
          <div className="max-w-xl">
            <div className="flex flex-col items-center gap-4">
              <div className="flex items-center gap-6 w-full">
                <CountryIcon country={country} className="size-6" />
                <p className="text-primary text-xl font-bold">{country.name}</p>
              </div>
              <div className="flex flex-col gap-4 w-full">
                <p className="text-primary text-[40px] font-bold">
                  Edit answers <br />
                  Commitment Assessment
                </p>
                <p className="text-primary">See the customer&apos;s answers.</p>
              </div>
            </div>
            <div className="mt-8 flex flex-col justify-start gap-6">
              <OfferCommitment componentAlone />
              <OfferPackaging name="Sales Packaging" isEdit />
              <OfferPackaging name="B2B Packaging" isEdit />
              <div className=" flex justify-end gap-3">
                <Button color="dark-blue" variant="outlined" size="small" onClick={handleCloseModal}>
                  Cancel
                </Button>
                <Button color="yellow" variant="filled" size="small" onClick={handleCloseModal}>
                  Save
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};
