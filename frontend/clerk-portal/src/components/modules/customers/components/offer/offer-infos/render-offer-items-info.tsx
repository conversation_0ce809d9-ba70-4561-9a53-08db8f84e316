import { CountryIcon } from "@/components/_common/country-icon";
// import { PriceInput } from "@/components/ui/PriceInput";
import { getCountryByCode } from "@/lib/api/country";
// import { useState } from "react";
// import { OfferModalEdit } from "../components/offer-modal-edit";
import { Delete, File, Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";
import { IOffer } from "@/lib/api/offers/types";
import { formatCurrency } from "@/utils/format-currency";
import { useTranslations } from "next-intl";

interface ExpenseData {
  feeName: string;
  price: number;
}

interface CountryData {
  countryCode: string;
  expenses: ExpenseData[];
}

interface RenderCountryInfoProps {
  country: CountryData;
}

export const RenderCountryInfo = async ({ country }: RenderCountryInfoProps) => {
  // const [enabledEdition, setEnabledEdition] = useState(false);
  // const [isOpenModalEdit, setIsOpenModalEdit] = useState(false);
  // const [prices, setPrices] = useState<number[]>(country.expenses.map(expense => expense.price));

  const t = useTranslations("OfferModalEdit");
  const prices = country.expenses.map((expense) => expense.price);

  const { name, flag_url } = await getCountryByCode(country.countryCode);

  // const handleEdit = () => setEnabledEdition(true);

  // const handleCancelEdit = () => {
  //   setEnabledEdition(false);
  //   setPrices(country.expenses.map(expense => expense.price));
  // };

  // const handleSave = () => {
  //   setIsOpenModalEdit(false);
  //   setEnabledEdition(false);
  // };

  // const handleChanged = (value: number, idx: number) => {
  //   const newPrices = [...prices];
  //   newPrices[idx] = value;
  //   setPrices(newPrices);
  // };

  return (
    <div className="pt-3 px-4 pb-6 rounded-2xl border border-surface-03">
      <div>
        <div className="flex justify-between items-center">
          <div className="flex items-center py-4 gap-3">
            <CountryIcon country={{ name, flag: flag_url }} />
            <p className="text-primary font-bold mt-1">{name}</p>
          </div>
          {/* {enabledEdition ? (
            <button className="underline text-primary font-bold" onClick={() => setIsOpenModalEdit(true)}>
              Save changes
            </button>
          ) : (
            <button className="underline text-support-blue font-bold" onClick={handleEdit}>
              Edit price
            </button>
          )} */}
        </div>

        <hr className="text-on-surface-01 opacity-30" />

        {country.expenses.map((expense, idx) => (
          <div key={idx}>
            <div className="flex items-center py-4 gap-3 justify-between">
              <p className="text-primary text-sm">{expense.feeName}</p>
              {/* {enabledEdition ? (
                <div className="w-1/4">
                  <PriceInput
                    placeholder="€"
                    value={prices[idx]}
                  // onChange={(value) => handleChanged(value, idx)}
                  />
                </div>
              ) : ( */}
              <p className="text-primary text-sm font-bold flex-none">{formatCurrency(prices[idx])}</p>
              {/* )} */}
            </div>
            <hr className="text-on-surface-01 opacity-30" />
          </div>
        ))}
      </div>

      {/* <OfferModalEdit
        isOpen={isOpenModalEdit}
        setIsOpen={setIsOpenModalEdit}
        onClickChange={handleSave}
        onClickCancel={handleCancelEdit}
      /> */}
    </div>
  );
};

interface RenderOfferInfoProps {
  offer: IOffer;
}

export const RenderOfferItemsInfo = ({ offer }: RenderOfferInfoProps) => {
  const t = useTranslations("OfferModalEdit");
  return (
    <div>
      {!offer.offerItems || offer.offerItems.length === 0 ? (
        <div className="flex flex-col items-center justify-center mt-8">
          <Lightbulb className="size-10 fill-tonal-dark-cream-40" />
          <p className="text-tonal-dark-cream-40">{t("noOffersAvailable")}</p>
        </div>
      ) : (
        <div className="mt-5 flex flex-col gap-6">
          {offer.file && (
            <div className="flex items-center gap-2 w-full">
              <File className="size-5 fill-primary" />
              <p className="text-primary text-sm">{offer.file.name}</p>
              <Delete className="size-5 fill-primary ml-auto" />
            </div>
          )}

          {offer.offerItems.map((item, itemIdx) => (
            <div key={itemIdx} className="p-1 rounded-lg">
              <p className="text-lg font-bold text-primary mb-4">{item.categoryName || `Category ${itemIdx + 1}`}</p>

              {item.countriesOffers?.map((country, countryIdx) => (
                <RenderCountryInfo country={country} key={countryIdx} />
              ))}
            </div>
          ))}

          <div className="text-sm flex justify-between items-center">
            <p className="text-sm text-primary font-bold">{t("paymentConditions")}</p>
            <p className="text-sm text-primary font-bold">{offer.paymentCondition}</p>
          </div>

          <div className="flex justify-between bg-[#D1EDF7] py-4 px-4 rounded-lg items-center">
            <p className="text-primary font-bold">{t("total")}</p>
            <p className="text-xl text-primary font-bold">{formatCurrency(offer.total || 0)}</p>
          </div>
        </div>
      )}
    </div>
  );
};
