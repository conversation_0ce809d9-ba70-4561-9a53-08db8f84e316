"use client";

import { WorkspacePremium } from "@arthursenno/lizenzero-ui-react/Icon";
import { useState } from "react";
import { OfferRenderItems, OfferRenderItemsProps } from "./offer-render-items";
import { FlowConfirmOfferModal } from "./check-offers-modal";
import { cn } from "@/lib/utils";

interface OfferItemsProps extends Omit<OfferRenderItemsProps, "showGenerate"> {
  initialShowItems?: boolean;
  className?: string;
}

export const OfferInfos = ({ initialShowItems = false, className, ...props }: OfferItemsProps) => {
  const [showGenerate, setShowGenerate] = useState(initialShowItems);

  return (
    <div className={cn("flex flex-row items-center relative", className)}>
      <div className="flex flex-col gap-6 drop-shadow-md bg-white w-full py-10 pl-7 pr-10 rounded-4xl min-h-[200px]">
        <OfferRenderItems showGenerate={showGenerate} setShowGenerate={setShowGenerate} {...props} />
      </div>
      <div className="hidden md:block">
        <div className="absolute top-[40px]">
          <div className="bg-on-surface-04 w-12 h-12 rounded-full absolute z-10 my-auto  right-2 top-0 bottom-0 flex justify-center items-center">
            <WorkspacePremium className="fill-white size-7" />
          </div>
          <svg
            className="drop-shadow-[5px_2px_2px_rgba(0,0,0,0.05)]"
            width="40"
            height="128"
            viewBox="0 0 40 128"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M-1.52638e-06 0C-1.38053e-06 12.2314 7.07429 22.8064 17.3429 27.8331C17.3886 27.856 17.2971 27.8101 17.3429 27.8331C30.6743 34.3385 40 48.129 40 64C40 79.871 30.6743 93.6615 17.3429 100.167C17.2971 100.19 17.3886 100.144 17.3429 100.167C7.07429 105.194 -1.45858e-07 115.769 0 128L-1.52638e-06 0Z"
              fill="white"
            />
          </svg>
        </div>
      </div>
      <FlowConfirmOfferModal />
    </div>
  );
};
