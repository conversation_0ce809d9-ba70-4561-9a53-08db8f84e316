import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { Dispatch, SetStateAction } from "react";
import { useTranslations } from "next-intl";

interface OfferModalSendProps {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  onClickSend: () => void;
  onClickCancel?: () => void;
}

export const OfferModalSend = ({ isOpen, setIsOpen, onClickSend, onClickCancel }: OfferModalSendProps) => {
  const handleOnOpenChange = () => {
    setIsOpen(false);
    onClickCancel?.();
  };

  const t = useTranslations("OfferModalEdit");
  const c = useTranslations("common");
  return (
    <Modal
      open={isOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !rounded-[52px] w-full !py-9 !px-9 !bg-surface-01 overflow-x-auto"
    >
      <div>
        <div className="flex flex-col gap-4">
          <p className="text-primary text-[28px] font-bold">{t("sendOffer")}</p>
          <p className="text-tonal-dark-cream-20">{t("confirmActionSendignOffer")}</p>
        </div>
        <div className="flex justify-end gap-3 mt-10">
          <Button color="dark-blue" variant="outlined" size="small" onClick={handleOnOpenChange}>
            {c("cancel")}
          </Button>
          <Button
            color="yellow"
            variant="filled"
            size="small"
            onClick={onClickSend}
            trailingIcon={<KeyboardArrowRight className="size-5 fill-primary" />}
          >
            {t("send")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
