import { OfferCountry } from "@/components/modules/customers/components/offer/offer-country";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add } from "@arthursenno/lizenzero-ui-react/Icon";

interface OfferServiceProps {
  name: string;
  showAddBtn?: boolean;
  countries: {
    name: string;
    flag: string;
  }[];
}

export const OfferService = ({ name, showAddBtn = true, countries }: OfferServiceProps) => {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between mt-14">
        <p className={`text-xl font-bold text-primary`}>{name}</p>
        {showAddBtn && (
          <Button variant="filled" color="yellow" size="small" leadingIcon={<Add className={"size-4 fill-primary"} />}>
            Add
          </Button>
        )}
      </div>

      <div className="flex flex-col gap-4">
        {countries.map((country, idx) => (
          <OfferCountry country={country} key={idx} />
        ))}
      </div>
    </div>
  );
};
