import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { useFormContext } from "react-hook-form";
import { useEffect, useState } from "react";
import { calculatorFormData } from "./calculator-provider";
import { CalculatorLicense } from "./interface";
import { useQueryFilter } from "@/hooks/use-query-filter";

interface CalculatorSubmitProps {
  countriesLicenses?: CalculatorLicense;
  onSubmit: () => void;
}

export function CalculatorSubmit({ countriesLicenses, onSubmit }: CalculatorSubmitProps) {
  const { paramValues, changeParam } = useQueryFilter(["selected-codes", "select-price-list"]);

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const selectedCountryCodes = paramValues?.["selected-codes"]?.split(",").filter(Boolean) ?? [];

  const [showError, setShowError] = useState(false);

  const items = countriesLicenses?.items;

  const {
    handleSubmit,
    formState: { errors },
  } = useFormContext<calculatorFormData>();

  if (!items) return null;

  const unconfirmedCommitmentError =
    errors && errors.items && Object.values(errors.items).find((i) => i?.commitment?.filled)?.commitment?.filled;

  const errorMessage = (() => {
    const itemsLicenses = Object.values(items).filter((i) => i.license);

    if (!itemsLicenses.length) return "Please add at least one country to continue your purchase.";

    if (itemsLicenses.some((i) => !i.license?.commitment.filled))
      return "Please fill the form to continue your purchase.";

    const hasAtLeastOnePackageAdded = itemsLicenses.some(
      (i) => Object.values(i.license?.packagingServices || {}).some((p) => p.added) && i.license?.commitment.filled
    );

    if (!hasAtLeastOnePackageAdded) return "Please add at least one packaging service to continue your purchase.";

    if (!errors || Object.keys(errors).length === 0) return null;

    if (errors.items?.minimum?.message) return errors.items?.minimum?.message;
    if (unconfirmedCommitmentError) return unconfirmedCommitmentError.message;

    return "Please fill the form to continue your purchase.";
  })();

  function handleContinue() {
    if (errorMessage) return setShowError(true);

    onSubmit();
  }

  //   useEffect(() => {
  //     setShowError(false);
  //   }, [items]);

  return (
    <div className="space-y-4">
      {!!errorMessage && showError && <p className="text-right text-error">{errorMessage}</p>}
      <div className="flex md:items-center md:justify-end">
        <Button
          type="button"
          variant="filled"
          size="medium"
          trailingIcon={<KeyboardArrowRight />}
          onClick={handleContinue}
          disabled={selectedCountryCodes.length === 0}
          className="w-full md:max-w-72"
          color={errorMessage && showError ? "red" : "yellow"}
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
