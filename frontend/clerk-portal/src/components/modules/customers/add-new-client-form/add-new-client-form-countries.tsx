"use client";

import { CountryInput } from "@/components/_common/forms/country-input";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { CountryCardRow } from "@/components/ui/country-card-row";
import { PublishedCountry, useLiberatedCountries } from "@/hooks/use-liberated-countries";
import { useEffect, useState } from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import { AddNewClientFormSchemaData } from "./add-new-client-form-provider";
import { useTranslations } from "next-intl";

export function AddNewClientFormCountries() {
  const [selectCountries, setSelectCountries] = useState<PublishedCountry[]>([]);

  const { liberatedCountries } = useLiberatedCountries();

  const {
    clearErrors,
    formState: { errors },
    watch,
  } = useFormContext<AddNewClientFormSchemaData>();
  const service = watch(`service`);

  const { append: appendToCountries, remove: removeFromCountries } = useFieldArray({
    name: "countries",
  });

  function handleSelectCountry(country: PublishedCountry) {
    const checkCountry = selectCountries.some((item) => item.name === country.name);

    if (checkCountry) return;

    setSelectCountries((current) => [...current, country]);
    appendToCountries(country.code);

    clearErrors(`countries`);
  }
  const t = useTranslations("AddNewClientFormCountries");

  function handleRemoveCountry(country: PublishedCountry, idx: number) {
    removeFromCountries(idx);
    setSelectCountries((current) => current.filter((curr) => curr.name !== country.name));

    clearErrors(`countries`);
  }

  useEffect(() => {
    if (service === `DIRECT_LICENSE`) {
      removeFromCountries();
      setSelectCountries([]);
      clearErrors(`countries`);
    }
  }, [service]);

  if (service === `DIRECT_LICENSE`) return null;

  return (
    <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7 gap-6">
      <div className="flex flex-col">
        <div className="flex items-center gap-2 mb-2">
          <p className="text-primary font-medium text-xl">{t("title")}</p>
          <QuestionTooltip>
            <QuestionTooltipDescription>
              Lorem ipsum dolor sit, amet consectetur adipisicing elit. Dolorem rem placeat tenetur dignissimos nisi,
              excepturi maxime ut enim debitis eveniet in repudiandae sit possimus impedit voluptatibus cum?
              Praesentium, officia accusantium.
            </QuestionTooltipDescription>
          </QuestionTooltip>
        </div>
        <p className="text-primary text-sm">{t("subtitle")}</p>
      </div>
      <div className="w-full">
        <CountryInput countries={liberatedCountries} onSelectCountry={handleSelectCountry} />
      </div>
      {!!selectCountries?.length && (
        <div className="w-full">
          <p className="text-sm text-tonal-dark-cream-30">
            <b>{selectCountries?.length.toString().padStart(2, `0`)}</b>
            {` `}
            {t("selected")}
          </p>
          <div className="flex flex-col gap-7 mt-6 w-full">
            {selectCountries?.map((country, idx) => (
              <CountryCardRow key={idx} country={country} onClickRemove={() => handleRemoveCountry(country, idx)} />
            ))}
          </div>
        </div>
      )}
      {!!errors.countries && (
        <div className="flex justify-start items-center mt-2.5 space-x-2 ">
          <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
            {errors.countries.message}
          </span>
        </div>
      )}
    </div>
  );
}
