import { COUNTRIES } from "@/utils/countries";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, East } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { AddNewClientFormSchemaData } from "./add-new-client-form-provider";

const RenderInfo = ({ keyValue, value }: { keyValue: string; value?: string }) => {
  return (
    <div className="flex flex-col gap-2 p-4">
      <p className="text-sm text-tonal-dark-cream-30">{keyValue}</p>
      <p className="text-base text-primary">{value}</p>
    </div>
  );
};
export const CheckCustomerDataModal = ({
  isOpen,
  onClickConfirmation,
  onOpenChange,
  infoClient,
  isLoading,
}: {
  isOpen: boolean;
  onClickConfirmation: () => void;
  onOpenChange?: () => void;
  infoClient?: AddNewClientFormSchemaData;
  isLoading?: boolean;
}) => {
  const t = useTranslations("AddNewClient");
  const c = useTranslations("common");
  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={onOpenChange}
    >
      <div className="p-5 max-h-[85vh] overflow-auto ">
        <div className="flex flex-row w-full justify-between">
          <div className="flex flex-col  gap-2 justify-start">
            <p className="text-[28px] text-primary font-bold">{t("title")}</p>
          </div>
          <div className="flex justify-end">
            <button onClick={onOpenChange}>
              <Clear className="size-6 fill-primary" />
            </button>
          </div>
        </div>
        <div className="mt-2">
          <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("confirmInformations")}</p>
        </div>

        <div className="my-4 bg-white rounded-[20px] ">
          <RenderInfo keyValue="Company name" value={infoClient?.companyName} />
          <RenderInfo keyValue="Country" value={infoClient?.countryCode} />
          <RenderInfo keyValue="Full name" value={infoClient?.firstName + ` ` + infoClient?.surname} />
          <RenderInfo keyValue="Login Information" value={infoClient?.emailLogin} />
          <RenderInfo keyValue="Billing Adress" value={infoClient?.streetAndNumber} />

          {!!infoClient?.countries.length && (
            <div className="flex flex-col gap-2 p-4">
              <p className="text-sm text-tonal-dark-cream-30">
                {t("countriesSelected")} ({infoClient?.countries?.length})
              </p>
              <div className="flex gap-3">
                {infoClient?.countries?.map((country, idx) => {
                  const countryInfo = COUNTRIES.find((item) => item.code === country);

                  if (!countryInfo) return null;

                  return (
                    <Image
                      key={idx}
                      src={countryInfo?.flag}
                      width={24}
                      height={24}
                      alt={`${countryInfo.name} flag`}
                      className="size-6 rounded-full object-cover"
                    />
                  );
                })}
              </div>
            </div>
          )}
        </div>

        <div className=" flex justify-end">
          <Button
            trailingIcon={<East />}
            color="yellow"
            variant="filled"
            size="medium"
            onClick={onClickConfirmation}
            disabled={isLoading}
          >
            {isLoading ? c("loading") : c("confirm")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
