"use client";

import { useQuery } from "@tanstack/react-query";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { CountUp } from "@/components/ui/count-up";
import { Skeleton } from "@/components/ui/skeleton";
import { getCustomers } from "@/lib/api/customer";
import { getNameInitials } from "@/utils/get-name-initials";
import type { ICountry } from "@/lib/api/country/types";
import { getCountOpenTasks } from "@/lib/api/home";
import { useTranslations } from "next-intl";

interface CountryKpisProps {
  country: ICountry;
}

export function CountryKpis({ country }: CountryKpisProps) {
  const t = useTranslations("CountryCard");
  const { data: customers, isLoading: customerIsLoading } = useQuery({
    queryKey: ["customers", { country_code: country.code }],
    queryFn: async () => {
      const response = await getCustomers({ country_code: country.code });

      return (response.customers || []).filter((customer) =>
        customer.contracts.some((contract) => contract.type === "DIRECT_LICENSE" || contract.type === "EU_LICENSE")
      );
    },
  });

  const { data: numberTasks, isLoading } = useQuery({
    queryKey: ["monday-tasks-countries", country?.code],
    queryFn: async () => getCountOpenTasks(),
    staleTime: 30000,
  });

  if (customerIsLoading || isLoading) {
    return <CountryKpisSkeleton />;
  }

  const representatives = country.followers || [];

  return (
    <div className="mt-8 flex flex-col md:flex-row items-start md:items-center gap-4">
      <div className="bg-tonal-green-90 flex items-center justify-between md:justify-center gap-2 h-20 p-5 w-full md:w-auto rounded-xl">
        <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("licensedCustomers")}:</p>
        <p className="text-title-1 text-tonal-dark-green-20 font-bold">
          <CountUp start={0} end={customers?.length ?? 0} />
        </p>
      </div>
      <div className="bg-tonal-blue-96 flex items-center justify-between md:justify-center gap-2 h-20 p-5 w-full md:w-auto rounded-xl">
        <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("openTasks")}:</p>
        <p className="text-title-1 text-tonal-dark-blue-10 font-bold">
          <CountUp start={0} end={numberTasks?.[country.name] || 0} />
        </p>
      </div>
      {!!representatives.length && (
        <div className="bg-tonal-blue-96 flex items-center justify-between md:justify-center gap-2 h-20 p-5 w-full md:w-auto rounded-xl">
          <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("representatives")}:</p>
          <div className="flex gap-2">
            {representatives?.map((representative) => (
              <Avatar key={representative.id}>
                <AvatarFallback>
                  {getNameInitials(`${representative.user_first_name} ${representative.user_last_name}`)}
                </AvatarFallback>
              </Avatar>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export function CountryKpisSkeleton() {
  return (
    <div className="mt-10 max-w-[800px] flex flex-col md:flex-row items-start md:items-center gap-4">
      <Skeleton className="w-full h-20" />
      <Skeleton className="w-full h-20" />
      <Skeleton className="w-full h-20" />
    </div>
  );
}
