"use client";

import { useQueryFilter } from "@/hooks/use-query-filter";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add } from "@arthursenno/lizenzero-ui-react/Icon";

interface AddRequiredInformationModalTriggerProps {
  countryCode: string;
}

export function AddRequiredInformationModalTrigger({ countryCode }: AddRequiredInformationModalTriggerProps) {
  const { changeParam } = useQueryFilter(["addRequiredInformation"]);

  function handleOpen() {
    changeParam("addRequiredInformation", countryCode);
  }

  return (
    <Button variant="filled" color="yellow" size="small" leadingIcon={<Add />} onClick={handleOpen}>
      Add
    </Button>
  );
}
