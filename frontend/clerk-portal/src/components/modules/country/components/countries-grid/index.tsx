"use client";

import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useMemo } from "react";
import { findBestMatch } from "string-similarity";

import { CountryCard, CountryCardPlaceholder } from "@/components/modules/country/components/country-card";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { getCountriesOverview } from "@/lib/api/country";
import { Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";

import { getCountOpenTasks } from "@/lib/api/home";
import { BestMatchSearch } from "../best-match-search";
import { useTranslations } from "next-intl";

export function CountriesGrid() {
  const t = useTranslations("CountriesGrid");
  const { paramValues } = useQueryFilter(["search", "order", "filter"]);

  const search = paramValues.search || "";
  const order = paramValues.order || "ASC";
  const filter = paramValues.filter || "ALL";

  const session = useSession();

  const userId = session?.data?.user?.id;

  const { data: countries, status: queryStatus } = useQuery({
    queryKey: ["countries-overview", search],
    queryFn: async () => await getCountriesOverview({ search }),
  });

  const { data: numberTasks, isLoading } = useQuery({
    queryKey: ["monday-tasks-countries"],
    queryFn: async () => getCountOpenTasks(),
    staleTime: 30000,
  });

  const orderedCountries = useMemo(() => {
    if (!countries) return [];

    if (!order) return countries;

    if (order === "ASC") {
      return countries.sort((a, b) => a.name.localeCompare(b.name));
    }

    if (order === "DESC") {
      return countries.sort((a, b) => b.name.localeCompare(a.name));
    }

    return countries;
  }, [countries, order]);

  const filteredCountries = useMemo(() => {
    if (!userId) return orderedCountries;
    if (filter === "REPRESENTATIVE") {
      return orderedCountries.filter((c) => c.followers.some((follower) => follower.user_id === Number(userId)));
    }
    return orderedCountries;
  }, [filter, orderedCountries, userId]);

  if (queryStatus === "pending" || isLoading) {
    return <CountriesGridPlaceholder />;
  }

  // No results found
  if (search && !filteredCountries.length) {
    const countriesNames = filteredCountries.map(({ name }) => name);
    const bestMatch = findBestMatch(search, countriesNames.length === 0 ? [""] : countriesNames);

    return (
      <div className="space-y-4">
        <p className="text-tonal-dark-cream-40">
          <strong>00</strong> {t("resultsFound")}. <BestMatchSearch bestMatch={bestMatch} />
        </p>
        <div className="w-full flex flex-col gap-4 px-1.5 items-center justify-center text-tonal-dark-cream-40">
          <Lightbulb width={48} height={48} className="fill-tonal-dark-cream-40" />
          <p className="font-normal text-xl text-center">
            {t("noResults")} &quot;{search}&quot; {t("inCountries")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid gap-6 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
      {filteredCountries?.map((country) => (
        <CountryCard key={country.id} country={country} openTasksLength={numberTasks?.[country.name] || 0} />
      ))}
    </div>
  );
}

export function CountriesGridPlaceholder() {
  return (
    <ol className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: 6 }).map((_, idx) => (
        <li key={idx} className="w-full">
          <CountryCardPlaceholder />
        </li>
      ))}
    </ol>
  );
}
