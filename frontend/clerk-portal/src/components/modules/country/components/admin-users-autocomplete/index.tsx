"use client";

import React from "react";
import { useQuery } from "@tanstack/react-query";
import { PopoverAnchor } from "@radix-ui/react-popover";

import { Clear, Search } from "@arthursenno/lizenzero-ui-react/Icon";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { getNameInitials } from "@/utils/get-name-initials";
import { cn } from "@/lib/utils";
import { getAdminUsers } from "@/lib/api/admin";
import { UserTypes } from "@/utils/user";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Skeleton } from "@/components/ui/skeleton";
import { getCountryOverview } from "@/lib/api/country";
import { ICountry } from "@/lib/api/country/types";
import { useTranslations } from "next-intl";

type Option = { key: number; value: string; label: string };

interface AdminUsersAutocompleteProps {
  country: ICountry;
  label?: string;
  value?: number[];
  onValueChange: (value: number[]) => void;
}

const roles: `${UserTypes}`[] = ["admin", "clerk"];

export function AdminUsersAutocomplete(props: AdminUsersAutocompleteProps) {
  const t = useTranslations("ForgotPassword");
  const { country, label = "Search for user", value, onValueChange } = props;

  const { data: adminUsers, status: queryStatus } = useQuery({
    queryKey: ["admin-users", { roles }],
    queryFn: async () => {
      const adminUsers = await getAdminUsers({ roles });
      if (country) {
        return adminUsers.filter((u) => !country.followers.some((follower) => follower.user_id === u.id));
      }
      return [];
    },
    enabled: !!country,
  });

  const defaultOptions = React.useMemo<Option[]>(() => {
    return adminUsers?.map((u) => ({ key: u.id, value: u.name, label: u.name })) || [];
  }, [adminUsers]);

  const [open, setOpen] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState<Option[]>([]);

  // React.useEffect(() => {
  //   if (value) {
  //     const selectedOptions = defaultOptions.filter((opt) => value.includes(opt.key));
  //     setSelectedOptions(selectedOptions);
  //   }
  // }, [defaultOptions, value]);

  function handleSelectValue(currentValue: string) {
    const selectedOption = selectedOptions?.find((opt) => opt.key.toString() === currentValue);

    // unselect option
    if (selectedOption) {
      handleRemoveValue(selectedOption.key);
      return;
    }

    const currentUser = adminUsers?.find((u) => u.id.toString() === currentValue);

    if (currentUser) {
      const newOption: Option = { key: currentUser.id, value: currentUser.name, label: currentUser.name };
      setSelectedOptions((prevSelectedOptions) => [...prevSelectedOptions, newOption]);

      const selectedIds: number[] = selectedOptions.map((opt) => opt.key);
      onValueChange(selectedIds.concat(newOption.key));
    }
  }

  function handleRemoveValue(optionKey: number) {
    setSelectedOptions((prevSelectedOptions) => prevSelectedOptions.filter((opt) => opt.key !== optionKey));

    const selectedIds: number[] = selectedOptions.map((opt) => opt.key);
    onValueChange(selectedIds.filter((id) => id !== optionKey));
  }

  if (queryStatus === "pending") {
    return (
      <div className="w-full flex flex-col gap-2">
        <Skeleton className="h-3 w-1/4" />
        <Skeleton className="h-14 w-full" />
      </div>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <Command>
        <PopoverAnchor className="relative flex flex-col items-start w-full p-1">
          <label htmlFor="admin-users" className="text-primary text-base font-centra mb-2 font-normal">
            {label}
          </label>

          <div className="group relative flex flex-wrap items-center gap-2 min-h-14 w-full rounded-2xl px-3 py-2 overflow-hidden bg-background border border-tonal-dark-cream-80 focus-within:ring-2 focus-within:ring-primary">
            <Search width={24} height={24} className="fill-tonal-dark-cream-60" />

            {(selectedOptions || [])?.map((selectedOption) => (
              <div key={selectedOption.key} className="flex items-center py-1 pl-2 pr-1 rounded-xl bg-tonal-beige-90">
                <Avatar>
                  <AvatarFallback>{getNameInitials(selectedOption.value)}</AvatarFallback>
                </Avatar>
                <span className="ml-2 mt-1 flex-1 text-primary font-normal pr-4">{selectedOption.value}</span>
                <button type="button" aria-label="Remove" onClick={() => handleRemoveValue(selectedOption.key)}>
                  <Clear className="size-5 fill-tonal-blue-40" />
                </button>
              </div>
            ))}

            <PopoverTrigger asChild className="w-full">
              <CommandInput
                id="admin-users"
                placeholder={t("searchForUser")}
                WrapperClasses="border-0 overflow-hidden"
                IconClasses="hidden"
                className="w-fit"
              />
            </PopoverTrigger>
          </div>
        </PopoverAnchor>

        <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] overflow-hidden shadow-elevation-04-1 pointer-events-auto p-0 pr-1 py-3 rounded-2xl">
          <CommandList>
            <CommandEmpty>No users found.</CommandEmpty>
            <CommandGroup>
              {(defaultOptions || []).map((opt) => {
                const isSelected = selectedOptions?.some((selectedOpt) => selectedOpt.key === opt.key);

                return (
                  <CommandItem
                    key={opt.key}
                    value={opt.value}
                    onSelect={() => {
                      handleSelectValue(opt.key.toString());
                      // setOpen(false);
                    }}
                    className="py-5 px-4 flex items-center gap-4 cursor-pointer hover:bg-support-blue/10"
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>{getNameInitials(opt.label)}</AvatarFallback>
                    </Avatar>
                    <span className={cn("text-primary text-base flex-1 font-normal", { "font-bold": isSelected })}>
                      {opt.label}
                    </span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </PopoverContent>
      </Command>
    </Popover>
  );
}
