import { TextCopy } from "@/components/ui/text-copy";
import { Delete, Help } from "@arthursenno/lizenzero-ui-react/Icon";
import StatusBadge, { StatusType } from "../../../country/components/task-type";
import { RequiredInformation } from "@/lib/api/required-information/types";
import { RequiredInformationIcon } from "@/components/modules/customers/customer-country/customer-country-required-informations/required-information-icon";

type RequiredInformationInputProps = {
  information: RequiredInformation;
};

const translateStatus = (information: RequiredInformation): StatusType => {
  if (information.status === "OPEN") {
    return "Awaiting answer";
  }

  if (information.status === "DONE") {
    return "Waiting Approval";
  }

  if (information.status === "APPROVED") {
    return "Accepted";
  }

  if (information.status === "DECLINED") {
    return "Declined";
  }

  return "Awaiting answer";
};

const RequiredInformationInput = ({ information }: RequiredInformationInputProps) => {
  return (
    <div className="flex flex-col gap-2 my-3">
      <div className="flex flex-col md:flex-row items-center justify-between w-full">
        <div className="flex items-center gap-2">
          <Help className="size-6 fill-[#808FA9]" />
          <RequiredInformationIcon requiredInformationType={information.type} />
          <p className="text-paragraph-regular font-medium text-tonal-dark-cream-20">{information.name}</p>
        </div>
        <StatusBadge status={translateStatus(information)} />
      </div>
      {!!information.answer && (
        <div className="flex items-center justify-between w-full mt-3">
          <div className="flex items-center gap-2 pl-9 w-full md:w-1/2">
            <TextCopy text={information.answer ?? ""} className="w-full" />
          </div>
          <Delete className="size-6 fill-[#002652]" />
        </div>
      )}
    </div>
  );
};

export default RequiredInformationInput;
