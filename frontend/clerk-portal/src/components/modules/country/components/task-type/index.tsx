import { cn } from "@/lib/utils";
import React from "react";

export type StatusType =
  | "Open"
  | "Accepted"
  | "Registration"
  | "Volume Reports"
  | "Invoice"
  | "Terminations"
  | "Waiting Signature"
  | "Awaiting answer"
  | "Processed"
  | "Unprocessed Invoice"
  | "Waiting Approval"
  | "To be generated"
  | "Waiting for acceptance"
  | `Declined`;

interface StatusProps {
  status: StatusType;
  isSmall?: boolean;
}

const StatusBadge: React.FC<StatusProps> = ({ status, isSmall }) => {
  const statusColors = {
    Accepted: "text-[#339933] bg-[#D8F2D8]",
    Registration: "text-tonal-dark-green-30 bg-tonal-dark-green-80",
    "Volume Reports": "text-support-blue bg-tonal-dark-blue-90",
    Invoice: "text-tonal-red-70 bg-tonal-pink-96",
    Declined: "text-tonal-red-70 bg-tonal-pink-96",
    Terminations: "text-on-surface-03 bg-surface-03",
    "Waiting Signature": "text-on-surface-04 bg-tonal-beige-90",
    "Awaiting answer": "text-on-surface-04 bg-tonal-beige-90",
    Processed: "text-[#339933] bg-[#D8F2D8]",
    Open: "text-[#FF9E14] bg-[#FFF3E1]",
    "Unprocessed Invoice": "text-[#FF9E14] bg-[#FFF3E1]",
    "Waiting for acceptance": "text-[#FF9E14] bg-[#FFF3E1]",
    "Waiting Approval": "hidden",
    "To be generated": "text-on-alert-container bg-alert-container",
  };

  return (
    <div
      className={cn(
        "flex items-center justify-center rounded-lg px-4",
        isSmall ? `py-[6px]` : `py-2`,
        statusColors[status]?.split(" ")[1]
      )}
    >
      <span
        className={cn(
          isSmall ? `text-xs font-medium` : "text-sm/[16px] font-bold",
          statusColors[status]?.split(" ")[0]
        )}
      >
        {status}
      </span>
    </div>
  );
};

export default StatusBadge;
