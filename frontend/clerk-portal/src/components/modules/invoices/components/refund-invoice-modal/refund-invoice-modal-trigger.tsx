"use client";

import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";

import { Cached } from "@arthursenno/lizenzero-ui-react/Icon";
import { DropdownItem } from "@/components/_common/dropdown";

interface RefundInvoiceModalTriggerProps {
  transactionId: string;
}

export function RefundInvoiceModalTrigger({ transactionId }: RefundInvoiceModalTriggerProps) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  function handleClick() {
    const params = new URLSearchParams(searchParams.toString());
    params.set("refund-invoice", transactionId);

    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  }

  return (
    <DropdownItem
      key="refund"
      onClick={handleClick}
      className="w-full inline-flex items-center gap-4 text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
    >
      <Cached className="-mt-1 size-5 fill-primary" /> Refund
    </DropdownItem>
  );
}
