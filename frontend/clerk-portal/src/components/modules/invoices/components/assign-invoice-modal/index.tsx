"use client";

import Image from "next/image";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Cached, Clear, KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { CurrencyInput } from "@/components/_common/currency-input";
import { Separator } from "@/components/_common/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ScrollArea } from "@/components/ui/scroll-area";
import { assignTransactionToInvoices, getPotentialMatches, getTransactionById } from "@/lib/api/transaction";
import { formatCurrency } from "@/utils/format-currency";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { formatDate } from "@/utils/format-date";

const assignInvoiceFormSchema = z.object({
  value: z.coerce.number(),
});

type AssignInvoiceFormSchemaType = z.infer<typeof assignInvoiceFormSchema>;

export function AssingInvoiceModal() {
  const t = useTranslations("AssignInvoiceModal");
  const c = useTranslations("common");
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [selectedInvoiceIds, setSelectedInvoiceIds] = useState<string[]>([]);
  const [isSuccess, setIsSuccess] = useState(false);

  const transactionId = searchParams.get("assign-invoice");
  const isModalOpen = transactionId !== null;

  const form = useForm<AssignInvoiceFormSchemaType>({
    resolver: zodResolver(assignInvoiceFormSchema),
    defaultValues: {
      value: 0,
    },
  });

  const { data: transaction, isLoading: isTransactionLoading } = useQuery({
    queryKey: ["transaction"],
    queryFn: () => getTransactionById(Number(transactionId)),
    enabled: isModalOpen && !!transactionId,
  });

  const { data: invoices, isLoading: isInvoicesLoading } = useQuery({
    queryKey: ["potential-invoices"],
    queryFn: () => getPotentialMatches(Number(transactionId)),
    enabled: isModalOpen && !!transactionId,
  });

  useEffect(() => {
    if (!isModalOpen) {
      // clear state
      setIsSuccess(false);
      setSelectedInvoiceIds([]);
      form.reset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalOpen]);

  if (isTransactionLoading || isInvoicesLoading || !transaction) {
    return null;
  }

  function handleCloseModal() {
    const params = new URLSearchParams(searchParams.toString());
    params.delete("assign-invoice");

    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  }

  function handleOnOpenChange(open: boolean) {
    if (!open) handleCloseModal();
  }

  async function handleSubmit(values: AssignInvoiceFormSchemaType) {
    try {
      if (!transactionId) {
        throw new Error(t("null"));
      }

      const payload = {
        transactionId: transactionId,
        invoiceIds: selectedInvoiceIds,
        amount: Math.round(values.value * 100),
      };

      console.log({ payload });
      const response = await assignTransactionToInvoices(payload);

      if (!response.success) {
        throw new Error(response.errorMessage);
      }

      // eslint-disable-next-line no-console
      console.log(response.data);
      setIsSuccess(true);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error);
      setIsSuccess(false);
    } finally {
      // handleCloseModal();
    }
  }

  function handleSelectInvoiceItem(invoiceId: string) {
    setSelectedInvoiceIds((prevState) => {
      const foundedIndex = prevState.findIndex((id) => id === invoiceId);
      if (foundedIndex < 0) {
        return [...prevState, invoiceId];
      }
      return prevState.filter((id) => id !== invoiceId);
    });
  }

  function handleCleanResults() {
    // TODO
  }

  const modalSuccessContent = (
    <div className="space-y-10 pt-8">
      <div className="flex items-center gap-2">
        <Image alt="Leaf seal" src="/assets/images/leaf-seal.svg" width={35} height={36} />
        <h3 className="mt-1.5 flex-1 font-bold text-[1.75rem] text-primary">{t("invoiceSuccess")}</h3>
      </div>
      <div className="flex justify-end">
        <Button type="button" color="dark-blue" variant="filled" size="medium" onClick={handleCloseModal}>
          {c("close")}
        </Button>
      </div>
    </div>
  );
  const modalContent = (
    <>
      <div className="flex items-center justify-end">
        <Clear onClick={handleCloseModal} className="w-8 h-8 fill-primary hover:opacity-75 cursor-pointer" />
      </div>

      <div className="space-y-4">
        <h3 className="font-bold text-[1.75rem] text-primary">{t("assignInvoice")}</h3>
        <p className="text-tonal-dark-cream-20">{t("assignSubtitle")}</p>
      </div>

      <form onSubmit={form.handleSubmit(handleSubmit)} className="mt-10 w-full">
        <div className="grid grid-cols-2 items-center gap-8">
          <div className="flex flex-col gap-2">
            <span className="text-primary">{t("transactionAmount")}</span>
            <p className="py-4 text-tonal-dark-cream-10">{formatCurrency(transaction.amount)}</p>
          </div>

          <Controller
            control={form.control}
            name="value"
            render={({ field }) => (
              <CurrencyInput
                id="valueToAssign"
                placeholder={t("setValue")}
                value={field.value}
                onValueChange={({ floatValue }) => field.onChange(floatValue)}
                label={t("setValue")}
              />
            )}
          />
        </div>

        {/* <Separator className="my-8" />

        <Controller
          control={form.control}
          name="invoiceOrCustomerId"
          render={({ field }) => (
            <div className="px-0.5">
              <InvoiceOrCustomerSelect value={field.value} onValueChange={field.onChange} />
            </div>
          )}
        /> */}

        <Separator className="my-8" />

        <div className="flex items-end gap-3 mb-8">
          <div className="space-y-3 flex-1">
            <h3 className="text-xl text-primary font-bold">{t("suggestions")}</h3>
            <p className="text-tonal-dark-cream-20">{t("potentialMatches")}</p>
          </div>

          <Button type="button" color="light-blue" variant="text" size="medium">
            <Cached className="size-5 fill-support-blue" /> {t("reloadResults")}
          </Button>
        </div>

        {/* Results */}
        <ul id="results" className="w-full">
          <RadioGroup onValueChange={handleSelectInvoiceItem}>
            {invoices &&
              invoices.map((invoice: any) => {
                const invoiceId = invoice.id;
                const customerName = invoice.payment_customer.name;
                const formatAmount = formatCurrency(invoice.amount);
                const issueDate = formatDate(invoice.created_at);
                const dueDate = formatDate(new Date(new Date(invoice.created_at).getTime() + 30 * 24 * 60 * 60 * 1000));

                return (
                  <li key={`potential-invoice-${invoiceId}`} className="grid grid-cols-4 min-h-16 py-2">
                    <div className="flex items-center gap-3">
                      <RadioGroupItem value={invoiceId} />
                      <div className="flex flex-col gap-1 text-primary">
                        <strong>#{invoiceId}</strong>
                        <span>{customerName}</span>
                      </div>
                    </div>
                    <div className="flex flex-col pl-8 gap-1 text-tonal-dark-cream-20">
                      <strong>{t("dateIssued")}</strong>
                      <span>{issueDate}</span>
                    </div>
                    <div className="flex flex-col gap-1 text-right text-tonal-dark-cream-20">
                      <strong>{t("dueDate")}</strong>
                      <span>{dueDate}</span>
                    </div>
                    <div className="flex flex-col gap-1 text-right text-tonal-dark-cream-20">
                      <strong>{t("amount")}</strong>
                      <span>{formatAmount}</span>
                    </div>
                  </li>
                );
              })}
          </RadioGroup>
        </ul>

        <button
          type="button"
          onClick={handleCleanResults}
          className="mt-8 text-support-blue underline text-sm font-bold"
        >
          {t("cleanResults")}
        </button>

        <div className="mt-10 flex justify-end">
          <Button
            type="submit"
            variant="filled"
            size="medium"
            color="yellow"
            className="sm:min-w-52"
            disabled={selectedInvoiceIds.length === 0}
          >
            {c("continue")} <KeyboardArrowRight className="-mt-1 size-5 fill-on-tertiary" />
          </Button>
        </div>
      </form>
    </>
  );

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !rounded-[52px] w-full max-w-[672px] !max-h-full !py-8 !px-9 !pr-2"
    >
      <ScrollArea>
        <div className="w-full max-h-[85vh] pr-5">{isSuccess ? modalSuccessContent : modalContent}</div>
      </ScrollArea>
    </Modal>
  );
}
