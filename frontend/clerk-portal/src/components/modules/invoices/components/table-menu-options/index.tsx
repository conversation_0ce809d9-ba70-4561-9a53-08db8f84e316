"use client";

import { useRouter } from "@/i18n/navigation";

import { Launch, MoreVert } from "@arthursenno/lizenzero-ui-react/Icon";
import { Dropdown, DropdownItem } from "@/components/_common/dropdown";
import { TransactionStatus } from "@/lib/api/transaction/types";
import { AssignInvoiceModalTrigger } from "@/components/modules/invoices/components/assign-invoice-modal/assign-invoice-modal-trigger";
import { RefundInvoiceModalTrigger } from "@/components/modules/invoices/components/refund-invoice-modal/refund-invoice-modal-trigger";

interface TableMenuOptionsProps {
  transactionId: string;
  customerId: string;
  transactionStatus: TransactionStatus;
}

export function TableMenuOptions({ transactionId, customerId, transactionStatus }: TableMenuOptionsProps) {
  const router = useRouter();

  function handleRedirectToCustomerPage() {
    router.push(`/customers/${customerId}`);
  }

  return (
    <Dropdown
      className="min-w-40 max-w-40"
      trigger={
        <button
          type="button"
          aria-label="Open menu"
          className="size-6 inline-flex items-center justify-center rounded-full hover:bg-primary/[0.18]"
        >
          <MoreVert className="size-5 fill-primary" />
        </button>
      }
    >
      <DropdownItem
        key="see-client-page"
        onClick={handleRedirectToCustomerPage}
        className="w-full inline-flex items-center gap-4 text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
      >
        <Launch className="-mt-1 size-5 fill-primary" /> See client page
      </DropdownItem>

      {transactionStatus === TransactionStatus.ASSIGNED && <RefundInvoiceModalTrigger transactionId={transactionId} />}
      {transactionStatus === TransactionStatus.UNASSIGNED && (
        <AssignInvoiceModalTrigger transactionId={transactionId} />
      )}
    </Dropdown>
  );
}
