"use client";

import { Link, usePathname } from "@/i18n/navigation";
import { requestPasswordReset } from "@/lib/api/auth";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { SuccessResetPassword } from "./success-reset-password";
import { UserTypes } from "@/utils/user";
import { Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { AuthTitle } from "../components/auth-title";
import { z } from "zod";

const forgotPasswordSchema = z.object({
  email: z.string().email(),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export function ForgotPasswordPage() {
  const [recover, setRecover] = useState(false);

  const pathname = usePathname();
  const role = pathname.includes("partner-hub") ? UserTypes.PARTNER : UserTypes.CUSTOMER;

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, isLoading, isSubmitting },
  } = useForm<{
    email: string;
  }>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  async function submit({ email }: ForgotPasswordFormData) {
    try {
      const res = await requestPasswordReset(email, `${process.env.NEXT_PUBLIC_DOMAIN}/auth/recover-password`);

      if (res.data.statusCode !== 201 && res?.status !== 201) {
        setError("email", { message: res?.data.message });
        return;
      }
    } catch (error: any) {
      setError("email", { message: error });
      enqueueSnackbar(error, { variant: "error" });
      return;
    }

    setRecover(true);
  }

  const backUrl = role === UserTypes.CUSTOMER ? "/auth/login" : "/partner-hub/auth/login";

  const t = useTranslations("ForgotPassword");
  const c = useTranslations("common");

  if (recover) return <SuccessResetPassword />;

  return (
    <>
      <AuthTitle title={t("title")} subtitle={t("emailPrompt")} />
      <form className="w-full" onSubmit={handleSubmit(submit)}>
        <div className="flex mb-10 ">
          <div className="w-full  mb-5">
            <div className="flex">
              <Input
                label={t("email")}
                placeholder={t("emailPlaceholder")}
                {...register("email")}
                type="email"
                variant={errors.email ? "error" : "enabled"}
                errorMessage={errors.email && errors.email.message}
                rightIcon={errors.email && <Error className="fill-error size-5" />}
              />
            </div>
          </div>
        </div>

        <Button
          color="yellow"
          size="medium"
          variant="filled"
          className="w-full"
          disabled={errors.email || isLoading || isSubmitting ? true : false}
        >
          {isLoading || isSubmitting ? "Loading..." : t("button")}
        </Button>
      </form>
      <div className="mt-4 text-center w-full flex items-center justify-center">
        <Link href={backUrl}>
          <Button variant="text" size="small" color="light-blue">
            {c("backToLogin")}
          </Button>
        </Link>
      </div>
    </>
  );
}
