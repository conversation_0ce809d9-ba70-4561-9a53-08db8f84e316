import { Visibility, VisibilityOff } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { ComponentProps, forwardRef, useState } from "react";

export const PasswordInput = forwardRef<HTMLInputElement, ComponentProps<typeof Input>>(({ ...props }, ref) => {
  const [isVisible, setIsVisible] = useState(false);

  const visibilityIcon = isVisible ? (
    <Visibility className="cursor-pointer fill-primary" width={20} onClick={() => setIsVisible(!isVisible)} />
  ) : (
    <VisibilityOff className="cursor-pointer fill-primary" width={20} onClick={() => setIsVisible(!isVisible)} />
  );

  return <Input ref={ref} type={isVisible ? "text" : "password"} rightIcon={visibilityIcon} {...props} />;
});

PasswordInput.displayName = "PasswordInput";
