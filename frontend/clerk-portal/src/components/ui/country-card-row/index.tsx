"use client";

import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { PublishedCountry } from "@/hooks/use-liberated-countries";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Delete } from "@arthursenno/lizenzero-ui-react/Icon";
import Image from "next/image";

export const CountryCardRow = ({
  country,
  info,
  onClickRemove,
}: {
  country: PublishedCountry;
  info?: string;
  onClickRemove?: () => void;
}) => {
  const handleClickRemove = () => {
    onClickRemove && onClickRemove();
  };

  return (
    <div className="flex bg-background rounded-4xl w-full py-5 px-7 justify-between items-start">
      <div className="flex items-center gap-3">
        {info && (
          <Button variant="text" size="iconSmall" color="gray" onClick={handleClickRemove} type="button">
            <Delete className="size-5 fill-primary" />
          </Button>
        )}
        <Image
          src={country.flag_url}
          alt={`${country.name} flag`}
          width={28}
          height={28}
          className="rounded-full size-7 object-cover"
        />
        <p className="text-primary font-bold text-xl">{country.name}</p>
      </div>
      {info ? (
        <QuestionTooltip>
          <QuestionTooltipDescription>{info}</QuestionTooltipDescription>
        </QuestionTooltip>
      ) : (
        <Button variant="text" size="iconSmall" color="gray" onClick={handleClickRemove}>
          <Delete className="size-5 fill-primary" />
        </Button>
      )}
    </div>
  );
};
