import { cn } from "@/lib/utils";

export type StatusBadgeVariant = "success" | "error" | "warning" | "info" | "neutral";

type StatusBadgeSize = "sm" | "md" | "lg";

const colors: Record<StatusBadgeVariant, { foreground: string; background: string }> = {
  success: { foreground: "text-success", background: "bg-success-container" },
  error: { foreground: "text-error", background: "bg-tonal-red-90" },
  warning: { foreground: "text-alert", background: "bg-alert-container" },
  info: { foreground: "text-support-blue", background: "bg-tonal-dark-blue-90" },
  neutral: { foreground: "text-tonal-dark-cream-30", background: "bg-tonal-dark-cream-80" },
};

const sizes: Record<StatusBadgeSize, { padding: string; fontSize: string }> = {
  sm: { padding: "px-2 py-1", fontSize: "text-xs" },
  md: { padding: "px-3 py-2", fontSize: "text-sm" },
  lg: { padding: "px-4 py-3", fontSize: "text-base" },
};

export interface StatusBadgeProps {
  variant?: StatusBadgeVariant;
  size?: StatusBadgeSize;
  label: string;
}

export function StatusBadge({ variant = "neutral", size = "md", label }: StatusBadgeProps) {
  return (
    <div className={cn("flex items-center justify-center rounded-lg", sizes[size].padding, colors[variant].background)}>
      <span className={cn("font-bold", colors[variant].foreground, sizes[size].fontSize)}>{label}</span>
    </div>
  );
}
