import { Link } from "@/i18n/navigation";
import { Liz<PERSON><PERSON>o<PERSON>ogo } from "@arthursenno/lizenzero-ui-react/Figure";
import { CountryDropdown } from "./country-dropdown";
import { ProfileDropdown } from "./profile-dropdown";
import { localeList } from "@/i18n/locales";

export function AuthHeader() {
  const locales = localeList.map((l) => ({
    name: l.languageName,
    code: l.languageCode,
    flag: l.flagImageUrl,
  }));

  return (
    <nav className="h-20 bg-background text-grey-blue shadow shadow-elevation-01-2  border-b-[1px] border-tonal-dark-cream-80 z-20 w-full mx-auto lg:pl-0 p-0">
      <div className="w-full px-4">
        <div className="max-w-7xl w-full mx-auto">
          <div className="h-20 pt-6 pb-4 w-full mx-auto flex justify-between items-center">
            <Link href="/">
              <LizenzeroLogo width={150} />
            </Link>
            <div className="flex items-center gap-3 md:gap-4 lg:gap-6">
              <CountryDropdown localeList={locales} />
              <ProfileDropdown />
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
