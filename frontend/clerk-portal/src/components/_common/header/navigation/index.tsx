"use client";

import { usePathname } from "@/i18n/navigation";
import { Link } from "@/i18n/navigation";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface NavigationProps {
  className?: string;
}

export function Navigation({ className }: NavigationProps) {
  const pathname = usePathname();
  const t = useTranslations("nav");

  return (
    <div className={cn("flex gap-5", className)}>
      <Link
        href="/"
        data-active={pathname === "/"}
        className="inline-block text-primary font-bold p-2 border-background border-b hover:border-primary data-[active=true]:border-primary transition-all duration-300"
      >
        {t("dashboard")}
      </Link>
      <Link
        href="/customers"
        data-active={pathname.includes("customers")}
        className="inline-block text-primary font-bold p-2 border-background border-b hover:border-primary data-[active=true]:border-primary transition-all duration-300"
      >
        {t("customers")}
      </Link>
      <Link
        href="/countries"
        data-active={pathname.includes("countries")}
        className="inline-block text-primary font-bold p-2 border-background border-b hover:border-primary data-[active=true]:border-primary transition-all duration-300"
      >
        {t("countries")}
      </Link>
      <Link
        href="/invoices"
        data-active={pathname.includes("invoices")}
        className="inline-block text-primary font-bold p-2 border-background border-b hover:border-primary data-[active=true]:border-primary transition-all duration-300"
      >
        {t("invoices")}
      </Link>
    </div>
  );
}
