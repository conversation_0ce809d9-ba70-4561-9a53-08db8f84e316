import { Link } from "@/i18n/navigation";
import { Liz<PERSON><PERSON>oLogo } from "@arthursenno/lizenzero-ui-react/Figure";
import { CountryDropdown } from "../country-dropdown";
import { ProfileDropdown } from "../profile-dropdown";
import { Navigation } from "../navigation";
import { localeList } from "@/i18n/locales";
import { DashboardHeaderProvider } from "./dashboard-header-context";
import { DashboardHeaderDrawer, DashboardHeaderDrawerTrigger } from "./dashboard-header-drawer";

export function DashboardHeader() {
  const locales = localeList.map((l) => ({
    name: l.languageName,
    code: l.languageCode,
    flag: l.flagImageUrl,
  }));

  return (
    <DashboardHeaderProvider>
      <nav className="h-20 bg-background text-grey-blue shadow shadow-elevation-01-2 fixed top-0 left-0 w-full mx-auto lg:pl-0 p-0 z-[20]">
        <div className="w-full px-4">
          <div className="max-w-7xl w-full mx-auto">
            <div className="relative h-20 pt-6 pb-4 w-full mx-auto flex justify-between items-center">
              <Navigation className="hidden lg:flex" />
              <div className="flex lg:hidden items-center gap-3">
                <DashboardHeaderDrawerTrigger />
                <Link href="/">
                  <LizenzeroLogo width={150} />
                </Link>
              </div>

              <Link href="/" className="hidden lg:block absolute left-1/2 -translate-x-1/2">
                <LizenzeroLogo width={150} />
              </Link>
              <div className="flex items-center gap-3 md:gap-4 lg:gap-6">
                <CountryDropdown localeList={locales} />
                <ProfileDropdown />
              </div>
            </div>
          </div>
        </div>
        <DashboardHeaderDrawer />
      </nav>
    </DashboardHeaderProvider>
  );
}
