"use client";

import { KeyboardArrowDown, Logout } from "@arthursenno/lizenzero-ui-react/Icon";
import * as HoverCard from "@radix-ui/react-hover-card";
import { signOut, useSession } from "next-auth/react";
import Divider from "../../divider";
import { useTranslations } from "next-intl";

export function ProfileDropdown() {
  const session = useSession();
  const t = useTranslations("nav");

  if (session.status !== "authenticated") return null;

  const user = session.data.user;

  function handleLogout() {
    signOut({
      redirect: true,
      callbackUrl: "/en/auth/login",
    });
  }

  return (
    <HoverCard.Root openDelay={100} closeDelay={100}>
      <HoverCard.Trigger className="" asChild>
        <div className="flex items-center gap-1 cursor-pointer">
          <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center">
            <span className="text-primary font-medium text-xl">
              {(user.first_name[0] || "").toLocaleUpperCase()}
              {(user.last_name[0] || "").toLocaleUpperCase()}
            </span>
          </div>
          <KeyboardArrowDown className="w-[24px] fill-primary" />
        </div>
      </HoverCard.Trigger>

      <HoverCard.Portal>
        <HoverCard.Content
          className="w-[310px] bg-background p-6 rounded-2xl focus:outline-none z-[999999]"
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
          align="end"
        >
          <div className="space-y-3">
            <div className="space-y-1">
              <p className="text-tonal-dark-cream-10 font-bold">
                {user.first_name} {user.last_name}
              </p>
              <p className="text-sm text-tonal-dark-cream-30 w-full overflow-hidden text-ellipsis whitespace-nowrap">
                {user.email}
              </p>
            </div>
          </div>

          <Divider initialMarginDisabled className="my-5" />

          <div onClick={() => handleLogout()} className="group flex items-center gap-2 cursor-pointer">
            <Logout width={24} className="fill-primary" />
            <p className="text-primary font-bold group-hover:underline underline-offset-2">{t("logout")}</p>
          </div>
        </HoverCard.Content>
      </HoverCard.Portal>
    </HoverCard.Root>
  );
}
