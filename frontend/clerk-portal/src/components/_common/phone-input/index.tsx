"use client";

import { useState } from "react";
import InputMask from "react-input-mask";

import { KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Dropdown, DropdownItem } from "@/components/_common/dropdown";
import { Separator } from "@/components/_common/separator";
import { COUNTRIES, EU_COUNTRY_CODES } from "@/utils/countries";
import { cn } from "@/lib/utils";

interface PhoneInputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

const EU_COUNTRIES = COUNTRIES.filter((country) => EU_COUNTRY_CODES.includes(country.code));

export function PhoneInput({ className, placeholder, ...props }: PhoneInputProps) {
  const [selectedCountry, setSelectedCode] = useState(EU_COUNTRIES[0]);

  function handleSelectCountry(index: number) {
    setSelectedCode(EU_COUNTRIES[index]);
  }

  return (
    <div
      className={cn(
        "group relative flex items-center h-14 w-full rounded-2xl px-3 py-2 bg-background border border-tonal-dark-cream-80 focus-within:ring-2 focus-within:ring-primary",
        className
      )}
    >
      <div className="flex mr-4">
        <div className="mr-3 flex items-center gap-1">
          <Dropdown
            className="px-0 w-[var(--radix-dropdown-menu-trigger-width)] max-h-[320px] overflow-y-auto"
            trigger={
              <button type="button" className="flex items-center gap-1">
                <Avatar>
                  <AvatarImage src={selectedCountry.flag} alt={selectedCountry.name} />
                </Avatar>
                <KeyboardArrowDown width={20} height={20} className="fill-tonal-dark-cream-40" />
              </button>
            }
          >
            {EU_COUNTRIES.map((country, index) => (
              <DropdownItem key={country.code} asChild>
                <button
                  type="button"
                  aria-label="Select Country"
                  onClick={() => handleSelectCountry(index)}
                  className="flex items-center gap-2 p-4 w-full hover:cursor-pointer hover:bg-primary/10"
                >
                  <Avatar>
                    <AvatarImage src={country.flag} alt={country.name} />
                    <AvatarFallback>{country.name}</AvatarFallback>
                  </Avatar>
                  <span className="mt-1 text-primary">{country.name}</span>
                </button>
              </DropdownItem>
            ))}
          </Dropdown>

          <span className="mt-1 text-primary text-right">{selectedCountry.phone.code}</span>
        </div>

        <Separator orientation="vertical" className="h-6" />
      </div>

      <InputMask
        {...props}
        // maskPlaceholder=" "
        mask={selectedCountry.phone.mask}
        placeholder={placeholder ?? String(selectedCountry.phone.mask ?? "")}
        className="mt-1 w-full h-full bg-none border-0 outline-none focus:outline-none text-tonal-dark-cream-10 placeholder-tonal-dark-cream-60"
      />
    </div>
  );
}
