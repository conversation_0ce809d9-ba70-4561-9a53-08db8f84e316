import { ReactNode } from "react";

import { Help } from "@arthursenno/lizenzero-ui-react/Icon";

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface QuestionTooltipProps {
  children: ReactNode;
  className?: string;
}

export function QuestionTooltip({ children, className }: QuestionTooltipProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div>
            <Help className={cn("size-5 fill-[#808FA9] hover:fill-support-blue border-white")} />
          </div>
        </TooltipTrigger>
        <TooltipContent
          side="bottom"
          align="start"
          className={cn("p-5 bg-white shadow-elevation-04-1 w-[280px] rounded-[16px]", className)}
        >
          {children}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

interface QuestionTooltipTitleProps {
  children: ReactNode;
  className?: string;
}

export function QuestionTooltipTitle({ children, className }: QuestionTooltipTitleProps) {
  return <div className={cn("flex items-center gap-2", className)}>{children}</div>;
}

interface QuestionTooltipDescriptionProps {
  children: ReactNode;
  className?: string;
}

export function QuestionTooltipDescription({ children, className }: QuestionTooltipDescriptionProps) {
  return <div className={cn("text-tonal-dark-cream-20 px-2 py-5", className)}>{children}</div>;
}
