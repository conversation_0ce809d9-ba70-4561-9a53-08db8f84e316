import type { SVGProps } from "react";
interface SVGRProps {
  title?: string;
  titleId?: string;
}

const Cardboard = ({ title, titleId, ...props }: SVGProps<SVGSVGElement> & SVGRProps) => (
  <svg
    width="49"
    height="48"
    viewBox="0 0 49 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-labelledby={titleId}
    {...props}
  >
    {title ? <title id={titleId}>{title}</title> : null}
    <path
      d="M28.1443 15.6127H28.166V27.8335L26.1022 26.6605L24.0384 27.8335L21.9745 26.6605L19.9107 27.8335V15.6127H19.9324L20.5407 10.7607H2.37885L0.79295 13.7999C0.271558 14.8023 0 15.9007 0 17.031V40.8221C0 42.6669 1.52073 44.1598 3.39991 44.1598H45.6001C47.4793 44.1598 49 42.6669 49 40.8221V16.999C49 15.9007 48.7393 14.8129 48.2396 13.8319L46.6755 10.7607H27.5469L28.1552 15.6127H28.1443Z"
      fill="#002652"
    />
    <path
      d="M44.7202 6.93236L43.7426 5.01287C43.3733 4.29839 42.6347 3.83984 41.82 3.83984H26.6888L27.0689 6.93236H44.7202Z"
      fill="#002652"
    />
    <path
      d="M21.4097 3.85051H7.31035C6.50654 3.85051 5.7679 4.29839 5.39858 5.0022L4.38839 6.94302H21.0295L21.4097 3.85051Z"
      fill="#002652"
    />
  </svg>
);

export default Cardboard;
