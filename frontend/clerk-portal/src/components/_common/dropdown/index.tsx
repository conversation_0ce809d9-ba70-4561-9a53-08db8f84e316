import { cn } from "@/lib/utils";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";

export const DropdownItem = DropdownMenu.Item;

interface IDropdownProps {
  children?: React.ReactNode;
  trigger?: React.ReactNode;
  className?: string;
}

export const Dropdown = ({ children, trigger, className }: IDropdownProps) => {
  return (
    <DropdownMenu.Root modal={false}>
      <DropdownMenu.Trigger className="focus:outline-none" asChild>
        {trigger}
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className={cn("min-w-[220px] bg-background py-3 rounded-2xl focus:outline-none z-[999999]", className)}
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
        >
          {children}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
};
