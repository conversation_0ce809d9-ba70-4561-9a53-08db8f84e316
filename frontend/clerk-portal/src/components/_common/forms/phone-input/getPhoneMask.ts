import { COUNTRIES } from "@/utils/countries";

type Country = (typeof COUNTRIES)[number];

export function getPhoneMask(country: Country, phoneNumber: string): { mask: string; isBiggestMask: boolean } {
  const { mask } = country.phone;

  if (typeof mask === "string") {
    return { mask, isBiggestMask: true };
  }

  if (Array.isArray(mask)) {
    let selectedMask = mask[0];

    if (phoneNumber.length === 0) {
      return { mask: selectedMask, isBiggestMask: mask.every((item) => selectedMask.length > item.length) };
    }

    for (const m of mask) {
      const strippedMask = m.replace(/[^0-9]/g, "");
      const strippedPhone = phoneNumber.replace(/[^0-9]/g, "");
      if (strippedMask.length >= strippedPhone.length) {
        selectedMask = m;
        break;
      }
    }

    return {
      mask: selectedMask,
      isBiggestMask: mask.every((item) => selectedMask.length >= item.length),
    };
  }

  return { mask: "", isBiggestMask: false };
}
