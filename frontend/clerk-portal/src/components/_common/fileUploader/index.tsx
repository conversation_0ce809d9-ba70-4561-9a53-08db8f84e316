import { ReactNode } from "react";
import { File, Delete } from "@arthursenno/lizenzero-ui-react/Icon";
import Divider from "../divider";
import { useTranslations } from "next-intl";

const inputStyle: React.CSSProperties = {
  position: "absolute",
  left: 0,
  top: 0,
  width: "100%",
  height: "100%",
  opacity: 0,
  cursor: "pointer",
};

const Container = ({ children }: { children: ReactNode }) => (
  <div
    className="border border-tonal-dark-cream-60 border-dashed rounded-lg py-8 px-10 flex flex-col gap-4 bg-surface-02 items-center justify-center cursor-pointer"
    draggable={true}
  >
    {children}
  </div>
);

interface UploadInputProps {
  file: File | undefined;
  setFile: (file: File | undefined) => void;
  description?: string;
  acceptedFileTypes: string[];
}

const UploadInput = ({ acceptedFileTypes: rawTypes, file, setFile }: UploadInputProps) => {
  const acceptedFileTypes = rawTypes.map((type) => `.${type}`).join(", ");

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFile = event.target.files && event.target.files[0];
    if (uploadedFile) setFile(uploadedFile);
  };

  const t = useTranslations("common");

  if (file) {
    return (
      <div className="flex flex-col gap-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 w-full">
            <File className="fill-primary flex-none" height={24} width={24} />
            <p className="text-primary text-base break-words max-w-[calc(100%-48px-24px)]">{file.name}</p>
          </div>
          <Delete
            className="fill-primary cursor-pointer flex-none"
            height={24}
            width={24}
            onClick={() => setFile(undefined)}
          />
        </div>
        <Divider initialMarginDisabled />
      </div>
    );
  }

  return (
    <div className="relative overflow-hidden inline-block cursor-pointer w-full">
      <input
        type="file"
        style={inputStyle}
        className="cursor-pointer "
        onChange={handleFileChange}
        accept={acceptedFileTypes}
      />
      <Container>
        <div className="flex flex-row gap-1">
          <button className="z-5 text-tonal-dark-cream-20 text-small-paragraph-bold font-bold bg-transparent">
            {t("uploadExcel")}
          </button>
          <p className="text-tonal-dark-cream-50 text-sm">{t("orDrag")}</p>
        </div>
      </Container>
    </div>
  );
};

export default UploadInput;
