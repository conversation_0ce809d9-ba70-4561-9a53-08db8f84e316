import type { Metadata } from "next";
import { Suspense } from "react";

import {
  FollowedCountriesFilter,
  FollowedCountriesFilterPlaceholder,
} from "@/components/modules/country/components/followed-countries-filter";
import Container from "@/components/_common/container/container";
import { TaskKanban, TaskKanbanPlaceholder } from "@/components/modules/home/<USER>/task-kanban";
import { useTranslations } from "next-intl";

export const metadata: Metadata = {
  title: "Home",
};

interface HomePageProps {
  searchParams: {
    country?: string;
  };
}

export default function HomePage({ searchParams }: HomePageProps) {
  const countryFilter = searchParams.country ?? "ALL";

  const t = useTranslations("Monday");

  return (
    <main className="flex flex-grow">
      <Container className="bg-surface-01">
        <h1 className="font-bold text-xl text-primary">{t("name")}</h1>

        <section id="filter" className="mt-7 space-y-4">
          <h3 className="font-bold text-[#183362]">{t("followedCountries")}</h3>
          <Suspense fallback={<FollowedCountriesFilterPlaceholder />}>
            <FollowedCountriesFilter />
          </Suspense>
        </section>

        <section id="tasks" className="flex flex-grow pt-8 pb-2">
          <Suspense key={countryFilter} fallback={<TaskKanbanPlaceholder />}>
            <TaskKanban />
          </Suspense>
        </section>
      </Container>
    </main>
  );
}
