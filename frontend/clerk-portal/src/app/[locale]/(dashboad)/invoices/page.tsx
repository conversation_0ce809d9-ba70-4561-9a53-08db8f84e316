import Container from "@/components/_common/container/container";
import { SearchInput } from "@/components/_common/search-input";
import { TitleAndSubTitle } from "@/components/_common/title-and-subtitle";
import { AssingInvoiceModal } from "@/components/modules/invoices/components/assign-invoice-modal";
import { BalanceSection, BalanceSectionPlaceholder } from "@/components/modules/invoices/components/balance-section";
import { FilterByYear } from "@/components/modules/invoices/components/filter-by-year";
import { InvoiceStatusFilter } from "@/components/modules/invoices/components/invoice-status-filter";
import { RefundInvoiceModal } from "@/components/modules/invoices/components/refund-invoice-modal";
import { TransactionsSection } from "@/components/modules/invoices/components/transactions-section";
import { TransactionsTablePlaceholder } from "@/components/modules/invoices/components/transactions-table";
import { ITransaction } from "@/lib/api/invoices/types";
import type { Metadata } from "next";
import { useTranslations } from "next-intl";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Invoices",
};

export const transactions: ITransaction[] = [
  {
    id: "fc20385a-4bc5-4e66-88cb-deda4a261f13",
    companyName: "Lautner CO.",
    externalReference: "585424",
    transactionAmount: -300.0,
    remainingTransactionAmount: null,
    iban: "20.12.2023",
    transactionDate: "2024-12-30",
    clientId: "928234",
    status: "unassigned",
  },
  {
    id: "7ad92f63-9df6-4d9e-ae65-b531d816c12b",
    companyName: "Skyline Industries",
    externalReference: "295832",
    transactionAmount: 1200.0,
    remainingTransactionAmount: 800.0,
    iban: "15.11.2023",
    transactionDate: "2023-11-15",
    clientId: "928234",
    status: "unassigned",
  },
  {
    id: "9f4bce01-08f7-45b4-8b1c-23c77a3d17d2",
    companyName: "TechHub Corp",
    externalReference: "431527",
    transactionAmount: -500.0,
    remainingTransactionAmount: null,
    iban: "25.10.2023",
    transactionDate: "2023-10-25",
    clientId: "928234",
    status: "unassigned",
  },
  {
    id: "5e8b1a4d-b347-4c47-8260-865d0c3a7f61",
    companyName: "Solaris Solutions",
    externalReference: "789654",
    transactionAmount: 2500.0,
    remainingTransactionAmount: null,
    iban: "20.09.2023",
    transactionDate: "2023-09-20",
    clientId: "928234",
    status: "assigned",
  },
];

interface InvoicesPageProps {
  searchParams: {
    invoiceStatus?: string;
    q?: string;
    year?: string;
  };
}

export default function InvoicesPage({ searchParams }: InvoicesPageProps) {
  const invoiceStatus = searchParams.invoiceStatus || "";
  const search = searchParams.q || "";
  const year = String(searchParams.year || new Date().getFullYear());

  const t = useTranslations("Invoices");

  return (
    <>
      <main className="flex flex-col sm:pt-7">
        <Container>
          <strong className="font-bold text-sm text-[#183362]">{t("title")}</strong>
          <TitleAndSubTitle icon={false} title={t("transactions")} className="mt-8 sm:mt-16" />
          <div className="bg-[#F7F5F2] w-[80%] rounded-2xl">
            <Suspense key={invoiceStatus + search + year} fallback={<BalanceSectionPlaceholder />}>
              <BalanceSection />
            </Suspense>
          </div>

          <div className="sm:mt-12 py-10 sm:px-6 bg-[#F7F5F2] rounded-2xl">
            <InvoiceStatusFilter />

            <div className="mt-2.5 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div className="w-full md:max-w-sm">
                <SearchInput placeholder={t("searchPlaceholder")} />
              </div>
              <div className="flex items-center md:justify-end gap-2">
                <FilterByYear />
              </div>
            </div>

            <Suspense key={invoiceStatus + search + year} fallback={<TransactionsTablePlaceholder className="mt-9" />}>
              <TransactionsSection
                invoiceStatus={invoiceStatus}
                search={search}
                year={year}
                data={transactions ?? []}
              />
            </Suspense>
          </div>
        </Container>
      </main>

      {/* Modals */}
      <AssingInvoiceModal />
      <RefundInvoiceModal />
    </>
  );
}
