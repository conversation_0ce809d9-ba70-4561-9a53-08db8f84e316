import { AuthHeader } from "@/components/_common/header/auth-header";
import Image from "next/image";
import { ReactNode } from "react";

export default async function Layout({ children }: { children: ReactNode }) {
  return (
    <>
      <AuthHeader />
      <main className="py-10 px-4 bg-background h-full">
        <div className="h-full w-full max-w-7xl mx-auto">
          <div className="w-full grid lg:grid-cols-2 bg-surface-03 rounded-[40px]">
            <div className="hidden lg:block col-span-1 rounded-[40px] overflow-hidden">
              <Image
                width={4000}
                height={1000}
                src="/assets/images/login-person.png"
                alt="Auth background image"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1 flex items-center justify-center mx-auto px-6 py-16 md:py-28">
              <div className="w-full lg:w-[400px]">{children}</div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
