import { NextRequest } from "next/server";

import { googlePlaces } from "@/lib/google/places";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const countryCode = searchParams.get("countryCode");
  const addressLine = searchParams.get("addressLine");

  if (!countryCode || !addressLine) return new Response(null, { status: 400 });

  const result = await googlePlaces.getPlaceSuggestions(
    { addressLine, countryCode },
    // isQat(request.url) ? "production" : "development"
    "development"
  );

  if (!result.success) return Response.json({ error: result.error }, { status: 400 });

  return Response.json(result.data);
}
