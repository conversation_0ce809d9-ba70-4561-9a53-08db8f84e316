{"name": "lizenzero-shop-saas-storefront", "version": "0.1.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check-types": "tsc --pretty --noEmit", "check-format": "prettier --check .", "check-lint": "eslint . --ext ts --ext tsx --ext js", "format": "prettier --write .", "check-all": "pnpm run check-format && npm run check-lint && npm run check-types && npm run build", "prettier-format": "prettier --config .prettierrc 'src/**/*.ts' --write", "prepare": "husky install"}, "dependencies": {"@arthursenno/lizenzero-ui-react": "3.0.0", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-tooltip": "^1.0.7", "@strapi/blocks-react-renderer": "^1.0.1", "@stripe/react-stripe-js": "^2.6.2", "@stripe/stripe-js": "^3.1.0", "@tanstack/react-query": "^5.61.5", "@tanstack/react-table": "^8.15.3", "axios": "^1.6.5", "clsx": "^2.1.1", "cmdk": "^1.0.0", "dayjs": "^1.11.10", "filesize": "^10.1.6", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jwt-decode": "^4.0.0", "mapbox-gl": "^3.0.1", "next": "14.2.3", "next-auth": "^4.24.5", "next-intl": "^4.3.4", "nookies": "^2.5.2", "notistack": "^3.0.1", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-hook-form": "^7.49.3", "react-icons": "^5.0.1", "react-input-mask": "2.0.4", "react-joyride": "^2.7.2", "react-map-gl": "^7.1.7", "react-number-format": "^5.4.3", "recharts": "^2.12.7", "server-only": "^0.0.1", "sharp": "^0.34.2", "stripe": "^15.1.0", "tailwind-merge": "^2.3.0", "use-exit-intent": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/accept-language-parser": "^1.5.6", "@types/mapbox-gl": "^2.7.19", "@types/node": "^20.11.14", "@types/react": "^18.2.48", "@types/react-dom": "^18", "@types/react-input-mask": "2.0.4", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "autoprefixer": "^10.0.1", "eslint": "^8.55.0", "eslint-config-next": "13.5.6", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^42.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.35.0", "husky": "^8.0.0", "lint-staged": "^15.2.2", "postcss": "^8", "prettier": "3.1.1", "tailwindcss": "^3.4.15", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}