export type Result<T> = { success: true; data: T } | { success: false; error: string };

export interface GetPlaceSuggestionsParams {
  addressLine: string;
  countryCode: string;
}

export interface PlaceSuggestionsResponse {
  predictions: Array<{
    description: string;
    place_id: string;
  }>;
}

export interface PlaceSuggestion {
  formattedAddress: string;
  placeId: string;
}

export interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

export interface PlaceDetails {
  formattedAddress: string;
  country: string | null;
  city: string | null;
  route: string | null;
  streetNumber: string | null;
  postalCode: string | null;
}
