import axios from "axios";
import { ApiEndpoints } from "../endpoints";

export interface AddressSuggestion {
  formattedAddress: string;
  placeId: string;
}

export async function getAddressSuggestions(countryCode: string, addressLine: string) {
  try {
    const response = await axios.get<AddressSuggestion[]>(ApiEndpoints.addressSuggestions.getSuggestions, {
      params: {
        countryCode,
        addressLine,
      },
    });

    return { success: true, data: response.data } as const;
  } catch (error: any) {
    if (error.response && error.response.data.message) {
      return { success: false, error: error.response.data.message } as const;
    }

    return { success: false, error: error.message || "Error getting address suggestions" } as const;
  }
}

export interface AddressSuggestionDetails {
  formattedAddress: string;
  country: string | null;
  city: string | null;
  route: string | null;
  streetNumber: string | null;
  postalCode: string | null;
}

interface SuccessResponse {
  success: true;
  data: AddressSuggestionDetails;
}

interface ErrorResponse {
  success: false;
  error: string;
}

type GetAutocompleteAddressDetailsResponse = SuccessResponse | ErrorResponse;

export async function getAddressSuggestionDetails(placeId: string): Promise<GetAutocompleteAddressDetailsResponse> {
  try {
    const response = await axios.get(ApiEndpoints.addressSuggestions.getDetails, {
      params: {
        placeId,
      },
    });

    return { success: true, data: response.data } as const;
  } catch (error: any) {
    if (error.response && error.response.data.message) {
      return { success: false, error: error.response.data.message } as const;
    }

    return { success: false, error: error.message || "Error getting address suggestion details" } as const;
  }
}
