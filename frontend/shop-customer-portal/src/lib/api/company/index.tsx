import { AxiosError } from "axios";
import { api } from "@/lib/api";

import { ApiEndpoints } from "../endpoints";
import { Company, CreateCompany, UpdateCompany } from "./types";

export async function getCompany(companyId: number) {
  const res = await api.get<Company>(ApiEndpoints.company.findById(companyId));

  return res.data;
}

export async function getCompanyMyPartner(partnerId: number): Promise<any> {
  try {
    const res = await api.get<Company>(ApiEndpoints.company.findByParnterId(partnerId));
    return res;
  } catch (error) {
    return error;
  }
}

export async function createCompany(data: CreateCompany) {
  try {
    const response = await api.post<Company>(ApiEndpoints.company.create, data);

    return {
      success: true,
      data: response.data,
    } as const;
  } catch (error: any) {
    if (error.response?.status !== 409) {
      return {
        success: false,
        error: error.response?.data?.message || "An unknown error occurred",
        type: "UNKNOWN",
      } as const;
    }

    const errorMessage = error.response.data?.message?.toLowerCase() || "";

    if (errorMessage.includes("vat")) {
      return {
        success: false,
        error: "VAT ID already in use",
        type: "VAT_IN_USE",
      } as const;
    }

    if (errorMessage.includes("tax")) {
      return {
        success: false,
        error: "Tax number already in use",
        type: "TAX_IN_USE",
      } as const;
    }

    if (errorMessage.includes("document")) {
      return {
        success: false,
        error: "Document already in use",
        type: "DOCUMENT_IN_USE",
      } as const;
    }

    if (errorMessage.includes("lucid")) {
      return {
        success: false,
        error: "LUCID number already registered",
        type: "LUCID_IN_USE",
      } as const;
    }

    return {
      success: false,
      error: error.response?.data?.message || "An unknown error occurred",
      type: "UNKNOWN",
    } as const;
  }
}

export async function updateCompany(companyId: number, data: UpdateCompany) {
  try {
    const response = await api.put<Company>(ApiEndpoints.company.update(companyId), data);

    return {
      success: true,
      data: response.data,
    } as const;
  } catch (error: any) {
    if (error.response?.status === 404) {
      return {
        success: false,
        error: "Company not found",
        type: "COMPANY_NOT_FOUND",
      } as const;
    }
    if (error.response?.status !== 409) {
      return {
        success: false,
        error: error.response?.data?.message || "An unknown error occurred",
        type: "UNKNOWN",
      } as const;
    }
    const errorMessage = error.response.data?.message?.toLowerCase() || "";

    if (errorMessage.includes("vat")) {
      return {
        success: false,
        error: "VAT ID already in use",
        type: "VAT_IN_USE",
      } as const;
    }

    if (errorMessage.includes("tax")) {
      return {
        success: false,
        error: "Tax number already in use",
        type: "TAX_IN_USE",
      } as const;
    }

    if (errorMessage.includes("document")) {
      return {
        success: false,
        error: "Document already in use",
        type: "DOCUMENT_IN_USE",
      } as const;
    }

    if (errorMessage.includes("lucid")) {
      return {
        success: false,
        error: "LUCID number already registered",
        type: "LUCID_IN_USE",
      } as const;
    }

    return {
      success: false,
      error: error.response?.data?.message || "An unknown error occurred",
      type: "UNKNOWN",
    } as const;
  }
}

export interface ValidateVatIdParams {
  vat_id: string;
  country_code: string;
  company_name: string;
  company_zipcode: string;
  company_city: string;
  company_street: string;
}

interface ValidateVatIdResponse {
  is_valid: boolean;
  message: string;
  data: unknown;
  error?: string;
}

export async function validateVatId(data: ValidateVatIdParams) {
  try {
    const validationResponse = await api.post<ValidateVatIdResponse>(ApiEndpoints.company.validateVatId, data);

    return validationResponse.data;
  } catch {
    return null;
  }
}
