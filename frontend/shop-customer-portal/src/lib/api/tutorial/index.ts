import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { CustomerTutorial, PostCustomerTutorial } from "./types";

export async function getCustomerTutorial(customer_id: number | string, service_type: string) {
  try {
    const response = await api.get<CustomerTutorial[]>(ApiEndpoints.customer.tutorials, {
      params: {
        customer_id,
        service_type,
      },
    });

    return response.data;
  } catch (error: any) {
    return null;
  }
}

export const postCustomerTutorial = async (data: PostCustomerTutorial) => {
  try {
    const response = await api.post<CustomerTutorial>(ApiEndpoints.customer.tutorials, data);
    return response.data;
  } catch (error: any) {
    return null;
  }
};
