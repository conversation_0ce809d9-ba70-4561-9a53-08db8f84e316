import { ApiEndpoints } from "../endpoints";
import { api } from "..";

import { ServiceNextStep } from "./types";

interface GetServiceNextStepsParams {
  licenseId?: number;
  actionGuideId?: number;
}

export async function getServiceNextSteps(params: GetServiceNextStepsParams) {
  try {
    const res = await api.get(ApiEndpoints.serviceNextSteps.getAll, {
      params: {
        license_id: params.licenseId,
        action_guide_id: params.actionGuideId,
      },
    });

    if (!res.data) return { success: false, error: "No data found" } as const;

    const formattedData: ServiceNextStep[] = res.data.map((step: any) => ({
      id: step.id,
      action_guide_id: step.action_guide_id || null,
      license_id: step.license_id || null,
      title: step.title,
      available_date: step.available_date,
      deadline_date: step.deadline_date,
      done_at: step.done_at || null,
    }));

    return { success: true, data: formattedData } as const;
  } catch (error: any) {
    return { success: false, error: error.message || "Failed to get service steps" } as const;
  }
}

interface UpdateServiceNextStepParams {
  done_at: string | null;
}

export const updateServiceNextStep = async (id: number, data: UpdateServiceNextStepParams) => {
  try {
    await api.put(ApiEndpoints.serviceNextSteps.update(id), data);

    return { success: true } as const;
  } catch (error: any) {
    return { success: false, error: error.message || "Failed to toggle service step" } as const;
  }
};
