import { api } from "@/lib/api";
import { ApiEndpoints } from "../endpoints";
import { ShoppingCart, ShoppingCartItem, ShoppingCartJourney } from "./types";
import { ContractType } from "../contracts/types";
import { signOut } from "next-auth/react";
import { AxiosError } from "axios";

interface CreateShoppingCartParams {
  journey: ShoppingCartJourney;
  email?: string;
  force?: boolean;
}

export async function createShoppingCart(data: CreateShoppingCartParams) {
  const response = await api.post<ShoppingCart>(ApiEndpoints.shoppingCart.post, data);

  if (!response.data || String(response.status).startsWith("4")) {
    throw new Error("Unable to create cart.");
  }

  return response.data;
}

export interface UpdateShoppingCartParams {
  journey?: ShoppingCartJourney;
  journey_step?: string;
  email?: string;
  vat_percentage?: number;
  items?: Omit<Partial<ShoppingCartItem>, "price_list" | "customer_commitment">[];
  payment?: Partial<ShoppingCart["payment"]>;
  coupon?: string | null;
  coupon_type?: "LINK" | "WRITTEN" | null;
  coupon_url_link?: string | null;
  affiliate_link?: string | null;
  affiliate_type?: "AFFILIATE_LINK" | "COUPON" | null;
  affiliate_partner_id?: number | null;
  affiliate_customer_id?: number | null;
}

export async function updateShoppingCart(id: string, data: UpdateShoppingCartParams) {
  const response = await api.put<ShoppingCart>(ApiEndpoints.shoppingCart.update(id), data);

  if (!response.data || response.status !== 200) throw response;

  return response.data;
}

export async function getShoppingCartById(id: string) {
  try {
    const response = await api.get<ShoppingCart>(ApiEndpoints.shoppingCart.getById(id));

    if (!response.data || response.status !== 200) return null;

    return response.data;
  } catch (error) {
    return null;
  }
}

export async function getShoppingCartByEmail(email: string) {
  try {
    const response = await api.get<ShoppingCart>(ApiEndpoints.shoppingCart.getByEmail(email));

    if (!response.data || response.status !== 200) return null;

    return response.data;
  } catch (error) {
    return null;
  }
}

export async function getPurchasedShoppingCartByEmail(email: string) {
  try {
    const response = await api.get<ShoppingCart>(ApiEndpoints.shoppingCart.getPurchasedByEmail(email));

    if (!response.data || response.status !== 200) return null;

    return response.data;
  } catch (error) {
    return null;
  }
}

export interface AddShoppingCartItemParams {
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  year: number;
  service_type: ContractType;
}

export async function addShoppingCartItem(id: string, data: AddShoppingCartItemParams) {
  const response = await api.post<ShoppingCart>(ApiEndpoints.shoppingCart.items.create(id), data);

  if (!response.data || String(response.status).startsWith("4")) {
    throw new Error("Unable to add item to cart.");
  }

  return response.data;
}

export interface UpdateShoppingCartItemParams {
  year?: number;
  packaging_services?: {
    id: number;
    name: string;
    fractions: Record<
      string,
      {
        code: string;
        name: string;
        weight: number;
      }
    >;
  }[];
}

export async function updateShoppingCartItem(id: string, itemId: number, data: UpdateShoppingCartItemParams) {
  const response = await api.put<ShoppingCart>(ApiEndpoints.shoppingCart.items.update(id, itemId), data);

  if (!response.data || String(response.status).startsWith("4")) {
    throw new Error("Unable to update item in cart.");
  }

  return response.data;
}

export async function removeShoppingCartItem(id: string, itemId: number) {
  const response = await api.delete<ShoppingCart>(ApiEndpoints.shoppingCart.items.delete(id, itemId));

  if (!response.data || String(response.status).startsWith("4")) {
    throw new Error("Unable to remove item from cart.");
  }

  return response.data;
}
