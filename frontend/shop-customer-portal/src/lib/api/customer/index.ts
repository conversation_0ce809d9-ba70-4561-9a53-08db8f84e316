import { api } from "..";
import { Contract } from "../contracts/types";
import { ApiEndpoints } from "../endpoints";
import { UpdateCustomer } from "./types";

interface FullCustomer {
  id: number;
  first_name: string;
  last_name: string;
  salutation: string;
  email: string;
  company_id: number;
  company_name: string | null;
  document_id: null;
  created_at: string;
  updated_at: string;
  deleted_at: null;
  user_id: number;
  is_active: boolean;
  language: string | null;
  currency: string | null;
  phones: {
    id: number;
    phone_number: string;
    customer_id: number;
    phone_type: string;
  }[];
  companies: {
    id: number;
    name: string;
    vat: string | null;
    tin: string | null;
    lucid: string | null;
    customer_id: number | null;
    description: null;
    managing_director: null;
    starting: null;
    website: string | null;
    partner_id: number | null;
    emails: { id: string; email: string }[];
    address: {
      country_code: string;
      address_line: string;
      city: string;
      zip_code: string;
      street_and_number: string;
      additional_address: string;
    };
    billing: {
      full_name: string;
      country_code: string;
      country_name: string;
      company_name: string;
      street_and_number: string;
      city: string;
      zip_code: string;
      is_custom: boolean;
    };
    created_at: string;
    updated_at: string;
    deleted_at: null;
  }[];
  contracts: Contract[];
}

export async function getCustomerByUserId(id: number) {
  try {
    const response = await api.get<FullCustomer>(ApiEndpoints.customer.getCustomerByUserId(id));

    return response.data;
  } catch (error: any) {
    return null;
  }
}

export async function getCustomerById(customerId: number) {
  try {
    const response = await api.get(ApiEndpoints.customer.getCustomer(customerId));

    return response.data;
  } catch (error: any) {
    return null;
  }
}

export const updateCustomer = async (customerId: number, data: UpdateCustomer) => {
  try {
    const res = await api.put(ApiEndpoints.customer.putCustomerByUserId(customerId), data);
    return res;
  } catch (error) {
    return error;
  }
};
