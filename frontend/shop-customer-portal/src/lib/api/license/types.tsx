import { Certificate } from "../certificates/types";
import { Contract } from "../contracts/types";
import { PackagingService } from "../packaging-services/types";
import { RequiredInformation } from "../required-informations/types";
import { ServiceNextStep } from "../service-next-steps/types";
import { ThirdPartyInvoice } from "../third-party-invoice/types";
import { Termination } from "../termination/types";
import { VolumeReport } from "../volume-report/types";
import { UploadedFile } from "../file/types";
import { PriceList } from "../shoppingCart/types";

export type License = {
  id: number;
  contract_id: number;
  registration_number: string;
  registration_status: LicenseRegistrationStatus;
  clerk_control_status: LicenseClerkControlStatus;
  contract_status: LicenseContractStatus;
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  year: number;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  start_date: string;
  end_date: string | null;
  termination?: Termination;
  files: UploadedFile[];
  price_list: PriceList[];
};

export type FullLicense = License & {
  packaging_services: (PackagingService & { volume_reports: VolumeReport[] })[];
  required_informations: RequiredInformation[];
  third_party_invoices: ThirdPartyInvoice[];
  next_steps: ServiceNextStep[];
  contract: Contract & { files: File[] };
  certificates: Certificate[];
  termination: Termination;
  price_list: PriceList[];
  pendencies: LicensePendency[];
  pendencies_status: "OPEN_TO_DOS" | "IN_REVIEW" | "DONE";
  files: UploadedFile[];
};

export type LicenseRegistrationStatus = "PENDING" | "IN_REVIEW" | "REGISTRATION" | "DONE";

export type LicenseClerkControlStatus = "PENDING" | "DONE";

export type LicenseContractStatus = "ACTIVE" | "TERMINATION_PROCESS" | "TERMINATED";

export interface LicensePendency {
  type: "REQUIRED_INFORMATIONS" | "VOLUME_REPORTS" | "INVOICES";
  label: string;
}
