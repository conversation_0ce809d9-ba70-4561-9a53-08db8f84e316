import { UploadedFile } from "../file/types";
import { License } from "../license/types";

export interface ThirdPartyInvoice {
  id: number;
  title: string;
  price: number;
  issued_at: string;
  status: ThirdPartyInvoiceStatus;
  license: License;
  license_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  files: UploadedFile[];
}

export type ThirdPartyInvoiceStatus = "OPEN" | "PAID" | "UNPROCESSED" | "CANCELLED";
