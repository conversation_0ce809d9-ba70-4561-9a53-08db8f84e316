import { Partner } from "../partner/types";

export interface MarketingMaterialsResponse {
  marketingMaterials: MarketingMaterial[];
  count: number;
  pages: number;
  current_page: number;
  limit: number;
}

export type MarketingMaterial = {
  id: number;
  name: string;
  start_date?: Date;
  end_date?: Date;
  category: MarketingMaterialCategory;
  partner_restriction: MarketingMaterialPartnerRestriction;
  files: any[];
  partners: MarketingMaterialPartner[];
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date;
};

export type MarketingMaterialPartner = {
  id: number;
  marketing_material_id: number;
  partner_id: number;
  marketing_material: MarketingMaterial;
  partner: Partner;
};

export enum MarketingMaterialCategory {
  STANDARD = "STANDARD",
  SPECIFIC_MATERIAL = "SPECIFIC_MATERIAL",
}

enum MarketingMaterialPartnerRestriction {
  ALL = "ALL",
  CLUSTER = "CLUSTER",
  SPECIFIC = "SPECIFIC",
}
