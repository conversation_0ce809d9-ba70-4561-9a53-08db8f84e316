import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { VolumeReportItem } from "./types";

export interface CreateVolumeReportItemsParam {
  setup_fraction_id: number;
  setup_fraction_code: string;
  setup_column_id: number;
  setup_column_code: string;
  value: number;
}

export async function createVolumeReportItems(data: CreateVolumeReportItemsParam[]): Promise<void> {
  const response = await api.post(ApiEndpoints.volumeReportItem.create, data);
  return response.data;
}

export async function getVolumeReportItemsByVolumeReportId(volumeReportId: number) {
  const response = await api.get<VolumeReportItem[]>(ApiEndpoints.volumeReportItem.getByVolumeReportId(volumeReportId));
  return response.data;
}
