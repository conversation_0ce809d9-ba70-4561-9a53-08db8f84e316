import { UploadedFile } from "../file/types";
import { License } from "../license/types";
import { RequiredInformation } from "../required-informations/types";
import { Termination } from "../termination/types";

export type ContractType = "EU_LICENSE" | "DIRECT_LICENSE" | "ACTION_GUIDE";

export interface Contract {
  id: number;
  customer_id: number;
  type: ContractType;
  status: ContractStatus;
  title: string;
  start_date: string;
  end_date: string;
  termination_date: string | null;
  termination_id: number | null;
  termination: Termination | null;
  licenses: License[];
  action_guides: ActionGuide[];
  files: UploadedFile[];
  general_informations: RequiredInformation[];
}

export type ContractStatus = "ACTIVE" | "TERMINATION_PROCESS" | "TERMINATED";

export type ActionGuide = {
  id: number;
  contract_id: number;
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  contract_status: ContractStatus;
  termination?: Termination;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
};
