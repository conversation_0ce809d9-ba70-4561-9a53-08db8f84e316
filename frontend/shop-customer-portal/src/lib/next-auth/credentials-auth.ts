import { api } from "@/lib/api";

export interface AuthenticateResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export async function authenticate(email: string, password: string): Promise<AuthenticateResponse> {
  try {
    const response = await api.post<AuthenticateResponse>("/auth/login", { email, password });

    if (response.status === 201) {
      return response.data;
    } else if (response.status === 401) {
      throw new Error("Invalid credentials");
    }

    throw new Error("Unknown error");
  } catch (error: any) {
    throw new Error(error.response?.data?.message || error.message);
  }
}

export async function authStatus() {
  try {
    const response = await api.get("/auth/status");
    if (response.status !== 200) return false;

    return true;
  } catch (error: any) {
    return false;
  }
}
