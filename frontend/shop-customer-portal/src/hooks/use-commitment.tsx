import { getCommitment, submitCommitment, SubmitCommitmentParams } from "@/lib/api/commitment";
import { useMutation, useQuery } from "@tanstack/react-query";

export function useCommitment(countryCode: string) {
  const { data: commitment, isLoading } = useQuery({
    queryKey: ["get-commitment", countryCode],
    queryFn: async () => {
      const commitmentResponse = await getCommitment(countryCode);

      if (!commitmentResponse.success) {
        throw new Error("Failed to fetch commitment questions");
      }

      return commitmentResponse.data;
    },
  });

  const { mutate: submitCommitmentMutation, isPending: isSubmittingCommitment } = useMutation({
    mutationKey: ["submit-commitment", countryCode],
    mutationFn: async (params: SubmitCommitmentParams) => {
      const commitmentSubmissionResponse = await submitCommitment({
        country_code: countryCode,
        year: 2025,
        customer_email: params.customer_email || "",
        shopping_cart_id: params.shopping_cart_id || undefined,
        commitment_answers: params.commitment_answers,
      });

      if (!commitmentSubmissionResponse.success) throw new Error(commitmentSubmissionResponse.error);

      const customerCommitment = commitmentSubmissionResponse.data;

      return customerCommitment;
    },
  });

  return {
    commitment,
    isLoading,
    isSubmitting: isSubmittingCommitment,
    submitCommitment: submitCommitmentMutation,
  };
}
