"use client";

import { enqueueSnackbar } from "notistack";
import { ContractType } from "@/lib/api/contracts/types";
import {
  addShoppingCartItem,
  AddShoppingCartItemParams,
  removeShoppingCartItem,
  updateShoppingCart,
  updateShoppingCartItem,
  UpdateShoppingCartItemParams,
  UpdateShoppingCartParams,
} from "@/lib/api/shoppingCart";
import { queryClient } from "@/lib/react-query";
import { useMutation } from "@tanstack/react-query";
import { AxiosError, isAxiosError } from "axios";
import { destroyCookie, parseCookies, setCookie } from "nookies";
import { useLiberatedCountries } from "./use-liberated-countries";
import { ShoppingCart, ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { createContext, ReactNode, useContext } from "react";
import { JourneyType } from "@/utils/journeys";

export function getCartCookie() {
  const cookies = parseCookies();
  return cookies["lizenzero-cart-id"] || null;
}

export function setCartCookie(cartId: string) {
  setCookie(null, "lizenzero-cart-id", cartId, {
    path: "/",
    maxAge: 7 * 24 * 60 * 60, // 7 days
  });
}

export function deleteCartCookie() {
  destroyCookie(null, "lizenzero-cart-id", { path: "/" });
}

// TODO: change hard-coded journey urls
export function getAccessedJourney(pathname: string) {
  if (pathname.includes("long-journey")) return "LONG";
  if (pathname.includes("quick-journey/action-guide")) return "QUICK_ACTION_GUIDE";
  if (pathname.includes("quick-journey/license")) return "QUICK_LICENSE";
  if (pathname.includes("quick-journey")) return "QUICK_LICENSE";
  if (pathname.includes("direct-license")) return "DIRECT_LICENSE";

  return null;
}

interface ShoppingCartContextType {
  shoppingCart: ShoppingCart;
  refreshCart: () => void;
  updateCart: (data: UpdateShoppingCartParams) => Promise<void>;
  addCartItem: (countryCode: string, serviceType: ContractType) => Promise<void>;
  updateCartItem: (itemId: number, data: UpdateShoppingCartItemParams) => Promise<void>;
  deleteCartItem: (itemId: number) => Promise<void>;
  updateCartItems: (items: Partial<ShoppingCartItem>[]) => Promise<void>;
  updateQueryData: (data: ShoppingCart) => void;
  isUpdatingCart: boolean;
}

const ShoppingCartContext = createContext<ShoppingCartContextType | null>(null);

interface ShoppingCartProviderProps {
  children: ReactNode;
  shoppingCart: ShoppingCart;
  email?: string;
  journey: JourneyType | null;
}

export function ShoppingCartProvider({ children, shoppingCart, email, journey }: ShoppingCartProviderProps) {
  const { liberatedCountries } = useLiberatedCountries();
  const currentYear = new Date().getFullYear();

  const queryKey = ["shopping-cart", { email, journey }];

  const shoppingCartId = String(shoppingCart?.id);

  const updateCartMutation = useMutation({
    mutationFn: (data: UpdateShoppingCartParams) => updateShoppingCart(shoppingCartId, data),
  });
  const addCartItemMutation = useMutation({
    mutationFn: (data: AddShoppingCartItemParams) => addShoppingCartItem(shoppingCartId, data),
  });
  const updateCartItemMutation = useMutation({
    mutationFn: (data: UpdateShoppingCartItemParams & { itemId: number }) =>
      updateShoppingCartItem(shoppingCartId, data.itemId, data),
  });
  const deleteCartItemMutation = useMutation({
    mutationFn: (itemId: number) => removeShoppingCartItem(shoppingCartId, itemId),
  });

  function refresh() {
    queryClient.invalidateQueries({ queryKey });
  }

  function updateQueryData(data: ShoppingCart) {
    queryClient.setQueryData(queryKey, { ...data });
  }

  async function update(data: UpdateShoppingCartParams) {
    if (!shoppingCart) return;

    await updateCartMutation.mutateAsync(data, {
      onSuccess: (data) => !!data && updateQueryData(data),
      onError: (error) => {
        if (!isAxiosError(error)) {
          return enqueueSnackbar("We couldn't update your cart. Please, try again.", { variant: "error" });
        }

        if (error.response?.status !== 403) {
          return enqueueSnackbar("We couldn't update your cart. Please, try again.", { variant: "error" });
        }

        deleteCartCookie();
        refresh();
      },
    });
  }

  async function addCartItem(countryCode: string, serviceType: ContractType) {
    if (!shoppingCart) return;

    const data = (() => {
      if (serviceType === "DIRECT_LICENSE") {
        if (countryCode !== "DE") return null;

        return {
          country_id: 5,
          country_code: "DE",
          country_name: "Germany",
          country_flag: "https://cdn.kcak11.com/CountryFlags/countries/de.svg",
          service_type: "DIRECT_LICENSE" as ContractType,
          year: currentYear,
        };
      }

      const foundCountry = liberatedCountries.find((country) => country.code === countryCode);

      if (!foundCountry) return null;

      return {
        country_id: foundCountry.id,
        country_code: foundCountry.code,
        country_name: foundCountry.name,
        country_flag: foundCountry.flag_url,
        service_type: serviceType,
        year: currentYear,
      };
    })();

    if (!data) return;

    await addCartItemMutation.mutateAsync(data, {
      onSuccess: (data) => {
        updateQueryData(data);
        enqueueSnackbar("Item added to cart successfully", { variant: "success" });
      },
      onError: () => enqueueSnackbar("Error adding item to cart", { variant: "error" }),
    });
  }

  async function updateCartItem(itemId: number, data: UpdateShoppingCartItemParams) {
    if (!shoppingCart) return;

    await updateCartItemMutation.mutateAsync(
      { itemId, ...data },
      {
        onSuccess: (data) => updateQueryData(data),
        onError: () => enqueueSnackbar("Error updating item", { variant: "error" }),
      }
    );
  }

  async function deleteCartItem(itemId: number) {
    if (!shoppingCart) return;

    await deleteCartItemMutation.mutateAsync(itemId, {
      onSuccess: (data) => updateQueryData(data),
      onError: () => enqueueSnackbar("Error deleting item", { variant: "error" }),
    });
  }

  async function updateCartItems(items: Partial<ShoppingCartItem>[]) {
    if (!shoppingCart) return;

    await updateCartMutation.mutateAsync(
      { items },
      {
        onSuccess: (data) => !!data && updateQueryData(data),
        onError: (error) => {
          const message = isAxiosError(error) ? error.response?.data?.message : "Error updating cart";

          enqueueSnackbar(message, { variant: "error" });
        },
      }
    );
  }

  const isUpdatingCart =
    updateCartMutation.isPending ||
    addCartItemMutation.isPending ||
    updateCartItemMutation.isPending ||
    deleteCartItemMutation.isPending;

  const value = {
    shoppingCart: shoppingCart!,
    refreshCart: refresh,
    updateCart: update,
    addCartItem,
    updateCartItem,
    deleteCartItem,
    updateCartItems,
    updateQueryData,
    isUpdatingCart,
  };

  return <ShoppingCartContext.Provider value={value}>{children}</ShoppingCartContext.Provider>;
}

export function useShoppingCart() {
  const context = useContext(ShoppingCartContext);
  if (!context) {
    throw new Error("useShoppingCart must be used within a ShoppingCartProvider");
  }
  return context;
}
