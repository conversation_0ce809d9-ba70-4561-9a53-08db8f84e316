export type InviteProduct = "direct-license" | "eu-license" | "action-guide" | "all";

export type Invite = {
  id: number;
  value: string;
  type: "link" | "code";
  expiration: Date;
  product: InviteProduct;
};

export type MonthlyInviteResult = {
  invite: Invite;
  period: "m";
  product: InviteProduct;
  results: { month: string; value: number }[];
};

export type YearlyInviteResult = {
  invite: Invite;
  period: "y";
  product: InviteProduct;
  results: { year: string; value: number }[];
};

export type InviteResult = MonthlyInviteResult | YearlyInviteResult;

export type Referral = {
  id: number;
  timestamp: Date;
  total: number;
  commissionPercentage: number;
  commissionValue: number;
  orderId: string;
  orderNumber: string;
  invite: Invite;
  firstTimePurchase: boolean;
};
