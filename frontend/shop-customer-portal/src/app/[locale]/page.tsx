import { Locale } from "@/i18n/locales";
import { redirect } from "@/i18n/navigation";
import { getServerUser } from "@/utils/get-server-user";
import { getJourneyPath } from "@/utils/journeys";
import { UserTypes } from "@/utils/user";

interface HomeProps {
  params: {
    locale: Locale;
  };
}

export default async function Home({ params: { locale } }: HomeProps) {
  const user = await getServerUser();

  if (user && user.role === UserTypes.PARTNER) {
    return redirect({ href: { pathname: "/partner-hub" }, locale });
  }

  const defaultPath = getJourneyPath("LONG", "SELECT_COUNTRIES");

  if (!defaultPath) {
    return redirect({ href: { pathname: "/auth/login" }, locale });
  }

  if (!user) return redirect({ href: { pathname: defaultPath }, locale });

  if (user.role === UserTypes.CUSTOMER) {
    return redirect({ href: { pathname: "/saas" }, locale });
  }

  redirect({ href: { pathname: "/auth/login" }, locale });
}
