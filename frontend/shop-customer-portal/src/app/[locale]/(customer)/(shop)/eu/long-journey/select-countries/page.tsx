"use client";

import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { JourneySelectCountries } from "@/components/modules/shop/journeys/components/journey-select-countries";
import { JourneySelectCountriesMap } from "@/components/modules/shop/journeys/components/journey-select-countries/journey-select-countries-map";
import {
  JourneySelectCountriesFormData,
  JourneySelectCountriesProvider,
} from "@/components/modules/shop/journeys/components/journey-select-countries/journey-select-countries-provider";
import { JourneySelectCountriesSubmit } from "@/components/modules/shop/journeys/components/journey-select-countries/journey-select-countries-submit";
import { BalloonPlus, Calculator, CheckCircle, Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useFormContext } from "react-hook-form";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";

import { useTranslations } from "next-intl";
import { CountryRecommendationModal } from "@/components/_common/modals/country-recommendation-modal";

export default function SelectCountries() {
  const t = useTranslations("shop.longJourney.selectCountries");
  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={Calculator} />
      <ShopContent containerClassName="overflow-hidden">
        <div className="grid grid-cols-1 md:grid-cols-2 pb-4 md:pb-16 gap-4">
          <div className="">
            <div className="flex items-start md:items-center mb-6 gap-4 w-full">
              <BalloonPlus className="fill-primary size-6 md:size-9 flex-none" />
              <p className="text-primary text-lg font-medium">{t("checkObligations")}</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex flex-row items-center gap-2">
                <p className={`text-primary md:text-5xl text-3xl font-semibold md:leading-h1`}>{t("countries")}</p>
              </div>
            </div>
            <p className="text-primary text-base leading-5">{t("selectInMap")}</p>
          </div>

          <div className="hidden md:flex items-center">
            <div className="bg-tonal-dark-blue-90 rounded-[52px] flex items-center p-8 gap-6 w-full">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center z-10 flex-none">
                <Lightbulb className="fill-tonal-dark-blue-90 w-8 h-8" />
              </div>

              <div className="space-y-2 z-10">
                <p className="text-primary font-bold text-xl">{t("benefit.title")}</p>
                <p className="text-primary">{t("benefit.description")}</p>
              </div>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <JourneySelectCountriesProvider>
            <div>
              <JourneySelectCountries />
            </div>
            <div className="space-y-8 md:space-y-10">
              <JourneySelectCountriesMap />
              <JourneySelectCountriesSubmit />
              <div className="flex md:hidden items-center">
                <div className="bg-tonal-dark-blue-90 rounded-[52px] flex items-center p-8 gap-6 w-full">
                  <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center z-10 flex-none">
                    <Lightbulb className="fill-tonal-dark-blue-90 w-8 h-8" />
                  </div>

                  <div className="space-y-2 z-10">
                    <p className="text-primary font-bold text-xl">{t("benefit.title")}</p>
                    <p className="text-primary">{t("benefit.description")}</p>
                  </div>
                </div>
              </div>
              <SelectCountriesTopics />
            </div>
          </JourneySelectCountriesProvider>
        </div>
      </ShopContent>
      <CountryRecommendationModal />
    </>
  );
}

function SelectCountriesTopics() {
  const t = useTranslations("shop.longJourney.selectCountries.topics");
  const { shoppingCart } = useShoppingCart();

  const {
    formState: { errors },
  } = useFormContext<JourneySelectCountriesFormData>();

  const licenseItems = shoppingCart.items.filter((i) => i.service_type === "EU_LICENSE");
  const allFilled =
    !!licenseItems.length &&
    licenseItems.every((i) => !!shoppingCart.customer_commitments.find((c) => c.country_code === i.country_code));
  const isValid = !errors.items;

  const calculatorCompleted = allFilled && isValid;

  const questions = [
    {
      question: t("one.question"),
      description: t("one.description"),
    },
    {
      question: t("two.question"),
      description: t("two.description"),
    },
    {
      question: t("three.question"),
      description: t("three.description"),
    },
  ];

  return (
    <div className="w-full p-10 rounded-[40px] bg-tonal-dark-cream-96 !mt-10 md:!mt-16 flex flex-col gap-7">
      {questions.map((q, idx) => (
        <RenderCheckQuestion
          checked={idx === 0 && calculatorCompleted}
          question={q.question}
          description={q.description}
          last={idx === 2}
          key={idx}
        />
      ))}
    </div>
  );
}

const RenderCheckQuestion = ({
  checked = false,
  question,
  description,
  last,
}: {
  checked?: boolean;
  question: string;
  description: string;
  last?: boolean;
}) => {
  return (
    <div data-checked={checked} className="group w-full flex flex-row items-start gap-7">
      <div className="flex flex-col items-center justify-center gap-2">
        <div className="w-10 h-10 rounded-full bg-[#ABC7FD] group-data-[checked=true]:bg-success flex items-center justify-center">
          {checked && <CheckCircle className="fill-white w-6 h-6" />}
          {!checked && <div className="w-5 h-5 rounded-full border-[2px] border-grey-blue"></div>}
        </div>
        {!last && <div className="rounded-full bg-tonal-dark-cream-60 h-[52px] w-0.5" />}
      </div>
      <div>
        <p className="font-bold text-xl text-grey-blue leading-none mt-2">{question}</p>
        <p className="text-base text-tonal-dark-cream-30 leading-none mt-2">{description}</p>
      </div>
    </div>
  );
};
