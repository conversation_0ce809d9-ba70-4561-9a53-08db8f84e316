"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { JourneySetPassword } from "@/components/modules/shop/journeys/components/journey-set-password";
import { IconBanner, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Lightbulb, ShoppingCart } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { useActionGuidePath } from "@/hooks/path/use-action-guide-path";

export default function SetPassword() {
  const t = useTranslations("shop.quickJourney.actionGuide.setPassword");
  return (
    <>
      <ShopBreadcrumb paths={useActionGuidePath()} Icon={ShoppingCart} />
      <ShopContent>
        <TitleAndSubTitle title={t("title")} subTitle={t("subTitle")} subText={t("subText")} />
        <div className="flex md:flex-row flex-col gap-6 w-full mb-20">
          <JourneySetPassword />

          <div className="flex flex-col gap-6 md:max-w-[40%]">
            <ShopBanner title="" style={{ width: "100%" }}>
              <IconBanner
                className="text-white "
                icon={() => <Lightbulb width={24} height={24} className="fill-tonal-dark-blue-80" />}
              />

              <div className="">
                <p className="font-bold text-base">{t("banner.title")}</p>
                <span className="w-full text-sm ">{t("banner.description")}</span>
              </div>
            </ShopBanner>
          </div>
        </div>
      </ShopContent>
    </>
  );
}
