"use client";
import { SelectCountriesMapModal } from "@/components/_common/modals/select-countries-map-modal";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopLicenseStepper } from "@/components/modules/shop/components/shop-stepper";
import { JourneyBilling } from "@/components/modules/shop/journeys/components/journey-billing";
import { ShoppingBasket } from "@arthursenno/lizenzero-ui-react/Icon";
import { useActionGuidePath } from "@/hooks/path/use-action-guide-path";
import { useTranslations } from "next-intl";

export default function BillingPage() {
  const t = useTranslations("shop.common.journey.billing");
  return (
    <>
      <ShopBreadcrumb paths={useActionGuidePath()} Icon={ShoppingBasket} />
      <ShopLicenseStepper step={3} />
      <ShopContent>
        <TitleAndSubTitle title={t("title")} subTitle={t("actionGuideSubtitle")} />
        <SelectCountriesMapModal />
        <JourneyBilling />
      </ShopContent>
    </>
  );
}
