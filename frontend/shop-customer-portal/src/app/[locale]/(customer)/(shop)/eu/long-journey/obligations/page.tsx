"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";

import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShoppingCart } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";

import { JourneyObligation } from "@/components/modules/shop/journeys/components/journey-obligation";

export default function LicenseObligations() {
  const t = useTranslations("shop.longJourney.obligations");

  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={ShoppingCart} />
      <ShopContent>
        <TitleAndSubTitle subText={t("subText")} title={t("title")} subTitle={t("subTitle")} />

        <JourneyObligation />
      </ShopContent>
    </>
  );
}
