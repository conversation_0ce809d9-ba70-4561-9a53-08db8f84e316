"use client";

import Container from "@/components/_common/container/container";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopLicenseStepper } from "@/components/modules/shop/components/shop-stepper";
import PackagingEstimator from "@/components/modules/shop/journeys/germany-journey/calculator/packaging-estimator";
import { GermanyCalculator } from "@/components/modules/shop/journeys/germany-journey/germany-calculator";
import { GermanyCalculatorProvider } from "@/components/modules/shop/journeys/germany-journey/germany-calculator-provider";
import { GermanyCalculatorSubmit } from "@/components/modules/shop/journeys/germany-journey/germany-calculator-submit";
import { Icons } from "@/components/ui/icons";
import { useMediaQuery } from "@/hooks/use-media-query";
import { IconBanner, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Calculator, ControllerPainel, Lightbulb, WorkspacePremium } from "@arthursenno/lizenzero-ui-react/Icon";
import { useRef } from "react";
import { useDirectLicensePath } from "@/hooks/path/use-direct-license-path";
import { useTranslations } from "next-intl";
import { ShopTrustpilot } from "@/components/modules/shop/components/shop-trustpilot";

export default function DirectLicenseCalculator() {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const estimatorRef = useRef(null);
  const t = useTranslations("shop.directLicense.calculator");

  return (
    <GermanyCalculatorProvider>
      <ShopBreadcrumb paths={useDirectLicensePath()} Icon={Calculator} />
      <ShopLicenseStepper step={1} />
      <ShopContent>
        <TitleAndSubTitle title={t("title")} subTitle={t("subTitle")} />

        <div className="flex flex-col md:flex-row justify-between gap-6">
          <div className="space-y-10">
            <GermanyCalculator estimatorRef={estimatorRef} />
            {!isMobile && (
              <div className="flex flex-row gap-2 items-center bg-surface-01 rounded-[20px] py-4 px-10">
                <Icons.speechBubble className="size-6" />
                <p className="text-primary">{t("description")}</p>
              </div>
            )}
          </div>

          {!isMobile && (
            <div className="flex flex-col gap-8 md:max-w-[40%]">
              <ShopBanner title="" style={{ width: "100%" }}>
                <div className="flex flex-col gap-10">
                  <div className="flex items-start">
                    <IconBanner
                      className="text-white "
                      icon={() => <Lightbulb width={24} height={24} className="fill-tonal-dark-blue-80" />}
                    />

                    <div className="">
                      <p className="font-bold text-base">{t("banners.one.title")}</p>
                      <span className="w-full text-sm ">{t("banners.one.description")}</span>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <IconBanner
                      className="text-white "
                      icon={() => <ControllerPainel width={24} height={24} className="fill-tonal-dark-blue-80" />}
                    />

                    <div className="">
                      <p className="font-bold text-base">{t("banners.two.title")}</p>
                      <span className="w-full text-sm ">{t("banners.two.description")}</span>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <IconBanner
                      className="text-white "
                      icon={() => <WorkspacePremium width={24} height={24} className="fill-tonal-dark-blue-80" />}
                    />

                    <div className="">
                      <p className="font-bold text-base">{t("banners.three.title")}</p>
                      <span className="w-full text-sm ">{t("banners.three.description")}</span>
                    </div>
                  </div>
                </div>
              </ShopBanner>

              <ShopTrustpilot />

              <GermanyCalculatorSubmit />
            </div>
          )}

          {isMobile && (
            <div className="flex flex-col gap-8">
              <GermanyCalculatorSubmit />
              <ShopTrustpilot />
              <div className="flex flex-row gap-2 items-center bg-surface-01 rounded-[20px] py-4 px-10">
                <Icons.speechBubble className="size-6" />
                <p className="text-primary">{t("description")}</p>
              </div>
              <ShopBanner title="" style={{ width: "100%" }}>
                <div className="flex flex-col gap-10">
                  <div className="flex items-start">
                    <IconBanner
                      className="text-white "
                      icon={() => <Lightbulb width={24} height={24} className="fill-tonal-dark-blue-80" />}
                    />

                    <div className="">
                      <p className="font-bold text-base">{t("banners.one.title")}</p>
                      <span className="w-full text-sm ">{t("banners.one.description")}</span>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <IconBanner
                      className="text-white "
                      icon={() => <ControllerPainel width={24} height={24} className="fill-tonal-dark-blue-80" />}
                    />

                    <div className="">
                      <p className="font-bold text-base">{t("banners.two.title")}</p>
                      <span className="w-full text-sm ">{t("banners.two.description")}</span>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <IconBanner
                      className="text-white "
                      icon={() => <WorkspacePremium width={24} height={24} className="fill-tonal-dark-blue-80" />}
                    />

                    <div className="">
                      <p className="font-bold text-base">Absolutely stress free!</p>
                      <span className="w-full text-sm ">{t("banners.three.description")}</span>
                    </div>
                  </div>
                </div>
              </ShopBanner>
            </div>
          )}
        </div>
      </ShopContent>

      <div ref={estimatorRef} className="bg-surface-02 py-32">
        <Container>
          <div className="flex flex-row items-center gap-4 mb-6 lg:max-w-[70%]">
            <div className="bg-primary rounded-full min-w-[100px] min-h-[100px] flex items-center justify-center">
              <Icons.questionMarkCalculator className="size-12" />
            </div>
            <span className="text-primary font-bold text-4xl">{t("estimator.title")}</span>
          </div>

          <p className="text-primary lg:max-w-[70%] mb-16">{t("estimator.description")}</p>

          <PackagingEstimator />
        </Container>
      </div>
    </GermanyCalculatorProvider>
  );
}
