import { Footer } from "@/components/_common/footer";
import { Head<PERSON> } from "@/components/_common/header";
import { ReactNode } from "react";
import { ShopHeaderContent, ShopWrapper } from "./shop-wrapper";
import { SidebarProvider } from "@/hooks/use-sidebar";
import { StripeProvider } from "@/components/providers/stripe-provider";

export default function ShopLayout({ children }: { children: ReactNode }) {
  return (
    <StripeProvider>
      <ShopWrapper>
        <nav className="h-20 bg-background text-grey-blue shadow shadow-elevation-01-2 fixed top-0 left-0 w-full mx-auto lg:pl-0 p-0 z-[20]">
          <div className="w-full px-4">
            <div className="max-w-7xl w-full mx-auto">
              <SidebarProvider>
                <Header>
                  <ShopHeaderContent />
                </Header>
              </SidebarProvider>
            </div>
          </div>
        </nav>
        <main className="pt-20">{children}</main>
        <Footer />
      </ShopWrapper>
    </StripeProvider>
  );
}
