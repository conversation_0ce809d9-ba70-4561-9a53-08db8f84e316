import { ReactNode } from "react";

import { Footer } from "@/components/_common/footer";
import { Header } from "@/components/_common/header";
import { SaasHeader } from "@/components/modules/saas/components/saas-header";
import { SaasSidebar } from "@/components/modules/saas/components/saas-sidebar";
import { SaasHeaderContent, SaasProvider } from "@/components/providers/saas-provider";
import { SidebarProvider } from "@/hooks/use-sidebar";

interface SaasLayoutProps {
  children: ReactNode;
  params: {
    locale: string;
  };
}

export default async function SaasLayout({ children, params }: SaasLayoutProps) {
  return (
    <SaasProvider>
      <div className="h-full min-h-screen w-full flex items-stretch">
        <SidebarProvider>
          <SaasSidebar />
          <div className="flex-1">
            <SaasHeader>
              <Header>
                <SaasHeaderContent />
              </Header>
            </SaasHeader>
            {children}
          </div>
        </SidebarProvider>
      </div>
      <div className="z-50 relative">
        <Footer />
      </div>
    </SaasProvider>
  );
}
