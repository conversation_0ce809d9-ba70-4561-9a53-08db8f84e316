"use client";
import { Divider } from "@/components/_common/divider";
import { CustomRadio } from "@/components/_common/forms/customRadio/custom-radio";
import { PhoneInput } from "@/components/_common/forms/phone-input";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { updateCustomer } from "@/lib/api/customer";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Check, Delete, East, EditCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { useEffect, useState } from "react";
import { useFieldArray, useForm, useWatch } from "react-hook-form";

import { PersonalSchema } from "@/components/_common/forms/schemas";
import { useCustomer } from "@/hooks/use-customer";
import { z } from "zod";
import { cn } from "@/lib/utils";

type PersonalFormData = z.infer<typeof PersonalSchema>;

export function PersonalDataForm() {
  const { customer, invalidateCustomer } = useCustomer();

  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<PersonalFormData>({
    resolver: zodResolver(PersonalSchema),
    mode: "all",
  });

  const errors = form.formState.errors;
  const salutation = useWatch({ control: form.control, name: "salutation" });
  const phoneWatch = useWatch({ control: form.control, name: "phone" });
  const phonesWatch = useWatch({ control: form.control, name: "phones" });
  const mobileWatch = useWatch({ control: form.control, name: "mobile" });

  const phoneList = useFieldArray({
    name: "phones",
    control: form.control,
  });

  function cancelEditing() {
    if (!customer) return;

    resetForm();
    setIsEditing(false);
  }

  async function submit(data: PersonalFormData) {
    if (!customer) return;

    await form.trigger();

    try {
      setIsLoading(true);

      if (!customer?.id) {
        enqueueSnackbar("An error occurred when trying to save the data", { variant: "error" });
        return;
      }

      const customerRes: any = await updateCustomer(customer?.id, {
        phones: [data.phone, data.mobile || "", ...(data.phones || []).map((phone) => phone.phone_number)],
        first_name: data.firstName,
        last_name: data.surname,
        salutation: data.salutation,
      });

      invalidateCustomer();

      if (!customerRes?.data)
        return enqueueSnackbar("An error occurred when trying to save the data", { variant: "error" });

      setIsEditing(false);
    } catch (error) {
      enqueueSnackbar("An error occurred while updating the user data", { variant: "error" });
      resetForm();
      setIsEditing(true);
    } finally {
      setIsLoading(false);
    }
  }

  function resetForm() {
    if (!customer) return;

    form.reset({
      firstName: customer?.first_name,
      surname: customer?.last_name,
      phone: customer?.phones?.[0]?.phone_number,
      mobile: customer?.phones?.[1]?.phone_number,
      phones: customer?.phones?.slice(2).map((phone) => ({ phone_number: phone.phone_number })),
      salutation: customer?.salutation,
    });
  }

  useEffect(() => {
    resetForm();
  }, [customer]);

  return (
    <form onSubmit={form.handleSubmit(submit)}>
      <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
        <div className="flex w-full flex-row justify-between align-middle">
          <div className="flex flex-row gap-4  mb-2">
            <p className="text-primary font-medium text-xl">Personal Data</p>
            <TooltipIcon info="Personal data form" />
          </div>

          {!isEditing && (
            <Button
              color="light-blue"
              size="small"
              variant="text"
              trailingIcon={<EditCircle />}
              onClick={() => setIsEditing(true)}
            >
              Edit
            </Button>
          )}
        </div>

        <p className="text-[#808FA9] font-light text-sm mb-8">*Mandatory Fields</p>

        <div className="space-y-6 w-full">
          <div>
            <p className={cn("mb-2", !isEditing ? "text-primary" : "text-on-surface-01")}>Salutation</p>

            <div className="flex gap-4">
              <CustomRadio
                checked={salutation === "Mr."}
                onChange={() => form.setValue("salutation", "Mr.")}
                label="Mr."
                disabled={!isEditing}
              />

              <CustomRadio
                checked={salutation === "Mrs."}
                onChange={() => form.setValue("salutation", "Mrs.")}
                label="Mrs."
                disabled={!isEditing}
              />
            </div>
          </div>

          <div className="grid md:grid-cols-2 w-full gap-8">
            <Input
              label="First Name"
              {...form.register("firstName")}
              placeholder="First Name"
              variant={!isEditing ? "disabled" : errors.firstName && "error"}
              enabled={isEditing}
              rightIcon={
                !errors.firstName &&
                form.getValues("firstName") &&
                isEditing && <Check width={20} height={20} className="fill-tonal-green-40" />
              }
              errorMessage={errors.firstName?.message}
            />

            <Input
              label="Surname"
              {...form.register("surname")}
              placeholder="Surname"
              variant={!isEditing ? "disabled" : errors.surname && "error"}
              enabled={isEditing}
              rightIcon={
                !errors.surname &&
                form.getValues("surname") &&
                isEditing && <Check width={20} height={20} className="fill-tonal-green-40" />
              }
              errorMessage={errors.surname?.message}
            />
          </div>

          <div className="grid md:grid-cols-2 w-full gap-8">
            <div className="space-y-2">
              <p className="text-primary">Phone *</p>
              <PhoneInput
                disabled={!isEditing}
                name="phone"
                defaultValue={phoneWatch}
                valueSetter={(value) => form.setValue("phone", value || "")}
                errorSetter={(valid) =>
                  valid ? form.clearErrors("phone") : form.setError("phone", { message: "Invalid phone number." })
                }
                isError={!!errors.phone}
              />
              {!!errors.phone && (
                <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                  <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                    {errors.phone.message}
                  </span>
                </div>
              )}
            </div>
            <div className="space-y-2">
              <p data-disabled={!isEditing} className="text-primary data-[disabled=true]:text-tonal-dark-cream-50">
                Mobile
              </p>
              <PhoneInput
                disabled={!isEditing}
                name="mobile"
                defaultValue={mobileWatch}
                valueSetter={(value) => form.setValue("mobile", value)}
                errorSetter={(valid) =>
                  valid
                    ? form.clearErrors("mobile")
                    : form.setError("mobile", { message: "Invalid phone number.", type: "mobile" })
                }
                isError={!!errors.mobile}
                required={false}
              />
              {!!errors.mobile && (
                <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                  <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                    {errors.mobile.message}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        <Divider />
        {isEditing && (
          <>
            <div className="space-y-6 w-full">
              <p className="text-primary">Add other contact phone numbers</p>

              {phoneList.fields.map((phone, index) => (
                <div className="flex gap-6 items-center" key={phone.id}>
                  <div className="lg:w-1/2 w-full">
                    <div className="space-y-2">
                      <p
                        data-disabled={!isEditing}
                        className="text-primary data-[disabled=true]:text-tonal-dark-cream-50"
                      >
                        Phone
                      </p>
                      <div className="space-y-2">
                        <div className="flex items-center gap-4">
                          <PhoneInput
                            name={`phones.${index}`}
                            defaultValue={phonesWatch?.[index]?.phone_number}
                            disabled={!isEditing}
                            valueSetter={(value) =>
                              value
                                ? form.setValue(`phones.${index}`, { phone_number: value })
                                : form.resetField(`phones.${index}`, {
                                    keepError: true,
                                    defaultValue: { phone_number: "" },
                                  })
                            }
                            errorSetter={(valid) =>
                              valid
                                ? form.clearErrors(`phones.${index}`)
                                : form.setError(`phones.${index}`, { message: "Invalid phone number." })
                            }
                            isError={!!errors?.phones?.[index]}
                          />
                          {isEditing && (
                            <Button
                              color="dark-blue"
                              size="iconSmall"
                              variant="text"
                              trailingIcon={<Delete style={{ fill: "inherit" }} />}
                              type="button"
                              onClick={() => {
                                phoneList.remove(index);
                              }}
                            />
                          )}
                        </div>
                        {!!errors.phones && errors.phones[index] && (
                          <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                            <span slot="errorMessage" className="font-centra text-sm text-tonal-red-40">
                              {errors.phones[index]!.message}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <Button
              onClick={() => phoneList.append({ phone_number: "" })}
              color="light-blue"
              size="small"
              variant="text"
              trailingIcon={<East />}
              className="mt-4"
            >
              Add phone
            </Button>
          </>
        )}
      </div>

      {isEditing && (
        <div className="flex flex-row gap-6 justify-end w-full mt-8">
          {!isLoading && (
            <Button onClick={cancelEditing} color="dark-blue" size="medium" variant="outlined" type="button">
              Cancel
            </Button>
          )}
          <Button
            color="yellow"
            trailingIcon={<East />}
            size="medium"
            variant="filled"
            type="submit"
            disabled={!!Object.keys(errors).length || isLoading}
          >
            {isLoading ? "Loading..." : "Save"}
          </Button>
        </div>
      )}
    </form>
  );
}
