"use client";

import { Checkbox } from "@/components/_common/checkbox";
import { useCustomer } from "@/hooks/use-customer";
import { getCustomerConsentsByCustomerId, updateCustomerConsent } from "@/lib/api/consent";
import { ConsentListItem, CustomerConsent } from "@/lib/api/consent/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, EditCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { enqueueSnackbar } from "notistack";
import { useEffect, useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";

export function ConsentForm() {
  const { customer } = useCustomer();

  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [consentList, setConsentList] = useState<ConsentListItem[]>([]);
  const [initialValues, setInitialValues] = useState<ConsentListItem[]>([]);

  function cancelChanges() {
    setConsentList(initialValues);
    setIsEditing(false);
  }

  async function confirmChanges() {
    try {
      setIsEditing(true);
      setIsLoading(true);

      const consentsThatChanged = consentList.filter(
        (consent) => consent.given !== initialValues.find((c) => c.id === consent.id)?.given
      );

      await Promise.all(
        consentsThatChanged.map((consent) => updateCustomerConsent(consent.id, { given: consent.given }))
      );

      setInitialValues(consentList);
    } catch (error) {
      enqueueSnackbar("An error occurred while saving the changes", { variant: "error" });
      setConsentList(initialValues);
      setIsEditing(false);
    } finally {
      setIsLoading(false);
    }
  }

  function handleCheckConsent(id: number) {
    if (!isEditing) return;

    setConsentList((current) =>
      current.map((consent) => {
        if (consent.id !== id) return consent;

        return { ...consent, given: !consent.given };
      })
    );
  }

  async function fetchCustomerConsents() {
    try {
      if (!customer) return;

      const res = await getCustomerConsentsByCustomerId(+customer.id);

      const consentListData = res.map((consent: CustomerConsent) => ({
        id: consent.id,
        name: consent.consent?.name,
        description: consent.consent?.description,
        given: consent.given,
      }));

      setInitialValues(consentListData);
      setConsentList(consentListData);
    } catch (err) {
      console.error(err);
    }
  }

  useEffect(() => {
    fetchCustomerConsents();
  }, []);

  return (
    <>
      <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
        <div className="flex flex-row justify-between w-full">
          <p className="text-primary font-medium text-xl mb-8">Consent Management</p>

          {!isEditing && !isLoading && (
            <Button
              color="light-blue"
              size="small"
              variant="text"
              trailingIcon={<EditCircle />}
              onClick={() => setIsEditing(true)}
            >
              Edit
            </Button>
          )}
          {isLoading && <CgSpinnerAlt size={12} className="animate-spin text-support-blue" />}
        </div>

        <div className="flex flex-col gap-6">
          {consentList.map((consent) => (
            <Checkbox
              key={consent.id}
              label={consent.name}
              description={consent.description}
              checked={consent.given}
              onChange={() => isEditing && handleCheckConsent(consent.id)}
              disabled={!isEditing}
            />
          ))}
        </div>
      </div>

      {isEditing && (
        <div className="flex flex-row gap-6 justify-end w-full mt-8">
          <Button onClick={cancelChanges} color="dark-blue" size="medium" variant="outlined">
            Cancel
          </Button>
          <Button onClick={confirmChanges} color="yellow" trailingIcon={<East />} size="medium" variant="filled">
            Save
          </Button>
        </div>
      )}
    </>
  );
}
