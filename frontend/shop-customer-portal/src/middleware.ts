import "server-only";

import { getToken } from "next-auth/jwt";
import createMiddleware from "next-intl/middleware";
import { NextRequest } from "next/server";
import { getDefaultLocale } from "./i18n/locales";
import { routing } from "./i18n/routing";
import { UserTypes } from "./utils/user";

const intlMiddleware = createMiddleware(routing);

const AUTH_PATHS = ["/partner-hub/auth", "/auth"];

const PRIVATE_PATHS = [
  "/saas",
  "/partner-hub",
  "/shopping-cart",
  "/obligations",
  "/set-password",
  "/informations",
  "/billing",
  "/purchase",
  "/conclusion",
];

export default async function middleware(req: NextRequest) {
  console.log("middleware", req.url, req.nextUrl.pathname);

  // Extract locale from the URL path
  const pathname = req.nextUrl.pathname;
  const pathnameIsMissingLocale = routing.locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );

  // Get the current locale from the URL or use default
  let currentLocale = getDefaultLocale();
  if (!pathnameIsMissingLocale) {
    const localeFromPath = pathname.split("/")[1];
    if (routing.locales.includes(localeFromPath as any)) {
      currentLocale = localeFromPath;
    }
  }

  console.log("currentLocale", currentLocale);
  const session = await getToken({ req });

  const isAuthPath = AUTH_PATHS.some((url) => pathname.includes(url));
  const isPrivatePath = PRIVATE_PATHS.some((url) => pathname.includes(url));
  const isPartnerHubPath = pathname.includes("/partner-hub");
  const isSaasPath = pathname.includes("/saas");

  if (!session) {
    if (isAuthPath) return intlMiddleware(req);

    if (isPrivatePath) {
      if (pathname.includes("/partner-hub")) {
        return Response.redirect(new URL(`/${currentLocale}/partner-hub/auth/login`, req.url));
      }

      return Response.redirect(new URL(`/${currentLocale}/auth/login`, req.url));
    }

    return intlMiddleware(req);
  }

  if (isAuthPath) {
    if (pathname.includes("/partner-hub")) {
      return Response.redirect(new URL(`/${currentLocale}/partner-hub`, req.url));
    }

    return Response.redirect(new URL(`/${currentLocale}/saas`, req.url));
  }

  if (isPartnerHubPath && session.role !== UserTypes.PARTNER) {
    if (session.role === UserTypes.CUSTOMER) {
      return Response.redirect(new URL(`/${currentLocale}/saas`, req.url));
    }

    return Response.redirect(new URL(`/${currentLocale}`, req.url));
  }

  if (isSaasPath && session.role !== UserTypes.CUSTOMER) {
    return Response.redirect(new URL(`/${currentLocale}/auth/login`, req.url));
  }

  console.log("intlMiddleware");
  return intlMiddleware(req);
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/images|assets|favicon.ico|fonts|.well-known).*)"],
};
