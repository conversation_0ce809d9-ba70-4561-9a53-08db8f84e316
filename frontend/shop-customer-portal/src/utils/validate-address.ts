import axios from "axios";

const GOOGLE_MAPS_API_KEY = "AIzaSyA2LOQBpoinaIMbx9TfgFLO32XQDusue_8";

type AddressApiResult = {
  responseId: string;
  result: {
    address: {
      formattedAddress: string;
      postalAddress: {
        regionCode: string;
        languageCode: string;
        locality?: string;
      };
      addressComponents: Array<{
        componentName: { text: string };
        componentType: string;
        confirmationLevel: string;
        inferred?: boolean;
      }>;
      missingComponentTypes?: string[];
      unconfirmedComponentTypes?: string[];
    };
    geocode: {
      location: {
        latitude: number;
        longitude: number;
      };
      plusCode: {
        globalCode: string;
      };
      bounds: {
        low: {
          latitude: number;
          longitude: number;
        };
        high: {
          latitude: number;
          longitude: number;
        };
      };
      featureSizeMeters: number;
      placeId: string;
      placeTypes: string[];
    };
    verdict: {
      inputGranularity: string;
      validationGranularity: string;
      geocodeGranularity: string;
      addressComplete?: boolean;
      hasInferredComponents?: boolean;
      hasUnconfirmedComponents?: boolean;
    };
  };
};

interface ValidateAddressParams {
  countryCode: string;
  city: string;
  zipCode: string;
  streetAndNumber: string;
  additionalAddressLine?: string;
}

export async function validateAddress({
  countryCode,
  city,
  zipCode,
  streetAndNumber,
  additionalAddressLine = "",
}: ValidateAddressParams) {
  const apiKey = GOOGLE_MAPS_API_KEY;

  const addressParts = [streetAndNumber, additionalAddressLine, city];

  const addressLine = addressParts.filter(Boolean).join(" - ");

  if (!apiKey) {
    throw new Error("Google Maps API key is not set");
  }

  const requestData = {
    address: {
      regionCode: countryCode,
      postalCode: zipCode,
      locality: city,
      addressLines: [addressLine],
    },
  };

  try {
    const response = await axios.post<AddressApiResult>(
      `https://addressvalidation.googleapis.com/v1:validateAddress?key=${apiKey}`,
      requestData
    );

    const { result } = response.data;

    if (result.address.missingComponentTypes) {
      return { valid: false, type: "VALIDATION_ERROR", error: "Address is not valid" };
    }

    return {
      valid: true,
      data: {
        formattedAddress: result.address.formattedAddress,
      },
    };
  } catch (error: any) {
    if (error.response) {
      return { valid: false, type: "API_ERROR", error: error.response.data.error.message };
    }

    return { valid: false, type: "API_ERROR", error: error.message };
  }
}
