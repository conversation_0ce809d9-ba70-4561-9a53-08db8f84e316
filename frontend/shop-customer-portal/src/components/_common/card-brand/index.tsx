import { <PERSON><PERSON>ogo, MastercardLogo } from "@arthursenno/lizenzero-ui-react/Figure";
import { CreditCard } from "@arthursenno/lizenzero-ui-react/Icon";

export function CardBrand({ brand }: { brand?: string }) {
  switch (brand) {
    case "visa":
      return <VisaLogo width={40} className="w-10" />;
    case "mastercard":
      return <MastercardLogo width={40} className="w-10" />;
    default:
      return <CreditCard className="w-10" />;
  }
}
