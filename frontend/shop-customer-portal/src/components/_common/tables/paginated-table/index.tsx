"use client";
import {
  ColumnDef,
  Table,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useState } from "react";

export interface IPaginatedTable {
  data: any[];
  columns: ColumnDef<any>[];
  itemsPerPage?: number;
  isLoading?: boolean;
}

type Pagination = {
  pageIndex: number;
  pageSize: number;
};

export default function PaginatedTable({ columns, data, itemsPerPage = 9, isLoading = false }: IPaginatedTable) {
  const [pagination, setPagination] = useState<Pagination>({
    pageIndex: 0,
    pageSize: itemsPerPage,
  });

  const table = useReactTable({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: { pagination },
  });

  const setPage = (page: number) => {
    setPagination({ pageIndex: page, pageSize: itemsPerPage });
  };

  return (
    <div className="flex flex-col">
      <div className="rounded-3xl overflow-hidden">
        <table className="w-full text-primary">
          <thead className="bg-surface-01">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th key={header.id} className="px-6 py-4 text-left">
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </thead>

          <tbody className="bg-white">
            {table.getRowModel().rows.map((row) => (
              <tr key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <td key={cell.id} className="py-4 px-6">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {isLoading && <span className="text-primary mx-auto mt-2">Loading...</span>}
      <PageNavigation table={table} pagination={pagination} setPage={setPage} />
    </div>
  );
}

const PageNavigation = ({
  table,
  pagination,
  setPage,
}: {
  table: Table<any>;
  pagination: Pagination;
  setPage: (p: number) => void;
}) => {
  const currentPage: number = pagination.pageIndex + 1;
  const pages: number[] = Array.from({ length: table.getPageCount() }, (_, index) => index + 1);

  return (
    <div className="flex items-center gap-2 text-primary mx-auto mt-6">
      {pages.map((p, i) => {
        const isCurrentPage: boolean = p === currentPage;
        return (
          <span
            key={i}
            onClick={() => setPage(p - 1)}
            className={`px-2 pb-1 pt-2 rounded cursor-pointer ${isCurrentPage && "bg-primary text-white"}`}
          >
            {p}
          </span>
        );
      })}
    </div>
  );
};
