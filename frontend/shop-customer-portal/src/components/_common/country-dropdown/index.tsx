"use client";

import { KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import Image from "next/image";
import { Locale, useLocale } from "next-intl";
import { Link, usePathname } from "@/i18n/navigation";

export function CountryDropdown({ localeList }: { localeList: { name: string; code: string; flag: string }[] }) {
  const locale = useLocale();
  const pathname = usePathname();

  const currentLocale = localeList.find((option) => option.code === locale) || localeList[0];

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger className="" asChild>
        <div className="flex items-center gap-1 cursor-pointer">
          <div className="flex items-center gap-2 lg:gap-4">
            <div className="size-6 md:size-8 rounded-full overflow-hidden">
              <Image
                src={currentLocale.flag}
                alt="Country image"
                width={30}
                height={30}
                className="w-full h-full object-cover"
              />
            </div>
            <span className="text-primary font-bold">{currentLocale.code.toUpperCase()}</span>
          </div>
          <KeyboardArrowDown className="hidden lg:block fill-primary" width={24} height={24} />
        </div>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="w-[310px] bg-background py-3 rounded-2xl focus:outline-none z-[999999]"
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
          align="end"
        >
          {localeList.map((option) => (
            <DropdownMenu.Item
              key={option.code}
              className="py-5 px-4 bg-background hover:bg-tonal-dark-cream-90 cursor-pointer outline-none"
              asChild
            >
              <Link
                href={pathname}
                prefetch={false}
                locale={option.code as Locale}
                className="flex items-center gap-4 w-full"
              >
                <div className="w-6 h-6 rounded-full overflow-hidden">
                  <Image
                    src={option.flag}
                    alt="Country image"
                    width={30}
                    height={30}
                    className="w-full h-full object-cover"
                  />
                </div>
                <p className="text-primary">{option.name}</p>
              </Link>
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
