"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add } from "@arthursenno/lizenzero-ui-react/Icon";

import { JourneySelectCountriesModal } from "@/components/modules/shop/journeys/components/journey-select-countries-modal";
import { Divider } from "@/components/_common/divider";
import { CountryIcon } from "@/components/_common/country-icon";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useTranslations } from "next-intl";
import { useQueryFilter } from "@/hooks/use-query-filter";

export function SelectCountriesMapModal() {
  const { shoppingCart } = useShoppingCart();
  const t = useTranslations("shop.common.flowSelectCountries");

  const { changeParam } = useQueryFilter(["select-countries"]);

  function handleAddCountry() {
    changeParam("select-countries", "true");
  }

  const items = shoppingCart.items.filter((item) => {
    if (shoppingCart.journey === "QUICK_ACTION_GUIDE") {
      return item.service_type === "ACTION_GUIDE";
    }

    return item.service_type !== "ACTION_GUIDE";
  });

  return (
    <>
      <div className="flex justify-between md:justify-start items-center gap-6">
        <div className="flex items-center gap-3">
          {items.map((item) => (
            <CountryIcon
              key={item.id}
              country={{ name: item.country_name, flag_url: item.country_flag }}
              className="size-6 md:size-10 rounded-full"
            />
          ))}
        </div>
        <div className="hidden md:block">
          <Button leadingIcon={<Add />} color="dark-blue" variant="outlined" size="small" onClick={handleAddCountry}>
            {t("addCountryButtonLabel")}
          </Button>
        </div>
        <div className="block md:hidden">
          <Button leadingIcon={<Add />} color="light-blue" variant="text" size="small" onClick={handleAddCountry}>
            {t("addCountryButtonLabel")}
          </Button>
        </div>
        <JourneySelectCountriesModal />
      </div>
      <Divider className="mt-4 md:mt-[24px] mb-10 md:mb-[64px]" />
    </>
  );
}
