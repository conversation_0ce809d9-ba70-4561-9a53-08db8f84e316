import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { FilterAlt, KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { cn } from "@/lib/utils";

interface SelectDropdownProps {
  options: { label: string; value: string; icon?: React.ReactNode; disabled?: boolean }[];
  value: string;
  onChangeValue: (value: string) => void;
}

export function SelectDropdown({ options, value, onChangeValue }: SelectDropdownProps) {
  const selectedOption = options.find((option) => option.value === value);

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <button className="flex items-center gap-3 focus:outline-primary hover:bg-support-blue/10 rounded-full px-3 py-2">
          <div className="">
            <FilterAlt width={20} height={20} className="fill-support-blue" />
          </div>
          <div className=" text-support-blue font-bold text-nowrap">{selectedOption?.label}</div>
          <div className="">
            <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
          </div>
        </button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="min-w-[220px] bg-background py-3 rounded-2xl focus:outline-none z-[999999]"
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
        >
          {options.map((option) => (
            <DropdownMenu.Item
              key={option.value}
              data-selected={option.value === value}
              data-disabled={option.disabled}
              className={cn(
                "group py-5 px-4 text-base focus:outline-none cursor-pointer text-primary hover:bg-tonal-dark-cream-90 data-[selected=true]:not([aria-disabled=true]):font-bold data-[selected=true]:not([aria-disabled=true]):text-support-blue aria-disabled:text-tonal-dark-cream-30 aria-disabled:hover:bg-none",
                {
                  "text-tonal-dark-cream-30 cursor-not": option.disabled,
                }
              )}
              onSelect={() => onChangeValue(option.value)}
              disabled={option.disabled}
            >
              {option.label}
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
