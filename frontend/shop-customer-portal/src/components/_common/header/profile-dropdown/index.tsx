"use client";

import { UserTypes } from "@/utils/user";
import { useSession } from "next-auth/react";
import { CustomerProfileDropdown } from "./customer-dropdown";
import { PartnerProfileDropdown } from "./partner-dropdown";

export function ProfileDropdown() {
  const session = useSession();

  if (session.status !== "authenticated") return null;

  const user = session.data.user;

  if (user.role === UserTypes.CUSTOMER) return <CustomerProfileDropdown />;

  if (user.role === UserTypes.PARTNER) return <PartnerProfileDropdown />;

  return null;
}
