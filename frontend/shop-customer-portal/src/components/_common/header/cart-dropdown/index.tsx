"use client";

import { Divider } from "@/components/_common/divider";
import { Link, usePathname } from "@/i18n/navigation";
import { ShoppingCart, ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { formatCurrency } from "@/utils/formatCurrency";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { ShoppingBasket } from "@arthursenno/lizenzero-ui-react/Icon";
import * as HoverCard from "@radix-ui/react-hover-card";
import { useState } from "react";
import { CountryIcon } from "../../country-icon";
import { JOURNEYS } from "@/utils/journeys";

interface CartDropdownProps {
  shoppingCart: ShoppingCart | null;
}

export function CartDropdown({ shoppingCart }: CartDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);

  const pathname = usePathname();

  const shoppingCartUrl = (() => {
    if (!shoppingCart) return null;

    const cartJourney = JOURNEYS[shoppingCart.journey];

    if (!cartJourney) return null;

    if (cartJourney.type === "DIRECT_LICENSE") return null;

    return `${cartJourney.basePath}/shopping-cart`;
  })();

  const purchaseUrl = (() => {
    if (!shoppingCart) return null;

    const cartJourney = JOURNEYS[shoppingCart.journey];

    if (!cartJourney) return null;

    return `${cartJourney.basePath}/purchase`;
  })();

  const pathnameFromSaas = pathname.includes("/saas");

  const groupedItems =
    shoppingCart?.items.reduce(
      (acc, item) => {
        if (!acc[item.country_code]) {
          acc[item.country_code] = {
            [item.service_type]: item,
          };
        }
        acc[item.country_code][item.service_type] = item;
        return acc;
      },
      {} as Record<string, Record<string, ShoppingCartItem>>
    ) || ({} as Record<string, Record<string, ShoppingCartItem>>);

  return (
    <HoverCard.Root onOpenChange={(open) => setIsOpen(open)} openDelay={100} closeDelay={100}>
      <HoverCard.Trigger className="" asChild>
        <div className="flex items-center gap-1 cursor-pointer relative">
          <ShoppingBasket width={32} className="fill-primary hover:fill-support-blue cursor-pointer" />
          {!!shoppingCart?.items.length && (
            <div className="absolute size-4 rounded-full bg-tertiary flex justify-center items-center right-[-5px] top-[-5px]">
              <p className="text-primary font-bold text-xs">{shoppingCart?.items.length}</p>
            </div>
          )}
        </div>
      </HoverCard.Trigger>

      <HoverCard.Portal>
        <HoverCard.Content
          className="w-[470px] bg-background rounded-2xl focus:outline-none z-[999999]"
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
          align="end"
        >
          {!shoppingCart?.items.length && <h1 className="text-center text-primary px-4 py-6">Cart is empty!</h1>}
          {!!shoppingCart?.items.length && (
            <div className="px-9 py-9 space-y-6">
              <h1 className="text-2xl text-primary font-bold">Your order</h1>
              <div className="space-y-4 pb-4 h-full max-h-56 overflow-y-auto overflow-x-auto">
                {Object.entries(groupedItems).map(([countryCode, groupedItem]) => {
                  const license = groupedItem["EU_LICENSE"];
                  const actionGuide = groupedItem["ACTION_GUIDE"];
                  const directLicense = groupedItem["DIRECT_LICENSE"];

                  const flagUrl = (license || actionGuide || directLicense)?.country_flag;
                  const countryName = (license || actionGuide || directLicense)?.country_name;

                  return (
                    <div key={countryCode} className="py-3 px-4 rounded-2xl border border-surface-03 bg-background">
                      <div>
                        <div className="flex items-center justify-between">
                          <div className="py-4 flex items-center gap-3">
                            <CountryIcon
                              country={{ flag_url: flagUrl, name: countryName }}
                              className="size-7 flex-none"
                            />
                            <p className="text-primary text-xl font-bold">{countryName}</p>
                          </div>
                        </div>
                        <Divider initialMarginDisabled />
                      </div>
                      {license && (
                        <>
                          <div>
                            <div className="flex items-center py-4 gap-3 justify-between">
                              <p className="text-primary text-sm">Registration fee</p>
                              <p className="text-primary text-sm font-bold flex-none">
                                {formatCurrency(license.price_list?.registration_fee || 0)}
                              </p>
                            </div>
                            <hr className="text-on-surface-01 opacity-30" />
                          </div>
                          <div>
                            <div className="flex items-center py-4 gap-3 justify-between">
                              <p className="text-primary text-sm">Handling fee</p>
                              <p className="text-primary text-sm font-bold flex-none">
                                {formatCurrency(license.price_list?.handling_fee || 0)}
                              </p>
                            </div>
                            <hr className="text-on-surface-01 opacity-30" />
                          </div>
                        </>
                      )}
                      {actionGuide && (
                        <div>
                          <div className="flex items-center py-4 gap-3 justify-between">
                            <p className="text-primary text-sm">Action guide</p>
                            <p className="text-primary text-sm font-bold flex-none">
                              {formatCurrency(actionGuide.price_list?.price || 0)}
                            </p>
                          </div>
                          <hr className="text-on-surface-01 opacity-30" />
                        </div>
                      )}
                      {directLicense && (
                        <div>
                          <div className="flex items-center py-4 gap-3 justify-between">
                            <p className="text-primary text-sm">Direct license</p>
                            <p className="text-primary text-sm font-bold flex-none">
                              {formatCurrency(directLicense.price)}
                            </p>
                          </div>
                          <hr className="text-on-surface-01 opacity-30" />
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
              <div className="mt-7 p-4 flex items-center justify-between rounded-b-2xl bg-[#CAE5EC]">
                <p className="text-primary font-bold">Total</p>
                <p className="text-primary font-bold text-xl">{formatCurrency(shoppingCart?.total)}</p>
              </div>
              <p className="italic text-on-surface-01 text-sm">
                A Third party fee may be charged at any time if needed to go through with the License Process.
              </p>

              {pathnameFromSaas && (
                <div className="flex items-center justify-end w-full">
                  <div className="grid grid-cols-2 gap-8">
                    {shoppingCartUrl ? (
                      <Link
                        href={{
                          pathname: shoppingCartUrl,
                          query: { fromSaas: true },
                        }}
                      >
                        <Button
                          color="light-blue"
                          variant="text"
                          size="medium"
                          style={{ width: "100%" }}
                          className="h-full justify-start"
                        >
                          Go to shopping cart
                        </Button>
                      </Link>
                    ) : (
                      <div />
                    )}
                    <Link href={purchaseUrl || ""}>
                      <Button
                        color="yellow"
                        variant="filled"
                        size="medium"
                        style={{ width: "100%" }}
                        className="text-nowrap"
                      >
                        Finalize purchase
                      </Button>
                    </Link>
                  </div>
                </div>
              )}
            </div>
          )}
        </HoverCard.Content>
      </HoverCard.Portal>
    </HoverCard.Root>
  );
}
