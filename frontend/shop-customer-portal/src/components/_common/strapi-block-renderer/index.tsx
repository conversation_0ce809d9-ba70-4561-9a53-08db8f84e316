"use client";

import Image from "next/image";

import { BlocksRenderer, type BlocksContent } from "@strapi/blocks-react-renderer";

export function StrapiBlockRenderer({ content }: { readonly content: BlocksContent }) {
  if (!content) return null;

  return (
    <BlocksRenderer
      content={content}
      blocks={{
        image: ({ image }) => {
          return <Image src={image.url} width={image.width} height={image.height} alt={image.alternativeText || ""} />;
        },
      }}
    />
  );
}
