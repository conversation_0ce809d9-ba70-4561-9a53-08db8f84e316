import { CSSProperties } from "react";

interface IDividerProps {
  className?: string;
  style?: CSSProperties;
  initialMarginDisabled?: boolean;
}

export function Divider({ className, style, initialMarginDisabled }: IDividerProps) {
  return (
    <div
      className={`border-t rounded  border-tonal-dark-cream-80 w-full ${
        !initialMarginDisabled && "my-8 md:my-10"
      } ${className}`}
      style={style}
    ></div>
  );
}
