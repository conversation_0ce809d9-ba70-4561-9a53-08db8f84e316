import { BiChevronDown } from "react-icons/bi";

import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { cn } from "@/lib/utils";
import { formatCurrency } from "@/utils/formatCurrency";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { useTranslations } from "next-intl";
import { Skeleton } from "@/components/ui/skeleton";

interface JourneyThirdPartyProps {
  className?: string;
}

export function JourneyThirdParty({ className }: JourneyThirdPartyProps) {
  const t = useTranslations("shop.common.journey.purchase.thirdParty");
  const { shoppingCart, isUpdatingCart } = useShoppingCart();

  const customerCommitments = shoppingCart.customer_commitments;
  const licenseItems = shoppingCart.items.filter(
    (item) =>
      item.service_type === "EU_LICENSE" && !!customerCommitments.find((c) => c.country_code === item.country_code)
  );

  const calculatorTotal = licenseItems.reduce((total, item) => {
    const calculator = item.calculator;

    if (!calculator) return total;

    const licenseCosts = calculator.license_costs;

    return total + licenseCosts;
  }, 0);

  const authorizedRepresentativeTotal = licenseItems.reduce((total, item) => {
    const customerCommitment = customerCommitments.find((c) => c.country_code === item.country_code);

    return total + (customerCommitment?.service_setup.representative_tier?.price || 0);
  }, 0);

  const otherCostsTotal = licenseItems.reduce((total, item) => {
    const customerCommitment = customerCommitments.find((c) => c.country_code === item.country_code);

    return (
      total +
      (customerCommitment?.service_setup.other_costs || []).reduce((total, otherCost) => (total += otherCost.price), 0)
    );
  }, 0);

  if (isUpdatingCart) return <JourneyThirdPartyItemSkeleton />;

  return (
    <div className={cn("flex flex-col rounded-xl p-4 bg-surface-02", className)}>
      <div className="space-y-2 mb-4">
        <p className="text-primary font-bold">{t("title")}</p>
        <p className="text-xs text-tonal-dark-cream-30">{t("description")}</p>
      </div>
      <div>
        <div className="flex items-center justify-between gap-2 py-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <p className="text-primary text-sm">{t("licensingCostsTotal")}*</p>
              <QuestionTooltip>
                <QuestionTooltipDescription>{t("licensingCostsTotalTooltip")}</QuestionTooltipDescription>
              </QuestionTooltip>
            </div>
            <p className="text-xs text-tonal-dark-cream-60">{t("annualHandlingFee")}</p>
          </div>
          <p className="text-sm text-primary font-bold flex-none">{formatCurrency(calculatorTotal)}</p>
        </div>
        <div className="flex items-center justify-between gap-2 py-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <p className="text-primary text-sm">{t("authorizedRepresentativeCosts.total")}</p>
              <QuestionTooltip>
                <QuestionTooltipTitle className="text-primary">
                  {t("authorizedRepresentativeCosts.tooltip")}
                </QuestionTooltipTitle>
                <QuestionTooltipDescription>
                  {t("authorizedRepresentativeCosts.tooltipDescription")}
                </QuestionTooltipDescription>
              </QuestionTooltip>
            </div>
          </div>
          <p className="text-sm text-primary font-bold flex-none">{formatCurrency(authorizedRepresentativeTotal)}</p>
        </div>
        <Divider style={{ margin: 0 }} />
        <div className="flex items-center justify-between gap-2 py-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <p className="text-primary text-sm">{t("otherCosts.total")}</p>
              <QuestionTooltip>
                <QuestionTooltipTitle className="text-primary">{t("otherCosts.tooltip")}</QuestionTooltipTitle>
                <QuestionTooltipDescription>{t("otherCosts.tooltipDescription")}</QuestionTooltipDescription>
              </QuestionTooltip>
            </div>
          </div>
          <p className="text-sm text-primary font-bold flex-none">{formatCurrency(otherCostsTotal)}</p>
        </div>
        <Divider style={{ margin: 0 }} />
      </div>
      <div className="flex flex-col gap-4 mt-6">
        {licenseItems.map((item) => (
          <ThirdPartyItem key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
}

function JourneyThirdPartyItemSkeleton() {
  return (
    <div className="flex flex-col rounded-xl p-4 bg-surface-02 gap-5">
      <div className="space-y-2">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-10 w-full" />
      </div>
      <div>
        {[...Array(3)].map((_, i) => (
          <div key={i}>
            <div className="flex items-center justify-between gap-2 py-4">
              <div className="space-y-1">
                <Skeleton className="h-5 w-40" />
              </div>
              <Skeleton className="h-5 w-20" />
            </div>
            <Divider style={{ margin: 0 }} />
          </div>
        ))}
      </div>
    </div>
  );
}

interface ThirdPartyItemProps {
  item: ShoppingCartItem;
}

function ThirdPartyItem({ item }: ThirdPartyItemProps) {
  const t = useTranslations("shop.common.journey.purchase.thirdParty.item");
  const { shoppingCart } = useShoppingCart();

  const calculatorTotal = (() => {
    if (!item.calculator) return 0;

    const licenseCosts = item.calculator.license_costs || 0;

    return licenseCosts;
  })();

  const customerCommitment = shoppingCart.customer_commitments.find((c) => c.country_code === item.country_code);

  const authorizedRepresentativeTotal = customerCommitment?.service_setup.representative_tier?.price || 0;

  const otherCostsTotal = (customerCommitment?.service_setup.other_costs || []).reduce(
    (total, otherCost) => (total += otherCost.price),
    0
  );

  return (
    <details className="group bg-white border border-surface-03 px-4 rounded-2xl">
      <summary className="flex items-center justify-between gap-2 py-4">
        <div className="flex items-center gap-3">
          <CountryIcon country={{ name: item.country_name, flag_url: item.country_flag }} className="size-7" />
          <p className="text-primary font-bold">{item.country_name}</p>
        </div>
        <div className="flex items-center gap-3">
          <p className="text-sm text-primary font-bold flex-none">{formatCurrency(calculatorTotal)}</p>
          <BiChevronDown width={32} className="fill-primary transition-all duration-300 size-6 group-open:rotate-180" />
        </div>
      </summary>
      <div className="py-4 flex items-center justify-between">
        <p className="text-primary text-sm">{t("licensingCosts")} </p>
        <p className="flex-none font-bold text-tonal-dark-cream-40">{formatCurrency(calculatorTotal)}</p>
      </div>
      <div className="py-4 flex items-center justify-between">
        <p className="text-primary text-sm">{t("authorizedRepresentativeCosts")} </p>
        <p className="flex-none font-bold text-tonal-dark-cream-40">{formatCurrency(authorizedRepresentativeTotal)}</p>
      </div>
      <div className="py-4 flex items-center justify-between">
        <p className="text-primary text-sm">{t("otherCosts")}</p>
        <p className="flex-none font-bold text-tonal-dark-cream-40">{formatCurrency(otherCostsTotal)}</p>
      </div>
    </details>
  );
}
