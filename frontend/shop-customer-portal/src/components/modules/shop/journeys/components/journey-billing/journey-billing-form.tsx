"use client";

import { <PERSON><PERSON><PERSON> } from "@/components/_common/card-brand";
import { CountryIcon } from "@/components/_common/country-icon";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { Combobox } from "@/components/ui/combobox";
import { Skeleton } from "@/components/ui/skeleton";
import { useCustomer } from "@/hooks/use-customer";
import { getCustomerActivePaymentMethods } from "@/lib/api/payment";
import { COUNTRIES } from "@/utils/consts/countries";
import { Check, RadioSelected, RadioUnselected } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import { CardCvcInput } from "@/components/_common/credit-card/card-cvc";
import { CardExpDateInput } from "@/components/_common/credit-card/card-exp-date";
import { CardNumberInput } from "@/components/_common/credit-card/card-number";
import { PaymentMethodType } from "@/lib/api/payment/types";
import { PaymentMethod } from "./journey-billing-payment-methods/payment-method";
import { BillingFormData } from "./journey-billing-provider";
import { PAYMENT_METHODS, PAYMENT_METHODS_KEYS } from "./payment-methods";
import { Checkbox } from "@/components/_common/checkbox";
import { useEffect } from "react";

export function JourneyBillingForm() {
  const { customer } = useCustomer();

  const t = useTranslations("shop.common.journey.billing");

  const form = useFormContext<BillingFormData>();

  const paymentMethodsQuery = useQuery({
    queryKey: ["customer-payment-methods"],
    queryFn: async () => {
      const paymentMethods = await getCustomerActivePaymentMethods(customer!.id);

      if (!paymentMethods || !paymentMethods.length) {
        form.setValue("paymentMethodType", "CREDIT_CARD");
        form.setValue("paymentMethodId", undefined);
        form.setValue("card.firstName", customer!.first_name);
        form.setValue("card.lastName", customer!.last_name);

        return paymentMethods;
      }

      const paymentMethod = paymentMethods[0];

      form.setValue("paymentMethodType", paymentMethod.type as PaymentMethodType);
      form.setValue("paymentMethodId", paymentMethod.id);

      return paymentMethods;
    },
    enabled: !!customer,
  });

  const isLoading = !customer || paymentMethodsQuery.isLoading;

  const customerPaymentMethods = paymentMethodsQuery.data;
  const customerCompany = customer?.company;

  function handleOnChangeCustomBilling(usePersonalDataOnBilling: boolean) {
    if (!customerCompany || !customerCompany.billing) return;

    if (!usePersonalDataOnBilling) {
      form.setValue("billing", {
        fullName: customerCompany.billing?.full_name,
        city: customerCompany.billing?.city,
        zipCode: customerCompany.billing?.zip_code,
        streetAndNumber: customerCompany.billing?.street_and_number,
        countryCode: customerCompany.billing?.country_code,
        countryName: customerCompany.billing?.country_name,
        companyName: customerCompany.billing?.company_name,
      });
      return;
    }

    form.setValue("billing", undefined);
  }

  function handleOnChangePaymentMethod(value: string) {
    if (!customer) return;

    form.setValue("saveForFuturePurchase", false);

    if (!PAYMENT_METHODS_KEYS.includes(value as PaymentMethodType)) {
      form.setValue("paymentMethodType", "CREDIT_CARD");
      form.setValue("paymentMethodId", value);
      return;
    }

    form.setValue("paymentMethodId", undefined);
    form.setValue("paymentMethodType", value as PaymentMethodType);

    if (value === "CREDIT_CARD") {
      form.setValue("card", {
        firstName: customer.first_name,
        lastName: customer.last_name,
      });
      form.clearErrors("card");
      return;
    }

    form.setValue("card", undefined);
  }

  useEffect(() => {
    if (!customerCompany || !customerCompany.billing) return;

    form.setValue("usePersonalDataOnBilling", !customerCompany.billing.is_custom);

    form.setValue(
      "billing",
      customerCompany.billing.is_custom
        ? {
            fullName: customerCompany.billing?.full_name,
            city: customerCompany.billing?.city,
            zipCode: customerCompany.billing?.zip_code,
            streetAndNumber: customerCompany.billing?.street_and_number,
            countryCode: customerCompany.billing?.country_code,
            countryName: customerCompany.billing?.country_name,
            companyName: customerCompany.billing?.company_name,
          }
        : undefined
    );
  }, [customerCompany]);

  const paymentMethodId = useWatch({ control: form.control, name: "paymentMethodId" });
  const paymentMethodType = useWatch({ control: form.control, name: "paymentMethodType" });
  const usePersonalDataOnBilling = useWatch({ control: form.control, name: "usePersonalDataOnBilling" });
  const billingCountryCode = useWatch({ control: form.control, name: "billing.countryCode" });

  const errors = form.formState.errors;

  return (
    <div className="md:flex-1">
      <div className="bg-on-primary rounded-4xl md:p-10 p-2">
        <div className="flex items-center gap-4 mb-9">
          <h1 className="text-grey-blue font-bold text-2xl mt-1">{t("paymentMethodTitle")}</h1>
          <TooltipIcon info={t("paymentMethodTooltip")} />
        </div>
        {isLoading && (
          <div className="bg-white rounded-2xl">
            <div className="space-y-4 p-4">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          </div>
        )}
        {!isLoading && (
          <>
            <div className="bg-white rounded-2xl">
              <div className="flex flex-col gap-x-6">
                {customerPaymentMethods?.map((pm) => (
                  <div
                    key={pm.id}
                    className="flex flex-row gap-4 items-center p-5"
                    onClick={() => handleOnChangePaymentMethod(pm.id)}
                  >
                    {paymentMethodId === pm.id ? (
                      <RadioSelected width={16} className="fill-primary" />
                    ) : (
                      <RadioUnselected width={16} className="fill-on-surface-01" />
                    )}
                    <CardBrand brand={pm.card_brand} />
                    <p className="text-primary">**** {pm.card_last_4}</p>
                  </div>
                ))}
                <div className="flex flex-col w-full">
                  {PAYMENT_METHODS.map((method, index) => (
                    <>
                      <PaymentMethod
                        key={method.value}
                        label={method.label}
                        icons={method.icons}
                        value={method.value}
                        paymentMethod={paymentMethodType}
                        paymentMethodId={paymentMethodId}
                        handleSetPaymentMethod={handleOnChangePaymentMethod}
                      />
                      {paymentMethodType === "CREDIT_CARD" && method.value === "CREDIT_CARD" && !paymentMethodId && (
                        <div className="flex flex-col gap-2 w-full">
                          <div className="flex flex-col md:flex-row gap-2 md:gap-6 px-5">
                            <Input
                              label={t("surname.label")}
                              placeholder={t("surname.placeholder")}
                              errorMessage={errors?.card?.lastName && errors.card?.lastName.message}
                              variant={errors.card?.lastName && "error"}
                              rightIcon={
                                !errors.card?.lastName &&
                                form.getValues("card.lastName") && (
                                  <Check width={20} height={20} className="fill-tonal-green-40" />
                                )
                              }
                              {...form.register("card.lastName")}
                            />
                            <Input
                              label={t("firstName.label")}
                              placeholder={t("firstName.placeholder")}
                              errorMessage={errors.card?.firstName && errors.card?.firstName.message}
                              variant={errors.card?.firstName && "error"}
                              rightIcon={
                                !errors.card?.firstName &&
                                form.getValues("card.firstName") && (
                                  <Check width={20} height={20} className="fill-tonal-green-40" />
                                )
                              }
                              {...form.register("card.firstName")}
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 px-5 w-full">
                            <div className="md:col-span-2">
                              <CardNumberInput />
                            </div>
                            <div className="col-span-1">
                              <CardCvcInput />
                            </div>
                            <div className="col-span-1">
                              <CardExpDateInput />
                            </div>
                          </div>
                          <div className="mx-5 my-3">
                            <Controller
                              name="saveForFuturePurchase"
                              render={({ field }) => (
                                <Checkbox
                                  label={t("saveForFuturePurchase.label")}
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </div>
                        </div>
                      )}
                    </>
                  ))}
                </div>
              </div>
            </div>

            <div className="my-3">
              <Controller
                name="usePersonalDataOnBilling"
                render={({ field }) => (
                  <Checkbox
                    label={t("usePersonalDataOnBilling")}
                    checked={field.value}
                    onChange={(e) => {
                      handleOnChangeCustomBilling(e.target.checked);
                      field.onChange(e);
                    }}
                  />
                )}
              />
            </div>
            {!usePersonalDataOnBilling && (
              <div className="">
                <div className="flex items-center pt-5">
                  <h1 className="text-grey-blue font-bold text-2xl">{t("billingInformation.title")}</h1>
                  <TooltipIcon info={t("billingInformation.tooltip")} />
                </div>
                <p className="text-tonal-dark-cream-50 text-sm py-5 font-light">
                  *{t("billingInformation.mandatoryFields")}
                </p>
                <div className="flex flex-col gap-y-3 ">
                  <div className="grid md:grid-cols-2 w-full gap-6">
                    <Input
                      label={`${t("fullName.label")} *`}
                      placeholder={t("fullName.placeholder")}
                      errorMessage={errors.billing?.fullName && errors.billing?.fullName.message}
                      variant={errors.billing?.fullName && "error"}
                      rightIcon={
                        !errors.billing?.fullName &&
                        form.getValues("billing.fullName") && (
                          <Check width={20} height={20} className="fill-tonal-green-40" />
                        )
                      }
                      {...form.register("billing.fullName")}
                    />

                    <div className="space-y-2">
                      <p className="text-primary">{t("companyCountry.label")} *</p>
                      <Combobox
                        items={COUNTRIES.map((c) => ({ label: c.name, value: c.code, flag_url: c.flag_url }))}
                        placeholder={t("companyCountry.placeholder")}
                        emptyText={t("companyCountry.emptyText")}
                        searchText={t("companyCountry.searchText")}
                        value={billingCountryCode}
                        onSelect={(value) => {
                          if (!value) return;

                          form.setValue("billing.countryCode", value.value);
                          form.setValue("billing.countryName", value.label);
                        }}
                        invalid={!!errors.billing?.countryCode}
                        renderItem={(item) => (
                          <div className="flex items-center gap-3">
                            <CountryIcon country={{ name: item.label, flag_url: item.flag_url }} className="size-6" />
                            {item.label}
                          </div>
                        )}
                      />
                      {!!errors.billing?.countryCode && (
                        <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                          <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                            {errors.billing?.countryCode.message}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="grid md:grid-cols-2 w-full gap-6">
                    <Input
                      label={t("companyName.label")}
                      placeholder={t("companyName.placeholder")}
                      errorMessage={errors.billing?.companyName && errors.billing?.companyName.message}
                      variant={errors.billing?.companyName && "error"}
                      rightIcon={
                        !errors.billing?.companyName &&
                        form.getValues("billing.companyName") && (
                          <Check width={20} height={20} className="fill-tonal-green-40" />
                        )
                      }
                      {...form.register("billing.companyName")}
                    />
                    <Input
                      label={`${t("streetNumber.label")} *`}
                      placeholder={t("streetNumber.placeholder")}
                      errorMessage={errors.billing?.streetAndNumber?.message}
                      {...form.register("billing.streetAndNumber")}
                      rightIcon={
                        !errors.billing?.streetAndNumber &&
                        form.getValues("billing.streetAndNumber") && (
                          <Check width={20} height={20} className="fill-tonal-green-40" />
                        )
                      }
                      variant={errors.billing?.streetAndNumber && "error"}
                    />
                  </div>
                  <div className="grid md:grid-cols-2 w-full gap-6">
                    <Input
                      label={`${t("zipCode.label")} *`}
                      placeholder={t("zipCode.placeholder")}
                      errorMessage={errors.billing?.zipCode?.message}
                      {...form.register("billing.zipCode")}
                      rightIcon={
                        !errors.billing?.zipCode &&
                        form.getValues("billing.zipCode") && (
                          <Check width={20} height={20} className="fill-tonal-green-40" />
                        )
                      }
                      variant={errors.billing?.zipCode && "error"}
                    />
                    <Input
                      label={`${t("city.label")} *`}
                      placeholder={t("city.placeholder")}
                      errorMessage={errors.billing?.city && errors.billing?.city.message}
                      variant={errors.billing?.city && "error"}
                      rightIcon={
                        !errors.billing?.city &&
                        form.getValues("billing.city") && (
                          <Check width={20} height={20} className="fill-tonal-green-40" />
                        )
                      }
                      {...form.register("billing.city")}
                    />
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
