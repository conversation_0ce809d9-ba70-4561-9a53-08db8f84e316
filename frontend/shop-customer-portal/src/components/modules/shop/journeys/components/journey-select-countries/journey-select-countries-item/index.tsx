"use client";

import { Delete, <PERSON>board<PERSON>rrow<PERSON><PERSON>, <PERSON><PERSON><PERSON>, CheckCircle } from "@arthursenno/lizenzero-ui-react/Icon";

import { useFormContext } from "react-hook-form";
import { JourneySelectCountriesFormData } from "../journey-select-countries-provider";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { JourneySelectCountriesItemCommitment } from "./journey-select-countries-item-commitment";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CountryIcon } from "@/components/_common/country-icon";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { useTranslations } from "next-intl";
interface JourneySelectCountriesItemProps {
  cartItem: ShoppingCartItem;
}

export function JourneySelectCountriesItem({ cartItem }: JourneySelectCountriesItemProps) {
  const t = useTranslations("shop.longJourney.selectCountries.item");
  const { shoppingCart, deleteCartItem } = useShoppingCart();

  const form = useFormContext<JourneySelectCountriesFormData>();

  async function handleRemoveItem() {
    if (!(shoppingCart.items.length - 1)) form.reset({ items: {} });

    await deleteCartItem(cartItem.id);

    form.clearErrors(`items.${cartItem.country_code}`);
  }

  const customerCommitment = shoppingCart.customer_commitments.find((c) => c.country_code === cartItem.country_code);

  const errors = form.formState.errors;

  const countryError = !!errors && !!errors.items && errors.items[cartItem.country_code];

  const feedbackStatus = (() => {
    if (countryError) return "error";

    if (customerCommitment) return "success";

    return null;
  })();

  return (
    <details className="group rounded-4xl bg-background overflow-hidden open:shadow-elevation-02-1">
      <summary className="flex items-center justify-between py-5 px-4 md:px-7">
        <div className="flex items-center gap-4">
          <Button variant="text" size="iconSmall" color="gray" onClick={handleRemoveItem} type="button">
            <Delete className="size-5 fill-primary" />
          </Button>
          <CountryIcon
            country={{ name: cartItem.country_name, flag_url: cartItem.country_flag }}
            className="size-6 md:size-8"
          />
          <p className="text-xl md:text-2xl font-bold text-primary">{cartItem.country_name}</p>
          {feedbackStatus && feedbackStatus === "success" && <CheckCircle className="fill-success w-5 h-5 flex-none" />}
          {feedbackStatus && feedbackStatus === "error" && <Error className="fill-error w-5 h-5 flex-none" />}
        </div>
        <div className="flex items-center gap-2">
          <QuestionTooltip>
            <QuestionTooltipDescription>{t("tooltip")}</QuestionTooltipDescription>
          </QuestionTooltip>
          <KeyboardArrowUp className="size-8 fill-primary group-open:-rotate-180 transition-all duration-300" />
        </div>
      </summary>
      <div className="px-4 md:px-10 pb-5">
        <JourneySelectCountriesItemCommitment cartItem={cartItem} />
      </div>
    </details>
  );
}
