"use client";

import { CreateNewPassword } from "@/components/_common/forms/schemas";
import { LoginModal } from "@/components/_common/modals/login-modal";
import { PasswordStrengthBar } from "@/components/_common/password-strength-bar";
import { PasswordInput } from "@/components/ui/password-input";
import { useRouter } from "@/i18n/navigation";
import { createPassword } from "@/lib/api/account";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm, useWatch } from "react-hook-form";

interface IForm {
  password: string;
  confirmPassword: string;
}

export function JourneySetPassword() {
  const t = useTranslations("shop.common.journey.setPassword");
  const globalT = useTranslations("global");
  const router = useRouter();
  const session = useSession();

  const form = useForm<IForm>({
    resolver: zodResolver(CreateNewPassword),
    mode: "onBlur",
  });

  const password = useWatch({ control: form.control, name: "password" });
  const confirmPassword = useWatch({ control: form.control, name: "confirmPassword" });

  const isNotValidConfirmPassword = password && confirmPassword && password !== confirmPassword;

  const [loading, setLoading] = useState(false);
  const [openModalCreateAccount, setOpenModalCreateAccount] = useState(false);

  const errors = form.formState.errors;

  async function onSubmit() {
    if (isNotValidConfirmPassword || session.status !== "authenticated") {
      return;
    }

    setLoading(true);

    const user = session.data?.user;

    const createPasswordResponse = await createPassword(Number(user.id), password);

    setLoading(false);

    if (!createPasswordResponse || !createPasswordResponse.access_token) return;

    await session.update(createPasswordResponse);

    return router.push("./informations");
  }

  return (
    <>
      <form
        className="flex flex-col gap-4 md:gap-6 flex-1 bg-surface-02 p-4 py-6 md:p-8 rounded-[40px]"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <p className="text-base text-[#808FA9]">*{globalT("validation.mandatoryFields")}</p>

        <PasswordInput
          {...form.register("password")}
          label={t("password.label")}
          placeholder={t("password.placeholder")}
          variant={errors.password && "error"}
          errorMessage={errors.password && errors.password.message}
        />

        <PasswordInput
          {...form.register("confirmPassword")}
          label={t("confirmPassword.label")}
          placeholder={t("confirmPassword.placeholder")}
          variant={(errors.confirmPassword || isNotValidConfirmPassword) && "error"}
          errorMessage={
            (errors.confirmPassword && errors.confirmPassword.message) ||
            (isNotValidConfirmPassword && t("confirmPassword.errors.mismatch"))
          }
        />

        <PasswordStrengthBar password={password} />

        <div className="flex mt-5 items-center justify-end w-full">
          <Button color="yellow" size="medium" variant="filled" disabled={loading} trailingIcon={<East />}>
            {loading ? t("button.loading") : t("button.label")}
          </Button>
        </div>
      </form>
      <LoginModal open={openModalCreateAccount} onClose={() => setOpenModalCreateAccount(false)} />
    </>
  );
}
