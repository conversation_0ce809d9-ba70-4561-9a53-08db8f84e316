"use client";

import { <PERSON> } from "@/i18n/navigation";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { JourneyObligationCart } from "./journey-obligation-cart";

export function JourneyObligation() {
  const t = useTranslations("shop.longJourney.obligations");

  return (
    <>
      <div className="flex flex-col md:flex-row gap-6 md:mt-16">
        <JourneyObligationCart obliged />
        <JourneyObligationCart />
      </div>
      <div className="flex justify-center md:justify-end mt-7 w-full">
        <Link href="./select-services">
          <Button trailingIcon={<East />} color="yellow" variant="filled" size="medium">
            {t("configureButton")}
          </Button>
        </Link>
      </div>
    </>
  );
}
