"use client";

import { IconBanner, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Padlock } from "@arthursenno/lizenzero-ui-react/Icon";

export function JourneyBillingBanner() {
  return (
    <ShopBanner title="" style={{ width: "100%" }}>
      <IconBanner
        className="text-white "
        icon={() => <Padlock width={24} height={24} className="fill-tonal-dark-blue-80" />}
      />

      <div className="">
        <p className="font-bold text-base">You can't go wrong</p>
        <span className="w-full text-sm ">
          Quantities can be flexibly adjusted at any time via your customer account, even after the contract has been
          concluded!
        </span>
      </div>
    </ShopBanner>
  );
}
