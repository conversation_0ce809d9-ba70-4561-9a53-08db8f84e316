"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { ContractType } from "@/lib/api/contracts/types";
import { PriceList, ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { formatCurrency } from "@/utils/formatCurrency";
import { useTranslations } from "next-intl";

interface PackagingObligationProps {
  serviceType: ContractType;
  selectedItems: (ShoppingCartItem | { country_code: string; price_list: PriceList })[];
  onSelectItem: (countryCode: string, serviceType: ContractType) => void;
  totalPrice: number;
  pricePerItem: number;
  title: string;
}

export function PackagingObligation({
  serviceType,
  selectedItems,
  onSelectItem,
  totalPrice,
  pricePerItem,
  title,
}: PackagingObligationProps) {
  const { shoppingCart } = useShoppingCart();
  const t = useTranslations("shop.longJourney.selectServices.packagingObligation");

  function handleToggleItem(item: ShoppingCartItem) {
    onSelectItem(item.country_code, serviceType);
  }

  const licenses = shoppingCart.items.filter((item) => item.service_type === "EU_LICENSE");

  const { obliged, doNothing } = licenses.reduce(
    (result, item) => {
      const customerCommitment = shoppingCart.customer_commitments.find(
        (commitment) => commitment.country_code === item.country_code
      );
      const isItemObliged = customerCommitment?.is_license_required;
      const target = isItemObliged ? "obliged" : "doNothing";

      result[target] = [...result[target], item];

      return result;
    },
    { obliged: [], doNothing: [] } as { obliged: ShoppingCartItem[]; doNothing: ShoppingCartItem[] }
  );

  return (
    <div>
      <div className="flex items-start mt-4 gap-4">
        <div className="">
          <p className="text-sm text-on-surface-04">{t("title")}</p>
          <div className="h-16 min-w-16 flex items-center flex-wrap gap-6 border border-on-surface-04 px-4 py-2 rounded-2xl mt-2 bg-surface-04">
            {obliged.map((item) => (
              <CountryIcon
                key={item.country_code}
                country={{ name: item.country_name, flag_url: item.country_flag }}
                onClick={() => handleToggleItem(item)}
                data-selected={!!selectedItems.find((i) => i.country_code === item.country_code)}
                className="size-9 cursor-pointer data-[selected=true]:outline data-[selected=true]:outline-2 data-[selected=true]:outline-tonal-dark-blue-50 data-[selected=true]:outline-offset-2"
              />
            ))}
          </div>
        </div>
        <div className="">
          <p className="text-sm text-tonal-dark-green-30">{t("doNothing")}</p>
          <div className="h-16 min-w-16 flex items-center flex-wrap gap-6 border border-tonal-dark-green-30 px-4 py-2 rounded-2xl mt-2 bg-success-container">
            {doNothing.map((item) => (
              <CountryIcon
                key={item.country_code}
                country={{ name: item.country_name, flag_url: item.country_flag }}
                onClick={() => handleToggleItem(item)}
                data-selected={!!selectedItems.find((i) => i.country_code === item.country_code)}
                className="size-9 cursor-pointer data-[selected=true]:outline data-[selected=true]:outline-2 data-[selected=true]:outline-tonal-dark-blue-50 data-[selected=true]:outline-offset-2"
              />
            ))}
          </div>
        </div>
      </div>
      <div className="mt-7 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <p className="text-primary text-sm font-bold">{title}</p>
          <p className="text-xs font-medium text-on-surface-01">
            {t("perCountry", { price: formatCurrency(pricePerItem) })}
          </p>
        </div>
        <p className="text-xl text-support-blue">
          <span className="text-base">{t("from")}</span>
          {` `}
          {formatCurrency(totalPrice)}
        </p>
      </div>
    </div>
  );
}
