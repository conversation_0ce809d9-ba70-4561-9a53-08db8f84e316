import { Add, RadioSelected, RadioUnselected, Remove } from "@arthursenno/lizenzero-ui-react/Icon";
import { useRef, useState } from "react";
import { useFormContext } from "react-hook-form";

import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";

import { JourneySelectCountriesFormData } from "../journey-select-countries-provider";

import { useCommitment } from "@/hooks/use-commitment";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { SubmitCommitmentParams } from "@/lib/api/commitment";
import { ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { enqueueSnackbar } from "notistack";
import { Spinner } from "@/components/ui/loader";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";
interface JourneySelectCountriesItemCommitmentProps {
  cartItem: ShoppingCartItem;
}

export function JourneySelectCountriesItemCommitment({
  cartItem,
}: Required<JourneySelectCountriesItemCommitmentProps>) {
  const t = useTranslations("shop.longJourney.selectCountries.item.commitment");
  const commitmentRef = useRef<HTMLDetailsElement>(null);

  const {
    register,
    setValue,
    formState: { errors },
    trigger,
    getValues,
    clearErrors,
  } = useFormContext<JourneySelectCountriesFormData>();

  const { shoppingCart, updateCartItem, updateQueryData } = useShoppingCart();
  const {
    commitment,
    isLoading: isLoadingCommitment,
    submitCommitment,
    isSubmitting: isSubmittingCommitment,
  } = useCommitment(cartItem.country_code);

  const customerCommitment = shoppingCart.customer_commitments.find((c) => c.country_code === cartItem.country_code);

  const [isCommitmentSubmitted, setIsCommitmentSubmitted] = useState(false);
  const [isEditingCommitment, setIsEditingCommitment] = useState(!customerCommitment);

  const commitmentErrors =
    !!errors &&
    !!errors.items &&
    !!errors.items[cartItem.country_code] &&
    errors.items[cartItem.country_code]!.commitmentAnswers;

  const unfilledQuestionErrors = errors?.items?.[cartItem.country_code]?.commitmentAnswers;

  async function handleConfirmCommitment() {
    const isValid = await trigger(`items.${cartItem.country_code}.commitmentAnswers`);

    if (!isValid) return;

    setIsCommitmentSubmitted(true);

    const commitmentAnswers = getValues(`items.${cartItem.country_code}.commitmentAnswers`);

    if (customerCommitment) {
      const isFormEqual = customerCommitment.commitment.every(
        (c) => commitmentAnswers[`criteria_${c.id}`] === c.answer
      );

      if (isFormEqual) {
        setIsEditingCommitment(false);
        return;
      }
    }

    const formattedCommitmentAnswers: SubmitCommitmentParams["commitment_answers"] = [];

    Object.entries(commitmentAnswers).forEach(([key, value]) => {
      const criteriaId = Number(key.split("_")[1]);

      if (!criteriaId) return;

      formattedCommitmentAnswers.push({
        criteria_id: criteriaId,
        answer: value,
      });
    });

    if (commitmentRef.current) commitmentRef.current.open = false;
    clearErrors(`items.${cartItem.country_code}`);

    submitCommitment(
      {
        country_code: cartItem.country_code,
        year: 2025,
        customer_email: shoppingCart.email || "",
        commitment_answers: formattedCommitmentAnswers,
        shopping_cart_id: shoppingCart.id,
      },
      {
        onSuccess: async (customerCommitment) => {
          const item = shoppingCart.items.find((i) => i.id === cartItem.id);

          if (!item) return;

          const packagingServices = customerCommitment.service_setup.packaging_services
            .filter((p) => p.obliged)
            .map((p) => ({
              id: p.id,
              name: p.name,
              fractions: p.report_set.fractions.reduce(
                (acc, fraction) => {
                  acc[fraction.code] = {
                    code: fraction.code,
                    name: fraction.name,
                    weight: 0,
                  };
                  return acc;
                },
                {} as Record<string, { code: string; name: string; weight: number }>
              ),
            }));

          const updatedItem: ShoppingCartItem = {
            ...item,
            packaging_services: packagingServices,
          };

          setValue(
            `items.${cartItem.country_code}.packagingServices`,
            packagingServices.reduce(
              (acc, p) => {
                acc[`p_${p.id}`] = p;
                return acc;
              },
              {} as Record<string, any>
            )
          );

          updateQueryData({
            ...shoppingCart,
            items: shoppingCart.items.map((item) => (item.id === cartItem.id ? updatedItem : item)),
          });
          setIsEditingCommitment(false);

          enqueueSnackbar(t("success"), { variant: "success" });
          updateCartItem(item.id, updatedItem);
        },
        onError: () => {
          enqueueSnackbar(t("error"), { variant: "error" });
        },
      }
    );
  }

  function handleEditCommitment() {
    setIsEditingCommitment(true);
  }

  function handleSaveAnswers() {
    handleConfirmCommitment();
  }

  if (isLoadingCommitment || !commitment)
    return (
      <div className="py-6 space-y-8 md:space-y-10">
        <div className="flex flex-col gap-4">
          <Skeleton className="h-6 w-full" />
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-4">
          <Skeleton className="h-6 w-full" />
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        </div>
      </div>
    );

  if (isSubmittingCommitment)
    return (
      <div className="pb-6">
        <div className="flex justify-center items-center gap-1 md:gap-2 px-2 py-3 border-[1px] border-tonal-dark-cream-80 rounded-md">
          <Spinner size="sm" />
          <p className="text-center text-primary"> {t("submitting")}</p>
        </div>
      </div>
    );

  return (
    <>
      <details ref={commitmentRef} className="group/commitment">
        <summary className="flex flex-col cursor-pointer">
          <div className="flex items-center justify-between py-2 md:py-6">
            <div className="flex items-center gap-4">
              <p
                data-invalid={!!commitmentErrors}
                className="md:text-xl font-bold text-primary data-[invalid=true]:text-error"
              >
                {t("title")}
              </p>
              <QuestionTooltip>
                <QuestionTooltipDescription>{t("tooltip")}</QuestionTooltipDescription>
              </QuestionTooltip>
            </div>
            <Remove className="hidden group-open/commitment:block size-8 fill-support-blue" />
            <Add className="block group-open/commitment:hidden size-8 fill-support-blue" />
          </div>
        </summary>
        <div className="flex flex-col pb-6">
          <div className="space-y-8 md:space-y-10">
            {commitment.map((question) => (
              <div key={question.id} className="flex flex-col gap-4">
                <p className="text-base text-tonal-dark-cream-10">{question.title}</p>
                {question.options.map((option) => (
                  <label
                    key={`${cartItem.country_code}-question-${question.id}-option-${option.id}`}
                    className="text-base text-tonal-dark-cream-20 flex gap-2 items-center cursor-pointer has-[input:checked]:cursor-default"
                  >
                    <input
                      type="radio"
                      {...register(`items.${cartItem.country_code}.commitmentAnswers.${`criteria_${question.id}`}`)}
                      value={option.value}
                      disabled={!isEditingCommitment}
                      className="hidden peer"
                      data-invalid={!!unfilledQuestionErrors?.[`criteria_${question.id}`]}
                    />
                    <RadioSelected className="hidden peer-checked:block size-5 fill-primary" />
                    <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error" />
                    {question.input_type === "SELECT" && <span className="mt-1">{option.option_value}</span>}
                    {question.input_type === "YES_NO" && <>{option.option_value === "YES" ? "Yes" : "No"}</>}
                  </label>
                ))}
                {!!unfilledQuestionErrors?.[`criteria_${question.id}`] && (
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-error">Missing information</p>
                    <QuestionTooltip className="fill-error hover:fill-error-opacity">
                      <QuestionTooltipDescription>
                        Please select an option for this question.
                      </QuestionTooltipDescription>
                    </QuestionTooltip>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        {!isEditingCommitment && (
          <Button type="button" variant="text" color="light-blue" size="medium" onClick={handleEditCommitment}>
            {t("editAnswers")}
          </Button>
        )}
        {isEditingCommitment && (
          <Button type="button" variant="text" color="light-blue" size="medium" onClick={handleSaveAnswers}>
            {t("saveAnswers")}
          </Button>
        )}
      </details>
    </>
  );
}
