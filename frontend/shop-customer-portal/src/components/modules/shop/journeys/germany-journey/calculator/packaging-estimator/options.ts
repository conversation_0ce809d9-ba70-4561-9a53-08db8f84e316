import { Icons } from "@/components/ui/icons";

export const options = [
  {
    id: 1,
    label: "Box S",
    group: "paper",
    multipliers: {
      paper: 65,
    },
    size: "small",
  },
  {
    id: 2,
    label: "Box S",
    description: "incl. Packing Tape",
    group: "paper",
    multipliers: {
      paper: 65,
      plastic: 1,
    },
    size: "small",
  },
  {
    id: 3,
    label: "Box S",
    description: "incl. Packing Tape & Filling Material from Paper",
    group: "paper",
    multipliers: {
      paper: 70,
    },
    size: "small",
  },
  {
    id: 4,
    label: "Box S",
    description: "incl. Packing Tape & Filling Material from Plastic",
    group: "paper",
    multipliers: {
      paper: 65,
      plastic: 2,
    },
    size: "small",
  },
  {
    id: 5,
    label: "Padded Envelopes S",
    description: "from paper",
    group: "paper",
    multipliers: {
      paper: 15,
      plastic: 2,
    },
    size: "small",
  },
  {
    id: 6,
    label: "Box M",
    group: "paper",
    multipliers: {
      paper: 275,
    },
    size: "medium",
  },
  {
    id: 7,
    label: "Box M",
    description: "incl. Packing Tape",
    group: "paper",
    multipliers: {
      paper: 275,
      plastic: 1,
    },
    size: "medium",
  },
  {
    id: 8,
    label: "Box M",
    description: "incl. Packing Tape & Filling Material from Paper",
    group: "paper",
    multipliers: {
      paper: 300,
    },
    size: "medium",
  },
  {
    id: 9,
    label: "Box M",
    description: "incl. Packing Tape & Filling Material from Plastic",
    group: "paper",
    multipliers: {
      paper: 275,
      plastic: 2,
    },
    size: "medium",
  },
  {
    id: 10,
    label: "Padded Envelopes M",
    description: "from Paper",
    group: "paper",
    multipliers: {
      paper: 22,
      plastic: 3,
    },
    size: "medium",
  },
  {
    id: 11,
    label: "Box L",
    group: "paper",
    multipliers: {
      paper: 400,
    },
    size: "large",
  },
  {
    id: 12,
    label: "Box L",
    description: "incl. Packing Tape",
    group: "paper",
    multipliers: {
      paper: 400,
      plastic: 1,
    },
    size: "large",
  },
  {
    id: 13,
    label: "Box L",
    description: "incl. Packing Tape & Filling Material from Paper",
    group: "paper",
    multipliers: {
      paper: 500,
    },
    size: "large",
  },
  {
    id: 14,
    label: "Box L",
    description: "incl. Packing Tape & Filling Material from Plastic",
    group: "paper",
    multipliers: {
      paper: 400,
      plastic: 5,
    },
    size: "large",
  },
  {
    id: 15,
    label: "Padded Envelopes L",
    description: "from Paper",
    group: "paper",
    multipliers: {
      paper: 30,
      plastic: 5,
    },
    size: "large",
  },
  {
    id: 16,
    label: "Mailing Bag S",
    description: "without Filling Material",
    group: "plastics",
    multipliers: {
      plastic: 10,
    },
    size: "small",
  },
  {
    id: 17,
    label: "Mailing Bag S",
    description: "with Filling Material",
    group: "plastics",
    multipliers: {
      plastic: 12,
    },
    size: "small",
  },
  {
    id: 18,
    label: "Mailing Bag M",
    description: "without Filling Material",
    group: "plastics",
    multipliers: {
      plastic: 12,
    },
    size: "medium",
  },
  {
    id: 19,
    label: "Mailing Bag M",
    description: "with Filling Material",
    group: "plastics",
    multipliers: {
      plastic: 12,
    },
    size: "medium",
  },
  {
    id: 20,
    label: "Container M",
    group: "plastics",
    multipliers: {
      plastic: 24.6,
    },
    size: "medium",
  },
  {
    id: 21,
    label: "Films M",
    description: "(Tube Film)",
    group: "plastics",
    multipliers: {},
    size: "medium",
  },
  {
    id: 22,
    label: "Mailing Bag L",
    description: "without Filling Material",
    group: "plastics",
    multipliers: {
      plastic: 15,
    },
    size: "large",
  },
  {
    id: 23,
    label: "Container L",
    group: "plastics",
    multipliers: {
      plastic: 27.1,
    },
    size: "large",
  },
  {
    id: 24,
    label: "Films L",
    description: "(Tube Film)",
    group: "plastics",
    multipliers: {},
    size: "large",
  },
  {
    id: 25,
    label: "Bottle / Glass S",
    description: "with Lid - Plastic",
    group: "glass",
    multipliers: {
      plastic: 1,
      glass: 150,
    },
    size: "small",
  },
  {
    id: 26,
    label: "Bottle / Glass S",
    description: "with Lid - Aluminum",
    group: "glass",
    multipliers: {
      glass: 150,
      aluminium: 1,
    },
    size: "small",
  },
  {
    id: 27,
    label: "Bottle / Glass S",
    description: "with Lid - Cork",
    group: "glass",
    multipliers: {
      glass: 150,
      otherMaterials: 1,
    },
    size: "small",
  },
  {
    id: 28,
    label: "Bottle / Glass M",
    description: "with Lid - Plastic",
    group: "glass",
    multipliers: {
      plastic: 1,
      glass: 500,
    },
    size: "medium",
  },
  {
    id: 29,
    label: "Bottle / Glass M",
    description: "with Lid - Aluminum",
    group: "glass",
    multipliers: {
      glass: 500,
      aluminium: 1,
    },
    size: "medium",
  },
  {
    id: 30,
    label: "Bottle / Glass M",
    description: "with Lid - Cork",
    group: "glass",
    multipliers: {
      glass: 500,
      otherMaterials: 1,
    },
    size: "medium",
  },
  {
    id: 31,
    label: "Bottle / Glass L",
    description: "with Lid - Plastic",
    group: "glass",
    multipliers: {
      plastic: 1,
      glass: 750,
    },
    size: "large",
  },
  {
    id: 32,
    label: "Bottle / Glass L",
    description: "with Lid - Aluminum",
    group: "glass",
    multipliers: {
      glass: 750,
      aluminium: 1,
    },
    size: "large",
  },
  {
    id: 33,
    label: "Bottle / Glass L",
    description: "with Lid - Cork",
    group: "glass",
    multipliers: {
      glass: 750,
      otherMaterials: 1,
    },
    size: "large",
  },
];
