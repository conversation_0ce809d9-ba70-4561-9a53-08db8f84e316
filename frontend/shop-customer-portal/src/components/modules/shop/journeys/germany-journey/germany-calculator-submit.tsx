"use client";

import { useRouter } from "@/i18n/navigation";
import { useFormContext, useWatch } from "react-hook-form";
import { SaveProgress } from "@/components/ui/btn-save-my-progress";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useCustomer } from "@/hooks/use-customer";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";

import { GermanyCalculatorFormData } from "./germany-calculator-provider";
import { useSession } from "next-auth/react";

export function GermanyCalculatorSubmit() {
  const router = useRouter();

  const session = useSession();
  const { customer } = useCustomer();
  const { shoppingCart, updateCartItem } = useShoppingCart();
  const [isLoading, setIsLoading] = useState(false);

  const {
    handleSubmit,
    formState: { errors },
    control,
  } = useFormContext<GermanyCalculatorFormData>();

  const packagingService = useWatch({ control, name: "packagingService" });

  async function handleNextStep() {
    setIsLoading(true);
    try {
      const directLicenseContract = customer?.contracts.find((c) => c.type === "DIRECT_LICENSE");

      const directLicenses = directLicenseContract?.licenses;

      const contractedYears = directLicenses?.map((license) => Number(license.year));

      const cartDirectLicenseItem = shoppingCart?.items?.find((item) => item.service_type === "DIRECT_LICENSE");

      if (!cartDirectLicenseItem) return;

      if (contractedYears?.includes(cartDirectLicenseItem.year)) {
        enqueueSnackbar("License for this year already acquired", { variant: "error" });
        router.push("/saas/direct-license");
        return;
      }

      await updateCartItem(cartDirectLicenseItem.id, {
        ...cartDirectLicenseItem,
        packaging_services: [
          {
            ...packagingService,
            fractions: packagingService.fractions || {},
          },
        ],
      });

      if (session.status === "unauthenticated") {
        router.push("./create-account");
        return;
      }

      if (!customer) {
        router.push("./set-password");
        return;
      }

      router.push("./informations");
    } finally {
      setIsLoading(false);
    }
  }

  const errorMessage = (() => {
    if (!errors || !Object.keys(errors).length) return null;

    return "Please fill the form to continue your purchase.";
  })();

  return (
    <div className="space-y-4">
      {!!errorMessage && <p className="text-right text-error">{errorMessage}</p>}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-12">
        <SaveProgress />
        <Button
          trailingIcon={<East />}
          color={errorMessage ? "red" : "yellow"}
          variant="filled"
          size="medium"
          onClick={(e) => {
            handleSubmit(() => {
              handleNextStep();
            })(e);
          }}
          disabled={isLoading}
          type="button"
        >
          {isLoading ? `Loading...` : `Next`}
        </Button>
      </div>
    </div>
  );
}
