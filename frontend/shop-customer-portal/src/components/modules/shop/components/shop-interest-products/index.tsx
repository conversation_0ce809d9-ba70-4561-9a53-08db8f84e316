import { useTranslations } from "next-intl";
import { ShopContent } from "../shop-content";
import { InterestProductCard } from "./interest-product-card";

interface ShopInterestProductsProps {
  addedProduct: "license" | "action-guide" | "workshops";
}

export function ShopInterestProducts({ addedProduct }: ShopInterestProductsProps) {
  const t = useTranslations("shop.common.journey.shoppingCart.interestProducts");

  const licenseCardItems = [
    t("license.one"),
    t("license.two"),
    t("license.three"),
    t("license.four"),
    t("license.five"),
    t("license.six"),
    t("license.seven"),
  ];

  const actionGuideCardItems = [
    t("actionGuide.one"),
    t("actionGuide.two"),
    t("actionGuide.three"),
    t("actionGuide.four"),
    t("actionGuide.five"),
  ];

  const workshopsCardItems = [
    t("workshop.one"),
    t("workshop.two"),
    t("workshop.three"),
    t("workshop.four"),
    t("workshop.five"),
  ];

  return (
    <ShopContent containerClassName="bg-[#F7F5F2] mt-10">
      <p className="text-on-tertiary text-[28px] font-bold mb-7">{t("interestedInOtherProducts")}</p>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <InterestProductCard
          type="EU_LICENSE"
          title={t("license.title")}
          description={t("license.description")}
          price={Number(t("license.price"))}
          contains={licenseCardItems}
          added={addedProduct === "license"}
        />
        <InterestProductCard
          type="ACTION_GUIDE"
          title={t("actionGuide.title")}
          description={t("actionGuide.description")}
          price={Number(t("actionGuide.price"))}
          contains={actionGuideCardItems}
          added={addedProduct === "action-guide"}
        />
        <InterestProductCard
          type="WORKSHOP"
          disabled
          title={t("workshop.title")}
          description={t("workshop.description")}
          price={Number(t("workshop.price"))}
          contains={workshopsCardItems}
          added={addedProduct === "workshops"}
        />
      </div>
    </ShopContent>
  );
}
