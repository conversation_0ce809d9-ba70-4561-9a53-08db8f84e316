"use client";

import { Icons } from "@/components/ui/icons";

export function ShopTrustpilot() {
  return (
    <div className="w-full flex gap-2 lg:gap-4 items-center justify-between bg-[#1C5853] py-4 px-9 rounded-[16px] h-14">
      <div className="flex items-center gap-4">
        <p className="text-lg font-bold">Hervorragend</p>
        <div className="flex flex-col lg:flex-row gap-2 lg:items-center">
          <div className="flex items-center justify-center gap-2">
            <Icons.filledStar className="size-5" />
            <Icons.filledStar className="size-5" />
            <Icons.filledStar className="size-5" />
            <Icons.filledStar className="size-5" />
            <Icons.halfFilledStar className="size-5" />
          </div>
        </div>
      </div>
      <div className="hidden md:flex items-end gap-[2zpx]">
        <Icons.filledStar className="w-5 h-5" />
        <p className="text-sm font-bold leading-[14px]">Trustpilot</p>
      </div>
    </div>
  );
}
