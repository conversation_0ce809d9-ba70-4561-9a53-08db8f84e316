"use client";

import { usePartner } from "@/hooks/use-partner";
import { downloadCustomerFile } from "@/lib/api/file";
import { UploadedFile } from "@/lib/api/file/types";
import { signContractPartner } from "@/lib/api/partner";
import { cn } from "@/lib/utils";
import { downloadFile } from "@/utils/download-file";
import { formatDateToDDMMYYYY } from "@/utils/formatDateToDDMMYYYY";
import { formatDateToGMT } from "@/utils/formatDateToGMT";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download, File } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";

export const PhContractForm = () => {
  const { partner } = usePartner();
  const contract = partner?.partner_contract;

  const [isLoading, setIsLoading] = useState(false);

  async function handleSignContract() {
    if (!partner) return;

    setIsLoading(true);

    const res: any = await signContractPartner(partner.id);

    setIsLoading(false);

    if (!res.data) return enqueueSnackbar("Error when trying to sign", { variant: "error" });
  }

  const downloadFileMutation = useMutation({
    mutationFn: async (file: UploadedFile) => {
      const downloadedFile = await downloadCustomerFile(file.id);

      downloadFile({ buffer: downloadedFile, fileName: file.original_name });
    },
  });

  return (
    <div className="mb-6">
      <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
        <div className="flex w-full flex-col gap-2 justify-between align-middle mb-6">
          <p className="text-primary font-medium text-xl">Contracting partner</p>
          <p className="text-primary">Review the contract by clicking ”confirm contract” .</p>
        </div>
        <div className="flex gap-2 items-center w-full">
          <File className={cn("size-20", contract ? `fill-primary` : `fill-on-surface-01`)} />
          <div className="flex justify-between items-center w-full">
            <div className="flex flex-col gap-3">
              <p className={cn("text-xl font-bold", contract ? `text-primary` : `text-tonal-dark-cream-20`)}>
                Partnership Contract
              </p>
              {contract ? (
                <p className="text-sm text-tonal-dark-cream-30">Agreed on: {formatDateToGMT(contract.created_at)}</p>
              ) : (
                <Button
                  color="yellow"
                  variant="filled"
                  size="small"
                  onClick={handleSignContract}
                  disabled={isLoading}
                  type="button"
                >
                  {isLoading ? `Loading...` : `I agree with the contract`}
                </Button>
              )}
            </div>
            {contract?.files.length && (
              <Button
                variant="text"
                color="light-blue"
                size="medium"
                className="text-base"
                type="button"
                onClick={() => downloadFileMutation.mutate(contract?.files[0])}
                leadingIcon={
                  downloadFileMutation.isPending && downloadFileMutation.variables.id === contract?.files[0].id ? (
                    <CgSpinnerAlt className="animate-spin size-5" />
                  ) : (
                    <Download className="size-5" />
                  )
                }
              >
                {downloadFileMutation.isPending ? "Downloading..." : "Download"}
              </Button>
            )}
          </div>
        </div>
        {!!contract?.agreed_on && (
          <div className="mt-6">
            <p className="text-on-tertiary text-xl font-bold">Changes agreed via e-mail</p>
            <div className="mt-4 flex flex-col gap-3">
              {contract.changes?.map((item, idx) => (
                <div key={idx}>
                  <p className="text-on-tertiary">
                    {idx + 1}. {item.change_description}
                  </p>
                  <p className="text-sm text-on-surface-01">Update in {formatDateToDDMMYYYY(item.updated_at)}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
