"use client";

import { ADDRESS_REGEX, SPECIAL_CHARS_REGEX, ZIP_CODE_REGEX } from "@/utils/regex";
import { zodResolver } from "@hookform/resolvers/zod";
import { ReactNode } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";

const partnerInformationFormSchema = z.object({
  companyName: z.string().regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field").min(1),
  countryCode: z.string().min(1),
  startingDateOfCooperation: z
    .string()
    .optional()
    .refine(
      (value) => {
        if (!value) return true;
        const inputDate = new Date(value);
        const today = new Date();
        return inputDate < today;
      },
      { message: "Invalid date" }
    ),
  partnerOfferWebsite: z.string().optional().or(z.literal("")),
  descriptionOfBusiness: z.string(),
  managingDirector: z
    .string()
    .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field")
    .optional()
    .or(z.literal("")),
  streetAndNumber: z
    .string()
    .min(1, {
      message: "Please enter a valid street and number",
    })
    .regex(ADDRESS_REGEX, "Special characters are not allowed for this field"),
  addressLine: z
    .string({
      required_error: "Please enter a valid address",
      invalid_type_error: "Please enter a valid address",
    })
    .min(1, { message: "Please enter a valid address" })
    .regex(ADDRESS_REGEX, "Special characters are not allowed for this field"),
  additionalAddressLine: z
    .string()
    .regex(ADDRESS_REGEX, "Special characters are not allowed for this field")
    .optional()
    .or(z.literal("")),
  zipCode: z
    .string()
    .min(2, { message: "Required" })
    .regex(ZIP_CODE_REGEX, "Special characters are not allowed for this field"),
  city: z
    .string()
    .min(2, { message: "Invalid city" })
    .regex(ADDRESS_REGEX, "Special characters are not allowed for this field"),
  contactName: z
    .string()
    .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field")
    .optional()
    .or(z.literal("")),
  contactEmail: z.string().email().optional().or(z.literal("")),
  contactPhone: z.string().optional(),
  internationalAccountNumber: z.string().optional(),
  businessIdentifierCode: z.string().optional(),
});

export type PartnerInformationFormData = z.infer<typeof partnerInformationFormSchema>;

interface PartnerInformationFormProviderProps {
  children: ReactNode;
}

export function PartnerInformationFormProvider({ children }: PartnerInformationFormProviderProps) {
  const methods = useForm({
    resolver: zodResolver(partnerInformationFormSchema),
    mode: "all",
  });

  return <FormProvider {...methods}>{children}</FormProvider>;
}
