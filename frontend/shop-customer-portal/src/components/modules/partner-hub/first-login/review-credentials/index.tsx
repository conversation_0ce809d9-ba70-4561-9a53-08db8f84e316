"use client";

import { PasswordInput } from "@/components/ui/password-input";
import { usePart<PERSON> } from "@/hooks/use-partner";
import { useRouter } from "@/i18n/navigation";
import { checkCredentials } from "@/lib/api/partner";
import {
  CONTAINS_LETTER_REGEX,
  CONTAINS_NON_ALPHANUMERIC_REGEX,
  CONTAINS_NUMBER_REGEX,
  EMAIL_REGEX,
} from "@/utils/regex";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, Edit } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const reviewCredentialsFormSchema = z.object({
  email: z.string().regex(EMAIL_REGEX, { message: "Invalid email" }).optional().or(z.literal("")),
  password: z
    .string()
    .min(6, "Password must have at least 6 characters")
    .regex(CONTAINS_LETTER_REGEX, "Enter a letter")
    .regex(CONTAINS_NUMBER_REGEX, "Enter a number")
    .regex(CONTAINS_NON_ALPHANUMERIC_REGEX, "Enter a special character")
    .optional()
    .or(z.literal("")),
});

export type ReviewCredentialsFormData = z.infer<typeof reviewCredentialsFormSchema>;

export function ReviewCredentialsForm() {
  const router = useRouter();
  const session = useSession();
  const { partner } = usePartner();

  const [isEmailEnabled, setIsEmailEnabled] = useState(false);
  const [isPasswordEnabled, setIsPasswordEnabled] = useState(false);

  const [loading, setLoading] = useState(false);

  const form = useForm<ReviewCredentialsFormData>({
    resolver: zodResolver(reviewCredentialsFormSchema),
    defaultValues: {
      email: partner?.email,
    },
  });

  async function submit(data: Partial<ReviewCredentialsFormData>) {
    const user = session.data?.user;

    if (!user) return;

    if (!isEmailEnabled && !isPasswordEnabled) return router.push("./information");

    if (isEmailEnabled) {
      if (!data.email) form.setError("email", { message: "Email is required" });
    }

    if (isPasswordEnabled) {
      if (!data.password) form.setError("password", { message: "Password is required" });
    }

    try {
      setLoading(true);

      const checkCredentialsResponse = await checkCredentials(user.id, {
        email: isEmailEnabled ? data.email : undefined,
        password: isPasswordEnabled ? data.password : undefined,
      });

      if (checkCredentialsResponse.response.status === 409) {
        form.setError("email", { message: "Email already registered" });
      }

      if (!checkCredentialsResponse.data) {
        throw new Error(checkCredentialsResponse?.response?.data?.message);
      }

      router.push("./information");
    } catch (e: any) {
      alert(e.message);
    } finally {
      setLoading(false);
    }
  }

  function handleCancel() {
    setIsEmailEnabled(false);
    setIsPasswordEnabled(false);
    form.reset();
  }

  useEffect(() => {
    if (!partner && session.data?.user?.email) {
      form.setValue("email", session.data.user.email);
    }
  }, [session.data?.user?.email, partner]);

  return (
    <form className="w-full md:w-3/5" onSubmit={form.handleSubmit(submit)}>
      <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
        <p className="text-primary font-medium text-xl mb-8">Login information</p>
        <div className="space-y-4 w-full">
          <div className="flex items-end gap-6">
            <div className="w-1/2">
              <Input
                {...form.register("email")}
                label="E-mail"
                placeholder="your email"
                enabled={isEmailEnabled}
                variant={!isEmailEnabled ? "disabled" : form.formState.errors.email && "error"}
                errorMessage={form.formState.errors.email?.message}
              />
            </div>

            {!isEmailEnabled && (
              <div className="pb-2">
                <Button
                  color="light-blue"
                  size="medium"
                  variant="text"
                  trailingIcon={<Edit className="fill-support-blue" />}
                  onClick={() => setIsEmailEnabled(true)}
                >
                  Change e-mail
                </Button>
              </div>
            )}
          </div>
          <div className="flex items-end gap-6">
            <div className="w-1/2">
              <PasswordInput
                {...form.register("password")}
                label="Password"
                placeholder="*********"
                enabled={isPasswordEnabled}
                variant={!isPasswordEnabled ? "disabled" : form.formState.errors.password && "error"}
                errorMessage={form.formState.errors.password?.message}
              />
            </div>

            {!isPasswordEnabled && (
              <div className="pb-2">
                <Button
                  color="light-blue"
                  size="medium"
                  variant="text"
                  trailingIcon={<Edit className="fill-support-blue" />}
                  onClick={() => setIsPasswordEnabled(true)}
                >
                  Change password
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {(isEmailEnabled || isPasswordEnabled) && !loading && (
        <div className="flex items-center justify-end my-1">
          <Button type="button" onClick={handleCancel} color="dark-blue" size="medium" variant="outlined">
            Cancel
          </Button>
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-6 justify-end w-full my-8">
        {!loading && (
          <Button
            type="button"
            onClick={() => router.push(`./information`)}
            color="dark-blue"
            size="medium"
            variant="outlined"
            className="w-full md:w-60"
          >
            Skip
          </Button>
        )}
        <Button
          color="yellow"
          trailingIcon={<East />}
          size="medium"
          variant="filled"
          disabled={loading}
          className="w-full md:w-60"
        >
          {loading ? "Loading..." : "Continue"}
        </Button>
      </div>
    </form>
  );
}
