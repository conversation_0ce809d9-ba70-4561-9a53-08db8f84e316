"use client";

import { LoadingScreen } from "@/components/_common/loading-screen";
import { Link } from "@/i18n/navigation";
import { downloadCustomerFile } from "@/lib/api/file";
import { getMarketingMaterials } from "@/lib/api/marketing-materials";
import { MarketingMaterialCategory, MarketingMaterialsResponse } from "@/lib/api/marketing-materials/types";
import { intlDateFormatter } from "@/lib/utils";
import { formatDateToDDMMYYYY } from "@/utils/formatDateToDDMMYYYY";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download, File, Search } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import { BiLinkExternal } from "react-icons/bi";
import { SaasBreadcrumb } from "../../saas/components/saas-breadcrumb";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { SaasContainer } from "../../saas/components/saas-container";

interface MarketingMaterialsProps {
  locale: string;
  search?: string;
}

export function MarketingMaterials({ locale, search }: MarketingMaterialsProps) {
  const session = useSession();

  const userId = session.data?.user.id;

  const { data, isLoading } = useQuery<MarketingMaterialsResponse>({
    queryKey: ["marketing-materials"],
    queryFn: async () => {
      return await getMarketingMaterials();
    },
  });

  if (isLoading) return <LoadingScreen />;

  async function handleDownload(files: any) {
    if (!userId) return;

    if (!files.length) {
      enqueueSnackbar("No files to download", { variant: "info" });
      return;
    }

    try {
      const downloadPromises = files.map(async (file: any) => {
        const response = await downloadCustomerFile(file.id);

        const blob = new Blob([response], { type: file.extension });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = file.original_name;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      });

      await Promise.all(downloadPromises);
    } catch (error) {
      console.error("Error downloading file:", error);
      enqueueSnackbar("Error downloading files", { variant: "error" });
    }
  }

  const materials = data?.marketingMaterials ?? [];

  const latestMaterials = materials.slice(-4);

  const filteredMaterials = search
    ? materials.filter((m) => m.name.toLowerCase().includes(search.toLowerCase()))
    : materials;
  const campaignMaterials = filteredMaterials.filter((m) => m.category === MarketingMaterialCategory.SPECIFIC_MATERIAL);
  const standardMaterials = filteredMaterials.filter((m) => m.category === MarketingMaterialCategory.STANDARD);

  return (
    <>
      <SaasBreadcrumb
        paths={[
          { label: "Hub", href: `/${locale}/partner-hub` },
          { label: "Marketing Materials", href: `/${locale}/partner-hub/marketing-materials` },
        ]}
      />
      <SaasContainer>
        <TitleAndSubTitle
          title="Marketing Materials"
          subText="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis."
        />
        <div className="bg-tonal-cream-96 rounded-[40px] p-10 w-full">
          <h3 className="text-primary text-2xl font-bold pb-6">Check out your page with Lizenzero!</h3>
          <div className="flex items-center justify-between rounded-[60px] font-bold border border-tonal-dark-cream-80 bg-[#fcfcfc] p-2 text-grey-blue max-w-[432px]">
            <p className="p-2 overflow-hidden text-ellipsis whitespace-nowrap">https://www.lizenzero.de/en/ebay</p>
            <Link href={`/${locale}/partner-hub`}>
              <Button
                variant="filled"
                color="yellow"
                size="iconXSmall"
                leadingIcon={<BiLinkExternal size={"1.25rem"} />}
              />
            </Link>
          </div>
          <p className="text-tonal-dark-cream-40 text-sm pt-2">
            Last update: {intlDateFormatter.format(new Date()).replaceAll("/", ".")}
          </p>
        </div>
        <div className="gap-8">
          <h3 className="text-primary text-2xl font-bold pb-6">New materials</h3>
          {latestMaterials.length > 0 ? (
            <div className="grid [grid-template-columns:repeat(auto-fit,209px)] gap-6 justify-center lg:justify-start">
              {latestMaterials.map((m) => (
                <div
                  key={`new-materials-${m.id}`}
                  className="rounded-3xl border border-surface-03 bg-white p-6 flex flex-col items-center"
                >
                  <File width={104} height={104} className="fill-primary" />
                  <div className="flex items-center gap-2">
                    <p className="text-primary font-bold text-xl">{m.name}</p>
                    <Button
                      onClick={() => handleDownload(m.files)}
                      variant="text"
                      color="light-blue"
                      size="iconSmall"
                      leadingIcon={<Download />}
                    />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="w-full text-center text-tonal-dark-cream-10 py-6">No materials found.</div>
          )}
        </div>
      </SaasContainer>
      <SaasContainer containerClassName="bg-tonal-cream-96">
        <div className="flex justify-between flex-wrap gap-4 items-start pb-10">
          <h3 className="text-primary text-2xl font-bold">All materials</h3>
          <form
            id="material-search-form"
            action={`/${locale}/partner-hub/marketing-materials`}
            className="rounded-3xl flex-grow border border-tonal-dark-cream-80 bg-[#fcfcfc] p-2 text-grey-blue max-w-[432px] py-3 px-4 flex gap-4 items-center"
          >
            <Search className="fill-primary" width={20} height={20} />
            <input defaultValue={search} placeholder="Search by name" name="search" className="outline-none" />
          </form>
        </div>

        <div className="p-6 rounded-[20px] bg-[#FCFCFC]">
          <h4 className="text-xl font-bold font-large-paragraph-bold text-tonal-dark-cream-10 pb-4">
            Campaign specific
          </h4>
          <div className="flex flex-col gap-4">
            {campaignMaterials.length > 0 ? (
              campaignMaterials.map((m) => (
                <div key={`campaign-materials-${m.id}`} className="flex gap-2 items-center flex-grow">
                  <File className="lg:w-20 lg:h-20 w-10 h-10 fill-primary flex-shrink-0" />
                  <div className="flex flex-col flex-grow overflow-hidden">
                    <div className="flex lg:justify-between lg:items-center gap-x-4 gap-y-2 flex-col lg:flex-row flex-grow">
                      <h5 className="text-xl font-bold font-large-paragraph-bold text-primary overflow-hidden whitespace-nowrap text-ellipsis">
                        {m.name}
                      </h5>
                      <Button
                        onClick={() => handleDownload(m.files)}
                        variant="text"
                        color="light-blue"
                        size="medium"
                        leadingIcon={<Download />}
                      >
                        Download
                      </Button>
                    </div>
                    <p className="text-tonal-dark-cream-40 text-sm pt-2">
                      {formatDateToDDMMYYYY(String(m.created_at))}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="w-full text-center text-tonal-dark-cream-10 py-6">No materials found.</div>
            )}
          </div>
        </div>

        <div className="p-6 rounded-[20px] bg-[#FCFCFC] mt-6">
          <h4 className="text-xl font-bold font-large-paragraph-bold text-tonal-dark-cream-10 pb-4">
            Standard Materials
          </h4>
          <div className="flex flex-col gap-4">
            {standardMaterials.length > 0 ? (
              standardMaterials.map((m) => (
                <div key={`campaign-materials-${m.id}`} className="flex gap-2 items-center flex-grow">
                  <File className="lg:w-20 lg:h-20 w-10 h-10 fill-primary flex-shrink-0" />
                  <div className="flex flex-col flex-grow overflow-hidden">
                    <div className="flex lg:justify-between lg:items-center gap-x-4 gap-y-2 flex-col lg:flex-row flex-grow">
                      <h5 className="text-xl font-bold font-large-paragraph-bold text-primary overflow-hidden whitespace-nowrap text-ellipsis">
                        {m.name}
                      </h5>
                      <a target="_blank" rel="noopener noreferrer">
                        <Button variant="text" color="light-blue" size="medium" leadingIcon={<Download />}>
                          Download
                        </Button>
                      </a>
                    </div>
                    <p className="text-tonal-dark-cream-40 text-sm pt-2">
                      {formatDateToDDMMYYYY(String(m.created_at))}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="w-full text-center text-tonal-dark-cream-10 py-6">No materials found.</div>
            )}
          </div>
        </div>
      </SaasContainer>
    </>
  );
}
