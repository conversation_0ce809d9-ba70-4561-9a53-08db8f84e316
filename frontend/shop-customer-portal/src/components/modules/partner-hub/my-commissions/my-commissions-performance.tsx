"use client";

import { CommissionsSearchParams } from "@/app/[locale]/partner-hub/(dashboard)/commissions/page";
import { SelectDropdown } from "@/components/_common/select-dropdown";
import { Link, useRouter } from "@/i18n/navigation";
import { Invite, InviteResult } from "@/types/partner-hub/invite";
import { commissionProducts } from "@/utils/partner-hub";
import { joinSearchParams } from "@/utils/path";
import { AdsClick } from "@arthursenno/lizenzero-ui-react/Icon";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { BiChevronDown } from "react-icons/bi";
import { FaTag } from "react-icons/fa";
import { MyCommissionsInviteComparisonChart } from "./my-commissions-invite-comparison-chart";

const periods = [
  { value: "m", label: "Monthly" },
  { value: "y", label: "Yearly" },
];

interface MyCommissionsPerformanceProps {
  searchParams: CommissionsSearchParams;
  invites: Invite[];
  results: [InviteResult | undefined, InviteResult | undefined];
}

export function MyCommissionsPerformance({ searchParams, invites, results }: MyCommissionsPerformanceProps) {
  const router = useRouter();

  const selectedProduct =
    commissionProducts.find((p) => p.value === searchParams.performanceProduct) ||
    commissionProducts[commissionProducts.length - 1];

  const selectedPeriod = periods.find((p) => p.value === searchParams.performancePeriod) || periods[0];
  return (
    <>
      <div className="pb-7">
        <div className="flex gap-2 flex-wrap justify-between">
          <h4 className="text-2xl font-bold text-primary">Performance</h4>
          <SelectDropdown
            options={periods.map((period) => ({
              label: period.label,
              value: period.value,
              disabled: period.value === selectedPeriod.value,
            }))}
            value={selectedPeriod.value}
            onChangeValue={(value) => {
              router.push(
                `?${joinSearchParams({
                  ...searchParams,
                  performancePeriod: value,
                })}`,
                { scroll: false }
              );
            }}
          />
        </div>
        <p className="text-[#808FA9] pt-2">Values in thousands of euros</p>
      </div>
      <div className="flex flex-col items-start gap-6">
        <SelectDropdown
          options={commissionProducts.map((product) => ({
            label: product.label,
            value: product.value,
            disabled: product.value === selectedProduct.value,
          }))}
          value={selectedProduct.value}
          onChangeValue={(value) => {
            router.push(
              `?${joinSearchParams({
                ...searchParams,
                performanceProduct: value,
              })}`,
              { scroll: false }
            );
          }}
        />

        <div className="flex gap-8 flex-wrap">
          <InviteSelector index={1} invites={invites} searchParams={searchParams} />
          <InviteSelector index={2} invites={invites} searchParams={searchParams} />
        </div>
        <MyCommissionsInviteComparisonChart data={results} />
      </div>
    </>
  );
}

function InviteSelector({
  index,
  searchParams,
  invites,
}: {
  index: 1 | 2;
  searchParams: CommissionsSearchParams;
  invites: Invite[];
}) {
  const selectedInvite1 = invites.find((i) => String(i.id) === searchParams.performanceInvite1);
  const selectedInvite2 = invites.find((i) => String(i.id) === searchParams.performanceInvite2);

  const selectedInvite = index === 1 ? selectedInvite1 : selectedInvite2;
  const comparedInvite = index === 1 ? selectedInvite2 : selectedInvite1;

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger className=" focus:outline-none" asChild>
        <button className="cursor-pointer flex gap-2 items-center text-support-blue">
          <div className="flex gap-2 items-center">
            {selectedInvite && (
              <>
                {selectedInvite.type === "code" && <FaTag size={16} className="fill-tonal-yellow-50" />}
                {selectedInvite.type === "link" && <AdsClick width={16} height={16} className="fill-support-blue" />}
              </>
            )}

            <p
              data-selected={!!selectedInvite}
              className="text-tonal-dark-cream-60 data-[selected=true]:text-tonal-dark-cream-10"
            >
              {selectedInvite?.value || "Select"}
            </p>
          </div>
          <BiChevronDown className="fill-primary size-5" />
        </button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="min-w-[220px] bg-background py-3 rounded-2xl focus:outline-none z-[999999]"
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
        >
          {invites
            .filter((i) => i.id !== comparedInvite?.id)
            .map((invite) => (
              <Link
                key={`commissions-performance-control-invite-1-${invite.id}`}
                href={(() => {
                  const newParams: CommissionsSearchParams = {
                    ...searchParams,
                    [index === 1 ? "performanceInvite1" : "performanceInvite2"]: String(invite.id),
                  };

                  if (selectedInvite?.id === invite.id) {
                    delete newParams[index === 1 ? "performanceInvite1" : "performanceInvite2"];
                  }

                  return `?${joinSearchParams(newParams)}`;
                })()}
                scroll={false}
              >
                <DropdownMenu.Item
                  data-selected={invite.id === selectedInvite?.id}
                  className="cursor-pointer outline-none text-primary px-4 py-2 hover:bg-surface-02 active:bg-on-error-opacity data-[selected=true]:font-bold transition-all"
                >
                  <div className="flex gap-2 items-center">
                    {invite.type === "code" && <FaTag size={16} className="fill-tonal-yellow-50" />}
                    {invite.type === "link" && <AdsClick width={16} height={16} className="fill-support-blue" />}
                    {invite.value}
                  </div>
                </DropdownMenu.Item>
              </Link>
            ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
