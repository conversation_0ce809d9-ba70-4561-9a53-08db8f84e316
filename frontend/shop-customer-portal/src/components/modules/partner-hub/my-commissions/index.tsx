"use client";

import { CommissionsSearchParams } from "@/app/[locale]/partner-hub/(dashboard)/commissions/page";
import { Link, useRouter } from "@/i18n/navigation";
import { commissionProducts, leadTypes } from "@/utils/partner-hub";
import { joinSearchParams } from "@/utils/path";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import {
  CalendarToday,
  CheckBox,
  CheckBoxOutlineBlank,
  Download,
  East,
  FilterAlt,
  Search,
} from "@arthursenno/lizenzero-ui-react/Icon";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { BiChevronDown } from "react-icons/bi";
import { commissionResult } from "@/utils/consts/partner-hub";

interface MyCommissionsProps {
  searchParams: CommissionsSearchParams;
}

export function MyCommissions({ searchParams }: MyCommissionsProps) {
  const router = useRouter();

  const listingProductsArray = Array.isArray(searchParams.listingProducts)
    ? [...searchParams.listingProducts]
    : searchParams.listingProducts
      ? [searchParams.listingProducts]
      : ["all"];

  const initialLeads = leadTypes.map((l) => l.value);

  const listingLeadsArray = Array.isArray(searchParams.listingLeads)
    ? [...searchParams.listingLeads]
    : searchParams.listingLeads
      ? [searchParams.listingLeads]
      : initialLeads;

  const selectedProducts = commissionProducts.filter((p) => listingProductsArray.includes(p.value)) || [
    commissionProducts[commissionProducts.length - 1],
  ];

  const selectedPage = searchParams.listingPage || "1";
  const totalPages = Math.ceil(commissionResult.count / 12);

  return (
    <div>
      <div className="flex justify-between gap-4 items-center flex-wrap pb-8">
        <DropdownMenu.Root>
          <DropdownMenu.Trigger className=" focus:outline-none" asChild>
            <button className="cursor-pointer flex gap-2 items-center text-support-blue text-[32px] overflow-hidden">
              <p className="font-bold overflow-hidden whitespace-nowrap text-ellipsis">
                {selectedProducts.map((p) => p.label).join(" ; ")}
              </p>
              <BiChevronDown className="size-8" />
            </button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Portal>
            <DropdownMenu.Content
              className="min-w-[220px] bg-background py-3 rounded-2xl focus:outline-none z-[999999]"
              style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
              sideOffset={5}
            >
              {commissionProducts.map((product) => {
                const isProductSelected = listingProductsArray.includes(product.value);

                return (
                  <Link
                    key={`commissions-listing-control-${product.value}`}
                    href={(() => {
                      const newParams = {
                        ...searchParams,
                        listingProducts: [...listingProductsArray],
                      };

                      if (isProductSelected) {
                        newParams.listingProducts.splice(newParams.listingProducts.indexOf(product.value), 1);
                      } else {
                        newParams.listingProducts.push(product.value);
                      }

                      if (newParams.listingProducts.length === 0) {
                        newParams.listingProducts = ["all"];
                      } else {
                        const allIndex = newParams.listingProducts.findIndex((p) => p === "all");

                        if (allIndex !== -1 && newParams.listingProducts.length > 1) {
                          if (product.value === "all") {
                            newParams.listingProducts = ["all"];
                          } else {
                            newParams.listingProducts.splice(allIndex, 1);
                          }
                        }
                      }

                      return `?${joinSearchParams(newParams)}`;
                    })()}
                    scroll={false}
                  >
                    <DropdownMenu.Item
                      onSelect={(e) => e.preventDefault()}
                      data-selected={isProductSelected}
                      className="cursor-pointer outline-none text-primary px-4 py-2 hover:bg-surface-02 active:bg-on-error-opacity data-[selected=true]:font-bold transition-all"
                    >
                      <div className="flex gap-1 items-center">
                        {isProductSelected ? (
                          <CheckBox className="fill-primary w-5 h-5" />
                        ) : (
                          <CheckBoxOutlineBlank className="fill-primary w-5 h-5" />
                        )}
                        {product.label}
                      </div>
                    </DropdownMenu.Item>
                  </Link>
                );
              })}
            </DropdownMenu.Content>
          </DropdownMenu.Portal>
        </DropdownMenu.Root>
        <div className="flex gap-3 flex-wrap">
          <CalendarToday width={20} height={20} className="fill-support-blue" />
          <p className="text-support-blue font-bold">28/08/2023</p>
          <p className="text-tonal-dark-cream-40">to</p>
          <p className="text-support-blue font-bold">28/09/2023</p>
        </div>
      </div>
      <div className="bg-white rounded-[40px] p-[60px] grid grid-cols-1 2xl:grid-cols-2 gap-y-4 gap-x-10">
        <div className="flex flex-col gap-4">
          <h6 className="text-tonal-dark-cream-10 text-2xl font-bold">Clients acquired in total</h6>
          <h4 className="text-primary font-bold text-4xl">2.365</h4>
          <h6 className="text-tonal-dark-cream-10 text-2xl font-bold">Total commission earned</h6>
          <div className="flex gap-2 items-center text-primary font-bold">
            <p className="text-2xl">€</p>
            <h4 className="text-4xl">25 150.81</h4>
          </div>
        </div>
        <div className="flex flex-col">
          <h6 className="text-tonal-dark-cream-10 text-2xl font-bold">My commission in total</h6>
          <div className="flex gap-2 items-center text-primary font-bold pt-2">
            <p className="text-2xl">€</p>
            <h4 className="text-4xl">361</h4>
          </div>
          <p className="text-primary font-bold pt-2 pb-6">Payment period: Monthly</p>
          <div className="flex">
            <Button variant="filled" color="yellow" size="medium" trailingIcon={<East />}>
              Request
            </Button>
          </div>
        </div>
      </div>

      <hr className="pb-8 mt-8 text-tonal-dark-cream-80" />

      <div>
        <h3 className="text-primary font-bold text-[28px]">
          {selectedProducts.map((p) => p.label).join(", ")} Clients
        </h3>
        <div className="flex justify-between flex-wrap gap-4 items-center pb-8">
          <p className="text-primary">Your reward will be cashed once this user finishes the registration process.</p>
          <div className="flex gap-6 items-center">
            <Button
              variant="text"
              color="gray"
              size="large"
              leadingIcon={<Download className="fill-tonal-dark-green-30" />}
              className="text-tonal-dark-green-30"
            >
              XLSX
            </Button>
            <Button
              variant="text"
              color="gray"
              size="large"
              leadingIcon={<Download className="fill-[#66A73F]" />}
              className="text-[#66A73F]"
            >
              CSV
            </Button>
          </div>
        </div>
        <div className="flex justify-between flex-wrap gap-3 items-center pb-8">
          <div className="flex-grow max-w-80 py-3 px-4 flex gap-4 border border-tonal-dark-cream-80 bg-[#FCFCFC] rounded-3xl items-center">
            <Search width={24} height={24} className="fill-tonal-dark-cream-10 flex-shrink-0" />
            <form
              className="flex flex-grow"
              onSubmit={(e) => {
                e.preventDefault();
                const searchValue = new FormData(e.currentTarget).get("listingSearch");

                const newParams = {
                  ...searchParams,
                  listingSearch: searchValue?.toString(),
                };

                router.push(`./commissions?${joinSearchParams(newParams)}`, {
                  scroll: false,
                });
              }}
            >
              <input
                name="listingSearch"
                placeholder="Search by name"
                className="flex-grow w-full max-w-72 outline-none bg-[transparent] text-tonal-dark-cream-10 placeholder:text-tonal-dark-cream-60"
              />
            </form>
          </div>
          <DropdownMenu.Root>
            <DropdownMenu.Trigger className=" focus:outline-none" asChild>
              <button className="cursor-pointer flex gap-2 items-center text-support-blue">
                <FilterAlt className="fill-support-blue" width={20} height={20} />
                <p className="font-bold overflow-hidden whitespace-nowrap text-ellipsis">
                  {initialLeads.every((l1) => listingLeadsArray.find((l2) => l2 === l1))
                    ? "All leads"
                    : listingLeadsArray
                        .map((l1) => {
                          return leadTypes.find((l2) => l2.value === l1)?.label;
                        })
                        .join(" ; ")}
                </p>
                <BiChevronDown className="size-5" />
              </button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Portal>
              <DropdownMenu.Content
                className="min-w-[220px] bg-background py-3 rounded-2xl focus:outline-none z-[999999]"
                style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
                sideOffset={5}
              >
                {leadTypes.map((lead) => {
                  const leadSelected = listingLeadsArray.includes(lead.value);
                  return (
                    <Link
                      key={`commissions-listing-lead-control-${lead.value}`}
                      href={(() => {
                        const newParams = {
                          ...searchParams,
                          listingLeads: [...listingLeadsArray],
                        };

                        if (leadSelected) {
                          newParams.listingLeads.splice(newParams.listingLeads.indexOf(lead.value), 1);
                        } else {
                          newParams.listingLeads.push(lead.value);
                        }

                        if (newParams.listingLeads.length === 0) {
                          newParams.listingLeads = initialLeads;
                        }

                        return `?${joinSearchParams(newParams)}`;
                      })()}
                      scroll={false}
                    >
                      <DropdownMenu.Item
                        onSelect={(e) => e.preventDefault()}
                        data-selected={leadSelected}
                        className="cursor-pointer outline-none text-primary px-4 py-2 hover:bg-surface-02 active:bg-on-error-opacity data-[selected=true]:font-bold transition-all"
                      >
                        <div className="flex gap-1 items-center">
                          {leadSelected ? (
                            <CheckBox className="fill-primary w-5 h-5" />
                          ) : (
                            <CheckBoxOutlineBlank className="fill-primary w-5 h-5" />
                          )}
                          {lead.label}
                        </div>
                      </DropdownMenu.Item>
                    </Link>
                  );
                })}
              </DropdownMenu.Content>
            </DropdownMenu.Portal>
          </DropdownMenu.Root>
        </div>
        <div className="w-full overflow-auto">
          <table className="">
            <thead className="text-left text-sm [&>th]:bg-surface-03 [&>th]:text-primary [&>th]:p-2 [&>th]:font-normal [&>th:first-child]:rounded-tl-[20px] [&>th:last-child]:rounded-tr-[20px] [&>th:first-child]:pl-7 [&>th:last-child]:pr-7">
              <th>Commission Date</th>
              <th>Paid Amount</th>
              <th>Commission (%)</th>
              <th>Commission (€)</th>
              <th>Order ID</th>
              <th>Order Number</th>
              <th>Lead Source</th>
              <th>First Time Purchase</th>
            </thead>
            <tbody>
              {commissionResult.results.map((r: any) => (
                <tr
                  key={`commission-table-result-${r.orderId}`}
                  className="bg-[#FCFCFC] [&>td]:text-primary [&>td]:px-2 [&>td]:py-6 [&>td:first-child]:pl-7 [&>td:last-child]:pr-7"
                >
                  <td>
                    {new Intl.DateTimeFormat("en-GB", {
                      day: "2-digit",
                      month: "2-digit",
                      year: "2-digit",
                    })
                      .format(r.commissionDate)
                      .replaceAll("/", ".")}
                  </td>
                  <td>{r.amount.toFixed(2)}</td>
                  <td>{r.commissionPercentage}%</td>
                  <td>€ {r.commissionValue.toFixed(2)}</td>
                  <td>{r.orderId}</td>
                  <td>{r.orderNumber}</td>
                  <td>{r.leadSource.value}</td>
                  <td>{r.firstTimePurchase ? "Yes" : "No"}</td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr>
                <td className="p-6 bg-surface-03 rounded-b-[20px]" colSpan={8}>
                  <div className="flex gap-6 justify-between">
                    <div className="flex gap-2 items-center">
                      <h6 className="text-tonal-dark-cream-10 font-bold text-xl">Results for:</h6>
                      <p className="text-primary">
                        {listingLeadsArray
                          .map((l1) => {
                            return leadTypes.find((l2) => l2.value === l1)?.label;
                          })
                          .join(" ; ")}
                      </p>
                    </div>
                    <div className="flex gap-6">
                      <div className="flex flex-col gap-2">
                        <h6 className="text-tonal-dark-cream-10 font-bold text-2xl">€ 1852.24</h6>
                        <p className="text-sm text-tonal-dark-cream-10">Total of net turnover</p>
                      </div>
                      <div className="flex flex-col gap-2">
                        <h6 className="text-tonal-dark-cream-10 font-bold text-2xl">€ 258.36</h6>
                        <p className="text-sm text-tonal-dark-cream-10">Total commission for</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </tfoot>
          </table>
          <div className="w-full flex gap-1 justify-center pt-10 pb-4">
            {Array.from(Array(totalPages).keys()).map((p) => (
              <Link
                key={`commission-table-page-${p + 1}`}
                href={(() => {
                  const newParams = {
                    ...searchParams,
                    listingPage: String(p + 1),
                  };

                  return `?${joinSearchParams(newParams)}`;
                })()}
                scroll={false}
                data-selected={selectedPage === String(p + 1)}
                className="data-[selected=true]:bg-primary data-[selected=true]:text-[#F5F5F5] text-primary rounded-lg px-2 py-1"
              >
                {p + 1}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
