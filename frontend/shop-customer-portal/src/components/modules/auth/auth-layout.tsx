import { Header } from "@/components/_common/header";
import { ProfileDropdown } from "@/components/_common/header/profile-dropdown";
import { ReactNode } from "react";
import { AuthLayoutImage } from "./components/auth-layout-image";

interface AuthLayoutProps {
  children: ReactNode;
}

export function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <>
      <div className="w-full px-4 border-b-[1px] border-tonal-dark-cream-80">
        <div className="max-w-7xl w-full mx-auto">
          <Header>
            <ProfileDropdown />
          </Header>
        </div>
      </div>
      <main className="py-10 px-4 bg-background h-full">
        <div className="h-full w-full max-w-7xl mx-auto">
          <div className="w-full grid lg:grid-cols-2 bg-surface-03 rounded-[40px]">
            <div className="hidden lg:block col-span-1 rounded-[40px] overflow-hidden">
              <AuthLayoutImage />
            </div>
            <div className="flex-1 flex items-center justify-center mx-auto px-6 py-16 md:py-28">
              <div className="w-full lg:w-[400px]">{children}</div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
