import { Step } from "react-joyride";
import { ServiceOnboardStep } from "@/components/modules/saas/services/components/service-onboard/service-onboard-step";

export const directLicenseOnboardSteps: Step[] = [
  {
    content: "first",
    placement: "center",
    target: "body",
  },
  {
    content: (
      <ServiceOnboardStep
        title="Declare change of volumes"
        description={`You can always change the volumes reported afterwards. \r\nNote: if you reduce your quantities by August 31th you can receive a refund for it!`}
      />
    ),
    placement: "right-start",
    placementBeacon: "top",
    target: "#sb-de-declare-volumes",
    isFixed: true,
    styles: {},
  },
  {
    content: (
      <ServiceOnboardStep
        title="Check your contract information"
        description="Easy way to access all of your contract information per country"
      />
    ),
    placement: "right-start",
    placementBeacon: "left",
    target: "#sb-eu-contract-management",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep
        title="Access your invoices and track payment status"
        description="Here you can check all payment related content"
      />
    ),
    placement: "right-start",
    placementBeacon: "top",
    target: "#sb-eu-invoices-payment",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep
        title="Access your contract information"
        description="By clicking on the title you are going to be redirected to the contract page"
      />
    ),
    placement: "left-start",
    placementBeacon: "top",
    target: "#contract-management",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep
        title="Keep track of your license progress!"
        description="You can visualize the next steps for your licensing"
      />
    ),
    placement: "left-start",
    target: "#next-steps",
    title: "Our projects",
  },
  {
    content: (
      <ServiceOnboardStep title="View your recent reports" description="Have a preview that shows your last reports" />
    ),
    placement: "right-start",
    target: "#declare-volumes",
    title: "Our Mission",
  },
  {
    content: (
      <ServiceOnboardStep
        title="Resources Savings"
        description="You can download a seal for embed in your website or social media!"
      />
    ),
    placement: "left-start",
    target: "#my-resources",
    title: "Our Mission",
  },
  {
    content: "last",
    placement: "center",
    target: "body",
  },
];
