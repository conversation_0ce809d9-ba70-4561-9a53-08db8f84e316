"use client";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { SaasDescription, SaasTitle } from "@/components/modules/saas/components/saas-title";
import { ServiceOnboard } from "@/components/modules/saas/services/components/service-onboard";
import Image from "next/image";

import { ContractManagementCard } from "../../components/dashboard-cards/contract-management-card";
import { DeclareVolumesCard } from "../../components/dashboard-cards/declare-volumes-card";
import { DirectLicenseInformationCard } from "./cards/license-information-card";
import { DashboardInviteCustomer } from "./dashboard-invite-customer";
import { DashboardMyResources } from "./dashboard-my-resources";
import { directLicenseOnboardSteps } from "./direct-license-onboard-steps";
import { NextStepsCard } from "../../components/dashboard-cards/next-steps-card";
import { useCustomer } from "@/hooks/use-customer";
import { useTranslations } from "next-intl";

const paths = [{ label: "Dashboard DE", href: "#" }];

export function DirectLicenseDashboard() {
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.directLicense.dashboard");

  const { customer } = useCustomer();

  if (!customer) return null;

  const directLicenseContract = customer.contracts.find((contract) => contract.type === "DIRECT_LICENSE");

  const directLicense = directLicenseContract?.licenses.find((license) => license.country_code === "DE");

  if (!directLicense) return null;

  return (
    <>
      <SaasBreadcrumb paths={paths} />
      <SaasContainer className="mb-16">
        <h2 className="text-primary text-3xl font-bold mb-20">
          {globalT("words.greetings", { name: `${customer.first_name} ${customer.last_name}` })}
        </h2>
        <SaasTitle>{t("title")}</SaasTitle>
        <SaasDescription>{t("description")}</SaasDescription>
      </SaasContainer>
      <SaasContainer className="py-16" containerClassName="bg-tonal-cream-96">
        <SaasTitle className="mb-12 justify-between">
          <div className="flex items-center gap-4">
            <Image
              src="/assets/images/europe_union.png"
              alt="Europe Union image"
              width={48}
              height={48}
              className="w-12 h-12"
            />
            Germany
          </div>
          <ServiceOnboard steps={directLicenseOnboardSteps} />
        </SaasTitle>
        <div className="grid grid-cols-1 md:grid-cols-11 gap-6">
          <DirectLicenseInformationCard />
          <ContractManagementCard />
          <DeclareVolumesCard countryCode="DE" />
          <NextStepsCard licenseId={directLicense.id} />
          <DashboardInviteCustomer />
          <DashboardMyResources />
        </div>
      </SaasContainer>
    </>
  );
}
