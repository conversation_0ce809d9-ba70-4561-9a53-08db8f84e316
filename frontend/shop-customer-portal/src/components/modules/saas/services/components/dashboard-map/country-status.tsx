import { cn } from "@/lib/utils";
import { STATUS_FILTERS, STATUS_STYLE } from ".";

export type MapCountryStatus = "OPEN_TO_DOS" | "IN_REVIEW" | "DONE";

interface CountryStatusProps {
  statusValue: MapCountryStatus;
}

export function CountryStatus({ statusValue }: CountryStatusProps) {
  const status = STATUS_FILTERS.find((s) => s.value === statusValue)!;
  const style = STATUS_STYLE[statusValue];

  return (
    <div
      data-status={statusValue}
      className={cn("md:py-2 py-1 md:px-4 px-2 rounded-lg flex-none inline-block", style.bgClass)}
    >
      <p className={cn("text-xs md:text-sm font-bold", style.textClass)}>{status.label}</p>
    </div>
  );
}
