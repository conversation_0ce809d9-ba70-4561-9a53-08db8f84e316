"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { SelectDropdown } from "@/components/_common/select-dropdown";
import { Link } from "@/i18n/navigation";
import { BaseCountry } from "@/lib/api/country/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add, Clear, Launch, Remove } from "@arthursenno/lizenzero-ui-react/Icon";
import { useState } from "react";

export type LawChangeItem = {
  country: BaseCountry;
  date: string;
  status: string;
  text: string;
  articleUrl: string;
};

const mockData: LawChangeItem[] = [
  {
    country: {
      id: 0,
      code: "FR",
      name: "France",
      flag_url:
        "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c3/Flag_of_France.svg/320px-Flag_of_France.svg.png",
    },
    date: "2023-05-13",
    status: "Major change",
    text: `In Ungarn hat sich die Gesetzeslage geändert! Sie sind nun auch verpflichtet, 
    wenn Sie nicht über eine ungarische Steuernummer verfügen. Weitere Informationen 
    finden Sie im Leitfaden.`,
    articleUrl: "#",
  },
  {
    country: {
      id: 0,
      code: "FR",
      name: "France",
      flag_url:
        "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c3/Flag_of_France.svg/320px-Flag_of_France.svg.png",
    },
    date: "2023-05-13",
    status: "Major change",
    text: `In Ungarn hat sich die Gesetzeslage geändert! Sie sind nun auch verpflichtet, 
    wenn Sie nicht über eine ungarische Steuernummer verfügen. Weitere Informationen 
    finden Sie im Leitfaden.`,
    articleUrl: "#",
  },
  {
    country: {
      id: 0,
      code: "DE",
      name: "Germany",
      flag_url:
        "https://upload.wikimedia.org/wikipedia/commons/thumb/b/ba/Flag_of_Germany.svg/320px-Flag_of_Germany.svg.png",
    },
    date: "2023-05-13",
    status: "Major change",
    text: `In Ungarn hat sich die Gesetzeslage geändert! Sie sind nun auch verpflichtet, 
    wenn Sie nicht über eine ungarische Steuernummer verfügen. Weitere Informationen 
    finden Sie im Leitfaden.`,
    articleUrl: "#",
  },
  {
    country: {
      id: 0,
      code: "PT",
      name: "Portugal",
      flag_url:
        "https://upload.wikimedia.org/wikipedia/commons/thumb/5/5c/Flag_of_Portugal.svg/255px-Flag_of_Portugal.svg.png",
    },
    date: "2023-05-13",
    status: "Major change",
    text: `In Ungarn hat sich die Gesetzeslage geändert! Sie sind nun auch verpflichtet, 
    wenn Sie nicht über eine ungarische Steuernummer verfügen. Weitere Informationen 
    finden Sie im Leitfaden.`,
    articleUrl: "#",
  },
  {
    country: {
      id: 0,
      code: "IT",
      name: "Italy",
      flag_url:
        "https://upload.wikimedia.org/wikipedia/commons/thumb/0/03/Flag_of_Italy.svg/320px-Flag_of_Italy.svg.png",
    },
    date: "2023-05-13",
    status: "Major change",
    text: `In Ungarn hat sich die Gesetzeslage geändert! Sie sind nun auch verpflichtet, 
    wenn Sie nicht über eine ungarische Steuernummer verfügen. Weitere Informationen 
    finden Sie im Leitfaden.`,
    articleUrl: "#",
  },
];
const renderFilter = ["All countries", "France", "Portugal", "Germany", "Italy"];

const initialFilters = {
  startDate: "",
  endDate: "",
  country: "All countries",
};

export function DashboardLawChanges() {
  const [filters, setFilters] = useState(initialFilters);

  const filteredData = mockData.filter((data) => {
    const matchesCountry = filters.country === "All countries" || data.country.name === filters.country;

    const dataDate = new Date(data.date);
    const startDate = filters.startDate ? new Date(filters.startDate) : null;
    const endDate = filters.endDate ? new Date(filters.endDate) : null;

    const matchesStartDate = startDate ? dataDate >= startDate : true;
    const matchesEndDate = endDate ? dataDate <= endDate : true;

    return matchesCountry && matchesStartDate && matchesEndDate;
  });

  return (
    <div
      className="flex-1 rounded-[32px] items-start bg-white flex flex-col py-6 md:py-7 px-4 md:px-8"
      id="dashboard-law-changes"
    >
      <h3 className="text-primary text-2xl font-bold mb-6">Law Changes</h3>

      <div className="flex md:flex-row flex-col md:items-center items-start gap-2">
        <div className="flex items-center gap-2">
          <input
            className="text-support-blue text-sm w-20"
            onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
            placeholder="date"
            type="date"
            value={filters.startDate}
          />
          <span className="text-sm text-on-surface-01">to</span>
          <input
            className="text-support-blue text-sm w-20"
            onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
            placeholder="date"
            type="date"
            value={filters.endDate}
          />
        </div>

        <div className="md:block hidden border-on-surface-01 border h-3 rounded-full" />

        <SelectDropdown
          options={renderFilter.map((country) => ({
            label: String(country),
            value: String(country),
          }))}
          value={filters.country}
          onChangeValue={(country) => setFilters({ ...filters, country })}
        />
      </div>
      <div className="md:mb-5 mb-6">
        <Button
          size="small"
          variant="text"
          color="dark-blue"
          onClick={() => setFilters(initialFilters)}
          trailingIcon={<Clear className="fill-primary size-6" />}
        >
          Clear filters
        </Button>
      </div>

      {filteredData.length > 0 ? (
        <>
          <div className="flex flex-col w-full gap-0">
            {filteredData.map((c, i) => (
              <CountryLawChange
                key={i}
                country={c.country}
                articleUrl={c.articleUrl}
                date={c.date}
                status={c.status}
                text={c.text}
              />
            ))}
          </div>

          <div className="flex items-center justify-center w-full mt-6">
            <Button variant="text" color="dark-blue" size="small" leadingIcon={<Add />}>
              Load More
            </Button>
          </div>
        </>
      ) : (
        <p className="text-error mt-6">No items found by the filters</p>
      )}
    </div>
  );
}

function CountryLawChange({ country, date, status, text, articleUrl }: LawChangeItem) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div
        data-open={isOpen}
        className="group flex flex-col w-full gap-2 p-4 bg-white data-[open=true]:bg-surface-01 border-b-[1px] border-tonal-dark-cream-80"
      >
        <div className="flex items-center gap-3">
          <CountryIcon country={country} className="size-8" />
          <p className="text-primary font-medium text-xl flex-1">{country.name}</p>
          <div
            onClick={() => setIsOpen(!isOpen)}
            className="flex items-center justify-center h-8 w-8 rounded-full cursor-pointer bg-tertiary group-data-[open=true]:bg-primary"
          >
            {isOpen ? <Remove fill="white" className="w-5 h-5" /> : <Add className="w-5 h-5" />}
          </div>
        </div>

        {isOpen && (
          <div className="flex flex-col gap-4 mb-1">
            <Link className="flex items-center gap-1 text-sm text-support-blue underline font-medium" href={articleUrl}>
              See full article <Launch className="fill-support-blue w-4 h-4" />
            </Link>
            <p className="text-sm text-tonal-dark-cream-30">{text}</p>
          </div>
        )}
        <div className="flex items-center gap-2">
          <p className="text-tonal-dark-cream-40 text-sm">{date.replaceAll("-", ".")}</p>
          <div className="border-on-surface-01 border h-2 rounded-full" />
          <p className="text-tonal-dark-green-30 text-sm font-bold">{status}</p>
        </div>
      </div>
    </>
  );
}
