import { Video } from "@/components/ui/video";
import { PlayCircleFilled } from "@arthursenno/lizenzero-ui-react/Icon";

interface ServiceOnboardStep {
  title: string;
  description: string;
  videoUrl?: boolean;
}

export function ServiceOnboardStep({ title, description, videoUrl }: ServiceOnboardStep) {
  return (
    <div className="space-y-3">
      <p className="text-primary font-bold text-xl">{title}</p>
      <p className="text-[#314F72] whitespace-pre-line">{description}</p>
      {!!videoUrl && (
        <Video
          className="mt-4 w-full h-48"
          url={"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"}
        />
      )}
    </div>
  );
}
