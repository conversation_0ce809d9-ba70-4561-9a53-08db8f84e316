"use client";
import { CountryIcon } from "@/components/_common/country-icon";
import { SelectDropdown } from "@/components/_common/select-dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { getPackagingServices } from "@/lib/api/packaging-services";
import { Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery } from "@tanstack/react-query";
import { useLicenseTabs } from "../../components/license-tabs/use-license-tabs";
import { DeclareLicenseVolumesForm } from "./declare-license-volumes-form";
import { useTranslations } from "next-intl";

export function DeclareLicenseVolumes() {
  const { selectedLicense, licenseYears, isLoading: isLoadingLicense } = useLicenseTabs();
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.euLicense.declareVolumes");

  const currentYear = new Date().getFullYear().toString();

  const { paramValues, changeParam } = useQueryFilter(["year"]);

  const currentLicenseYear = paramValues.year || currentYear;

  const {
    data: packagingServices,
    isLoading: isLoadingPackagingServices,
    isPending: isPendingPackagingServices,
  } = useQuery({
    queryKey: ["packaging-services", { license_id: selectedLicense?.id }],
    queryFn: async () => {
      if (!selectedLicense) return [];

      return getPackagingServices({
        license_id: selectedLicense.id,
        // interval: selectedInterval,
      });
    },
    enabled: !!selectedLicense,
  });

  if (!selectedLicense) return null;

  const hasVolumeReportOpen = packagingServices?.some((packagingService) =>
    packagingService.volume_reports.some((volumeReport) => volumeReport.status === "OPEN")
  );

  if (isLoadingPackagingServices)
    return (
      <div className="text-primary w-full rounded-[32px] items-start md:mt-20">
        <div className="flex justify-between w-full">
          <div className="flex items-center gap-4">
            <Skeleton className="size-10" />
            <Skeleton className="w-40 h-8" />
          </div>
          <Skeleton className="w-32 h-10" />
        </div>
      </div>
    );

  return (
    <div className="text-primary w-full rounded-[32px] items-start md:mt-20">
      <div className="space-y-6 pb-4">
        <div className="flex justify-between w-full">
          <div className="flex items-center gap-3">
            <CountryIcon
              country={{
                name: selectedLicense.country_name,
                flag_url: selectedLicense.country_flag,
              }}
              className="size-7"
            />
            <p className="text-primary font-bold text-3xl flex items-center">{selectedLicense.country_name}</p>
          </div>
          {hasVolumeReportOpen ? (
            <div className="flex space-x-2 items-center text-tonal-red-40 bg-tonal-red-96 py-2 px-4 text-sm rounded-md font-medium">
              <Error width={20} height={20} className="fill-tonal-red-40 " />
              <span>{t("status.volumeReportOpen")}</span>
            </div>
          ) : (
            <div className="flex space-x-2 items-center text-success bg-success-container py-2 px-4 text-sm rounded-md font-medium">
              <span>{t("status.volumeReportComplete")}</span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-4">
          <p className="text-primary font-bold text-xl flex items-center">{globalT("filters.licenseYear.label")}</p>
          <SelectDropdown
            options={licenseYears.map((year) => ({
              label: String(year),
              value: String(year),
            }))}
            value={String(currentLicenseYear)}
            onChangeValue={(value) => changeParam("year", value)}
          />
        </div>
      </div>
      <div className="flex flex-col gap-10">
        {packagingServices?.map((packagingService) => (
          <DeclareLicenseVolumesForm
            key={packagingService.id}
            packagingService={packagingService}
            countryCode={selectedLicense.country_code}
            licenseYear={Number(currentLicenseYear)}
            contractId={selectedLicense.contract_id}
          />
        ))}
      </div>
    </div>
  );
}
