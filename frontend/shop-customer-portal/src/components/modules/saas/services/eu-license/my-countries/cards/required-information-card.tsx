"use client";

import { <PERSON> } from "@/i18n/navigation";

import { BiLoader } from "react-icons/bi";
import { EditCircle, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery } from "@tanstack/react-query";

import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { useCustomer } from "@/hooks/use-customer";
import { getRequiredInformations } from "@/lib/api/required-informations";
import { RequiredInformationIcon } from "@/components/modules/saas/services/eu-license/required-information/required-information-icon";
import { cn } from "@/lib/utils";
import { RequiredInformationStatus } from "@/lib/api/required-informations/types";
import { Skeleton } from "@/components/ui/skeleton";
import { Divider } from "@/components/_common/divider";
import { StatusBadge } from "@/components/ui/status-badge";

interface RequiredInformationCardProps {
  countryCode: string;
}

const REQUIRED_INFORMATION_STATUS_LABEL: Record<RequiredInformationStatus, React.ReactNode> = {
  DECLINED: <div className="px-4 py-2 rounded-lg text-sm font-bold bg-tonal-red-80 text-error">Open to-dos</div>,
  OPEN: (
    <div className="px-4 py-2 rounded-lg text-sm font-bold bg-tonal-dark-cream-80 text-tonal-dark-cream-30">New</div>
  ),
  DONE: <div className="px-4 py-2 rounded-lg text-sm font-bold bg-tonal-green-80 text-success">Submitted</div>,
  APPROVED: <div className="px-4 py-2 rounded-lg text-sm font-bold bg-tonal-green-80 text-success">Approved</div>,
};

export function RequiredInformationCard({ countryCode }: RequiredInformationCardProps) {
  const { customer } = useCustomer();

  const euLicenseContract = customer?.contracts.find((c) => c.type === "EU_LICENSE");

  const license = euLicenseContract?.licenses.find((l) => l.country_code === countryCode);

  const { data: requiredInformation, isLoading: isLoadingRequiredInformation } = useQuery({
    queryKey: ["required-information", license?.id],
    queryFn: async () => {
      return license ? await getRequiredInformations({ license_id: license.id }) : [];
    },
    enabled: !!license,
  });

  if (!license) return null;

  const allDone = requiredInformation?.every((info) => info.status === "DONE");

  return (
    <div id="required-information" className="col-span-1 md:col-span-6 px-4 py-6 md:p-8 bg-background rounded-3xl">
      <div className="group flex items-center gap-3 mb-5">
        <h2 className={cn("text-2xl text-primary font-bold", { "text-error": !allDone })}>Required documents & info</h2>
        {!allDone && (
          <QuestionTooltip icon={<Error className="size-5 fill-error border-white" />}>
            <QuestionTooltipTitle className="text-error text-sm font-bold">
              <Error className="size-5 fill-error border-white" /> Open to-do
            </QuestionTooltipTitle>
            <QuestionTooltipDescription className="text-sm">
              This section needs action. Please enter the required information.
            </QuestionTooltipDescription>
          </QuestionTooltip>
        )}
        <Link href={`/saas/eu-license/required-information?license-id=${license.id}`}>
          <EditCircle className="size-7 fill-support-blue" />
        </Link>
      </div>

      <div className="space-y-2">
        {isLoadingRequiredInformation &&
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Skeleton className="size-10 rounded-lg" />
                  <Skeleton className="h-5 w-28" />
                </div>
                <Skeleton className="h-8 w-28" />
              </div>
              <Divider style={{ margin: 0 }} />
            </div>
          ))}
        {!isLoadingRequiredInformation &&
          !!requiredInformation?.length &&
          requiredInformation.map((information) => (
            <div
              key={information.id}
              data-status={information.status}
              className="group flex flex-col w-full gap-2 py-3 border-b-[1px] border-tonal-dark-cream-80"
            >
              <div className="flex items-center gap-2">
                <RequiredInformationIcon requiredInformationType={information.type} />
                <div className="flex items-center gap-2 flex-1 truncate">
                  {information.type === "TEXT" && <p className="text-tonal-dark-cream-20 font-medium">Text input</p>}
                  {information.type === "NUMBER" && (
                    <p className="text-tonal-dark-cream-20 font-medium">Registration number</p>
                  )}
                  {(information.type === "DOCUMENT" || information.type === "FILE" || information.type === "IMAGE") && (
                    <p className="text-tonal-dark-cream-20 font-medium">
                      {information.answer_files?.[0]?.original_name || information.name}
                    </p>
                  )}
                  <QuestionTooltip>
                    <QuestionTooltipDescription>
                      Lorem ipsum dolor sit amet consectetur, adipisicing elit.
                    </QuestionTooltipDescription>
                  </QuestionTooltip>
                </div>
                {information.status === "DONE" && <StatusBadge variant="success" label="Done" />}
                {information.status === "OPEN" && <StatusBadge variant="info" label="New" />}
                {information.status === "APPROVED" && <StatusBadge variant="success" label="Approved" />}
                {information.status === "DECLINED" && <StatusBadge variant="error" label="Open to-dos" />}
              </div>
            </div>
          ))}
        {!isLoadingRequiredInformation && !requiredInformation?.length && (
          <p className="text-center text-primary">No required documents registered</p>
        )}
      </div>
    </div>
  );
}
