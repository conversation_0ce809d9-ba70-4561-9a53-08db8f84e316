"use client";

import { Download } from "@arthursenno/lizenzero-ui-react/Icon";

import { downloadCustomerFile } from "@/lib/api/file";
import { LicenseRegistrationStatus } from "@/lib/api/license/types";
import { useCustomer } from "@/hooks/use-customer";
import { downloadFile } from "@/utils/download-file";
import { formatDateToDDMMYYYY } from "@/utils/formatDateToDDMMYYYY";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { enqueueSnackbar } from "notistack";
import { useTranslations } from "next-intl";

interface LicenseInformationCardProps {
  countryCode: string;
}

const LICENSE_REGISTRATION_STATUS_LABEL: Record<LicenseRegistrationStatus, React.ReactNode> = {
  PENDING: (
    <div className="px-4 py-2 rounded-lg text-sm font-bold bg-tonal-dark-cream-80 text-tonal-dark-cream-30">
      Pending
    </div>
  ),
  IN_REVIEW: (
    <div className="px-4 py-2 rounded-lg text-sm font-bold bg-tonal-dark-cream-80 text-tonal-dark-cream-30">
      In Review
    </div>
  ),
  REGISTRATION: (
    <div className="px-4 py-2 rounded-lg text-sm font-bold bg-tonal-dark-cream-80 text-tonal-dark-cream-30">
      Registration
    </div>
  ),
  DONE: <div className="px-4 py-2 rounded-lg text-sm font-bold bg-tonal-green-80 text-success">License Complete</div>,
};

export function LicenseInformationCard({ countryCode }: LicenseInformationCardProps) {
  const t = useTranslations("modules.saas.pages.euLicense.myCountries");
  const { customer } = useCustomer();

  const euLicenseContract = customer?.contracts.find((c) => c.type === "EU_LICENSE");

  if (!euLicenseContract) return null;

  const license = euLicenseContract.licenses.find((l) => l.country_code === countryCode);

  if (!customer || !license) return null;

  const proofOfRegistrationFile = license.files?.find((file) => file.type === "LICENSE_PROOF_OF_REGISTRATION");

  async function handleDownloadFile() {
    if (!customer) return;
    if (!proofOfRegistrationFile) return;

    enqueueSnackbar("Downloading file. Please wait...", { variant: "info" });

    try {
      const file = await downloadCustomerFile(proofOfRegistrationFile.id);

      downloadFile({ buffer: file, fileName: proofOfRegistrationFile.original_name });
      enqueueSnackbar("File downloaded successfully", { variant: "success" });
    } catch (error) {
      enqueueSnackbar("Error downloading file. Please try again.", { variant: "error" });
    }
  }

  return (
    <div id="licensing-information" className="col-span-1 md:col-span-6 px-4 py-6 md:p-8 bg-background rounded-3xl">
      <h2 className="text-title-3 text-primary font-bold mb-5">{t("licensingInformation.title")}</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 w-full">
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">
            {t("licensingInformation.registrationStatus")}:
          </label>
          <div data-status={license.registration_status} className="group w-fit">
            {LICENSE_REGISTRATION_STATUS_LABEL[license.registration_status]}
          </div>
        </div>
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">
            {t("licensingInformation.RegistrationNumber")}:
          </label>
          <p className="text-paragraph-regular text-primary">{license.registration_number}</p>
        </div>
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">
            {t("licensingInformation.licensingDate")}:
          </label>
          <p className="text-paragraph-regular text-primary">{formatDateToDDMMYYYY(license.start_date)}</p>
        </div>
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">
            {t("licensingInformation.licensingExpiration")}:
          </label>
          <p className="text-paragraph-regular text-primary">
            {license.end_date ? formatDateToDDMMYYYY(license.end_date) : "----"}
          </p>
        </div>
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">Proof of registration</label>
          {/* TODO: Download file */}
          <Button
            variant="text"
            color="light-blue"
            size="small"
            trailingIcon={<Download />}
            onClick={() => handleDownloadFile()}
            disabled={!proofOfRegistrationFile}
          >
            Download
          </Button>
        </div>
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">Type of license</label>
          <p className="text-paragraph-regular text-primary underline underline-offset-2">EU License</p>
        </div>
      </div>
    </div>
  );
}
