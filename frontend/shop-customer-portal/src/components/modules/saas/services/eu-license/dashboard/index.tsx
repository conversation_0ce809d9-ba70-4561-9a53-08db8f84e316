"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, Elipse, File } from "@arthursenno/lizenzero-ui-react/Icon";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

import { Divider } from "@/components/_common/divider";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { SaasDescription, SaasTitle } from "@/components/modules/saas/components/saas-title";
import { ServiceOnboard } from "@/components/modules/saas/services/components/service-onboard";

import { CountryIcon } from "@/components/_common/country-icon";
import { SelectDropdown } from "@/components/_common/select-dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { StatusBadge } from "@/components/ui/status-badge";
import { getLicenses } from "@/lib/api/license";
import { FullLicense, LicensePendency } from "@/lib/api/license/types";
import { getRequiredInformations } from "@/lib/api/required-informations";
import { cn } from "@/lib/utils";
import { useCustomer } from "@/hooks/use-customer";
import { useQuery } from "@tanstack/react-query";
import { DashboardBanner } from "../../components/dashboard-banner";
import { DashboardLawChanges } from "../../components/dashboard-law-changes";
import { DashboardMap, ORDER_FILTERS, STATUS_FILTERS } from "../../components/dashboard-map";
import { CountryStatus, MapCountryStatus } from "../../components/dashboard-map/country-status";
import { euLicenseOnboardSteps } from "./eu-license-onboard-steps";
import { useTranslations } from "next-intl";

function getPendencyHref(pendency: LicensePendency, countryCode: string) {
  if (pendency.type === "REQUIRED_INFORMATIONS") {
    return `./eu-license/required-information?country-code=${countryCode}`;
  }

  if (pendency.type === "VOLUME_REPORTS") {
    return `./eu-license/declare-volumes?country-code=${countryCode}`;
  }

  if (pendency.type === "INVOICES") {
    return `./eu-license/third-party-invoices?country-code=${countryCode}`;
  }

  return "";
}

function filterLicensesByStatus(licenses: FullLicense[], status: (typeof STATUS_FILTERS)[number]) {
  switch (status.value) {
    case "OPEN_TO_DOS":
      return licenses.filter((license) => license.pendencies_status === "OPEN_TO_DOS");
    case "IN_REVIEW":
      return licenses.filter((license) => license.pendencies_status === "IN_REVIEW");
    case "DONE":
      return licenses.filter((license) => license.pendencies_status === "DONE");
    default:
      return licenses.sort((a, b) => {
        const statusA = a.pendencies_status;
        const statusB = b.pendencies_status;

        const statusOrder = {
          OPEN_TO_DOS: 0,
          IN_REVIEW: 1,
          DONE: 2,
        };

        return statusOrder[statusA] - statusOrder[statusB];
      });
  }
}

const paths = [{ label: "Dashboard EU", href: "#" }];

export function EuLicenseDashboard() {
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.euLicense.dashboard");

  const [status, setStatus] = useState<(typeof STATUS_FILTERS)[number]>(STATUS_FILTERS[0]);

  const { customer } = useCustomer();

  const euLicenseContract = customer?.contracts.find((contract) => contract.type === "EU_LICENSE");

  const missingPowerOfAttorney = !!euLicenseContract?.general_informations.find(
    (information) => information.name === "Power of Attorney" && ["OPEN", "DECLINED"].includes(information.status)
  );

  const { data: licenses, isLoading: isLoadingLicenses } = useQuery({
    queryKey: ["licenses", euLicenseContract?.id],
    queryFn: () => getLicenses({ contract_id: euLicenseContract?.id }),
    enabled: !!euLicenseContract,
  });

  const { data: generalInformations, isLoading: isLoadingGeneralInformations } = useQuery({
    queryKey: ["general-informations", { contract_id: euLicenseContract?.id }],
    queryFn: async () => {
      if (!euLicenseContract) return [];

      return getRequiredInformations({ contract_id: euLicenseContract?.id });
    },
    enabled: !!euLicenseContract,
  });

  function handleChangeStatus(newStatus: (typeof STATUS_FILTERS)[number]) {
    setStatus(newStatus);
  }

  const filteredLicenses = filterLicensesByStatus(licenses || [], status);

  const mapCountries = filteredLicenses.map((license) => {
    return {
      id: license.id,
      code: license.country_code,
      name: license.country_name,
      flag_url: license.country_flag,
      status: license.pendencies_status as MapCountryStatus,
    };
  });

  const isEveryLicenseDone = !!licenses && licenses.every((l) => l.pendencies_status === "DONE");
  const isEveryGeneralInformationDone = generalInformations?.every((info) =>
    ["DONE", "APPROVED"].includes(info.status)
  );

  return (
    <>
      {missingPowerOfAttorney && (
        <SaasContainer containerClassName="max-md:pb-0 pb-10">
          <DashboardBanner />
        </SaasContainer>
      )}
      <SaasBreadcrumb paths={paths} />
      <SaasContainer containerClassName="max-md:pb-0 pb-10">
        {customer ? (
          <h2 className="text-primary text-3xl font-bold" id="test">
            {globalT("words.greetings", { name: `${customer.first_name} ${customer.last_name}` })}
          </h2>
        ) : (
          <Skeleton className="w-96 h-10" />
        )}
      </SaasContainer>
      <SaasContainer className="py-16" containerClassName="bg-tonal-cream-96">
        <div className="flex items-center justify-between mb-16">
          <div className="space-y-4">
            <SaasTitle>
              <Image src="/assets/images/europe_union.png" alt="Europe Union image" width={49} height={49} />
              {t("title")}
            </SaasTitle>
            <SaasDescription>{t("description")}</SaasDescription>
          </div>
          <ServiceOnboard steps={euLicenseOnboardSteps} />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
          <div
            className="col-span-1 md:col-span-6 bg-background rounded-[40px] py-6 md:py-10 px-4 md:px-6 space-y-6"
            id="eu-dashboard-licenses"
          >
            <div className="flex items-center gap-2">
              <p className="text-grey-blue text-2xl font-bold">{t("myLicenses.title")}</p>
              <TooltipIcon info={t("myLicenses.tooltip")} />
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <p className="text-grey-blue text-xl font-bold">{t("myLicenses.requiredInformation")}</p>
              </div>
              <div className="flex items-center py-3 border-b border-tonal-dark-cream-80">
                <div className="flex items-center gap-3 flex-1 text-primary font-bold">
                  <File className="size-7 fill-primary" />
                  {t("myLicenses.generalDocuments")}
                </div>
                {isLoadingGeneralInformations && <Skeleton className="w-20 h-6" />}
                {!isLoadingGeneralInformations && (
                  <>
                    {isEveryGeneralInformationDone ? (
                      <StatusBadge variant="success" label="Submitted" />
                    ) : (
                      <StatusBadge variant="error" label="Required" />
                    )}
                  </>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <p className="text-grey-blue text-xl font-bold">{t("myLicenses.purchased")}</p>
            </div>
            <div className="flex md:flex-row flex-col md:items-center items-start gap-4">
              <SelectDropdown
                options={STATUS_FILTERS.map((status) => ({
                  label: status.label,
                  value: status.value,
                }))}
                value={status.value}
                onChangeValue={(value) =>
                  handleChangeStatus(STATUS_FILTERS.find((s) => s.value === value) || STATUS_FILTERS[0])
                }
              />
            </div>
            {isEveryLicenseDone && (
              <div className="rounded-[20px] py-4 px-6 bg-success-container flex items-center gap-2 my-6">
                <Image
                  src={"/assets/images/leaf_seal.png"}
                  alt="leaf seal"
                  className="w-10 h-10"
                  width={40}
                  height={40}
                />
                <p className="text-tonal-dark-green-30 md:text-base text-sm">{t("myLicenses.wellDone")}</p>
              </div>
            )}
            <div>
              {isLoadingLicenses &&
                Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="mb-5">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 mb-3">
                        <Skeleton className="size-10 rounded-full flex-none" />
                        <div>
                          <Skeleton className="w-20 h-6 flex-none mb-4" />
                          <Skeleton className="w-28 h-3 flex-none mb-2" />
                          <Skeleton className="w-28 h-3 flex-none mb-2" />
                        </div>
                      </div>
                      <Skeleton className="w-24 h-8 flex-none" />
                    </div>
                    <Divider style={{ margin: 0 }} />
                  </div>
                ))}
              {filteredLicenses?.map((license) => {
                const statusValue = STATUS_FILTERS.find((s) => s.value === license.pendencies_status)?.value || "DONE";

                return (
                  <div key={license.id}>
                    <div className="flex py-3 items-start gap-3 justify-between">
                      <div className="flex items-start gap-3">
                        <Link
                          href={`./eu-license/countries/${license.country_code}`}
                          className="flex-nonerounded-full size-8 md:size-10 overflow-hidden"
                        >
                          <CountryIcon
                            country={{ flag_url: license.country_flag, name: license.country_name }}
                            className="size-full "
                          />
                        </Link>
                        <div className="flex-1">
                          <p className="text-primary font-bold text-base">{license.country_name}</p>
                          <ul
                            data-status={license.pendencies_status}
                            className="group flex flex-col gap-1 mt-1 text-error data-[status=OPEN_TO_DOS]:text-error data-[status=IN_REVIEW]:text-tonal-dark-cream-30  data-[status=DONE]:text-tonal-dark-green-30"
                          >
                            {!!license.pendencies.length &&
                              license.pendencies.map((pendency, index) => (
                                <Link
                                  className="block w-full"
                                  href={
                                    license.pendencies_status !== "IN_REVIEW"
                                      ? getPendencyHref(pendency, license.country_code)
                                      : ""
                                  }
                                  key={`${pendency.label}-${index}`}
                                >
                                  <li className="flex items-center gap-1 font-bold text-xs md:text-sm">
                                    <Elipse
                                      className={cn(
                                        "size-2 flex-none",
                                        license.pendencies_status === "OPEN_TO_DOS" && "fill-error",
                                        license.pendencies_status === "IN_REVIEW" && "fill-alert",
                                        license.pendencies_status === "DONE" && "fill-tonal-dark-green-30"
                                      )}
                                    />
                                    <span
                                      className={cn(
                                        "underline underline-offset-2 text-sm",
                                        license.pendencies_status === "OPEN_TO_DOS" && "text-error",
                                        license.pendencies_status === "IN_REVIEW" && "text-alert",
                                        license.pendencies_status === "DONE" && "text-tonal-dark-green-30"
                                      )}
                                    >
                                      {pendency.label}
                                    </span>
                                  </li>
                                </Link>
                              ))}
                            {!license.pendencies.length && (
                              <li className="flex items-center gap-1 font-bold text-xs md:text-sm">
                                <Elipse className="w-2 h-2 flex-none fill-tonal-dark-green-30" />
                                <span className="text-sm text-tonal-dark-green-30">License complete</span>
                              </li>
                            )}
                          </ul>
                        </div>
                      </div>
                      <CountryStatus statusValue={statusValue as MapCountryStatus} />
                    </div>
                    <Divider style={{ margin: 0 }} />
                  </div>
                );
              })}
            </div>

            <Link href="/eu/quick-journey/license/calculator" className="block">
              <Button color="yellow" variant="filled" size="medium" trailingIcon={<East />} className="w-full">
                {globalT("buttons.purchaseOtherCountry.label")}
              </Button>
            </Link>
          </div>
          <div className="w-full col-span-1 md:col-span-6 flex flex-col gap-6">
            <DashboardMap type="EU_LICENSE" countries={mapCountries} />
            <DashboardLawChanges />
          </div>
        </div>
      </SaasContainer>
    </>
  );
}
