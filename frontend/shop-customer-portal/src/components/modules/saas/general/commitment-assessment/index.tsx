"use client";

import { Divider } from "@/components/_common/divider";
import { FractionInput } from "@/components/_common/forms/fraction-input";
import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { getCommitment, submitCommitment } from "@/lib/api/commitment";
import { Commitment } from "@/lib/api/commitment/types";
import { useCustomer } from "@/hooks/use-customer";
import { setupFractionToCartFraction } from "@/utils/column-fraction-inverter";
import { setupColumnToCartColumn } from "@/utils/column-fraction-inverter";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add, Aluminium, Delete, East, RadioSelected, RadioUnselected } from "@arthursenno/lizenzero-ui-react/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { BiLoader } from "react-icons/bi";
import { CgSpinnerAlt } from "react-icons/cg";

import { z } from "zod";

interface CommitmentAssessmentProps {
  commitments?: Commitment;
  countryCode: string;
  onSave?: () => void;
  onSubmit?: () => void;
}

const commitmentAssessmentFormSchema = z.object({
  commitment: z.object({
    filled: z
      .boolean({
        required_error: `Click "confirm" to validate your answers.`,
        invalid_type_error: `Click "confirm" to validate your answers.`,
      })
      .refine((filled) => !!filled, { message: `Click "confirm" to validate your answers.` }),
    questions: z.record(
      z.string(),
      z.object({
        id: z.string(),
        title: z.string(),
        help_text: z.string().nullable(),
        input_type: z.string(),
        options: z.array(
          z.object({
            id: z.string(),
            option_value: z.string(),
            option_to_value: z.string().nullable(),
            value: z.string(),
          })
        ),
        answer: z.string(),
      })
    ),
  }),
  packagingServices: z.record(
    z.string(),
    z.object({
      fractions: z.record(z.string(), z.number().gte(0, { message: "The minimum weight is 0." }).optional()),
      added: z.boolean(),
    })
  ),
});

type commitmentAssessmentFormData = z.infer<typeof commitmentAssessmentFormSchema>;

export const CommitmentAssessment = ({ countryCode, onSave, commitments, onSubmit }: CommitmentAssessmentProps) => {
  const { customer } = useCustomer();
  const [showError, setShowError] = useState(false);

  const {
    setValue,
    formState: { errors, isSubmitting, isSubmitted },
    getValues,
    reset,
    control,
    watch,
    register,
  } = useForm<commitmentAssessmentFormData>({
    resolver: zodResolver(commitmentAssessmentFormSchema),
  });

  const commitmentErrors = !!errors && errors?.commitment;

  const commitment = watch(`commitment`);
  const criterias = Object.values(commitment?.questions || {});
  const packaging = watch(`packagingServices`);
  const packagingServices = Object.values(packaging || {});

  const allAnswered =
    !!Object.values(commitment?.questions || {}).length &&
    Object.values(commitment?.questions || {}).every((criteria) => !!criteria.answer);

  const errorMessage = (() => {
    if (!commitment?.filled) return "Please fill the form to continue your purchase.";

    const hasAtLeastOnePackageAdded = packagingServices?.some((p) => p.added) && commitment?.filled;

    if (!hasAtLeastOnePackageAdded) return "Please add at least one packaging service to continue your purchase.";

    if (!errors || Object.keys(errors).length === 0) return null;

    return "Please fill the form to continue your purchase.";
  })();

  async function handleSaveCommitment() {
    setValue(`commitment.filled`, true, {
      shouldValidate: true,
    });

    const normalizedCountryCode = countryCode.toUpperCase().trim();

    const commitmentAnswers = Object.values(commitment.questions || {}).map((q) => ({
      criteria_id: Number(q.id),
      answer: q.answer,
    }));

    const customerCommitmentResponse = await submitCommitment({
      country_code: normalizedCountryCode,
      year: 2025,
      commitment_answers: commitmentAnswers,
      customer_email: customer?.email,
    });

    if (!customerCommitmentResponse.success) return null;

    const customerCommitment = customerCommitmentResponse.data;
    const serviceSetup = customerCommitment.service_setup;

    const packagingServices = serviceSetup.packaging_services.reduce((result, packagingService) => {
      result[packagingService.id] = {
        id: String(packagingService.id),
        title: packagingService.name,
        added: packagingService.obliged,
        required: packagingService.obliged,
        price: 99,
        service: packagingService.name,
        reportSet: {
          id: packagingService.report_set.id,
          mode: packagingService.report_set.mode,
          name: packagingService.report_set.name,
          type: packagingService.report_set.type,
          fractions: packagingService.report_set.fractions.map(setupFractionToCartFraction),
          columns: packagingService.report_set.columns.map(setupColumnToCartColumn),
        },
        reportFrequency: packagingService.report_set_frequency,
        fractions: packagingService.report_set.fractions.reduce((result: any, fraction) => {
          result[`fraction_${fraction.id}`] = {
            key: String(fraction.id),
            name: fraction?.name,
            value: undefined,
          };
          return result;
        }, {}),
        fractionsCost: 0,
      };
      return result;
    }, {} as any);

    setValue(`packagingServices`, packagingServices);
    onSave?.();
  }

  function handleEditCommitment() {
    setValue(`commitment.filled`, false);
  }

  const getCountryCommitment = async () => {
    setShowError(false);

    const normalizedCountryCode = countryCode.toUpperCase().trim();
    const commitmentResponse = await getCommitment(normalizedCountryCode);

    if (!commitmentResponse.success) return;

    const commitmentCriterias = commitmentResponse.data;

    setValue(`commitment`, {
      filled: false,
      questions: commitmentCriterias.reduce(
        (acc, criteria) => {
          acc[String(`criteria_${criteria.id}`)] = {
            id: String(criteria.id),
            title: criteria.title,
            help_text: criteria.help_text,
            input_type: criteria.input_type,
            answer: commitments?.find((item) => item.id === criteria.id)?.answer || "",
            options: criteria.options.map((option) => ({
              id: String(option.id),
              value: option.value,
              option_value: option.option_value,
              option_to_value: option.option_to_value,
            })),
          };
          return acc;
        },
        {} as commitmentAssessmentFormData["commitment"]["questions"]
      ),
    });
  };

  const handleSubmit = async () => {
    // if (errorMessage) return setShowError(true);

    onSubmit?.();
  };

  useEffect(() => {
    getCountryCommitment();
  }, []);

  if (!criterias.length) {
    return (
      <div className="w-full flex justify-center items-center">
        <CgSpinnerAlt size={24} className="animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div>
      <div className="flex flex-col pb-6">
        <div className="space-y-8 md:space-y-10">
          {criterias.map((criteria) => (
            <div key={criteria.id} className="flex flex-col gap-4">
              <p className="text-base text-primary">{criteria.title}</p>
              {criteria.options.map((option) => (
                <label
                  key={`${criteria.id}_${option.value}`}
                  className="text-sm md:text-base text-tonal-dark-cream-20 flex gap-2 items-center cursor-pointer has-[input:checked]:cursor-default"
                >
                  <input
                    {...register(`commitment.questions.${`criteria_${criteria.id}`}.answer`)}
                    disabled={commitment.filled}
                    value={option.value}
                    type="radio"
                    className="hidden peer"
                    data-invalid={!!commitmentErrors && !!commitmentErrors.questions?.[criteria.id]}
                  />
                  <RadioSelected className="hidden peer-checked:block size-5 fill-primary" />
                  <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error" />
                  {criteria.input_type === "SELECT" && <span className="mt-1">{option.option_value}</span>}
                  {criteria.input_type === "YES_NO" && <>{option.option_value === "YES" ? "Yes" : "No"}</>}
                </label>
              ))}
              {!!commitmentErrors && !!commitmentErrors.questions?.[criteria.id] && (
                <p className="text-sm text-error">{commitmentErrors.questions?.[criteria.id]?.message}</p>
              )}
            </div>
          ))}
        </div>
      </div>
      {!commitment?.filled && (
        <Button
          type="button"
          variant="text"
          color="light-blue"
          size="medium"
          onClick={handleSaveCommitment}
          disabled={isSubmitting || !allAnswered}
        >
          Save answers
        </Button>
      )}
      {commitment?.filled && (
        <Button type="button" variant="text" color="light-blue" size="medium" onClick={handleEditCommitment}>
          Edit answers
        </Button>
      )}
      <Divider style={{ marginTop: 24 }} initialMarginDisabled />
      {commitment.filled && (
        <div className="flex flex-col gap-8 py-5 md:py-10">
          {!!packagingServices.length &&
            packagingServices.map((packagingService: any) => {
              const packagingErrors = errors?.packagingServices?.[packagingService.id];

              const fractionErrors = packagingErrors?.fractions;

              return (
                <div key={`${countryCode}_${packagingService.id}`} className="p-4 md:p-8 rounded-4xl bg-surface-03">
                  <div className="flex items-center gap-4">
                    <p className="text-xl font-bold text-tonal-dark-cream-30 mt-1">Result:</p>
                    <p
                      data-license={packagingService.required}
                      className="text-base font-bold mt-1 text-tonal-dark-green-30 data-[license=true]:text-on-surface-04"
                    >
                      {packagingService.required ? "Licensing required" : "Not obligated to license"}
                    </p>
                    {packagingService.added && (
                      <QuestionTooltip>
                        <QuestionTooltipDescription>
                          Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non. In volutpat
                          nisl nunc pellentesque.
                        </QuestionTooltipDescription>
                      </QuestionTooltip>
                    )}
                  </div>
                  {!packagingService.added && (
                    <p className="text-sm text-tonal-dark-cream-30 mt-4 mb-6">
                      Lorem ipsum dolor sit amet consectetur. Posuere eget gravida et tincidunt. Lorem ipsum dolor sit
                      amet consectetur. Posuere eget gravida et tincidunt.Lorem ipsum.
                    </p>
                  )}
                  <Divider style={{ margin: "20px 0" }} />
                  {packagingService.added ? (
                    <div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <p
                            data-invalid={!!packagingErrors}
                            className="text-2xl font-bold text-primary data-[invalid=true]:text-error"
                          >
                            {packagingService.title}
                          </p>
                          <QuestionTooltip>
                            <QuestionTooltipDescription>
                              Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non. In
                              volutpat nisl nunc pellentesque.
                            </QuestionTooltipDescription>
                          </QuestionTooltip>
                        </div>
                        <Button
                          type="button"
                          variant="text"
                          color="gray"
                          size="iconXSmall"
                          onClick={() => {
                            setValue(`packagingServices.${packagingService.id}.added`, false);
                          }}
                        >
                          <Delete className="size-5 fill-tonal-dark-cream-40" />
                        </Button>
                      </div>
                      <div>
                        <p
                          data-invalid={!!packagingErrors && isSubmitted}
                          className="text-sm text-tonal-dark-cream-30 data-[invalid=true]:text-error my-5"
                        >
                          Please provide the estimate quantity so we are able to estimate the future costs
                        </p>
                        <div className="rounded-[20px] overflow-hidden space-y-[1px] bg-tonal-dark-cream-80">
                          {Object.values(packagingService?.fractions || {}).map((fraction: any) => (
                            <div className="bg-white" key={`${countryCode}-fractionContainer-${fraction?.name}`}>
                              <div className="py-[14px] px-5 flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                                <div className="flex items-center gap-4">
                                  <QuestionTooltip>
                                    <QuestionTooltipTitle>
                                      <Aluminium width={24} className="fill-primary" />
                                      <p className="text-primary text-md font-bold">{fraction?.name}</p>
                                    </QuestionTooltipTitle>
                                    <QuestionTooltipDescription className="text-primary">
                                      Bottle tops, film for chocolate and tubes for skincare products: packaging made
                                      from aluminium is mostly used to package food, cosmetics or pharmaceutical
                                      products. Enter the estimated total weight of packaging that you will place on the
                                      German market in the respective licence year.
                                    </QuestionTooltipDescription>
                                  </QuestionTooltip>

                                  <div className="flex flex-1 items-center gap-3">
                                    <Aluminium className="size-6 md:size-9 fill-primary" />
                                    <p className="text-sm md:text-base font-bold text-primary">{fraction?.name}</p>
                                  </div>
                                </div>
                                <div className="flex items-center gap-4 w-full md:w-48 flex-shrink-0">
                                  <Controller
                                    key={`${countryCode}-fraction-${fraction?.key}`}
                                    name={`packagingServices.${packagingService.id}.fractions.fraction_${fraction?.key}.value`}
                                    control={control}
                                    render={({ field }) => (
                                      <FractionInput
                                        type="weight"
                                        {...field}
                                        data-invalid={!!fractionErrors && !!fractionErrors[`fraction_${fraction?.key}`]}
                                        onChange={(value) => {
                                          field.onChange(value);
                                        }}
                                      />
                                    )}
                                  />
                                  <span className="text-base text-primary">kg</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center justify-between">
                        <p className="text-2xl font-bold text-primary">{packagingService.title}</p>
                        <div className="flex items-center gap-10">
                          <Button
                            type="button"
                            variant="filled"
                            color="yellow"
                            size="iconXSmall"
                            onClick={() => {
                              setValue(`packagingServices.${packagingService.id}.added`, true);
                            }}
                          >
                            <Add className="size-5 fill-primary" />
                          </Button>
                        </div>
                      </div>
                      <p className="mt-2 text-sm text-tonal-dark-cream-30">
                        Lorem ipsum dolor sit amet consectetur. Posuere eget gravida et tincidunt. Lorem ipsum dolor sit
                        amet consectetur. Posuere eget gravida et tincidunt.Lorem ipsum.
                      </p>
                    </div>
                  )}
                </div>
              );
            })}
          {!packagingServices.length ? (
            <div className="flex justify-center items-center gap-1 md:gap-2 px-2 py-3 border-[1px] border-tonal-dark-cream-80 rounded-md">
              <BiLoader className="fill-primary animate-spin" />
              <p className="text-center text-primary"> Loading packaging services...</p>
            </div>
          ) : (
            <div className="flex flex-col justify-end items-end gap-6">
              {!!errorMessage && showError && <p className="text-right text-error">{errorMessage}</p>}
              <Button
                type="button"
                variant="filled"
                color="yellow"
                size="medium"
                trailingIcon={<East />}
                onClick={handleSubmit}
              >
                Analyse
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
