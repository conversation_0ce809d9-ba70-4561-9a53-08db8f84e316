"use client";

import { SelectDropdown } from "@/components/_common/select-dropdown";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { Skeleton } from "@/components/ui/skeleton";
import { StatusBadge } from "@/components/ui/status-badge";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { ContractType } from "@/lib/api/contracts/types";
import { getOrders } from "@/lib/api/orders";
import { Order, OrderItemSpecificationType } from "@/lib/api/orders/types";
import { useCustomer } from "@/hooks/use-customer";
import { downloadFile } from "@/utils/download-file";
import { formatCurrency } from "@/utils/formatCurrency";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import {
  Download,
  KeyboardArrowDown,
  Launch,
  RequestQuote,
  SdCardAlert,
  Task,
} from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";

import { InvoicePaymentModal } from "./invoice-payment-modal";
import { downloadCustomerFileByRelation } from "@/lib/api/file";

const ORDER_ITEM_SERVICE_TYPE_LABEL: Record<ContractType, string> = {
  EU_LICENSE: "Licensing Service",
  DIRECT_LICENSE: "Direct License",
  ACTION_GUIDE: "Action guide",
};

const ORDER_ITEM_SPECIFICATION_TYPE_LABEL: Record<OrderItemSpecificationType, string> = {
  HANDLING_FEE: "Handling fee",
  REGISTRATION_FEE: "Registration fee",
  VARIABLE_HANDLING_FEE: "Variable handling fee",
  DIRECT_LICENSE_PURCHASE: "Purchase",
  DIRECT_LICENSE_REFUND: "Refund credits",
  DIRECT_LICENSE_VOLUME_CHANGE: "Volume change",
};

function formatQueryDate(date: string) {
  return new Date(date).toISOString().split("T")[0];
}

export function InvoiceList() {
  const currentYear = new Date().getFullYear();

  const { customer } = useCustomer();

  const [selectedOrderForPayment, setSelectedOrderForPayment] = useState<Order | undefined>();
  const [idOrderLoading, setIdOrderLoading] = useState<number | null>(null);

  const licenseYears = (() => {
    if (!customer) return [];

    const contracts = customer.contracts;

    const years: number[] = [];

    for (const contract of contracts) {
      if (!!contract.licenses.length) {
        for (const license of contract.licenses) {
          if (!years.includes(license.year)) years.push(license.year);
        }
      }

      if (contract.type === "ACTION_GUIDE") {
        if (!years.includes(currentYear)) years.push(currentYear);
      }
    }

    return years.sort((a, b) => b - a);
  })();

  const { paramValues, changeParam } = useQueryFilter(["license-year", "start-date", "end-date"]);

  const licenseYear = paramValues["license-year"] || licenseYears[0];
  const startDate =
    paramValues["start-date"] ||
    formatQueryDate(new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString());
  const endDate =
    paramValues["end-date"] || formatQueryDate(new Date(new Date().setDate(new Date().getDate() + 1)).toISOString());

  const ordersQuery = useQuery({
    queryKey: ["invoices", { startDate, endDate, year: licenseYear }],
    queryFn: async () => {
      return await getOrders({
        customer_id: customer!.id,
        start_date: startDate,
        end_date: endDate,
        license_year: Number(licenseYear),
      });
    },
    enabled: !!customer,
  });

  const downloadOrderInvoiceMutation = useMutation({
    mutationFn: async (invoiceId: string) => {
      const downloadedFile = await downloadCustomerFileByRelation("order_id", invoiceId);

      downloadFile({ buffer: downloadedFile, fileName: `invoice-${invoiceId}.pdf` });
    },
  });

  function handleDownloadOrderInvoice(orderId: string) {
    setIdOrderLoading(Number(orderId));

    downloadOrderInvoiceMutation.mutate(orderId, {
      onSuccess: () => {
        setIdOrderLoading(null);
        enqueueSnackbar("Invoice downloaded successfully!", { variant: "success" });
      },
      onError: () => {
        setIdOrderLoading(null);
        enqueueSnackbar("An error occurred while downloading the invoice.", { variant: "error" });
      },
    });
  }

  const payInvoiceMutation = useMutation({
    //TODO: Implement payInvoice mutation
    mutationFn: async (invoiceId: string) => {
      const downloadedFile = await downloadCustomerFileByRelation("order_id", invoiceId);

      downloadFile({ buffer: downloadedFile, fileName: `invoice-${invoiceId}.pdf` });
    },
  });

  function handlePayInvoice(orderId: string) {
    // TODO: Implement payInvoice mutation
    payInvoiceMutation.mutate(orderId, {
      onSuccess: () => {
        enqueueSnackbar("Invoice downloaded successfully!", { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar("An error occurred while downloading the invoice.", { variant: "error" });
      },
    });
  }

  function handlePaymentModalOpen(order: Order) {
    setSelectedOrderForPayment(order);
  }

  function handlePaymentModalClose() {
    setSelectedOrderForPayment(undefined);
  }

  const orders = ordersQuery.data?.orders || [];

  return (
    <div className="bg-surface-02 rounded-4xl md:rounded-[40px] px-4 py-6 md:px-8 md:py-10 space-y-4">
      <div className="space-y-5">
        <div className="flex flex-row gap-4 items-center">
          <h2 className="text-primary font-bold text-2xl">Lizenzero's Service Fee</h2>
          <TooltipIcon info="Invoices that are paid direct to Lizenzero" />
        </div>
        <div className="flex flex-col md:flex-row md:items-center gap-4">
          <div className="flex flex-col md:flex-row gap-2">
            <p className="text-tonal-dark-cream-50 text-nowrap">Invoice period:</p>
            <div className="flex items-center gap-2">
              <div className="flex">
                <input
                  id="start-date-input"
                  onBlur={(e) => changeParam("start-date", e.target.value)}
                  onChange={(e) => changeParam("start-date", e.target.value)}
                  className="bg-surface-01 text-support-blue font-medium "
                  type="date"
                  max={endDate}
                  defaultValue={startDate}
                />
              </div>
              <span className="text-tonal-dark-cream-50">to</span>
              <div className="flex">
                <input
                  id="end-date-input"
                  onBlur={(e) => changeParam("end-date", e.target.value)}
                  onChange={(e) => changeParam("end-date", e.target.value)}
                  className="bg-surface-01 text-support-blue font-medium "
                  type="date"
                  min={startDate}
                  defaultValue={endDate}
                />
              </div>
            </div>
          </div>
          <span className="hidden md:block text-[#808FA9]">|</span>
          <div className="flex flex-col items-start md:items-center md:flex-row gap-2">
            <span className="text-tonal-dark-cream-50 text-nowrap">License year:</span>
            <SelectDropdown
              options={licenseYears.map((year) => ({
                label: String(year),
                value: String(year),
              }))}
              value={String(licenseYear)}
              onChangeValue={(year) => changeParam("license-year", year)}
            />
          </div>
        </div>
      </div>
      <div className="bg-white rounded-4xl px-3 py-4 p-6">
        {!ordersQuery.isLoading && !orders.length && (
          <p className="text-tonal-dark-cream-50 text-center py-6">No invoices found</p>
        )}
        {!ordersQuery.isLoading &&
          !!orders.length &&
          orders.map((order) => (
            <details key={order.id} className="[&>summary>.arrow-down]:open:rotate-180">
              <summary className="flex items-center gap-2 max-sm:gap-y-5 md:gap-4 px-0 pt-4 md:p-5 flex-wrap">
                <KeyboardArrowDown className="arrow-down hidden md:block size-6 fill-support-blue transition-all duration-300" />
                {order.status === "PAID" && <Task className="size-7 fill-success" />}
                {order.status === "OPEN" && <RequestQuote className="size-7 fill-error" />}
                {order.status === "CANCELLED" && <SdCardAlert className="size-7 fill-tonal-dark-cream-60" />}
                <div className="space-y-1">
                  <p className="text-primary text-sm md:text-base">Invoice Number {order.id}</p>
                  <p className="text-xs font-semibold text-tonal-dark-cream-10">
                    Date of issue
                    <span className="text-tonal-dark-cream-40 ml-1 font-normal">
                      {new Date(order.created_at).toLocaleDateString()}
                    </span>
                  </p>
                </div>
                <div className="px-2md:px-4 py-3 text-tonal-dark-cream-30 w-20 md:w-32 max-sm:ml-auto md:mx-auto text-right">
                  {formatCurrency(order.amount)}
                </div>
                <div className="flex sm:ml-auto max-sm:flex-1 items-center justify-between gap-4">
                  <div>
                    {order.status === "OPEN" && (
                      <Button
                        variant="text"
                        color="dark-blue"
                        size="medium"
                        leadingIcon={<Launch />}
                        onClick={() => handlePaymentModalOpen(order)}
                        className="text-nowrap"
                      >
                        Pay invoice
                      </Button>
                    )}
                    {order.status === "PAID" && <StatusBadge variant="success" label="Paid" size="lg" />}
                    {order.status === "CANCELLED" && <StatusBadge variant="neutral" label="Cancelled" size="lg" />}
                    {order.status === "PARTIALLY_PAID" && (
                      <StatusBadge variant="info" label="Partially paid" size="lg" />
                    )}
                  </div>
                  <div>
                    <Button
                      variant="text"
                      color="light-blue"
                      size="iconSmall"
                      disabled={downloadOrderInvoiceMutation.isPending}
                      leadingIcon={
                        downloadOrderInvoiceMutation.isPending && idOrderLoading === order.id ? (
                          <CgSpinnerAlt className="animate-spin" />
                        ) : (
                          <Download />
                        )
                      }
                      onClick={() => handleDownloadOrderInvoice(String(order.id))}
                    />
                  </div>
                </div>
              </summary>
              <div className="animate-slideDownAndFade transition-all duration-30 w-full md:w-[90%] mx-auto">
                <table className="min-w-full">
                  <thead className="border-b-2 border-tonal-dark-cream-80">
                    <tr>
                      <th className="py-2 text-[#808FA9] text-left text-xs font-medium">Product</th>
                      <th className="py-2 text-[#808FA9] text-left text-xs font-medium">Specification</th>
                      <th className="py-2 text-[#808FA9] text-left text-xs font-medium">Licensing Year</th>
                      <th className="py-2 text-[#808FA9] text-left text-xs font-medium">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {order.items.map((orderItem, index) => (
                      <tr key={index}>
                        <td className="py-2 flex flex-col text-sm">
                          <p className="text-[#808FA9] text-small-paragraph-regular font-medium">
                            {ORDER_ITEM_SERVICE_TYPE_LABEL[orderItem.service_type] || "---"}{" "}
                            {orderItem.service_type !== "ACTION_GUIDE" && `(${orderItem.license_year})`}
                          </p>
                          <p className="text-[#808FA9] text-small-paragraph-regular">{orderItem.country_name}</p>
                        </td>
                        <td className="py-2 text-[#808FA9] text-small-paragraph-regular">
                          <div className="space-y-1 font-medium">
                            {ORDER_ITEM_SPECIFICATION_TYPE_LABEL[orderItem.specification_type] || "---"}
                          </div>
                          <div>
                            {orderItem.description?.split("\n").map((line) => (
                              <p key={line} className="text-[#808FA9] text-xs">
                                {line}
                              </p>
                            ))}
                          </div>
                        </td>
                        <td className="py-2 text-[#808FA9] text-small-paragraph-regular">{orderItem.license_year}</td>
                        <td className="py-2 text-[#808FA9] text-small-paragraph-regular">
                          {formatCurrency(orderItem.price)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </details>
          ))}
        {ordersQuery.isLoading && (
          <div className="space-y-3">
            {Array.from({ length: orders.length || 6 }).map((_, index) => (
              <Skeleton key={index} className="h-12 rounded-2xl w-full" />
            ))}
          </div>
        )}
      </div>
      <InvoicePaymentModal
        order={selectedOrderForPayment}
        onClose={handlePaymentModalClose}
        handleDownloadOrderInvoice={handleDownloadOrderInvoice}
        isDownloadPending={downloadOrderInvoiceMutation.isPending}
      />
    </div>
  );
}
