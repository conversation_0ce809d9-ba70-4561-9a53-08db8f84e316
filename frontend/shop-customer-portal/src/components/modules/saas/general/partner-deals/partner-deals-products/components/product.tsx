import { Video } from "@/components/ui/video";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import Image from "next/image";
import { IProduct } from "./interfaces";
import { ProductCuponsInfo } from "./product-cupons-info";
import { ProductTitle } from "./product-title";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { cn } from "@/lib/utils";

const ProductRowInfo = ({ file, cupon, title, subTitle, position }: IProduct) => {
  return (
    <div className="flex rounded-[40px] overflow-hidden flex-col relative md:flex-row bg-white">
      <div className={cn("w-full md:w-1/3 h-96 md:h-auto relative", position === "right" && "order-1")}>
        {file.type === "VIDEO" ? (
          <Video className={"w-full h-full md:max-w-128"} url={file.url} />
        ) : (
          <Image
            className={"absolute w-full h-full md:max-w-128 rounded-4xl overflow-hidden object-cover"}
            alt="Product image"
            src={file.url}
            width={500}
            height={700}
          />
        )}
      </div>
      <div
        className={cn(
          "px-6 md:px-12 py-6 flex flex-1 flex-col gap-8 md:items-start",
          file.type === "IMAGE" ? "md:py-[98.5px]" : "md:py-[60px]"
        )}
      >
        <div className="md:pr-8">
          <ProductTitle title={title} subTitle={subTitle} />
        </div>
        <div className="flex flex-col gap-6 w-full md:items-start">
          {cupon && <ProductCuponsInfo {...cupon} isRow />}
          <Button
            trailingIcon={<East />}
            color="yellow"
            variant="filled"
            size="medium"
            className="w-full md:w-auto flex-none"
          >
            Go to offer
          </Button>
        </div>
      </div>
    </div>
  );
};

const ProductDefault = ({ file, cupon, title, subTitle, position }: IProduct) => {
  return (
    <div className="relative rounded-[40px] overflow-hidden">
      <div className="h-24 md:h-56">
        <div className="absolute w-full top-0">
          {file.type === "VIDEO" ? (
            <Video className={cn("w-full md:w-2/4 h-48 md:h-80", position === "right" && "ml-auto")} url={file.url} />
          ) : (
            <Image
              className={cn(
                "w-full md:w-2/4 h-48 md:h-80 rounded-4xl overflow-hidden object-cover",
                position === "right" && "ml-auto"
              )}
              alt="Product image"
              src={file.url}
              width={500}
              height={700}
            />
          )}
        </div>
      </div>
      <div className="pt-32 px-5 md:px-20 pb-6 md:pb-20 bg-white flex-col flex md:flex-row gap-8">
        <ProductTitle title={title} subTitle={subTitle} />
        <div className="flex flex-1 flex-col gap-6 md:items-start">
          {cupon && <ProductCuponsInfo {...cupon} />}
          <Button
            trailingIcon={<East />}
            color="yellow"
            variant="filled"
            size="medium"
            className="w-full md:w-auto flex-none"
          >
            Go to offer
          </Button>
        </div>
      </div>
    </div>
  );
};

export function Product({ type, ...restProps }: IProduct) {
  if (type) return <ProductRowInfo {...restProps} />;

  return <ProductDefault {...restProps} />;
}
