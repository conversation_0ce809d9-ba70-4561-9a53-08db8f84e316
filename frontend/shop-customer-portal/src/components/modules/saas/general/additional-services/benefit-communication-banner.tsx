import { Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";

export function BenefitCommunicationBanner() {
  return (
    <div className="w-full md:w-auto flex flex-col p-8 bg-[#EBF4FF] text-on-secondary gap-10 rounded-4xl">
      <div className="flex flex-row gap-6">
        <div className="flex justify-center items-center size-16 rounded-full bg-primary flex-none">
          <Lightbulb className="size-8 fill-secondary" />
        </div>
        <div className="flex flex-col">
          <span className="text-xl font-bold">Benefit communication.</span>
          <p>Lorem ipsum</p>
        </div>
      </div>
      <p className="text-base/none text-primary">The benefit communication should be provide by lizenzero.</p>
    </div>
  );
}
