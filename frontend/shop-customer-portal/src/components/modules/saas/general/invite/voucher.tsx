import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import { enqueueSnackbar } from "notistack";

export default function InviteVoucher({
  handleClick,
  price,
  guestUsers,
}: {
  handleClick: () => void;
  price: number;
  guestUsers: number;
}) {
  const handleRedeem = () => {
    if (!price) return enqueueSnackbar(`No voucher to purchase`, { variant: "error" });

    handleClick();
  };

  return (
    <div className="flex flex-col gap-10 col-span-4 bg-[#FFF3E1] p-10 rounded-[40px]">
      <h3 className="text-on-tertiary text-xl">Get a voucher</h3>

      <div className="flex flex-col gap-2">
        <div className="flex flex-row items-center text-primary font-bold gap-2">
          <span className="text-[32px]">€</span>
          <span className="text-5xl">{(price / 100).toFixed(2)}</span>
        </div>
        <span className="text-primary">
          You invited
          <span className="font-bold"> {guestUsers} people!</span>
        </span>
      </div>

      <div className="grid grid-cols-5 text-on text-[#D7D6D5] text-center items-center">
        <hr className="col-span-2" />
        <span className="col-span-1 text-xl text-on-tertiary">or</span>
        <hr className="col-span-2" />
      </div>

      <div className="flex flex-col gap-2">
        <h3 className="text-on-tertiary text-xl">You can help plant trees!</h3>

        <div className="flex flex-row gap-4">
          <Plant className="fill-white bg-[#1B6C64] rounded-full size-16 p-2" />
          <div className="flex flex-col justify-end">
            <span className="text-[#1B6C64] text-[32px] font-bold pb-0">50 m2</span>
            <span className="text-[#8C8A87] text-xs">metres of renaturalised moorland </span>
          </div>
        </div>
        <span className="text-on-tertiary">
          Equivalent of
          <span className="text-[#1B6C64] font-bold"> 560 trees!</span>
        </span>
      </div>

      <hr className="text-[#D7D6D5]" />

      <div>
        <Button
          variant="filled"
          size="medium"
          color="yellow"
          onClick={handleRedeem}
          trailingIcon={<East />}
          disabled={!price}
          className="w-full md:w-auto"
        >
          redeem
        </Button>
      </div>
    </div>
  );
}
