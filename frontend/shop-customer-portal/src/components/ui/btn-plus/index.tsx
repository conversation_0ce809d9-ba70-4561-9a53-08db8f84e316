import { Add } from "@arthursenno/lizenzero-ui-react/Icon";
import { ButtonHTMLAttributes } from "react";

export default function BtnPlus(props: ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      className="size-9 rounded-full bg-tertiary flex justify-center items-center disabled:bg-tonal-dark-cream-70/35"
      {...props}
    >
      <Add className={`${props.disabled ? "fill-on-surface-01" : "fill-primary"} size-5`} />
    </button>
  );
}
