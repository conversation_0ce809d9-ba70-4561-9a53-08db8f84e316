import { getColorCode } from "@/utils/get-color-code";

type GraphColorKey = number | string;

export class GraphColors {
  private readonly GRAPH_COLORS = [
    getColorCode("red"),
    getColorCode("blue"),
    getColorCode("green"),
    getColorCode("light-green"),
    getColorCode("orange"),
    getColorCode("yellow"),
  ];
  private readonly BOLD_GRAPH_COLORS = ["#FFCE00", "#1B6C64", "#009DD3", "#A9C8FF", "#002652", "#E64330", "#F1988D"];

  private colorMap: Map<GraphColorKey, string>;

  constructor(keys: GraphColorKey[], boldColors: boolean = false) {
    this.colorMap = new Map(
      keys.map((key, index) => {
        if (boldColors) {
          const colorIndex = index % this.BOLD_GRAPH_COLORS.length;
          return [key, this.BOLD_GRAPH_COLORS[colorIndex]];
        }
        const colorIndex = index % this.GRAPH_COLORS.length;
        return [key, this.GRAPH_COLORS[colorIndex]?.text];
      })
    );
  }

  getColor(key: GraphColorKey): string {
    return this.colorMap.get(key) || getColorCode("default").text;
  }
}
