import React, { createContext, useContext, ReactNode } from "react";
import { useQuery } from "@tanstack/react-query";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { getCountryByCode } from "@/lib/api/countries";
import { Country } from "@/types/country";
import { CriteriaType } from "@/types/service-setup/criteria";
import { useRouter } from "next/navigation";

export const SERVICE_SETUP_STEPS = [
  {
    label: "1. Service details",
    value: "packaging-services",
    criteriaLabel: "Add Obligation Criteria",
  },
  {
    label: "2. Obligation check",
    value: "obligation-check",
    criteriaLabel: "Add Obligation Criteria",
  },
  {
    label: "3. Fraction sets",
    value: "report-sets",
    criteriaLabel: "Add Set Criteria",
  },
  {
    label: "4. Reporting frequency",
    value: "report-frequencies",
    criteriaLabel: "Add Report Rhythm Criteria",
  },
  {
    label: "5. Third party costs",
    value: "representative-tier-and-other-costs",
    criteriaLabel: "Add Representative Tier Criteria",
  },
  {
    label: "6. Required information",
    value: "required-informations",
    criteriaLabel: "Add Document Criteria",
  },
] as const;

export type ServiceSetupStep = (typeof SERVICE_SETUP_STEPS)[number];

export type ServiceSetupStepValue = ServiceSetupStep["value"];

interface OpenCriteriasDrawerParams {
  type: CriteriaType;
  packagingServiceId?: number;
  requiredInformationId?: number;
}

interface ServiceSetupContextType {
  country: Country;
  isLoading: boolean;
  currentStep: ServiceSetupStep | null;
  openCriteriasDrawer: (params: OpenCriteriasDrawerParams) => void;
  closeCriteriasDrawer: () => void;
}

const ServiceSetupContext = createContext<ServiceSetupContextType | undefined>(undefined);

interface ServiceSetupProviderProps {
  countryCode: string;
  children: ReactNode;
}

export function ServiceSetupProvider({ countryCode, children }: ServiceSetupProviderProps) {
  const router = useRouter();
  const { data: country, isFetching: isLoading } = useQuery({
    queryKey: ["country", countryCode],
    queryFn: () => getCountryByCode(countryCode),
  });

  const { paramValues, changeParams } = useQueryFilter([
    "criterias",
    "step",
    "type",
    "packaging_service_id",
    "required_information_id",
  ]);

  const isCriteriasDrawerOpen = paramValues.criterias === "true";
  const currentStep = SERVICE_SETUP_STEPS.find((step) => step.value === paramValues.step) || null;

  if (!country) return null;

  function openCriteriasDrawer(params: OpenCriteriasDrawerParams) {
    changeParams(
      {
        criterias: !isCriteriasDrawerOpen ? "true" : undefined,
        type: params.type,
        packaging_service_id: params.packagingServiceId?.toString(),
        required_information_id: params.requiredInformationId?.toString(),
      },
      { scroll: true }
    );
  }

  function closeCriteriasDrawer() {
    // changeParams({
    //   criterias: undefined,
    //   type: undefined,
    //   packaging_service_id: undefined,
    //   required_information_id: undefined,
    // }, { scroll: false });
    router.back();
  }

  const value = {
    country,
    isLoading,
    currentStep,
    openCriteriasDrawer,
    closeCriteriasDrawer,
  };

  return <ServiceSetupContext.Provider value={value}>{children}</ServiceSetupContext.Provider>;
}

export function useServiceSetup() {
  const context = useContext(ServiceSetupContext);

  if (context === undefined) {
    throw new Error("useServiceSetup must be used within a ServiceSetupProvider");
  }
  return context;
}

export function withServiceSetupProvider<T extends { countryCode: string }>(WrappedComponent: React.ComponentType<T>) {
  return function WithServiceSetupProvider(props: T) {
    return (
      <ServiceSetupProvider countryCode={props.countryCode}>
        <WrappedComponent {...props} />
      </ServiceSetupProvider>
    );
  };
}
