import * as DropdownMenu from "@radix-ui/react-dropdown-menu";

export const DropdownItem = DropdownMenu.Item;

interface IDropdownProps {
  children?: React.ReactNode;
  trigger?: React.ReactNode;
}

export const Dropdown = ({ children, trigger }: IDropdownProps) => {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger className="focus:outline-none focus-visible:ring-2" asChild>
        {trigger}
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="min-w-[220px] bg-background py-3 rounded-2xl focus:outline-none focus-visible:ring-2 z-[1000] overflow-y-auto max-h-72"
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
        >
          {children}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
};
