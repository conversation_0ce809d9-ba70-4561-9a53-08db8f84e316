/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import Link from "next/link";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { recoverPassword } from "@/lib/api/auth";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { CheckCircle } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { SuccessRecoverPassword } from "./success-recover-password";
import { CONTAINS_LETTER_REGEX, CONTAINS_NON_ALPHANUMERIC_REGEX, CONTAINS_NUMBER_REGEX } from "@/utils/regex";
import { PasswordVisibilityToggle } from "../common/PasswordVisibilityToggle";
import { cn } from "@/utils/cn";
import { PasswordStrengthIndicator } from "../common/PasswordStrengthIndicator";

const recoverPasswordFormSchema = z.object({
  password: z
    .string({
      required_error: "translations.password.error",
    })
    .min(8, { message: "Enter at least 8 characters" })
    .regex(CONTAINS_LETTER_REGEX, "Enter a letter")
    .regex(CONTAINS_NUMBER_REGEX, "Enter a number")
    .regex(CONTAINS_NON_ALPHANUMERIC_REGEX, "Enter a special character"),
});

type RecoverPasswordFormData = z.infer<typeof recoverPasswordFormSchema>;

interface RecoverPasswordFormProps {
  email: string;
  token: string;
}

export function RecoverPasswordForm({ email, token }: RecoverPasswordFormProps) {
  const [seePassword, setSeePassword] = useState(false);
  const [recover, setRecover] = useState(false);
  const [newPassword, setNewPassword] = useState("");

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, isValid, isSubmitting },
  } = useForm<RecoverPasswordFormData>({
    resolver: zodResolver(recoverPasswordFormSchema),
    mode: "all",
  });

  async function submit(data: RecoverPasswordFormData) {
    try {
      const res = await recoverPassword(token, data.password, "PASSWORD_RESET");

      if (res.error || res.response?.status > 201) {
        setError("password", {
          message: "Please enter a valid password",
        });

        return;
      }

      setRecover(true);
    } catch (error: unknown) {}
  }
  if (recover) return <SuccessRecoverPassword email={email} />;

  return (
    <div className="size-full flex flex-col justify-center">
      <div className="text-left text-primary mb-10">
        <h1 className=" text-4xl mb-4 font-medium">Password recovery</h1>
        <span className=" text-sm">
          E-mail: <strong className="font-medium">{email}</strong>
        </span>
      </div>
      <form onSubmit={handleSubmit(submit)}>
        <div className="flex mb-10 ">
          <div className="w-full  mb-5">
            <div className="w-full flex justify-between">
              <p className="text-primary">Password</p>
            </div>
            <div className={cn("flex mb-3", isValid ? "[&_input]:pr-20" : null)}>
              <Input
                placeholder="New Password"
                {...register("password", {
                  onChange: (e) => {
                    const value = e.target.value;
                    setNewPassword(value);
                    const validation = recoverPasswordFormSchema.safeParse({ password: value });
                    if (validation.success) {
                      errors.password = undefined;
                    }
                  },
                })}
                type={seePassword ? "text" : "password"}
                variant={errors.password ? "error" : "enabled"}
                rightIcon={
                  <div className="flex flex-row items-center gap-4">
                    {isValid && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />}
                    <PasswordVisibilityToggle
                      errors={!!errors.password}
                      visibility={seePassword}
                      setVisibility={setSeePassword}
                    />
                  </div>
                }
              />
            </div>
            <div className=" text-tonal-dark-cream-30 font-light text-sm">
              {(newPassword || errors.password?.message) && (
                <PasswordStrengthIndicator password={newPassword} errMsg={errors.password?.message} />
              )}
            </div>
          </div>
        </div>

        <Button color="yellow" size="medium" variant="filled" disabled={!isValid || isSubmitting} className="w-full">
          {isSubmitting ? "Loading..." : "Save"}
        </Button>
      </form>
      <div className="mt-4">
        <div className="flex justify-center">
          <Link href="/[locale]/auth/login" as={`/en/auth/login`}>
            <span className="text-sm cursor-pointer font-medium text-tonal-blue-40">Back to login</span>
          </Link>
        </div>
      </div>
    </div>
  );
}
