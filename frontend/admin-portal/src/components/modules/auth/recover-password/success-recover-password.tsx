import { Button } from "@interzero/oneepr-react-ui/Button";
import { useRouter } from "next/navigation";

export function SuccessRecoverPassword({ email }: { email: string }) {
  const router = useRouter();
  const buttonClicked = () => {
    router.push("/auth/login");
  };
  return (
    <div className="text-left text-primary mb-10 flex flex-col size-full justify-center">
      <h1 className=" text-4xl font-medium">Password changed successfully!</h1>
      <p className="font-light text-sm">
        Email: <strong>{email}</strong>
      </p>
      <div className="flex justify-center mt-10">
        <Button size="medium" variant="filled" color="dark-blue" onClick={buttonClicked} className="w-full">
          Back to login
        </Button>
      </div>
    </div>
  );
}
