"use client";

import { useEffect, useState } from "react";
import { Controller, FormProvider, useFieldArray, useForm, useFormContext } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useMutation, useQuery } from "@tanstack/react-query";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { z } from "zod";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Help, KeyboardArrowRight, Delete } from "@interzero/oneepr-react-ui/Icon";
import { queryClient } from "@/lib/react-query";
import { getMarketingMaterial, updateMarketingMaterial } from "@/lib/api/marketing-materials";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { UploadMarketingMaterials } from "../upload-marketing-materials";
import { AutoComplete } from "@/components/common/autocomplete";
import { getPartners } from "@/lib/api/partner";

const updateMarketingMaterialFormSchema = z
  .object({
    name: z.string().trim().min(1, { message: "Name is required" }),
    start_date: z
      .string()
      .optional() // Torna o campo opcional
      .refine((value) => !value || !isNaN(new Date(value).getTime()), {
        message: "Invalid date format",
      })
      .refine((value) => !value || new Date(value) > new Date(), {
        message: "Start date must be in the future",
      }),
    end_date: z
      .string()
      .optional() // Torna o campo opcional
      .refine((value) => !value || !isNaN(new Date(value).getTime()), {
        message: "Invalid date format",
      }),
    category: z.enum(["STANDARD", "SPECIFIC_MATERIAL"]).default("STANDARD"),
    partner_restriction: z.enum(["ALL", "CLUSTER", "SPECIFIC"]).default("ALL"),
    partners: z
      .array(z.object({ name: z.string().trim().min(1, "Name is required"), id: z.number() }))
      .optional()
      .default([]),
  })
  .refine(
    (data) => {
      if (data.start_date && data.end_date) {
        return new Date(data.end_date) > new Date(data.start_date);
      }
      return true;
    },
    {
      message: "End date cannot be earlier than start date.",
      path: ["end_date"],
    }
  )
  .refine(
    (data) => {
      if (data.partner_restriction === "SPECIFIC") {
        return data.partners.length > 0;
      }
      return true;
    },
    {
      message: "Add at least one partner",
      path: ["partner_restriction"],
    }
  );

type UpdateMarketingMaterialFormData = z.infer<typeof updateMarketingMaterialFormSchema>;

interface CreateMarketingMaterialFormProps {
  title: string;
  marketingMaterialId: string | number;
}

export function UpdateMarketingMaterialForm({ title, marketingMaterialId }: CreateMarketingMaterialFormProps) {
  const router = useRouter();
  const [documents, setDocuments] = useState<Array<File & { id?: string }>>([]);

  const form = useForm<UpdateMarketingMaterialFormData>({
    resolver: zodResolver(updateMarketingMaterialFormSchema),
  });

  function dateToYYYYMMDD(date: string) {
    return new Date(date).toISOString().split("T")[0];
  }

  const { isLoading: isMarketingMaterialLoading } = useQuery({
    queryKey: ["marketing-material", marketingMaterialId],
    queryFn: async () => {
      const response = await getMarketingMaterial(marketingMaterialId);

      form.reset({
        name: response?.name,
        start_date: response?.start_date ? dateToYYYYMMDD(response.start_date) : undefined,
        end_date: response?.end_date ? dateToYYYYMMDD(response.end_date) : undefined,
        category: response?.category,
        partner_restriction: response?.partner_restriction,
        ...(Array.isArray(response?.partners) && {
          partners: response.partners.map((partner) => ({
            id: partner.partner_id,
            name: `${partner.partner.first_name} ${partner.partner.last_name}`,
          })),
        }),
      });

      if (response?.files) {
        setDocuments(
          response.files.map((file) => {
            const fileObject = new File([], file.name);
            return Object.assign(fileObject, { id: file.id });
          })
        );
      }

      return response;
    },
    enabled: !!marketingMaterialId,
  });

  const { fields, append, remove } = useFieldArray({ control: form.control, name: "partners" });

  const { mutate, isPending: isUpdatingMarketingMaterial } = useMutation({
    mutationFn: (data: UpdateMarketingMaterialFormData) => {
      return updateMarketingMaterial(marketingMaterialId, {
        ...data,
        partners: data.partners.map((partner) => partner.id),
        start_date: data.start_date ? new Date(data.start_date).toISOString() : undefined,
        end_date: data.end_date ? new Date(data.end_date).toISOString() : undefined,
        files: documents,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["marketing-materials"] });
      enqueueSnackbar("Marketing material updated successfully", { variant: "success" });
      form.reset();
      router.push("/marketing-materials");
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  function handleFormSubmit(data: UpdateMarketingMaterialFormData) {
    if (data.partners.some((partner) => partner.id === 0)) {
      const index = data.partners.findIndex((partner) => partner.id === 0);
      form.setError(`partners.${index}.name`, { message: "Please select a partner", type: "manual" });
      return;
    }

    const hasDuplicatePartners = data.partners.some((partner, index) =>
      data.partners.some((p, i) => i !== index && p.id === partner.id)
    );

    if (hasDuplicatePartners) {
      const duplicatedPartnerIndex = data.partners.findIndex((partner) =>
        data.partners.some((p) => p.id === partner.id && p.id !== 0)
      );
      form.setError(`partners.${duplicatedPartnerIndex}.name`, {
        message: "Duplicated partner",
        type: "manual",
      });
      return;
    }

    if (documents.length > 0) {
      mutate(data);
    }
  }

  function handleUploadDocument(files: File[]) {
    const document = files?.[0] || null;
    if (document) {
      setDocuments((prevDocuments) => [...prevDocuments, document]);
    }
  }

  function handleRemoveDocument(index: number) {
    setDocuments((prevDocuments) => prevDocuments.filter((_, i) => i !== index));
  }

  const partnerRestriction = form.watch("partner_restriction");

  return (
    <FormProvider {...form}>
      <form className="w-full space-y-10" onSubmit={form.handleSubmit(handleFormSubmit)}>
        <div className="col-span-3 bg-white rounded-3xl py-9 px-8">
          <h3 className="text-primary text-2xl font-bold mb-1">{title}</h3>
          <p className="text-[#808FA9] text-sm mb-9">*Mandatory Fields</p>
          <div className="w-full space-y-6">
            <div className="w-full grid grid-cols-1">
              <Input
                label="Name of campaign *"
                placeholder="Name of campaign"
                {...form.register("name")}
                variant={form.formState.errors.name ? "error" : "default"}
                errorMessage={form.formState.errors.name?.message}
              />
            </div>
            <div className="w-full grid grid-cols-1 md:grid-cols-5 gap-6">
              <div className="col-span-2">
                <Input
                  {...form.register("start_date")}
                  label="Start Date"
                  placeholder="00/00/0000"
                  type="date"
                  variant={form.formState.errors.start_date ? "error" : "default"}
                  errorMessage={form.formState.errors.start_date?.message}
                />
              </div>
              <div className="col-span-2">
                <Input
                  {...form.register("end_date")}
                  label="End Date"
                  placeholder="00/00/0000"
                  type="date"
                  variant={form.formState.errors.end_date ? "error" : "default"}
                  errorMessage={form.formState.errors.end_date?.message}
                />
              </div>
            </div>
            <div className="flex flex-col gap-6">
              <p className="text-primary">Category *</p>
              <div className="flex flex-col gap-3">
                <Controller
                  control={form.control}
                  name="category"
                  render={({ field: { onChange, value } }) => (
                    <RadioGroup
                      value={value}
                      onValueChange={(newValue) => onChange(newValue)}
                      disabled={isMarketingMaterialLoading}
                    >
                      <div className="flex flex-col items-start gap-3">
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="STANDARD" className="block" />
                          Standard materials
                        </label>
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="SPECIFIC_MATERIAL" className="block" />
                          Campaign specific materials
                        </label>
                      </div>
                    </RadioGroup>
                  )}
                />
              </div>
            </div>
            <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
            <div className="flex flex-col gap-6">
              <div className="flex items-center gap-3">
                <p className="text-primary">Add partners *</p>
                <Help className="size-5 fill-primary" />
              </div>
              <div className="flex flex-col gap-3">
                <Controller
                  control={form.control}
                  name="partner_restriction"
                  render={({ field: { onChange, value } }) => (
                    <RadioGroup
                      value={value}
                      onValueChange={(newValue) => onChange(newValue)}
                      disabled={isMarketingMaterialLoading}
                    >
                      <div className="flex flex-col items-start gap-3">
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="ALL" className="block" />
                          All partners
                        </label>
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="CLUSTER" className="block" />
                          Cluster
                        </label>
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="SPECIFIC" className="block" />
                          Specific partner
                        </label>
                        {!!form.formState.errors.partner_restriction && (
                          <p className="text-sm text-error">{form.formState.errors.partner_restriction.message}</p>
                        )}
                      </div>
                    </RadioGroup>
                  )}
                />
              </div>
              {(partnerRestriction === "CLUSTER" || partnerRestriction === "SPECIFIC") && (
                <div className="w-full flex flex-col items-start gap-6 px-6">
                  {fields.map((field, index) => (
                    <div key={field.id} className="relative w-full grid grid-cols-[1fr,0.1fr] items-center gap-2">
                      <PartnerInput index={index} />
                      <Button
                        type="button"
                        size="iconSmall"
                        variant="text"
                        color="dark-blue"
                        onClick={() => remove(index)}
                        disabled={isMarketingMaterialLoading}
                      >
                        <Delete className="size-5 fill-primary" />
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    size="small"
                    variant="text"
                    color="light-blue"
                    trailingIcon={<KeyboardArrowRight className="size-5 fill-support-blue" />}
                    onClick={() => append({ name: "", id: 0 })}
                    disabled={isMarketingMaterialLoading}
                  >
                    Add partner
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-4 py-9 px-8">
          <h3 className="text-primary text-2xl font-bold mb-6">Marketing Materials</h3>
          {form.formState.isSubmitted && documents.length === 0 && (
            <p className="text-sm text-error">You must upload at least one document</p>
          )}
          <UploadMarketingMaterials
            documents={documents}
            setDocuments={handleUploadDocument}
            onRemoveDocument={handleRemoveDocument}
          />
        </div>
        <div className="flex items-center justify-end gap-6">
          <Button type="button" onClick={() => router.back()} variant="outlined" color="dark-blue" size="medium">
            Cancel
          </Button>
          <Button
            type="submit"
            variant="filled"
            color="yellow"
            size="medium"
            disabled={isUpdatingMarketingMaterial || form.formState.isSubmitting || !form.formState.isDirty}
          >
            {isUpdatingMarketingMaterial || form.formState.isSubmitting ? "Saving..." : "Save Changes "}
          </Button>
        </div>
      </form>
    </FormProvider>
  );
}

function PartnerInput({ index }: { index: number }) {
  const {
    formState: { errors },
    setValue,
    watch,
  } = useFormContext<UpdateMarketingMaterialFormData>();
  const [search, setSearch] = useState("");

  const partnerId = watch(`partners.${index}.id`);
  const partnerName = watch(`partners.${index}.name`);

  const { data: partners, isLoading } = useQuery({
    queryKey: ["partners", search],
    queryFn: async () => {
      const response = await getPartners({ name: search });
      return response.map((partner) => ({
        value: partner.id.toString(),
        label: `${partner.first_name} ${partner.last_name}`,
      }));
    },
  });

  useEffect(() => {
    if (!!partnerId) {
      setSearch(partnerName);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [partnerId]);

  return (
    <div className="flex flex-col gap-2">
      <AutoComplete
        isLoading={isLoading}
        items={partners ?? []}
        onChange={(value) => setValue(`partners.${index}.id`, Number(value))}
        onSelect={({ label }) => setValue(`partners.${index}.name`, label)}
        onSearchValueChange={setSearch}
        searchValue={search}
        value={partnerId?.toString()}
        placeholder="Name of partner"
        hasError={!!errors["partners"]?.[index]}
      />
      {!!errors["partners"] && <p className="text-sm text-error">{errors["partners"][index]?.name?.message}</p>}
    </div>
  );
}
