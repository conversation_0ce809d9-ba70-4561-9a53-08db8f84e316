"use client";

import { useEffect, useState } from "react";
import { enqueueSnackbar } from "notistack";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Controller, useForm, useWatch } from "react-hook-form";
import { z } from "zod";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { PhoneInput } from "@/components/common/phone-input";
import { Divider } from "@/components/common/divider";
import { Skeleton } from "@/components/ui/skeleton";
import type { UpdateBrokerDto } from "@/lib/api/broker/types";
import { getBroker, updateBroker } from "@/lib/api/broker";
import { queryClient } from "@/lib/react-query";

const editBrokerDataFormSchema = z
  .object({
    name: z.string().optional(),
    vat: z.string().optional(),
    tax_number: z.string().optional(),
    radio_selection: z.enum(["VAT", "TAX"]),
    email: z.string().email("Invalid e-mail").min(1, "Required"),
    phone: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.radio_selection === "VAT" && !data.vat?.trim()) {
      ctx.addIssue({ path: ["vat"], message: "Required", code: z.ZodIssueCode.custom });
    }
    if (data.radio_selection === "TAX" && !data.tax_number?.trim()) {
      ctx.addIssue({ path: ["tax_number"], message: "Required", code: z.ZodIssueCode.custom });
    }
  });

type EditBrokerDataFormData = z.infer<typeof editBrokerDataFormSchema>;

interface EditBrokerDataDialogProps {
  enableContactInfo?: boolean;
  brokerId: number;
  children: React.ReactNode;
}

export function EditBrokerDataDialog({ enableContactInfo = true, brokerId, children }: EditBrokerDataDialogProps) {
  const [open, setOpen] = useState(false);

  const {
    data: brokerData,
    status: queryStatus,
    refetch,
  } = useQuery({
    queryKey: ["broker", brokerId],
    queryFn: async () => await getBroker(brokerId),
    enabled: false,
  });

  const defaultValues: Partial<EditBrokerDataFormData> = {
    name: brokerData?.name || "",
    vat: brokerData?.vat || "",
    tax_number: brokerData?.tax || "",
    email: brokerData?.email || "",
    phone: brokerData?.phone || "",
    radio_selection: brokerData?.tax ? "TAX" : "VAT",
  };

  const { formState, ...form } = useForm<EditBrokerDataFormData>({
    resolver: zodResolver(editBrokerDataFormSchema),
    defaultValues,
  });

  const { phone: phoneWatch, radio_selection: radioWatch } = useWatch({ control: form.control });

  const { mutateAsync } = useMutation({
    mutationFn: (data: EditBrokerDataFormData) => {
      const payload = {
        name: data.name,
        email: data.email,
        phone: data.phone,
      } as UpdateBrokerDto;

      if (data.radio_selection === "VAT") payload.vat = data.vat;
      else if (data.radio_selection === "TAX") payload.tax = data.tax_number;

      return updateBroker(brokerId, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["brokers-data"] });
      queryClient.refetchQueries({ queryKey: ["broker", brokerId] });
      enqueueSnackbar("Broker updated successfully", { variant: "success" });
      setOpen(false);
      form.reset();
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  useEffect(() => {
    if (brokerId) refetch();
    if (!brokerData) return;

    form.reset(defaultValues);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [brokerId, brokerData]);

  async function handleFormSubmit(data: EditBrokerDataFormData) {
    await mutateAsync(data);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-2xl !z-[1001]">
        <DialogHeader>
          <DialogTitle className="font-bold sm:text-[1.75rem] text-support-blue">Edit Broker Data</DialogTitle>
          <DialogDescription />
        </DialogHeader>
        {queryStatus === "pending" && (
          <div className="w-full grid md:grid-cols-2 gap-6">
            <div className="space-y-2 col-span-full">
              <Skeleton className="h-3 w-1/3" />
              <Skeleton className="h-14 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-3 w-1/3" />
              <Skeleton className="h-14 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-3 w-1/3" />
              <Skeleton className="h-14 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-3 w-1/3" />
              <Skeleton className="h-14 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-3 w-1/3" />
              <Skeleton className="h-14 w-full" />
            </div>
            <div className="col-span-full flex justify-end">
              <Skeleton className="h-14 w-[200px] rounded-full" />
            </div>
          </div>
        )}
        {queryStatus === "success" && (
          <form onSubmit={form.handleSubmit(handleFormSubmit)} className="w-full grid md:grid-cols-2 gap-6 mt-10">
            <div className="col-span-full">
              <Controller
                name="name"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    label="Broker Name"
                    placeholder="Name"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                    {...field}
                  />
                )}
              />
            </div>
            <Controller
              control={form.control}
              name="radio_selection"
              render={({ field: { onChange, value } }) => (
                <RadioGroup value={value} onValueChange={(newValue) => onChange(newValue)} className="col-span-full">
                  <div data-value={radioWatch} className="group w-full grid md:grid-cols-2 gap-6 items-start px-1">
                    <label className="flex flex-col">
                      <div className="flex items-center gap-2 text-primary group-data-[value='TAX']:!text-[#8C8A87] cursor-pointer">
                        <RadioGroupItem value="VAT" className="block" />
                        VAT
                      </div>
                      {radioWatch === "VAT" ? (
                        <Input
                          placeholder="VAT Number"
                          variant={formState.errors.vat ? "error" : "default"}
                          errorMessage={formState.errors.vat?.message}
                          {...form.register("vat")}
                        />
                      ) : (
                        <input
                          disabled
                          placeholder="VAT Number"
                          className="bg-[#BEBDBB61] placeholder-[#8C8A87] border-tonal-dark-cream-80 mt-1.5 block w-full border rounded-2xl p-4 focus:outline-[#8C8A87]"
                        />
                      )}
                    </label>
                    <label className="flex flex-col">
                      <div className="flex items-center gap-2 text-primary group-data-[value='VAT']:!text-[#8C8A87] cursor-pointer">
                        <RadioGroupItem value="TAX" className="block" />
                        TAX
                      </div>
                      {radioWatch === "TAX" ? (
                        <Input
                          placeholder="TAX Number"
                          variant={formState.errors.tax_number ? "error" : "default"}
                          errorMessage={formState.errors.tax_number?.message}
                          {...form.register("tax_number")}
                        />
                      ) : (
                        <input
                          disabled
                          placeholder="TAX Number"
                          className="bg-[#BEBDBB61] placeholder-[#8C8A87] border-tonal-dark-cream-80 mt-1.5 block w-full border rounded-2xl p-4 focus:outline-[#8C8A87]"
                        />
                      )}
                    </label>
                  </div>
                </RadioGroup>
              )}
            />
            {enableContactInfo && (
              <>
                <Divider className="col-span-full !my-0" />
                <p className="col-span-full text-tonal-dark-cream-30">Contact informations</p>
                <Controller
                  name="email"
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <Input
                      type="email"
                      label="E-mail *"
                      placeholder="E-mail"
                      variant={error ? "error" : "default"}
                      errorMessage={error?.message}
                      {...field}
                    />
                  )}
                />
                <div className="flex flex-col gap-2">
                  <label htmlFor="mobile-phone" className="text-primary">
                    Mobile
                  </label>
                  <PhoneInput
                    id="mobile-phone"
                    name="phone"
                    defaultValue={phoneWatch}
                    valueSetter={(value) => form.setValue("phone", value || ``)}
                    errorSetter={(isValid) =>
                      isValid ? form.clearErrors("phone") : form.setError("phone", { message: "Invalid phone number." })
                    }
                    isError={!!formState.errors.phone}
                    contentClassName="z-[9999]"
                    isModal
                  />
                  {!!formState.errors.phone && (
                    <span className="text-sm text-error">{formState.errors.phone.message}</span>
                  )}
                </div>
              </>
            )}

            <DialogFooter className="col-span-full flex justify-end mt-10">
              <Button type="submit" variant="filled" color="yellow" size="medium" disabled={formState.isSubmitting}>
                {formState.isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
