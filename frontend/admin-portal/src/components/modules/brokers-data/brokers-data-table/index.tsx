/* eslint-disable @typescript-eslint/ban-ts-comment */

"use client";

import Link from "next/link";
import { createColumnHelper } from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import { MoreVerticalIcon } from "lucide-react";

import { Edit, FilterAlt, KeyboardArrowDown, KeyboardArrowRight, Phone } from "@interzero/oneepr-react-ui/Icon";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import AsyncPaginatedTable from "@/components/common/async-paginated-table";
import { DateRangeFilter } from "@/components/common/date-range-filter";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
// import { formatCurrency } from "@/utils/format-currency";
import { dateManager } from "@/utils/date-manager";
import { YEARS } from "@/utils/get-years";
import type { Broker } from "@/lib/api/broker/types";
import { getBrokers } from "@/lib/api/broker";
import { useDateRangeFilter } from "@/hooks/use-date-range-filter";
import { useQueryFilter } from "@/hooks/use-query-filter";

import { BrokerContactInfoDialog } from "../broker-contact-info-dialog";
import { EditBrokerDataDialog } from "../edit-broker-data-dialog";

const columnHelper = createColumnHelper<Broker>();

export function BrokersDataTable() {
  const { paramValues, changeParam, changeParams } = useQueryFilter(["search", "page", "startDate", "endDate", "year"]);

  const search = paramValues.search || undefined;
  const page = paramValues.page ? Number(paramValues.page) : 1;
  const year = paramValues.year;
  const startDate = paramValues.startDate ? dateManager(paramValues.startDate).format("YYYY-MM-DD") : undefined;

  const { selectedMonth, selectedYear, endDate } = useDateRangeFilter({
    defaultMonth: startDate ? startDate.split("-")[1] : undefined,
    defaultYear: year,
  });

  const yearsFilter = YEARS.filter((y) => Number(y.value) <= new Date().getFullYear()).reverse();

  const { data: brokersData, isLoading } = useQuery({
    queryKey: ["brokers-data", { page, limit: 10, search, year: selectedYear.value, startDate }],
    queryFn: async () => {
      const _endDate = dateManager().startOf("month");
      _endDate.setMonth(parseInt(endDate.month.value) - 1);
      _endDate.setYear(parseInt(endDate.year.value));

      const endDateStr = _endDate.format("YYYY-MM-DD");

      return await getBrokers({ page, limit: 10, search, year: selectedYear.value, startDate, endDate: endDateStr });
    },
  });

  function handleSearch(value: string) {
    changeParams({ search: value, page: "1" });
  }

  function handleChangeYear(year: typeof selectedYear) {
    changeParams({ year: year.value, startDate: undefined, endDate: undefined, page: "1" });
  }

  function handleChangeMonth(month: typeof selectedMonth) {
    const startDate = dateManager().startOf("month");
    startDate.setMonth(parseInt(month.value) - 1);
    startDate.setYear(parseInt(selectedYear.value));

    changeParams({ page: "1", startDate: startDate.format("YYYY-MM-DD") });
  }

  const columns = [
    columnHelper.accessor("id", {
      header: () => <span className="px-0" />,
      cell: (info) => {
        const brokerId = info.getValue();
        return (
          <Dropdown
            trigger={
              <button className="flex items-center justify-center rounded-full size-6 text-primary hover:bg-tonal-dark-cream-70 transition-colors">
                <MoreVerticalIcon className="size-4" />
              </button>
            }
          >
            <DropdownItem asChild>
              <BrokerContactInfoDialog brokerId={brokerId}>
                <button className="flex flex-shrink-0 items-center gap-4 w-full text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                  <Phone className="size-4 fill-primary" /> Contact
                </button>
              </BrokerContactInfoDialog>
            </DropdownItem>
            <DropdownItem asChild>
              <EditBrokerDataDialog brokerId={brokerId}>
                <button className="flex flex-shrink-0 items-center gap-4 w-full text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                  <Edit className="size-4 fill-primary" /> Edit
                </button>
              </EditBrokerDataDialog>
            </DropdownItem>
          </Dropdown>
        );
      },
    }),
    // @ts-ignore
    columnHelper.accessor("number", {
      header: () => <span className="px-0">Broker Number</span>,
      cell: () => {
        return <span className="px-4 text-base text-[#002652]">{"---"}</span>;
      },
    }),
    columnHelper.accessor("name", {
      header: () => <span className="px-0">Broker Name</span>,
      cell: (info) => <span className="px-4">{info.getValue()}</span>,
    }),
    columnHelper.accessor("enroled_at", {
      header: () => <span className="px-0">Enrolled Data</span>,
      cell: (info) => <span className="px-4">{dateManager(info.getValue()).format("DD.MM.YYYY")}</span>,
    }),
    // @ts-ignore
    columnHelper.accessor("invoice_amount", {
      header: () => <span className="px-0">Invoice amount</span>,
      cell: () => <span className="px-4">{"---"}</span>,
    }),
    // @ts-ignore
    columnHelper.accessor("number_of_clients", {
      header: () => <span className="px-0">Number of clients</span>,
      cell: () => <span className="px-4">{"---"}</span>,
    }),
    // @ts-ignore
    columnHelper.accessor("order_year", {
      header: () => <span className="px-0">Order Year</span>,
      cell: (info) => (
        <div className="px-4 flex items-center justify-between gap-2">
          <span className="flex-1">{"---"}</span>
          <Link href={`/brokers-data/${info.row.original.id}`}>
            <KeyboardArrowRight width={28} height={28} className="text-[black]" />
          </Link>
        </div>
      ),
    }),
  ];

  return (
    <div className="flex flex-col gap-6 rounded-3xl">
      <div className="flex items-center justify-between gap-8">
        <div className="relative flex-1 max-w-[320px]">
          <DatatableSearch onSearch={handleSearch} placeholder="Search by name" />
        </div>
        <div className="flex items-center gap-6">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <FilterAlt width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedYear.value}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {yearsFilter.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeYear(filter)}
                className="flex items-center gap-2 text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <div className="h-4 border w-px border-tonal-dark-cream-60" />
          <DateRangeFilter
            endDate={endDate}
            selectedMonth={selectedMonth}
            selectedYear={selectedYear}
            onMonthChange={handleChangeMonth}
            onYearChange={handleChangeYear}
          />
        </div>
      </div>
      <div className="rounded-xl overflow-hidden text-surface-01">
        <AsyncPaginatedTable
          columns={columns}
          currentPage={page}
          isLoading={isLoading}
          data={brokersData?.brokers || []}
          pages={brokersData?.pages || 0}
          onPageChange={(page) => changeParam("page", page.toString())}
          noResultsMessage={search ? `No results for "${search}" in Brokers Data` : "No brokers found"}
        />
      </div>
    </div>
  );
}
