"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { createColumnHelper } from "@tanstack/react-table";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { Download, KeyboardArrowRight, SortDown } from "@interzero/oneepr-react-ui/Icon";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Datatable } from "@/components/common/datatable";

interface CertificateData {
  id: string;
  broker_number: string;
  broker_name: string;
  participation_download_url: string;
  order_download_url: string;
  created_at: string;
}

const mockData: CertificateData[] = Array.from({ length: 40 }).map((_, idx) => ({
  id: (idx + 1).toString(),
  broker_number: "714028249764",
  broker_name: "Lauter & Co.",
  participation_download_url: "https://example.com/participation.pdf",
  order_download_url: "https://example.com/order.pdf",
  created_at: "2023-01-01",
}));

const columnHelper = createColumnHelper<CertificateData>();

export function CertificatesTable() {
  const router = useRouter();

  const [data] = useState<CertificateData[]>(mockData);
  const [filteredData, setFilteredData] = useState<CertificateData[]>(data);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    let filtered = [...data];

    // Filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(
        (item) => item.broker_name.toLowerCase().includes(term) || item.broker_number.includes(term)
      );
    }

    setFilteredData(filtered);
  }, [data, searchTerm]);

  const columns = [
    columnHelper.accessor("broker_number", {
      header: ({ column }) => {
        return (
          <Button
            type="button"
            variant="text"
            size="small"
            color="gray"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Broker Number <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </Button>
        );
      },
      cell: (info) => {
        return <span className="text-base">{info.getValue()}</span>;
      },
    }),
    columnHelper.accessor("broker_name", {
      header: ({ column }) => {
        return (
          <Button
            type="button"
            variant="text"
            size="small"
            color="gray"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Broker Name <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </Button>
        );
      },
    }),
    columnHelper.accessor("participation_download_url", {
      header: "Participation Certificate",
      cell: () => (
        <Button variant="text" size="small" color="dark-blue" leadingIcon={<Download />}>
          Download all
        </Button>
      ),
    }),
    columnHelper.accessor("id", {
      header: "Order Certificate",
      cell: (info) => (
        <div className="flex items-center justify-between">
          <Button variant="text" size="small" color="dark-blue" leadingIcon={<Download />}>
            Download all
          </Button>
          <Button
            variant="text"
            size="iconSmall"
            color="dark-blue"
            onClick={() => router.push(`/certificates/${info.getValue()}`)}
          >
            <KeyboardArrowRight className="fill-[#000] size-7" />
          </Button>
        </div>
      ),
    }),
  ];

  return (
    <div className="flex flex-col gap-10 rounded-3xl">
      <div className="relative max-w-[320px]">
        <DatatableSearch onSearch={setSearchTerm} defaultValue={searchTerm} placeholder="Search" />
      </div>
      <div className="rounded-xl overflow-hidden text-surface-01">
        <Datatable columns={columns} data={filteredData} />
      </div>
    </div>
  );
}
