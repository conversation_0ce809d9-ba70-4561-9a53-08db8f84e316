"use client";

import Link from "next/link";
import { useQuery } from "@tanstack/react-query";

import { Download, KeyboardArrowLeft, WorkspacePremium } from "@interzero/oneepr-react-ui/Icon";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { Skeleton } from "@/components/ui/skeleton";

import { CertificateTable } from "./certificate-table";

interface CertificateData {
  id: string;
  broker_name: string;
  broker_number: string;
  created_at: string;
}

const mockData: CertificateData = {
  id: "1",
  broker_number: "714028249764",
  broker_name: "Lauter & Co.",
  created_at: "2023-01-01",
};

interface CertificateModuleProps {
  certificateId: string;
}

export function CertificateModule({ certificateId }: CertificateModuleProps) {
  const { data: certificate, isLoading } = useQuery({
    queryKey: ["certificate", certificateId],
    queryFn: async () => {
      // return getCustomer(certificateId);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      return mockData;
    },
  });

  if (isLoading) {
    return <CertificateModuleSkeleton />;
  }

  return (
    <>
      <ModuleContent>
        <ModuleTitle
          icon={WorkspacePremium}
          title="Certificates"
          description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
        />
      </ModuleContent>
      <div className="bg-tonal-dark-cream-90">
        <ModuleContent>
          <Link
            href="/certificates"
            className="inline-flex items-center justify-start gap-2 font-bold text-support-blue"
          >
            <KeyboardArrowLeft className="size-5 fill-support-blue" /> Back
          </Link>
          <div className="mt-6 flex items-center gap-6 text-primary">
            <span className="font-bold text-[1.75rem]">{certificate?.broker_name}</span>
            <div className="h-5 border border-primary" />
            <span className="font-bold text-[1.75rem]">{certificate?.broker_number}</span>
          </div>
          <div className="mt-12">
            <Tabs defaultValue="participation-certificate" className="w-full flex-1">
              <TabsList className="w-full grid grid-cols-3 pt-6 pb-4 gap-0">
                <TabsTrigger
                  className="px-10 !bg-[transparent] !text-primary border-b-2 border-tonal-dark-cream-80 data-[state=active]:border-primary opacity-40 data-[state=active]:opacity-100 rounded-none"
                  value="participation-certificate"
                >
                  Participation Certificate
                </TabsTrigger>
                <TabsTrigger
                  className="px-10 !bg-[transparent] !text-primary border-b-2 border-tonal-dark-cream-80 data-[state=active]:border-primary opacity-40 data-[state=active]:opacity-100 rounded-none"
                  value="order-certificate"
                >
                  Order Certificate
                </TabsTrigger>
                <Button
                  variant="filled"
                  color="dark-blue"
                  size="small"
                  leadingIcon={<Download />}
                  disabled
                  className="ml-auto"
                >
                  Download all
                </Button>
              </TabsList>
              <TabsContent value="participation-certificate">
                <CertificateTable />
              </TabsContent>
              <TabsContent value="order-certificate">
                <CertificateTable />
              </TabsContent>
            </Tabs>
          </div>
        </ModuleContent>
      </div>
    </>
  );
}

function CertificateModuleSkeleton() {
  return (
    <>
      <ModuleContent>
        <ModuleTitle
          icon={WorkspacePremium}
          title="Certificates"
          description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
        />
      </ModuleContent>
      <div className="bg-tonal-dark-cream-90">
        <ModuleContent>
          <Skeleton className="h-5 w-16" />
          <div className="mt-6 flex items-center gap-6 text-primary">
            <Skeleton className="h-5 w-1/4" />
            <Skeleton className="h-5 w-1/4" />
          </div>
          <div className="mt-12 flex items-center gap-1">
            <Skeleton className="h-10 w-1/3" />
            <Skeleton className="h-10 w-1/3" />
            <Skeleton className="ml-auto h-10 w-1/5 rounded-full" />
          </div>
          <div className="mt-10 flex items-center">
            <Skeleton className="h-8 w-1/5 rounded-full" />
            <Skeleton className="ml-auto h-5 w-1/4" />
            <Skeleton className="ml-2 h-5 w-1/4" />
          </div>
          <div className="mt-9">
            <Skeleton className="h-10 w-full mb-7" />
            <div className="flex flex-col gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between gap-4">
                  <Skeleton className="h-7 w-[200px]" />
                  <Skeleton className="h-7 w-[200px]" />
                  <Skeleton className="h-7 w-[200px]" />
                  <Skeleton className="h-7 w-[200px]" />
                  <Skeleton className="h-7 w-[200px]" />
                  <Skeleton className="h-7 w-[200px]" />
                </div>
              ))}
            </div>
          </div>
        </ModuleContent>
      </div>
    </>
  );
}
