"use client";

import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { KeyboardArrowLeft } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const createCompanyFormSchema = z.object({
  company_name: z.string({ message: "Company name is required" }).min(1, { message: "Company name is required" }),
  register_number: z
    .string({ message: "Register number is required" })
    .min(1, { message: "Register number is required" }),
  vat_id: z
    .string({ message: "VAT-ID is required", invalid_type_error: "VAT-ID must be a number" })
    .min(1, { message: "VAT-ID is required" }),
  tax: z
    .string({ message: "Tax is required", invalid_type_error: "Tax must be a number" })
    .min(1, { message: "Tax is required" }),
  address: z.string({ message: "Address is required" }).min(1, { message: "Address is required" }),
  city: z.string({ message: "City is required" }).min(1, { message: "City is required" }),
  contact_name: z.string({ message: "Contact name is required" }).min(1, { message: "Contact name is required" }),
  phone: z.string({ message: "Phone number is required" }).min(1, { message: "Phone number is required" }),
  email: z
    .string({ message: "Email is required" })
    .email({ message: "Email is required" })
    .min(1, { message: "Email is required" }),
});

type CreateCompanyFormData = z.infer<typeof createCompanyFormSchema>;

export default function AddNewCompanyForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<CreateCompanyFormData>({
    resolver: zodResolver(createCompanyFormSchema),
  });

  async function handleFormSubmit(data: CreateCompanyFormData) {
    setIsLoading(true);
    try {
      // await createUser({
      //   name: `${data.first_name} ${data.surname}`,
      //   email: data.email,
      //   password: data.password,
      //   role_id: Number(data.role),
      //   is_active: data.status === STATUS_VALUES[0],
      // });
      // eslint-disable-next-line no-console
      console.log(data);

      enqueueSnackbar("Company created successfully", { variant: "success" });
      router.push("/my-companies");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const status = error?.response?.status;

      if (status === 400 || status === 409) {
        enqueueSnackbar("Email already in use", { variant: "error" });
        return setError("email", { message: "Email already in use" });
      }

      enqueueSnackbar("Error creating user", { variant: "error" });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <ModuleContent>
      <ModuleTitle
        title="Add new company"
        description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
      />
      <div className="flex items-center gap-1" onClick={() => router.push("/my-companies")}>
        <KeyboardArrowLeft className="fill-[#009dd3] size-6 cursor-pointer" />
        <p className="text-paragraph-regular text-support-blue font-bold hover:underline cursor-pointer">
          Back to My companies
        </p>
      </div>
      <div>
        <form className="w-full" onSubmit={handleSubmit(handleFormSubmit)}>
          <div className="bg-white p-6 mt-6 rounded-lg w-full space-y-6">
            <div className="mb-6">
              <h3 className="text-xl text-primary font-bold">New company</h3>
              <p className="text-small-paragraph-regular text-[#808FA9]">*Mandatory Fields</p>
            </div>
            <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-12">
              <Input
                label="Company name *"
                placeholder="Company name"
                {...register("company_name")}
                variant={errors.company_name?.message ? "error" : "default"}
                errorMessage={errors.company_name?.message}
              />
              <Input
                label="Registration number *"
                placeholder="Registration number"
                {...register("register_number")}
                variant={errors.register_number?.message ? "error" : "default"}
                errorMessage={errors.register_number?.message}
                autoComplete="off"
              />
            </div>
            <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-12">
              <Input
                label="VAT-ID *"
                placeholder="VAT-ID"
                {...register("vat_id")}
                variant={errors.vat_id?.message ? "error" : "default"}
                errorMessage={errors.vat_id?.message}
              />
              <Input
                label="Tax *"
                placeholder="TAX"
                {...register("tax")}
                variant={errors.tax?.message ? "error" : "default"}
                errorMessage={errors.tax?.message}
              />
            </div>
            <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-12">
              <Input
                label="Address *"
                placeholder="1363, Bunts Rd Lakewood"
                {...register("address")}
                variant={errors.address?.message ? "error" : "default"}
                errorMessage={errors.address?.message}
              />
              <Input
                label="City *"
                placeholder="City"
                {...register("city")}
                variant={errors.city?.message ? "error" : "default"}
                errorMessage={errors.city?.message}
              />
            </div>
            <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-12">
              <Input
                label="Contact name *"
                placeholder="Contact name"
                {...register("contact_name")}
                variant={errors.contact_name?.message ? "error" : "default"}
                errorMessage={errors.contact_name?.message}
              />
              <Input
                label="E-mail *"
                placeholder="@email.com"
                type="email"
                {...register("email")}
                variant={errors.email?.message ? "error" : "default"}
                errorMessage={errors.email?.message}
              />
            </div>
            <div className="w-[47%] grid grid-cols-1">
              <Input
                label="Phone number *"
                placeholder="Phone number"
                {...register("phone")}
                variant={errors.phone?.message ? "error" : "default"}
                errorMessage={errors.phone?.message}
              />
            </div>
          </div>
          <div className="flex items-center justify-end mt-8 w-full gap-6">
            <Button variant="outlined" color="dark-blue" size="medium" className="w-[250px]">
              Cancel
            </Button>
            <Button
              type="submit"
              variant="filled"
              color="yellow"
              size="medium"
              className="w-[250px]"
              disabled={isLoading}
            >
              {isLoading ? "Loading..." : "Save"}
            </Button>
          </div>
        </form>
      </div>
    </ModuleContent>
  );
}
