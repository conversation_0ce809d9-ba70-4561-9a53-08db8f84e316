"use client";

import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { KeyboardArrowLeft, MapsHomeWork } from "@interzero/oneepr-react-ui/Icon";
import { useRouter } from "next/navigation";
import { CompanyProfileTable } from "./company-profile-orders-table";
import { useState } from "react";
import { Input } from "@interzero/oneepr-react-ui/Input";

export default function CompanyProfileModule() {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [companyInfo, setCompanyInfo] = useState({
    companyName: "Lauter & Co.",
    registrationNumber: "DE1714028249764",
    vatId: "---",
    tax: "12656",
    address: "1363, Bunts Rd Lakewood",
    city: "CN - Cologne",
    contactName: "Mr. <PERSON>",
    email: "<EMAIL>",
    phoneNumber: "656565 65655",
  });

  interface CompanyInfo {
    companyName: string;
    registrationNumber: string;
    vatId: string;
    tax: string;
    address: string;
    city: string;
    contactName: string;
    email: string;
    phoneNumber: string;
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    setCompanyInfo((prevInfo: CompanyInfo) => ({
      ...prevInfo,
      [name]: value,
    }));
  };

  return (
    <ModuleContent>
      <ModuleTitle
        icon={MapsHomeWork}
        title="My Companies"
        description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
      />
      <div className="flex items-center gap-1 mb-8" onClick={() => router.push("/my-companies")}>
        <KeyboardArrowLeft className="fill-[#009dd3] size-6 cursor-pointer" />
        <p className="text-paragraph-regular text-support-blue font-bold hover:underline cursor-pointer">
          Back to My companies
        </p>
      </div>

      <div className="w-full bg-white p-6 my-6 rounded-lg space-y-6">
        <div className="flex items-center justify-between">
          <p className="text-primary text-title-3 font-bold">Company information</p>
          {!isEditing && (
            <Button variant="text" color="light-blue" size="medium" onClick={() => setIsEditing(!isEditing)}>
              Edit
            </Button>
          )}
        </div>
        <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-8">
          {Object.entries(companyInfo).map(([key, value]) => (
            <div key={key} className="flex flex-col gap-2">
              <p className="text-small-paragraph-regular text-tonal-dark-cream-30">
                {key.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())}
              </p>
              {isEditing ? (
                <Input
                  name={key}
                  value={value}
                  onChange={handleInputChange}
                  className="text-paragraph-regular text-primary"
                />
              ) : (
                <p className="text-paragraph-regular text-primary">{value}</p>
              )}
            </div>
          ))}
        </div>
      </div>
      {isEditing && (
        <div className="flex items-center justify-end gap-2">
          <Button
            variant="outlined"
            color="dark-blue"
            size="medium"
            className="w-[250px]"
            onClick={() => setIsEditing(!isEditing)}
          >
            Cancel
          </Button>
          <Button
            variant="filled"
            color="yellow"
            size="medium"
            className="w-[250px]"
            onClick={() => setIsEditing(!isEditing)}
          >
            Save
          </Button>
        </div>
      )}
      <div className="mt-12">
        <CompanyProfileTable />
      </div>
    </ModuleContent>
  );
}
