import { formatCurrency } from "@/utils/format-currency";
import Image from "next/image";

export interface CountryLicenseItemProps {
  position: number;
  label: string;
  flag: string;
  license: string;
  revenue: number;
  licensesCount: number;
  color: string;
}

export function CountryLicenseItem({
  position,
  label,
  flag,
  license,
  revenue,
  licensesCount,
  color,
}: CountryLicenseItemProps) {
  return (
    <div className="grid grid-cols-10 py-4 border-b border-tonal-dark-cream-80 items-center">
      <span className="col-span-1 text-xl text-tonal-dark-cream-40 font-bold">{position}º</span>
      <div className="col-span-9 flex items-center w-full gap-4">
        <div className="flex-shrink-0 w-12 h-12 rounded-full overflow-hidden relative">
          <Image src={flag} alt="country_flag" fill className="object-cover" />
        </div>

        <div className="flex flex-col flex-grow">
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center gap-2">
              <span className="text-tonal-dark-cream-10">{label}</span>
              <span className="text-sm italic text-tonal-dark-cream-50">{license}</span>
              <div style={{ backgroundColor: color }} className="size-3 rounded-full" />
            </div>
            {/* <Link size={12} className="text-support-blue" /> */}
          </div>
          <div className="flex items-center justify-between gap-4">
            <div></div>
            <span className="text-tonal-dark-cream-50 text-sm whitespace-nowrap">{formatCurrency(revenue / 100)}</span>
          </div>
          <div className="self-end mt-1">
            <span className="text-sm text-tonal-dark-cream-50">{licensesCount} licenses sold</span>
          </div>
        </div>
      </div>
    </div>
  );
}
