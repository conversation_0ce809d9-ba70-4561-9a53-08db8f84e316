"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { getAccountsAndObligations } from "@/lib/api/dashboard/accounts-and-obligations";
import { useQuery } from "@tanstack/react-query";
import { ArrowDown, ArrowUp } from "lucide-react";

export function AccountsCreated() {
  const { data, isLoading, isSuccess } = useQuery({
    queryKey: ["accounts-and-obligations"],
    queryFn: () => getAccountsAndObligations(),
  });

  return (
    <div id="accounts-created" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <h3 className="text-primary text-2xl font-bold">Accounts created</h3>
      {isLoading && <Skeleton className="h-52 rounded-3xl" />}
      {isSuccess && (
        <div className="flex flex-col gap-4 bg-surface-01 rounded-3xl p-8">
          <span className="text-6xl text-[#009DD3] font-bold">{data.accounts.total}</span>
          <span className="text-primary">Accounts Created</span>

          {data.accounts.growth < 0 ? (
            <div className="flex flex-row gap-2 items-center">
              <ArrowDown size={12} className="text-error" />
              <span className="text-error font-bold">
                {Math.abs(data.accounts.growth).toFixed()}% less than last month
              </span>
            </div>
          ) : (
            <div className="flex flex-row gap-2 items-center">
              <ArrowUp size={12} className="text-success" />
              <span className="text-success font-bold">{data.accounts.growth.toFixed()}% more than last month</span>
            </div>
          )}
        </div>
      )}

      <h3 className="text-primary text-2xl font-bold">Obligation assessments</h3>
      {isLoading && <Skeleton className="h-52 rounded-3xl" />}
      {isSuccess && (
        <div className="flex flex-col gap-4 bg-surface-01 rounded-3xl p-8">
          <span className="text-6xl text-[#1B6C64] font-bold">{data.obligations.total}</span>
          <span className="text-primary">Number of obligation assessment</span>

          {data.obligations.growth < 0 ? (
            <div className="flex flex-row gap-2 items-center">
              <ArrowDown size={12} className="text-error" />
              <span className="text-error font-bold">
                {Math.abs(data.obligations.growth).toFixed()}% less than last month
              </span>
            </div>
          ) : (
            <div className="flex flex-row gap-2 items-center">
              <ArrowUp size={12} className="text-success" />
              <span className="text-success font-bold">{data.obligations.growth.toFixed()}% more than last month</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
