"use client";

import { DashboardPlaceHolder } from "@/components/modules/dashboard/placeholder/placeholder";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { getTopServices } from "@/lib/api/dashboard/top-services";
import { KeyboardArrowDown, Sort } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { ServiceItem } from "./service-item";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

export function TopServices() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }
  const { data, isSuccess, isLoading, isError } = useQuery({
    queryKey: ["top-services", selectedLicenseYear.value],
    queryFn: () => getTopServices({ year: +selectedLicenseYear.value }),
  });
  const showPlaceholder = (isSuccess && data.data.length === 0) || isError;

  return (
    <div id="top-services" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row justify-between items-center">
        <h3 className="text-primary text-2xl font-bold">Top services</h3>

        <div className="flex flex-row gap-6 items-center">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
        </div>
      </div>

      {showPlaceholder && <DashboardPlaceHolder itemName="Top Services" />}
      <div className="flex flex-col">
        {isLoading && (
          <>
            <Skeleton className="h-12" />
            <Skeleton className="h-12 mt-3" />
            <Skeleton className="h-12 mt-3" />
            <Skeleton className="h-12 mt-3" />
            <Skeleton className="h-12 mt-3" />
            <Skeleton className="h-12 mt-3" />
            <Skeleton className="h-12 mt-3" />
          </>
        )}
        {isSuccess &&
          data.data.map((item, idx) => (
            <ServiceItem
              key={item.setup_packaging_service_id}
              position={idx + 1}
              label={item.label}
              countries={item.total_countries}
              licenses={item.total_licenses}
            />
          ))}
      </div>
    </div>
  );
}
