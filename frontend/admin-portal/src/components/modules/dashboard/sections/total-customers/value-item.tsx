export function ValueItem({ value, label, isCurrency = true }: { value: number; label: string; isCurrency?: boolean }) {
  const paddedValue = value.toString().padStart(3, "0");
  const formattedValue = isCurrency ? `€ ${paddedValue}` : paddedValue;

  return (
    <div className="flex flex-col gap-2 h-[65px]">
      <span className="text-3xl text-primary font-bold">{formattedValue}</span>
      <span className="text-primary">{label}</span>
    </div>
  );
}
