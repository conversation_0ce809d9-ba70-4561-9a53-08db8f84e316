"use client";

import { But<PERSON> } from "@interzero/oneepr-react-ui/Button";
import { createColumnHelper } from "@tanstack/react-table";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import {
  Sort,
  KeyboardArrowDown,
  Edit,
  FileCopy,
  Delete,
  Download,
  MoreVert,
  AdsClick,
} from "@interzero/oneepr-react-ui/Icon";
import { useRouter } from "next/navigation";
import { DeleteDiscountCodeDialog } from "../delete-discount-code-dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { BuyXGetYDiscount, BuyXGetYProduct, Coupon } from "@/lib/api/coupon/types";
import { useQuery } from "@tanstack/react-query";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { getAllCoupons } from "@/lib/api/coupon";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import Status from "@/components/common/license-status";
import AsyncPaginatedTable from "@/components/common/async-paginated-table";

const LEAD_FILTERS = [
  { label: "All leads", value: "ALL" },
  { label: "Coupons", value: "COUPONS" },
  { label: "Links", value: "LINKS" },
];

const STATUS_FILTERS = [
  { label: "All Status", value: "ALL" },
  { label: "Active", value: "ACTIVE" },
  { label: "Inactive", value: "INACTIVE" },
];

const isBuyXGetYDiscount = (coupon: Coupon): coupon is Coupon & { buy_x_get_y: BuyXGetYDiscount } => {
  return coupon.discount_type === "BUY_X_PRODUCTS_GET_Y_DISCOUNT";
};

const isBuyXGetYProduct = (coupon: Coupon): coupon is Coupon & { buy_x_get_y: BuyXGetYProduct } => {
  return coupon.discount_type === "BUY_X_PRODUCTS_GET_Y_PRODUCTS";
};

const formatCouponDiscount = (coupon: Coupon) => {
  if (coupon.discount_type === "PERCENTAGE") {
    return `${coupon.value}%`;
  }

  if (coupon.discount_type === "ABSOLUTE") {
    return `€ ${coupon.value / 100}`;
  }

  if (isBuyXGetYDiscount(coupon)) {
    if (coupon.buy_x_get_y.discountType === "PERCENTAGE") {
      return `${coupon.buy_x_get_y.discountValue}%`;
    }

    return `€ ${coupon.buy_x_get_y.discountValue / 100}`;
  }

  if (isBuyXGetYProduct(coupon)) {
    return `Buy ${coupon.buy_x_get_y.buyAtLeast} get ${coupon.buy_x_get_y.receiveAtLeast} products`;
  }

  return "";
};

const columnHelper = createColumnHelper<Coupon>();

export function DiscountCodesTable() {
  const { paramValues, changeParam, changeParams } = useQueryFilter([
    "page",
    "search",
    "lead_type",
    "status",
    "start_date",
    "end_date",
  ]);
  const searchTerm = paramValues.search ?? "";
  const selectedLead = LEAD_FILTERS.find((filter) => filter.value === paramValues.lead_type) ?? LEAD_FILTERS[0];
  const selectedStatus = STATUS_FILTERS.find((filter) => filter.value === paramValues.status) ?? STATUS_FILTERS[0];

  const { data, isLoading } = useQuery({
    queryKey: ["coupons", paramValues],
    queryFn: () => {
      const { lead_type, search, status, start_date, end_date } = paramValues;

      const leadType = lead_type === "LINKS" ? "link" : lead_type === "COUPONS" ? "code" : undefined;
      const isActive = status === "ACTIVE" ? true : status === "INACTIVE" ? false : undefined;
      const code = search ?? undefined;
      const startDate = start_date ?? undefined;
      const endDate = end_date ?? undefined;

      return getAllCoupons({
        page: Number(paramValues.page ?? 1),
        limit: 10,
        include_uses: true,
        lead_type: leadType,
        code: code,
        is_active: isActive,
        start_date: startDate,
        end_date: endDate,
      });
    },
  });

  const router = useRouter();

  function handleDuplicate(coupon: Coupon) {
    sessionStorage.setItem("duplicatedCoupon", JSON.stringify(coupon));
    router.push("/discount-codes/create");
  }

  function handleEdit(coupon: Coupon) {
    router.push(`/discount-codes/${coupon.id}/edit`);
  }

  const columns = [
    columnHelper.accessor("code", {
      header: "Code / Link",
      enableSorting: true,
      cell: (info) => (
        <div
          className="flex items-center justify-start gap-2 min-w-[250px]"
          title={info.row.original.link ?? info.row.original.code}
        >
          <span className="text-primary text-sm truncate max-w-[220px]">
            {info.row.original.link ?? info.row.original.code}
          </span>
          {info.row.original.link ? (
            <AdsClick className="w-5 h-5 flex-shrink-0 fill-support-blue" />
          ) : (
            <FileCopy className="w-4 h-4 flex-shrink-0 fill-support-blue" />
          )}
        </div>
      ),
    }),
    columnHelper.accessor("discount_type", {
      header: "Discount",
      enableSorting: true,
      cell: (info) => {
        return <span className="text-primary ml-4">{formatCouponDiscount(info.row.original)}</span>;
      },
    }),
    columnHelper.accessor("start_date", {
      header: "Start Date",
      enableSorting: true,
      cell: (info) => (
        <span className="text-primary ml-4">
          {info.getValue() ? new Date(info.getValue()).toLocaleDateString() : "-"}
        </span>
      ),
    }),
    columnHelper.accessor("end_date", {
      header: "End Date",
      enableSorting: true,
      cell: (info) => (
        <span className="text-primary ml-4">
          {info.getValue() ? new Date(info.getValue()).toLocaleDateString() : "-"}
        </span>
      ),
    }),
    columnHelper.accessor("max_uses", {
      header: "Used times / total allowed",
      enableSorting: true,
      cell: (info) => (
        <span className="text-primary ml-4">
          {info.row.original.coupon_uses?.length || 0} / {info.row.original.max_uses || 0}
        </span>
      ),
    }),
    columnHelper.accessor("is_active", {
      header: "Status",
      enableSorting: true,
      cell: (info) => (
        <div className="ml-4">
          <Status status={info.getValue() ? "Active" : "Inactive"} />
        </div>
      ),
    }),
    columnHelper.accessor("description", {
      header: "Description",
      enableSorting: true,
      cell: (info) => (
        <div className="min-w-[200px] truncate ml-4" title={info.getValue()}>
          <span className="text-primary text-sm">{info.getValue()}</span>
        </div>
      ),
    }),
    columnHelper.display({
      id: "actions",
      header: " ",
      cell: (info) => (
        <Popover>
          <PopoverTrigger>
            <MoreVert className="w-6 h-6 fill-primary mt-2" />
          </PopoverTrigger>
          <PopoverContent side="bottom" className="w-40 overflow-hidden shadow-elevation-04-1 border-none p-0">
            <div className="flex flex-col gap-2">
              <Button
                variant="text"
                size="small"
                color="dark-blue"
                className="w-full flex justify-start p-3 py-3 rounded-none"
                leadingIcon={<Edit />}
                onClick={() => handleEdit(info.row.original)}
              >
                Edit
              </Button>
              <Button
                variant="text"
                size="small"
                color="dark-blue"
                className="w-full flex justify-start p-3 py-3 rounded-none"
                leadingIcon={<FileCopy />}
                onClick={() => handleDuplicate(info.row.original)}
              >
                Duplicate
              </Button>
              <DeleteDiscountCodeDialog discountCodeId={info.row.original.id}>
                <Button
                  variant="text"
                  size="small"
                  color="dark-blue"
                  className="w-full flex justify-start p-3 py-3 rounded-none"
                  leadingIcon={<Delete className="fill-primary" />}
                >
                  Delete
                </Button>
              </DeleteDiscountCodeDialog>
            </div>
          </PopoverContent>
        </Popover>
      ),
    }),
  ];

  return (
    <div className="flex flex-col gap-6 bg-cream rounded-3xl">
      <div className="flex justify-between items-center px-8 pt-8">
        <div className="flex items-center gap-6">
          <h2 className="text-2xl font-bold text-primary">All Discounts</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button color="light-blue" size="medium" variant="text" leadingIcon={<Download />}>
            PDF
          </Button>
          <Button
            color="gray"
            size="medium"
            variant="text"
            leadingIcon={<Download />}
            className="text-[#66A73F] fill-[#66A73F]"
          >
            Excel
          </Button>
        </div>
      </div>
      <div className="px-8 flex items-center justify-between gap-4">
        <div className="relative flex-1 max-w-[320px]">
          <DatatableSearch
            onSearch={(value) => changeParam("search", value)}
            defaultValue={searchTerm}
            placeholder="Search by code or link"
          />
        </div>
        <div className="flex items-center gap-4">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLead.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LEAD_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => {
                  changeParam("lead_type", filter.value);
                }}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedStatus.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {STATUS_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => changeParam("status", filter.value)}
                className="flex items-center gap-2 text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.value !== "ALL" && (
                  <div
                    data-status={filter.value === "ACTIVE"}
                    className="w-3 h-3 rounded-full mr-2 data-[status='true']:bg-tonal-green-40 data-[status='false']:bg-tonal-cream-20"
                  />
                )}
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <MonthDatePickerWithRange
            onDateChange={(value) => {
              changeParams({
                start_date: value?.from?.toISOString() ?? "",
                end_date: value?.to?.toISOString() ?? "",
              });
            }}
          />
        </div>
      </div>
      <div className="px-8 pb-8">
        <AsyncPaginatedTable
          isLoading={isLoading}
          data={data?.coupons ?? []}
          currentPage={data?.current_page ?? 1}
          onPageChange={(page) => changeParam("page", page.toString())}
          pageSize={data?.limit ?? 10}
          pages={data?.pages ?? 1}
          noResultsMessage="No coupons found"
          showHeaderOnNoResults
          onRowClick={(coupon) => handleEdit(coupon)}
          columns={columns}
          useBuiltInSort
        />
      </div>
    </div>
  );
}
