"use client";

import { CustomDropdown } from "@/components/common/custom-dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { getPartnerCouponsPerformance } from "@/lib/api/partner";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { ChevronDown } from "lucide-react";
import { useParams } from "next/navigation";
import { useState, useMemo } from "react";
import { XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from "recharts";

export function PartnerCouponsPerformance() {
  const { id } = useParams();
  const [groupBy, setGroupBy] = useState<"quarter" | "year">("quarter");

  const { data: couponsPerformance, isLoading: isCouponsPerformanceLoading } = useQuery({
    queryKey: ["partner-coupons-performance", groupBy],
    queryFn: () => getPartnerCouponsPerformance(Number(id), groupBy),
    enabled: !!id,
  });

  // Transform the data for the chart
  const chartData = useMemo(() => {
    if (!couponsPerformance?.data) return [];

    // Get all unique time periods (quarters or years)
    const timePeriods = new Set<string>();
    couponsPerformance.data.forEach((coupon) => {
      if (groupBy === "quarter") {
        (coupon.values as { quarter: string; value: number }[]).forEach((value) => {
          timePeriods.add(value.quarter);
        });
      } else {
        (coupon.values as { year: string; value: number }[]).forEach((value) => {
          timePeriods.add(value.year);
        });
      }
    });

    // Create data points for each time period
    return Array.from(timePeriods).map((period) => {
      const dataPoint: Record<string, unknown> = {
        period: period,
      };

      // Add values for each coupon
      couponsPerformance.data.forEach((coupon) => {
        const value =
          groupBy === "quarter"
            ? (coupon.values as { quarter: string; value: number }[]).find((v) => v.quarter === period)
            : (coupon.values as { year: string; value: number }[]).find((v) => v.year === period);
        dataPoint[coupon.code] = value?.value || 0;
      });

      return dataPoint;
    });
  }, [couponsPerformance, groupBy]);

  return (
    <div className="bg-white p-10 pb-7 rounded-[40px]">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold text-primary">Coupons Performance</h2>
        <CustomDropdown
          options={[
            { label: "Quarter", value: "quarter" },
            { label: "Year", value: "year" },
          ]}
          selectedOption={groupBy}
          handleSelect={(selectedOptionValue) => {
            setGroupBy(selectedOptionValue as "quarter" | "year");
          }}
          showFilterIcon={false}
        />
      </div>
      <p className="text-[#808FA9] mt-2">Values in thousands of euros</p>
      {isCouponsPerformanceLoading || !id ? (
        <div className="mt-4">
          <div className="flex items-center gap-4">
            {[1, 2].map((index) => (
              <div className="flex items-center gap-2 my-1" key={index}>
                <Skeleton className="w-4 h-4 rounded-full" />
                <Skeleton className="w-16 h-4" />
                <Skeleton className="w-4 h-4" />
              </div>
            ))}
          </div>
          <div className="mt-4">
            <Skeleton className="w-full h-[300px]" />
          </div>
        </div>
      ) : (
        <>
          <div className="mt-4 flex items-center gap-4">
            {couponsPerformance?.data.map((coupon, index) => (
              <div className="flex items-center gap-2 my-1" key={coupon.code}>
                <div
                  className={cn(
                    "w-4 h-4 rounded-full mt-[2px]",
                    index === 0 && "bg-tertiary",
                    index === 1 && "bg-support-blue"
                  )}
                />
                <p className="text-sm uppercase text-primary">{coupon.code}</p>
                <ChevronDown className="-mt-[2px]" />
              </div>
            ))}
          </div>
          <div className="-ml-4 w-full">
            <ResponsiveContainer width="104%" height={300}>
              <AreaChart data={chartData} margin={{ top: 20, right: 20, left: 0, bottom: 0 }}>
                <defs>
                  {couponsPerformance?.data.map((coupon, index) => (
                    <linearGradient key={coupon.code} id={`color${coupon.code}`} x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={index === 0 ? "#FFCE0033" : "#009DD3"} stopOpacity={0.5} />
                      <stop offset="95%" stopColor={index === 0 ? "#FFCE0033" : "#009DD3"} stopOpacity={0} />
                    </linearGradient>
                  ))}
                </defs>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis />
                <Tooltip />
                {couponsPerformance?.data.map((coupon, index) => (
                  <Area
                    key={coupon.code}
                    type="monotone"
                    dataKey={coupon.code}
                    stroke={index === 0 ? "#FFCE0033" : "#009DD3"}
                    fillOpacity={1}
                    fill={`url(#color${coupon.code})`}
                  />
                ))}
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </>
      )}
    </div>
  );
}
