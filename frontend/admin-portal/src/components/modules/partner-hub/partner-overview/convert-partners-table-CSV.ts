import { Partner } from "@/lib/api/partner/types";
import { dateManager } from "@/utils/date-manager";
import { getCouponUses, getEarnedRevenue, getLinksUses, getReductionOfRevenue, getTotalEarnings } from "..";

export const convertPartnersTableToCSV = (partners: Partner[]) => {
  const escapeComma = (text: string) => {
    return text.includes(",") ? `"${text}"` : text;
  };

  const headers = [
    "Partner",
    "Status",
    "Discount Code Used",
    "Links Used",
    "Total Earnings",
    "Earned Revenue",
    "Reduction of Revenue",
    "Enroled",
    "Code",
    "Link",
  ];

  const csvRows = [headers.join(",")];

  partners.forEach((partner) => {
    const row = [
      escapeComma(`${partner.first_name} ${partner.last_name}`),
      escapeComma(partner.status ?? ""),
      escapeComma(getCouponUses(partner.coupons ?? []).toString()),
      escapeComma(getLinksUses(partner.coupons ?? []).toString()),
      escapeComma(`€ ${getTotalEarnings(partner)}`),
      escapeComma(`€ ${getEarnedRevenue(partner)}`),
      escapeComma(`€ ${getReductionOfRevenue(partner)}`),
      escapeComma(
        partner.coupons
          ?.map((coupon) => dateManager(coupon.coupon.created_at).format("DD.MM.YYYY")) // Acessando `coupon.created_at`
          .join(", ") || "--"
      ),
      escapeComma(
        partner.coupons
          ?.map((coupon) => coupon.coupon.code ?? "--") // Acessando `coupon.code`
          .join(", ") || "--"
      ),
      escapeComma(
        partner.coupons
          ?.map((coupon) => coupon.coupon.link ?? "--") // Acessando `coupon.link`
          .join(", ") || "--"
      ),
    ];
    csvRows.push(row.join(","));
  });

  return csvRows.join("\n");
};
