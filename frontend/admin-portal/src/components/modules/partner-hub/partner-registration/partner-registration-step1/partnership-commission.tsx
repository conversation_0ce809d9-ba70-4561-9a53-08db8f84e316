"use client";

import { CheckboxInput } from "@/components/ui/checkbox";
import { Controller, useFormContext } from "react-hook-form";
import { CreatePartnerParams } from "..";

export function PartnershipCommission() {
  const form = useFormContext<CreatePartnerParams>();

  return (
    <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10 my-12">
      <p className="text-2xl font-bold text-primary">Partnership commission</p>
      <Controller
        control={form.control}
        name="step_1.noProvisionNegotiated"
        render={({ field }) => (
          <CheckboxInput
            label="No provision negotiated"
            checked={field.value}
            onChange={(e) => {
              field.onChange(e.target.checked);
              if (e.target.checked) {
                form.setValue("step_2.commissionTerms", undefined);
              }
            }}
          />
        )}
      />
    </div>
  );
}
