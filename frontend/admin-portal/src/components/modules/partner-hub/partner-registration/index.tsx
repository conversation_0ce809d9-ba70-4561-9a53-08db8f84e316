"use client";

import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { createPartner } from "@/lib/api/partner";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Loader2Icon } from "lucide-react";
import { useRouter } from "next/navigation";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { partnerRegistrationFormSchema } from "./partner-registration-form-schema";
import { PartnerRegistrationStep1 } from "./partner-registration-step1";
import { PartnerRegistrationStep2 } from "./partner-registration-step2";

export type CreatePartnerParams = z.infer<typeof partnerRegistrationFormSchema>;

export function PartnerRegistrationModule() {
  const router = useRouter();
  const queryClient = useQueryClient();

  const [formStep, setFormStep] = useState<number>(1);

  const form = useForm<CreatePartnerParams>({
    resolver: zodResolver(partnerRegistrationFormSchema),
    reValidateMode: "onBlur",
    mode: "onBlur",
  });
  const password = form.watch("step_1.loginInformation.password");
  const confirmPassword = form.watch("step_1.loginInformation.passwordConfirmation");
  const email = form.watch("step_1.loginInformation.email");
  const confirmEmail = form.watch("step_1.loginInformation.emailConfirmation");

  const isNotValidConfirmPassword = password && confirmPassword && password !== confirmPassword;
  const isNotValidConfirmEmail = email && confirmEmail && email !== confirmEmail;

  const handleNextStep = async () => {
    const isValid = await form.trigger(["step_1"]);

    if (isValid && !isNotValidConfirmPassword && !isNotValidConfirmEmail) {
      setFormStep(2);
      return;
    }

    const fieldsFromFirstStep = Object.keys(form.getValues()).filter(
      (key) => key !== "discounts" && key !== "commissionTerms"
    );
    const errors = Object.keys(form.formState.errors).filter((key) => fieldsFromFirstStep.includes(key));

    if (!form.getValues("step_1.country")) {
      form.setFocus("step_1.country");
      return;
    }

    if (!errors.length && !isNotValidConfirmPassword && !isNotValidConfirmEmail) {
      setFormStep(2);
      return;
    }

    const firstError = Object.keys(form.formState.errors)[0] as keyof z.infer<typeof partnerRegistrationFormSchema>;
    form.setFocus(firstError);
    const errorRef = form.formState.errors[firstError]?.ref;
    if (errorRef && "current" in errorRef) {
      // @ts-expect-error - errorRef is a ref object
      errorRef?.current?.scrollIntoView();
    }
  };

  const { mutate, isPending } = useMutation({
    mutationFn: async ({ step_1, step_2 }: CreatePartnerParams) => {
      await createPartner({
        contract_file: step_1.contract_file,
        company: {
          name: step_1.companyName,
          industry_sector: step_1.industrySector,
          starting_date: new Date(step_1.startingDateOfCooperation).toISOString(),
          website: step_1.partnerOfferWebsite,
          description: step_1.description,
          owner_name: step_1.directorOrCeoName,
          country_code: step_1.country,
          // federal_state: step_1.federalState,
          city: step_1.city,
          zip_code: step_1.zipCode,
          street_and_number: step_1.streetAndNumber,
          additional_address_line: step_1.additionalAddressLine,
          contact_name: step_1.contact.name,
          contact_email: step_1.contact.email,
          contact_phone: step_1.contact.phone,
        },
        partner_email: step_1.loginInformation.email,
        partner_password: step_1.loginInformation.password,
        partner_firstname: step_1.directorOrCeoName.split(" ")[0],
        partner_lastname: step_1.directorOrCeoName.split(" ")[1],
        no_provision_negotiated: step_1.noProvisionNegotiated,
        payout_cycle: step_2.commissionTerms?.payoutCicle,
        commission_mode: step_2.commissionTerms?.commissionMode,
        coupons: step_2.discounts?.map((discount) => Number(discount.id)) ?? undefined,
        marketing_material_id: step_1.marketingMaterial?.id || undefined,
        ...(step_1.marketingMaterial?.isNewMarketingMaterial && {
          new_marketing_material_name: step_1.marketingMaterial.name as string,
          new_marketing_material_files: step_1.marketingMaterial.files,
        }),
      });
    },
    onSuccess: () => {
      enqueueSnackbar("Partner created successfully", { variant: "success" });
      queryClient.invalidateQueries({ queryKey: ["partners"] });
      router.push("/partner-hub");
    },
    // eslint-disable-next-line
    onError: (error: any) => {
      // TODO: Remove, it could log sensitive data in production
      // eslint-disable-next-line no-console
      console.error(error);
      enqueueSnackbar(error?.response?.data?.message || "Failed to create partner", { variant: "error" });

      if (error?.status === 409) {
        setFormStep(1);
        form.setError("step_1.loginInformation.email", { message: "Email already used" });
      }
    },
  });

  function onSubmit(data: CreatePartnerParams) {
    mutate(data);
  }

  return (
    <FormProvider {...form}>
      <ModuleContent containerClassName="bg-white">
        <div className="flex items-start justify-between">
          <ModuleTitle
            title="Register Partner"
            description="Upload the draft of the cooperation agreement, set the login information and attach marketing materials."
          />
        </div>
        {formStep === 1 && <PartnerRegistrationStep1 onNextStep={handleNextStep} />}
        {formStep === 2 && (
          <>
            <PartnerRegistrationStep2 onBack={() => setFormStep(1)} />
            <div className="flex items-center w-full justify-end gap-6">
              <Button
                type="button"
                color="dark-blue"
                size="medium"
                variant="outlined"
                onClick={() => {
                  setFormStep(1);
                }}
              >
                Cancel
              </Button>
              <Button
                type="button"
                color="yellow"
                size="medium"
                variant="filled"
                disabled={isPending}
                {...(isPending && {
                  leadingIcon: <Loader2Icon className="animate-spin" />,
                })}
                onClick={async () => {
                  const isValid = await form.trigger();
                  if (isValid) {
                    onSubmit(form.getValues());
                  }
                }}
              >
                Create Partner
              </Button>
            </div>
          </>
        )}
      </ModuleContent>
    </FormProvider>
  );
}
