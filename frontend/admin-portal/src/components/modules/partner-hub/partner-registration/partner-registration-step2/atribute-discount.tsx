"use client";

import { AutoComplete } from "@/components/common/autocomplete";
import { Divider } from "@/components/common/divider";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { CheckboxInput } from "@/components/ui/checkbox";
import { getAllCoupons } from "@/lib/api/coupon";
import { Coupon } from "@/lib/api/coupon/types";
import { cn } from "@/utils/cn";
import { dateManager } from "@/utils/date-manager";
import { formatCurrency } from "@/utils/format-currency";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, AdsClick, Delete, LocalOffer } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { CreatePartnerParams } from "..";
import { NewDiscountModal } from "../new-discount-modal";

export function AtributeDiscount() {
  const form = useFormContext<CreatePartnerParams>();
  const [searchCode, setSearchCode] = useState<string>("");
  const [currentSelectedCoupon, setCurrentSelectedCoupon] = useState<string>("");
  const [discountModalOpen, setDiscountModalOpen] = useState<boolean>(false);

  const { data: coupons, isLoading: isLoadingCoupons } = useQuery({
    queryKey: ["coupons", searchCode],
    queryFn: async () => {
      const response = await getAllCoupons({ code: searchCode });
      return response.coupons.map((coupon) => ({
        label: coupon.code,
        value: coupon.id.toString(),
        coupon,
      }));
    },
  });

  return (
    <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10 my-12">
      <p className="text-2xl font-bold text-primary">Atribute Discount</p>
      <AutoComplete
        items={coupons ?? []}
        onChange={setCurrentSelectedCoupon}
        onSearchValueChange={setSearchCode}
        searchValue={searchCode}
        value={currentSelectedCoupon}
        isLoading={isLoadingCoupons}
        placeholder="Search for discounts"
        hasError={!!form.formState?.errors?.step_2?.discounts}
      />
      {form.formState?.errors?.step_2?.discounts && <p className="text-error text-base">Required field</p>}
      <div className="flex items-center justify-end gap-2">
        <Button
          type="button"
          color="dark-blue"
          size="small"
          variant="outlined"
          onClick={() => {
            setCurrentSelectedCoupon("");
            setSearchCode("");
            form.setValue("step_2.discounts", []);
            form.clearErrors("step_2.discounts");
          }}
        >
          Cancel
        </Button>
        <Button
          type="button"
          color="dark-blue"
          size="small"
          variant="filled"
          onClick={() => {
            if (!currentSelectedCoupon) return;
            const coupon = coupons?.find((coupon) => coupon.value === currentSelectedCoupon);
            if (!coupon) return;
            const isAlreadyAdded = form
              .getValues("step_2.discounts")
              ?.some(
                (discount: { code: string; id: string; coupon?: { label?: string } }) => discount.code === coupon.label
              );
            if (isAlreadyAdded) return;
            form.setValue("step_2.discounts", [
              ...(form.getValues("step_2.discounts") ?? []),
              { id: coupon.value, code: coupon.label, coupon: coupon.coupon },
            ]);
            setCurrentSelectedCoupon("");
            setSearchCode("");
            form.clearErrors("step_2.discounts");
          }}
        >
          Apply Coupon
        </Button>
      </div>
      {form.watch("step_2.discounts")?.map((discount: { id: string; code: string; coupon?: Coupon }) => {
        const elegibleProducts = discount.coupon?.elegible_products
          ? (Object.keys(discount.coupon.elegible_products) as ["direct_license", "eu_license", "action_guide"])
          : undefined;

        return (
          <Accordion type="single" collapsible key={discount.id}>
            <AccordionItem value={discount.id} className="border-none">
              <AccordionTrigger
                className={cn(
                  "bg-white rounded-2xl px-4 w-full aria-expanded:rounded-t-2xl aria-expanded:rounded-b-none"
                )}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <span className="text-tonal-dark-green-30 text-base uppercase">{discount.code}</span>
                    <LocalOffer className="fill-tonal-dark-green-30 size-6" />
                    <AdsClick className="fill-tonal-dark-green-30 size-6" />
                  </div>
                  <Button
                    type="button"
                    color="light-blue"
                    size="iconMedium"
                    variant="text"
                    leadingIcon={<Delete className="fill-primary" />}
                    onClick={(e) => {
                      e.stopPropagation();
                      const formValue = form.getValues("step_2.discounts") ?? [];
                      form.setValue(
                        "step_2.discounts",
                        formValue.filter(
                          (d: { code: string; id: string; coupon?: { label?: string } }) => d.id !== discount.id
                        )
                      );
                    }}
                  />
                </div>
              </AccordionTrigger>
              <AccordionContent className="bg-white rounded-b-2xl px-4">
                <Divider initialMarginDisabled className="mb-4" />
                <div className="flex flex-col gap-4">
                  <div className="flex items-center gap-2">
                    <p className="text-primary font-medium text-base">Period:</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-tonal-dark-cream-20 font-medium text-base">Starts:</p>
                    <p className="text-primary text-base">
                      {discount.coupon?.start_date ? dateManager(discount.coupon.start_date).format("DD.MM.YYYY") : "-"}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-tonal-dark-cream-20 font-medium text-base">End:</p>
                    <p className="text-primary text-base">
                      {discount.coupon?.end_date ? dateManager(discount.coupon.end_date).format("DD.MM.YYYY") : "-"}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-primary font-medium text-base">Parameters:</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-tonal-dark-cream-20 font-medium text-base">Maximum order value:</p>
                    <p className="text-primary text-base">
                      {discount.coupon?.max_amount ? formatCurrency(discount.coupon.max_amount) : "-"}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-tonal-dark-cream-20 font-medium text-base">Minimum order value:</p>
                    <p className="text-primary text-base">
                      {discount.coupon?.min_amount ? formatCurrency(discount.coupon.min_amount) : "-"}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-tonal-dark-cream-20 font-medium text-base">Number of vouchers:</p>
                    <p className="text-primary text-base">
                      {discount.coupon?.max_uses ? discount.coupon.max_uses : "-"}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-tonal-dark-cream-20 font-medium text-base">Commission amount:</p>
                    <p className="text-primary text-base">
                      {discount.coupon?.commission_percentage ? `${discount.coupon.commission_percentage}%` : "-"}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-primary text-base">Products Included:</p>
                  </div>
                  <div className="flex flex-col gap-2">
                    <CheckboxInput
                      label="EU complaince service licesing"
                      checked={!elegibleProducts ? true : elegibleProducts.includes("eu_license")}
                      disabled
                    />
                    <CheckboxInput
                      label="DE direct licensing"
                      checked={!elegibleProducts ? true : elegibleProducts.includes("direct_license")}
                      disabled
                    />
                    <CheckboxInput
                      label="Action Guide"
                      checked={!elegibleProducts ? true : elegibleProducts.includes("action_guide")}
                      disabled
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        );
      })}

      <div className="flex items-center gap-2">
        <NewDiscountModal open={discountModalOpen} onOpenChange={setDiscountModalOpen}>
          <Button type="button" color="light-blue" size="medium" variant="text" leadingIcon={<Add />}>
            New Discount
          </Button>
        </NewDiscountModal>
      </div>
    </div>
  );
}
