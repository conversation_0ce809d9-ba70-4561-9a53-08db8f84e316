"use client";

import { useEffect, useState } from "react";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { z } from "zod";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { FractionIcon } from "@/components/ui/fraction-icon";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { queryClient } from "@/lib/react-query";

const editOrderFormSchema = z.object({
  fractions: z.array(
    z.object({
      code: z.string(),
      name: z.string(),
      value: z.coerce.number({
        invalid_type_error: "The field value must be a number",
      }),
    })
  ),
});

type EditOrderFormData = z.infer<typeof editOrderFormSchema>;

interface EditOrderDialogProps {
  orderId: string | number;
  children: React.ReactNode;
}

export function EditOrderDialog({ orderId, children }: EditOrderDialogProps) {
  const [isOpen, setIsOpen] = useState(false);

  const {
    data: order,
    refetch,
    status: queryStatus,
  } = useQuery({
    queryKey: ["order", orderId],
    queryFn: async () => {
      await new Promise((resolve) => setTimeout(resolve, 3000));
      return {
        id: "1001",
        status: "OPEN",
        total_price: 150659 * 100, // in cents
        certificate: false,
        broker_number: "B-1000000",
        broker_name: "Techcent",
        customer_number: "C-100021",
        company_name: "Lauter & Co.",
        transfer_date: "2023-02-25",
        order_number: "O-1000365",
        register_number: "DE1714028249764",
        year: 2023,
        fractions: [
          {
            id: 1,
            code: "alu",
            name: "Aluminium",
            value: 56,
            fraction_icon: "/assets/svg/aluminium.svg",
          },
          {
            id: 2,
            code: "glass",
            name: "Glass",
            value: 56,
            fraction_icon: "/assets/svg/aluminium.svg",
          },
          {
            id: 3,
            code: "composite",
            name: "Composite beverage cartoons",
            value: 56,
            fraction_icon: "/assets/svg/aluminium.svg",
          },
          {
            id: 4,
            code: "plastic",
            name: "Plastic",
            value: 56,
            fraction_icon: "/assets/svg/aluminium.svg",
          },
          {
            id: 5,
            code: "paper",
            name: "Paper / Paperboard / Cardboard",
            value: 56,
            fraction_icon: "/assets/svg/aluminium.svg",
          },
          {
            id: 6,
            code: "other",
            name: "Other composite packaging",
            value: 56,
            fraction_icon: "/assets/svg/aluminium.svg",
          },
          {
            id: 7,
            code: "metals",
            name: "Ferrous metals",
            value: 56,
            fraction_icon: "/assets/svg/aluminium.svg",
          },
        ],
        created_at: "2023-01-01",
      };
    },
    enabled: isOpen && !!orderId,
  });

  const { mutateAsync } = useMutation({
    mutationFn: (data: EditOrderFormData) => {
      // return updateOrder(orderId, data);

      // eslint-disable-next-line no-console
      console.log(data);
      return new Promise((resolve) => setTimeout(resolve, 3000));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["orders"] });
      setIsOpen(false);
    },
    onError: (error) => enqueueSnackbar(error.message, { variant: "error" }),
  });

  const defaultValues: Partial<EditOrderFormData> = {
    fractions: order?.fractions.map((fraction) => ({
      code: fraction.code,
      name: fraction.name,
      value: fraction.value ?? 0,
    })),
  };

  const { formState, ...form } = useForm<EditOrderFormData>({
    resolver: zodResolver(editOrderFormSchema),
    defaultValues,
  });

  const { fields } = useFieldArray({ control: form.control, name: "fractions" });

  useEffect(() => {
    if (orderId) refetch();

    if (!orderId) return;

    form.reset(defaultValues);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderId, order]);

  async function handleSubmit(data: EditOrderFormData) {
    await mutateAsync(data);
  }

  const hasErrors = !!formState.errors.fractions;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      {queryStatus === "success" && (
        <DialogContent className="bg-surface-01 py-9 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="font-bold sm:text-[1.75rem] text-support-blue">Edit Order</DialogTitle>
            <DialogDescription> </DialogDescription>
          </DialogHeader>
          <div className="my-6 w-full grid md:grid-cols-2 gap-y-6 gap-x-8">
            <div className="flex flex-col items-start gap-2">
              <p className="text-primary">Broker Name</p>
              <span className="text-tonal-dark-cream-10">{order.broker_name}</span>
              <span className="text-tonal-dark-cream-40 text-sm">{order.broker_number}</span>
            </div>
            <div className="flex flex-col items-start gap-2">
              <p className="text-primary">Order number</p>
              <span className="text-tonal-dark-cream-10">{order.order_number}</span>
            </div>
            <div className="flex flex-col items-start gap-2">
              <p className="text-primary">Company</p>
              <span className="text-tonal-dark-cream-10">{order.company_name}</span>
            </div>
            <div className="flex flex-col items-start gap-2">
              <p className="text-primary">Register number</p>
              <span className="text-tonal-dark-cream-10">{order.register_number}</span>
            </div>
          </div>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="w-full space-y-6">
            <ul className="bg-background w-full flex flex-col rounded-[20px]">
              {fields?.map((field, index) => (
                <li
                  key={field.id}
                  className="w-full flex items-center py-3.5 px-5 border-b last:border-b-0 border-b-tonal-dark-cream-80"
                >
                  <FractionIcon size="small" iconUrl="/assets/svg/aluminium.svg" className="bg-transparent" />
                  <p className="ml-3 font-bold text-base text-primary flex-1">{field.name}</p>
                  <Controller
                    name={`fractions.${index}.value`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <div className="mx-4 flex w-full max-w-40">
                        <Input
                          placeholder="0.00"
                          variant={error ? "error" : "default"}
                          // errorMessage={error?.message}
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </div>
                    )}
                  />
                  <span className="text-primary">kg</span>
                </li>
              ))}
            </ul>
            {hasErrors && <p className="text-error text-right">You must enter valid characters to save.</p>}
            <DialogFooter>
              <Button
                type="submit"
                variant="filled"
                color={hasErrors ? "red" : "yellow"}
                size="medium"
                disabled={formState.isSubmitting}
              >
                {formState.isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      )}
    </Dialog>
  );
}
