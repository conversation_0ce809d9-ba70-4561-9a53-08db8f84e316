"use client";

import { FullReportSet } from "@/types/service-setup";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { useFormContext } from "react-hook-form";
import { ReportSetDelete } from "../components/report-set-delete";
import { ReportSetFormData } from "../components/report-set-form-provider";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { ReportSetFractions } from "../components/report-set-fractions";
import { Tabs, TabsList, TabsContent, TabsTrigger } from "@/components/ui/tabs";
import { ReportSetPriceLists } from "../components/report-set-price-lists";
import { Error } from "@interzero/oneepr-react-ui/Icon";
import { useMutationState } from "@tanstack/react-query";
import { ReportSetSheetFile } from "../components/report-set-sheet-file";

interface ReportSetExcelFormProps {
  countryCode: string;
  reportSet: FullReportSet;
}

export function ReportSetExcelForm({ reportSet }: ReportSetExcelFormProps) {
  const {
    register,
    formState: { errors },
  } = useFormContext<ReportSetFormData>();

  const updateReportSetMutation = useMutationState({
    filters: {
      mutationKey: ["update-report-set", reportSet.id],
      status: "pending",
    },
  });

  const isSubmitting = !!updateReportSetMutation.length;

  return (
    <div className="mt-6 space-y-10">
      <div className="bg-background rounded-[20px] p-8 space-y-6">
        <div className="flex items-center justify-between">
          <p className="text-primary text-lg font-bold">New report set</p>
          <ReportSetDelete reportSetId={reportSet.id} />
        </div>
        <div className="w-full">
          <Input
            label="Name of the Report Set*"
            placeholder="Name for the Report Set"
            {...register("name")}
            variant={errors.name ? "error" : "default"}
            errorMessage={errors.name?.message}
          />
        </div>
      </div>
      <h3 className="text-primary text-3xl font-bold">1° Step: Upload excel file</h3>
      <ReportSetSheetFile key={reportSet.sheet_file?.id} sheetFile={reportSet.sheet_file} />
      <h3 className="text-primary text-3xl font-bold">2° Step: Set calculator fractions for this service</h3>
      <div className="bg-background rounded-[20px] py-6 px-5 space-y-8 w-full">
        <Tabs defaultValue="fractions" className="w-full">
          <TabsList className="pt-6 pb-4">
            <TabsTrigger value="fractions">
              {errors.fractions && <Error className="fill-error size-4 mr-2" />}
              Fractions
            </TabsTrigger>
            <TabsTrigger value="price-lists">
              {errors.price_lists && <Error className="fill-error size-4 mr-2" />}
              Dual System Fraction Pricing
            </TabsTrigger>
          </TabsList>
          <TabsContent value="fractions">
            <ReportSetFractions />
          </TabsContent>
          <TabsContent value="price-lists">
            <ReportSetPriceLists />
          </TabsContent>
        </Tabs>
      </div>
      <div className="w-full flex items-center justify-end">
        <Button
          type="submit"
          variant="filled"
          color={!!Object.keys(errors).length ? "red" : "yellow"}
          size="large"
          className="w-60"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Saving..." : "Save"}
        </Button>
      </div>
    </div>
  );
}
