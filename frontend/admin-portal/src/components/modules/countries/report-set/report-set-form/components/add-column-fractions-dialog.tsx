import { CheckboxInput } from "@/components/ui/checkbox";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add } from "@interzero/oneepr-react-ui/Icon";
import { useState } from "react";
import { Controller, useForm, useFormContext, useWatch } from "react-hook-form";
import { z } from "zod";
import { ReportSetFormData } from "./report-set-form-provider";

interface AddColumnFractionsDialogProps {
  columnCode: string;
  onAdd: (data: string[]) => void;
}

const addColumnFractionsFormSchema = z.object({
  fractions: z.record(z.string(), z.boolean()),
});

export type AddColumnFractionsFormData = z.infer<typeof addColumnFractionsFormSchema>;

export function AddColumnFractionsDialog({ columnCode, onAdd }: AddColumnFractionsDialogProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const generalForm = useFormContext<ReportSetFormData>();

  const foundColumn = findColumnByCode(columnCode);

  const defaultValues = {
    fractions: (generalForm.getValues("columns")?.find((column) => column.code === columnCode)?.fractions || []).reduce(
      (acc, columnFraction) => {
        acc[columnFraction.fraction_code] = true;
        return acc;
      },
      {} as AddColumnFractionsFormData["fractions"]
    ),
  };

  const { handleSubmit, reset, control } = useForm<AddColumnFractionsFormData>({
    defaultValues,
  });

  function findColumnByCode(code: string) {
    const columns = generalForm.getValues("columns");

    if (!columns) return null;

    // Only search for second level columns as only they can have fractions
    for (const column of columns) {
      const foundChild = column.children.find((child) => child.code === code);

      if (!foundChild) continue;

      return {
        column: foundChild,
        parent: column,
      };
    }

    return null;
  }

  function handleFormSubmit({ fractions }: AddColumnFractionsFormData) {
    const formattedData = Object.entries(fractions)
      .filter(([, isChecked]) => isChecked)
      .map(([fractionCode]) => fractionCode);

    onAdd(formattedData);
    setIsDialogOpen(false);
  }

  async function handleDialogOpenChange(open: boolean) {
    if (!open) {
      reset();
    }

    setIsDialogOpen(open);
  }

  const fractions = useWatch({ control: generalForm.control, name: "fractions" });

  if (!foundColumn) return null;

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <Button type="button" variant="text" color="light-blue" size="small" leadingIcon={<Add />}>
          Select fractions
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-surface-03 py-9 max-w-xl">
        <DialogHeader>
          <DialogTitle>Select Fractions (level 2)</DialogTitle>
          <DialogDescription>Select fractions available for this column</DialogDescription>
        </DialogHeader>
        <div className="my-6">
          <p className="text-primary font-bold text-xl">Fractions Available</p>
        </div>
        <div className="flex flex-col gap-6 w-full bg-background p-6 rounded-4xl">
          {!!fractions.length &&
            fractions.map((firstLevelFraction) => (
              <div key={firstLevelFraction.code} className="space-y-5">
                <p className="text-primary font-bold">{firstLevelFraction.name} level 1</p>
                <div className="space-y-2">
                  {!firstLevelFraction.has_second_level && (
                    <Controller
                      key={firstLevelFraction.code}
                      control={control}
                      name={`fractions.${firstLevelFraction.code}`}
                      defaultValue={
                        !!foundColumn.column.fractions.find((f) => f.fraction_code === firstLevelFraction.code)
                      }
                      render={({ field }) => (
                        <CheckboxInput
                          id={firstLevelFraction.code}
                          onChange={(e) => field.onChange(e.target.checked)}
                          defaultChecked={field.value}
                          label={`${firstLevelFraction.name}`}
                        />
                      )}
                    />
                  )}
                  {firstLevelFraction.has_second_level &&
                    !firstLevelFraction.has_third_level &&
                    firstLevelFraction.children.map((secondLevelFraction) => (
                      <Controller
                        key={secondLevelFraction.code}
                        control={control}
                        name={`fractions.${secondLevelFraction.code}`}
                        defaultValue={
                          !!foundColumn.column.fractions.find((f) => f.fraction_code === secondLevelFraction.code)
                        }
                        render={({ field }) => (
                          <CheckboxInput
                            id={secondLevelFraction.code}
                            onChange={(e) => field.onChange(e.target.checked)}
                            defaultChecked={field.value}
                            label={`${secondLevelFraction.name}`}
                          />
                        )}
                      />
                    ))}
                  {firstLevelFraction.has_second_level &&
                    firstLevelFraction.has_third_level &&
                    firstLevelFraction.children.map((secondLevelFraction) =>
                      secondLevelFraction.children.map((thirdLevelFraction) => (
                        <Controller
                          key={thirdLevelFraction.code}
                          control={control}
                          name={`fractions.${thirdLevelFraction.code}`}
                          defaultValue={
                            !!foundColumn.column.fractions.find((f) => f.fraction_code === thirdLevelFraction.code)
                          }
                          render={({ field }) => (
                            <CheckboxInput
                              id={thirdLevelFraction.code}
                              onChange={(e) => field.onChange(e.target.checked)}
                              defaultChecked={field.value}
                              label={`${secondLevelFraction.name} / ${thirdLevelFraction.name}`}
                            />
                          )}
                        />
                      ))
                    )}
                </div>
              </div>
            ))}
          <div className="flex items-center justify-end">
            <Button
              type="button"
              variant="filled"
              size="medium"
              color="yellow"
              onClick={() => handleSubmit(handleFormSubmit)()}
              disabled={!fractions.length}
            >
              Save Fractions
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
