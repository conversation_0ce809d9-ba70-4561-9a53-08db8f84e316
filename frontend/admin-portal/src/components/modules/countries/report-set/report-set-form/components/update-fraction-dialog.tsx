import { CheckboxInput } from "@/components/ui/checkbox";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FractionIcon } from "@/components/ui/fraction-icon";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowRight, RefreshCcw } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { FormFraction } from "./report-set-form-provider";
import { AddFractionIconSchema, ReportSetFractionIcons } from "./report-set-fraction-icons";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";

interface UpdateFractionDialogProps {
  fraction: FormFraction;
  onUpdate: (data: UpdateFractionFormData) => void;
}

const updateFractionFormSchema = z.object({
  name: z
    .string({ message: "Fraction name is required" })
    .min(1, { message: "Fraction name is required" })
    .regex(SPECIAL_CHARS_REGEX, "Invalid name"),
  description: z
    .string()
    .refine((val) => val === "" || SPECIAL_CHARS_REGEX.test(val), "Invalid description")
    .optional()
    .default(""),
  icon: z.string().default("plastics"),
  fraction_icon_id: z.number({ message: "Fraction icon is required" }),
  fraction_icon_image_url: z.string().optional().default(""),
  is_active: z.boolean().default(true),
});

export type UpdateFractionFormData = z.infer<typeof updateFractionFormSchema>;

export function UpdateFractionDialog({ fraction, onUpdate }: UpdateFractionDialogProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isFractionIconsOpen, setIsFractionIconsOpen] = useState(false);

  const defaultValues = {
    ...fraction,
    fraction_icon_id: fraction.fraction_icon.id,
    fraction_icon_image_url: fraction.fraction_icon.image_url,
  };

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
    control,
  } = useForm<UpdateFractionFormData>({
    resolver: zodResolver(updateFractionFormSchema),
    defaultValues,
  });

  useEffect(() => {
    reset(defaultValues);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDialogOpen]);

  function handleFormSubmit(data: UpdateFractionFormData) {
    onUpdate(data);
    setIsDialogOpen(false);
  }

  async function handleDialogOpenChange(open: boolean) {
    if (!open) {
      setIsFractionIconsOpen(false);
    }

    setIsDialogOpen(open);
  }

  function handleAddFractionIcon({ fraction_icon_id, fraction_icon_image_url }: AddFractionIconSchema) {
    if (!fraction_icon_id) return;

    setValue("fraction_icon_id", fraction_icon_id);
    setValue("fraction_icon_image_url", fraction_icon_image_url);
    setIsFractionIconsOpen(false);
  }

  const fractionIconId = useWatch({ control, name: "fraction_icon_id" });
  const fractionIconImageUrl = useWatch({ control, name: "fraction_icon_image_url" });

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <Button type="button" variant="text" color="light-blue" size="small">
          Edit
        </Button>
      </DialogTrigger>
      <DialogContent className="px-8 max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Update fraction (level 01)</DialogTitle>
          <DialogDescription>
            {!isFractionIconsOpen ? "Update fraction (level 01) informations" : "Select or upload image"}
          </DialogDescription>
        </DialogHeader>
        {isFractionIconsOpen && (
          <ReportSetFractionIcons
            key={fractionIconId}
            onClose={() => setIsFractionIconsOpen(false)}
            onAddFractionIcon={handleAddFractionIcon}
            defaultFractionIconId={fractionIconId}
          />
        )}
        {!isFractionIconsOpen && (
          <div id="add-new-information-form" className="w-full space-y-10">
            <div className="w-full space-y-6">
              <div className="flex items-center gap-3">
                <CheckboxInput {...register("is_active")} />
                <div
                  data-error={!!errors.fraction_icon_id}
                  onClick={() => setIsFractionIconsOpen(true)}
                  className="rounded-lg cursor-pointer hover:opacity-80 border-[1.5px] border-transparent data-[error=true]:border-error"
                >
                  <div className="relative size-16 overflow-hidden flex-none cursor-pointer border-[1.5px] border-transparent hover:border-primary data-[selected=true]:border-primary rounded-md transition-all duration-100">
                    <FractionIcon iconUrl={fractionIconImageUrl} size="large" />
                    <div className="z-10 absolute bottom-1 right-1 rounded-full flex items-center justify-center">
                      <RefreshCcw className="size-3 stroke-support-blue" />
                    </div>
                  </div>
                </div>
                <Input placeholder="Fraction name" {...register("name")} variant={errors.name ? "error" : "default"} />
              </div>
              <Textarea
                label="Description"
                placeholder="Description"
                {...register("description")}
                errorMessage={errors.description?.message}
                rows={3}
              />
            </div>
            <div className="flex flex-col mt-8">
              <div className="flex items-center justify-end">
                <Button
                  form="add-new-information-form"
                  type="submit"
                  variant="filled"
                  color="yellow"
                  size="medium"
                  trailingIcon={<ArrowRight />}
                  onClick={() => handleSubmit(handleFormSubmit)()}
                >
                  Save
                </Button>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
