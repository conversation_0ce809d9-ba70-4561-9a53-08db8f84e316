"use client";

import { CheckCircle } from "@interzero/oneepr-react-ui/Icon";
import { AlertCircle } from "lucide-react";

import { ReportSetsForm } from "./report-sets-form";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { getServiceSetupPackagingServices, getServiceSetupReportSets } from "@/lib/api/service-setups";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { ReportSet } from "@/types/service-setup/report-set";

interface ServiceSetupReportSetsProps {}

export function isReportSetsComplete(packagingServices: PackagingService[], reportSets: ReportSet[]) {
  try {
    if (!packagingServices?.length) throw new Error();

    if (!reportSets?.length) throw new Error();

    packagingServices.forEach((packagingService) => {
      const packagingServiceReportSets = reportSets?.filter(
        (reportSet) => reportSet.packaging_service_id === packagingService.id
      );

      if (!packagingServiceReportSets?.length) throw new Error();

      if (packagingServiceReportSets?.length === 1) return;

      if (!packagingService.has_report_set_criteria) throw new Error();

      return;
    });

    return true;
  } catch {
    return false;
  }
}

export function ServiceSetupReportSets({}: ServiceSetupReportSetsProps) {
  const { paramValues, changeParam, deleteAllParams } = useQueryFilter(["step"]);

  const isSelected = paramValues.step === "report-sets";

  function handleOpenStep() {
    changeParam("step", "report-sets");
  }

  function handleCloseStep() {
    deleteAllParams();
  }

  const { country } = useServiceSetup();

  const { data: reportSets, isFetching } = useQuery({
    queryKey: ["service-setup-report-sets", country.code],
    queryFn: () => getServiceSetupReportSets(country.code),
  });

  const { data: packagingServices } = useQuery({
    queryKey: ["service-setup-packaging-services", country.code],
    queryFn: () => getServiceSetupPackagingServices(country.code),
  });

  const isComplete = isReportSetsComplete(packagingServices || [], reportSets || []);

  if (isFetching) {
    return (
      <div className="bg-background rounded-[20px] p-8 space-y-2" id="report-sets-step">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    );
  }

  return (
    <div
      data-selected={isSelected}
      data-complete={isComplete}
      className="group bg-background rounded-[20px] p-8 cursor-pointer data-[selected=true]:cursor-default"
      onClick={() => !isSelected && handleOpenStep()}
      id="report-sets-step"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-primary text-2xl font-bold">3. Fraction sets</h3>
        {!isSelected && isComplete && <CheckCircle className="size-6 fill-success transition-all duration-300" />}
        {!isComplete && <AlertCircle className="size-6 fill-error transition-all duration-300" />}
      </div>
      {isSelected && (
        <ReportSetsForm
          packagingServices={packagingServices || []}
          reportSets={reportSets || []}
          onCloseStep={handleCloseStep}
        />
      )}
    </div>
  );
}
