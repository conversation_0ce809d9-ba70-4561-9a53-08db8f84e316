import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Textarea } from "@/components/ui/textarea";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { deleteRequiredInformation, updateRequiredInformation } from "@/lib/api/required-information";
import { queryClient } from "@/lib/react-query";
import { RequiredInformation } from "@/types/service-setup/required-information";
import { downloadFile } from "@/utils/download-file";
import { formatFileSize } from "@/utils/format-file-size";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Delete, Download, Visibility, VisibilityOff } from "@interzero/oneepr-react-ui/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import { CornerDownRight } from "lucide-react";
import { useSession } from "next-auth/react";
import { useEffect } from "react";
import { FormProvider, useFieldArray, useForm, useFormContext, useWatch } from "react-hook-form";
import { z } from "zod";
import { REQUIRED_INFORMATION_TYPES } from "..";
import { CriteriasButton } from "../../criterias-drawer/criterias-button";
import { AddNewInformationDialog } from "../add-new-information-dialog";
import { enqueueSnackbar } from "notistack";
import { useMutation } from "@tanstack/react-query";
import { CgSpinnerAlt } from "react-icons/cg";

const requiredInformationsFormSchema = z
  .object({
    requiredInformations: z.array(
      z.object({
        id: z.number(),
        name: z.string().min(1, "Name is required"),
        description: z.string().default(""),
        type: z.enum(["TEXT", "NUMBER", "DOCUMENT", "FILE", "IMAGE"]),
        description_visible: z.boolean().default(false),
        file_id: z.string().nullable(),
        file: z
          .object({
            id: z.string(),
            name: z.string(),
            original_name: z.string(),
            size: z.string(),
          })
          .nullable(),
        has_criteria: z.boolean().optional(),
      })
    ),
  })
  .superRefine((data, ctx) => {
    data.requiredInformations.forEach((requiredInformation, index) => {
      if (["DOCUMENT", "FILE", "IMAGE"].includes(requiredInformation.type) && !requiredInformation.file) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "File is required",
          path: ["requiredInformations", index, "file"],
        });
      }
    });
  });

type RequiredInformationsFormData = z.infer<typeof requiredInformationsFormSchema>;

interface RequiredInformationsFormProps {
  requiredInformations: RequiredInformation[];
  onCloseStep: () => void;
}

export function RequiredInformationsForm({ requiredInformations, onCloseStep }: RequiredInformationsFormProps) {
  const { country } = useServiceSetup();

  const methods = useForm<RequiredInformationsFormData>({
    resolver: zodResolver(requiredInformationsFormSchema),
    defaultValues: {
      requiredInformations,
    },
  });

  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
    reset,
  } = methods;

  useEffect(() => {
    reset({ requiredInformations });

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requiredInformations]);

  const { fields: requiredInformationsFields, remove } = useFieldArray({
    control,
    name: "requiredInformations",
    keyName: "key",
  });

  async function handleFormSubmit(data: RequiredInformationsFormData) {
    try {
      if (!data.requiredInformations.length) return;

      const promises = data.requiredInformations.map(async (requiredInformation) => {
        updateRequiredInformation(requiredInformation.id, {
          description: requiredInformation.description,
          file_id: requiredInformation.file_id || null,
        });
      });

      await Promise.allSettled(promises);

      queryClient.invalidateQueries({ queryKey: ["service-setup-required-informations", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });

      enqueueSnackbar("Required informations saved successfully", { variant: "success" });
      onCloseStep();
    } catch {
      enqueueSnackbar("Failed to save required information", { variant: "error" });
    }
  }

  return (
    <FormProvider {...methods}>
      <form id="required-informations-form" onSubmit={handleSubmit(handleFormSubmit)} className="mt-6 space-y-6">
        {requiredInformationsFields.map((field, fieldIndex) => (
          <RequiredInformationItem
            key={field.key}
            requiredInformation={field}
            fieldIndex={fieldIndex}
            onRemove={() => remove(fieldIndex)}
          />
        ))}
        <div className="space-y-6">
          <AddNewInformationDialog />
          <div className="flex items-center justify-end gap-10">
            <Button type="button" onClick={() => onCloseStep()} variant="text" color="dark-blue" size="medium">
              Cancel
            </Button>
            <Button
              form="required-informations-form"
              type="submit"
              variant="filled"
              color={!!Object.keys(methods.formState.errors).length ? "red" : "dark-blue"}
              size="medium"
              className="w-60"
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      </form>
    </FormProvider>
  );
}

interface RequiredInformationItemProps {
  requiredInformation: RequiredInformationsFormData["requiredInformations"][number];
  fieldIndex: number;
  onRemove: (index: number) => void;
}

function RequiredInformationItem({ requiredInformation, fieldIndex, onRemove }: RequiredInformationItemProps) {
  const {
    register,
    formState: { errors },
    control,
  } = useFormContext<RequiredInformationsFormData>();
  const { country } = useServiceSetup();
  const { data: session } = useSession();

  async function handleDeleteRequiredInformation(index: number) {
    if (!requiredInformation.id) {
      onRemove(index);
      return;
    }

    try {
      await deleteRequiredInformation(requiredInformation.id);

      onRemove(index);

      queryClient.invalidateQueries({ queryKey: ["service-setup", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
      enqueueSnackbar("Required information deleted successfully", { variant: "success" });
    } catch {
      enqueueSnackbar("Failed to delete required information", { variant: "error" });
    }
  }

  const downloadFileMutation = useMutation({
    mutationFn: async () => {
      if (!requiredInformation.file) return;

      await downloadFile({
        userId: session?.user.id,
        userRole: session?.user.role,
        fileId: requiredInformation.file.id,
        fileName: requiredInformation.file.original_name,
      });
    },
  });

  const deleteFileMutation = useMutation({
    mutationFn: async () => {
      if (!requiredInformation.file) return;

      return updateRequiredInformation(requiredInformation.id, {
        file_id: null,
      });
    },
  });

  async function handleDownloadFile() {
    if (!session) return;

    downloadFileMutation.mutate(undefined, {
      onSuccess: () => {
        enqueueSnackbar("File downloaded successfully", { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar("Failed to download file", { variant: "error" });
      },
    });
  }

  async function handleDeleteFile() {
    if (!requiredInformation.id) return;

    deleteFileMutation.mutate(undefined, {
      onSuccess: () => {
        enqueueSnackbar("File deleted successfully", { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["service-setup-required-informations", country.code] });
        queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
      },
      onError: () => {
        enqueueSnackbar("Failed to delete file", { variant: "error" });
      },
    });
  }

  const descriptionVisible = useWatch({
    control,
    name: `requiredInformations.${fieldIndex}.description_visible`,
  });

  return (
    <div data-error={!!errors.requiredInformations?.[fieldIndex]} className="space-y-6 group">
      <input type="hidden" className="hidden" {...register(`requiredInformations.${fieldIndex}.id`)} />
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="h-10 w-10 flex items-center justify-center">
            {REQUIRED_INFORMATION_TYPES[requiredInformation.type].icon()}
          </div>
          <div>
            <p className="text-primary text-sm font-bold group-data-[error=true]:text-error">
              {requiredInformation.name}
            </p>
          </div>
        </div>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <button type="button" className="text-sm font-bold text-error hover:bg-error/30 rounded-full py-1 px-3">
              Remove Required Information
            </button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete service?</AlertDialogTitle>
              <AlertDialogDescription>
                By clicking on ”confirm” you are deleting this package and all content within it.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Back</AlertDialogCancel>
              <AlertDialogAction onClick={() => handleDeleteRequiredInformation(fieldIndex)}>Confirm</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
      {errors.requiredInformations?.[fieldIndex]?.file && (
        <p className="text-error text-sm">
          <span className="font-bold mr-1">{errors.requiredInformations?.[fieldIndex]?.file?.message}.</span>
          Please remove this information and create a new one.
        </p>
      )}
      {requiredInformation.file && (
        <div className="w-full flex flex-col gap-2">
          <div className="w-full flex items-center justify-between text-sm text-tonal-dark-cream-20 gap-4">
            <div className="w-[300px] overflow-hidden text-ellipsis whitespace-nowrap">
              {requiredInformation.file.original_name}
            </div>
            <div className="flex-none flex items-center justify-end gap-2">
              <div className="w-20 flex items-center justify-end">
                {formatFileSize(Number(requiredInformation.file.size))}
              </div>
              <div className="w-20 flex items-center justify-end">{new Date().toLocaleDateString()}</div>
              <Button
                type="button"
                variant="text"
                color="dark-blue"
                size="iconSmall"
                disabled={downloadFileMutation.isPending}
                trailingIcon={
                  downloadFileMutation.isPending ? <CgSpinnerAlt className="size-4 animate-spin" /> : <Download />
                }
                onClick={handleDownloadFile}
              />
              <Button
                type="button"
                variant="text"
                color="dark-blue"
                size="iconSmall"
                disabled={deleteFileMutation.isPending}
                trailingIcon={
                  deleteFileMutation.isPending ? (
                    <CgSpinnerAlt className="size-4 animate-spin" />
                  ) : (
                    <Delete className="fill-primary" />
                  )
                }
                onClick={handleDeleteFile}
              />
            </div>
          </div>
        </div>
      )}
      <div className="w-full space-y-6">
        <div className="flex items-center gap-4 pl-4">
          <CornerDownRight className="size-5 stroke-tonal-dark-cream-70" />
          <p className="text-tonal-dark-cream-40">Additional information</p>
          <label htmlFor={`requiredInformations.${fieldIndex}.description_visible`} className="cursor-pointer">
            <input
              type="checkbox"
              id={`requiredInformations.${fieldIndex}.description_visible`}
              className="hidden"
              {...register(`requiredInformations.${fieldIndex}.description_visible`)}
            />

            {descriptionVisible ? (
              <Visibility className="size-5 stroke-tonal-dark-cream-50" />
            ) : (
              <VisibilityOff className="size-5 stroke-tonal-dark-cream-50" />
            )}
          </label>
        </div>
        {descriptionVisible && (
          <div className="pl-10">
            <Textarea
              className="w-full"
              placeholder="Add details to your request"
              maxLength={350}
              rows={6}
              {...register(`requiredInformations.${fieldIndex}.description`)}
              errorMessage={errors.requiredInformations?.[fieldIndex]?.description?.message}
            />
          </div>
        )}
      </div>
      <CriteriasButton
        criteriaType="REQUIRED_INFORMATION"
        hasCriteria={!!requiredInformation.has_criteria}
        requiredInformationId={requiredInformation.id}
        criteriaText="Document Criteria"
      />
      <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
    </div>
  );
}
