import { useServiceSetup } from "@/hooks/use-service-setup";
import {
  getServiceSetupCriterias,
  getServiceSetupOtherCosts,
  getServiceSetupReportFrequencies,
  getServiceSetupReportSets,
} from "@/lib/api/service-setups";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery } from "@tanstack/react-query";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import { Criteria, CriteriaMode, CriteriaType } from "@/types/service-setup/criteria";
import { useEffect, useMemo } from "react";
import { createCriteria, updateCriteria } from "@/lib/api/criterias";
import { queryClient } from "@/lib/react-query";
import { z } from "zod";
import { CriteriaCard, CriteriaCardSkeleton, FormCriteria } from "./criteria-card";
import { ReportSet } from "@/types/service-setup/report-set";
import { ReportSetFrequency } from "@/types/service-setup/report-set-frequency";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";
import { enqueueSnackbar } from "notistack";
import { PreviewCommitmentDialog } from "@/components/common/preview/preview-commitment-dialog";
import { getPackagingService } from "@/lib/api/packaging-services";
import { CHARACTER_COUNTER_DEFAULTS } from "@/components/ui/character-counter";
import { Add } from "@interzero/oneepr-react-ui/Icon";

const PACKAGING_SERVICE_CRITERIA_OPTIONS = [
  { value: "OBLIGED", label: "Obliged to license" },
  { value: "NOT_OBLIGED", label: "Not obliged" },
];

const REQUIRED_INFORMATION_CRITERIA_OPTIONS = [
  { value: "REQUEST", label: "Request document" },
  { value: "NOT_REQUEST", label: "Not request document" },
];

export const CRITERIA_TYPES: {
  label: string;
  value: Criteria["type"];
  title: string;
  hasCalculatorCriteria: boolean;
  hasCommitmentCriteria: boolean;
}[] = [
  {
    label: "2. Obligation check",
    value: "PACKAGING_SERVICE",
    title: "Add Obligation Criteria",
    hasCalculatorCriteria: false,
    hasCommitmentCriteria: true,
  },
  {
    label: "3. Fraction sets",
    value: "REPORT_SET",
    title: "Add Set Criteria",
    hasCalculatorCriteria: false,
    hasCommitmentCriteria: true,
  },
  {
    label: "4. Reporting frequency",
    value: "REPORT_FREQUENCY",
    title: "Add Report Rhythm Criteria",
    hasCalculatorCriteria: false,
    hasCommitmentCriteria: true,
  },

  {
    label: "5. Third party costs",
    value: "OTHER_COST",
    title: "Add third party costs criteria",
    hasCalculatorCriteria: true,
    hasCommitmentCriteria: true,
  },
  {
    label: "6. Required information",
    value: "REQUIRED_INFORMATION",
    title: "Add Required Information Criteria",
    hasCalculatorCriteria: true,
    hasCommitmentCriteria: true,
  },
] as const;

export const CRITERIA_TYPE_VALUES = CRITERIA_TYPES.map((type) => type.value);

export const CRITERIA_INPUT_TYPES = [
  {
    value: "YES_NO",
    label: "Yes/No",
  },
  {
    value: "SELECT",
    label: "Select",
  },
] as const;

const CRITERIA_INPUT_TYPE_VALUES = CRITERIA_INPUT_TYPES.map((type) => type.value);

export const CRITERIA_CALCULATOR_TYPES = [
  {
    value: "LICENSE_FEES",
    label: "License Fees",
  },
  {
    value: "TOTAL_IN_TONS",
    label: "Total in Tons",
  },
  {
    value: "TOTAL_IN_KG",
    label: "Total in Kg",
  },
] as const;

const CRITERIA_CALCULATOR_TYPE_VALUES = CRITERIA_CALCULATOR_TYPES.map((type) => type.value);

export const criteriasFormSchema = z
  .object({
    criterias: z.array(
      z.object({
        id: z.coerce
          .number()
          .transform((value) => Number(value) || undefined)
          .optional(),
        type: z.enum([CRITERIA_TYPE_VALUES[0], ...CRITERIA_TYPE_VALUES]),
        mode: z.enum(["CALCULATOR", "COMMITMENT"]),
        title: z
          .string()
          .max(
            CHARACTER_COUNTER_DEFAULTS.MAX_TITLE_LENGTH,
            `Title must be ${CHARACTER_COUNTER_DEFAULTS.MAX_TITLE_LENGTH} characters or less`
          )
          .nullable(),
        help_text: z
          .string()
          .max(
            CHARACTER_COUNTER_DEFAULTS.MAX_HELP_TEXT_LENGTH,
            `Help text must be ${CHARACTER_COUNTER_DEFAULTS.MAX_HELP_TEXT_LENGTH} characters or less`
          )
          .nullable(),
        input_type: z.enum([CRITERIA_INPUT_TYPE_VALUES[0], ...CRITERIA_INPUT_TYPE_VALUES]).nullable(),
        calculator_type: z.enum([CRITERIA_CALCULATOR_TYPE_VALUES[0], ...CRITERIA_CALCULATOR_TYPE_VALUES]).nullable(),
        country_id: z.number(),
        packaging_service_id: z.number().nullable(),
        required_information_id: z.number().nullable(),
        options: z
          .array(
            z.object({
              id: z.coerce
                .number()
                .transform((value) => Number(value) || undefined)
                .optional(),
              option_value: z.string({ message: "Value is required" }).min(1, "Value is required"),
              option_to_value: z.string().nullable(),
              value: z.string({ message: "Value is required" }).min(1, "Value is required"),
            })
          )
          .min(1, "At least one option is required"),
      })
    ),
  })
  .superRefine((data, ctx) => {
    data.criterias.forEach((criteria, index) => {
      if (criteria.mode === "COMMITMENT") {
        if (!criteria.title) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Title is required",
            path: ["criterias", index, "title"],
          });
        }

        if (criteria.title && !SPECIAL_CHARS_REGEX.test(criteria.title)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Title can only contain letters, numbers, spaces and question marks",
            path: ["criterias", index, "title"],
          });
        }

        if (criteria.help_text && !SPECIAL_CHARS_REGEX.test(criteria.help_text)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Help text can only contain letters, numbers, spaces and question marks",
            path: ["criterias", index, "help_text"],
          });
        }

        if (!criteria.input_type) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Input type is required",
            path: ["criterias", index, "input_type"],
          });
        }
      }

      if (criteria.mode === "CALCULATOR") {
        if (!criteria.calculator_type) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Calculator type is required",
            path: ["criterias", index, "calculator_type"],
          });
        }

        criteria.options.forEach((option, optionIndex) => {
          const optionBefore = criteria.options[optionIndex - 1];

          if (
            optionBefore &&
            optionBefore.option_to_value &&
            Number(option.option_value) <= Number(optionBefore.option_to_value)
          ) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: "From value must be greater than previous value",
              path: ["criterias", index, "options", optionIndex, "option_value"],
            });
          }

          if (!option.option_to_value) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: "Value is required",
              path: ["criterias", index, "options", optionIndex, "option_to_value"],
            });
          }

          if (option.option_to_value && Number(option.option_to_value) <= Number(option.option_value)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: "To value must be greater than from value",
              path: ["criterias", index, "options", optionIndex, "option_to_value"],
            });
          }
        });
      }
    });
  });

export type CriteriasFormData = z.infer<typeof criteriasFormSchema>;

interface CriteriasFormProps {
  type: CriteriaType;
  packagingServiceId?: number;
  requiredInformationId?: number;
}

export function CriteriasForm({ type, packagingServiceId, requiredInformationId }: CriteriasFormProps) {
  const { country } = useServiceSetup();

  const criteriaType = CRITERIA_TYPES.find((t) => t.value === type)!;

  const queryKey = packagingServiceId
    ? ["criterias", country.code, type, packagingServiceId]
    : requiredInformationId
      ? ["criterias", country.code, type, requiredInformationId]
      : ["criterias", country.code, type];

  const { data: _criterias, isFetching } = useQuery({
    queryKey,
    queryFn: () =>
      getServiceSetupCriterias(country.code, {
        type,
        packagingServiceId: packagingServiceId,
        requiredInformationId: requiredInformationId,
      }),
  });
  const criterias = useMemo(() => {
    return (_criterias || []).map((c) => {
      return {
        ...c,
        country_id: country?.id,
        packaging_service_id: packagingServiceId || null,
        required_information_id: requiredInformationId || null,
      };
    });
  }, [_criterias, country, packagingServiceId, requiredInformationId]);

  const { data: service } = useQuery({
    queryKey: ["services", country.code, type, packagingServiceId],
    queryFn: async () => {
      if (!packagingServiceId) {
        return null;
      }
      return await getPackagingService(packagingServiceId);
    },
  });

  const { data: optionValues } = useQuery({
    queryKey: ["optionValues", type, country.code, packagingServiceId, requiredInformationId],
    refetchOnMount: true,
    queryFn: async () => {
      if (type === "PACKAGING_SERVICE") return PACKAGING_SERVICE_CRITERIA_OPTIONS;

      if (type === "REQUIRED_INFORMATION") return REQUIRED_INFORMATION_CRITERIA_OPTIONS;

      if (type === "REPORT_SET") {
        return (await getServiceSetupReportSets(country.code))
          .filter((reportSet) => reportSet.packaging_service_id === packagingServiceId)
          .map((reportSet: ReportSet) => ({
            value: String(reportSet.id),
            label: reportSet.name,
          }));
      }

      if (type === "REPORT_FREQUENCY") {
        return (await getServiceSetupReportFrequencies(country.code))
          .filter((frequency) => frequency.packaging_service_id === packagingServiceId)
          .map((frequency: ReportSetFrequency) => ({
            value: String(frequency.id),
            label: frequency.rhythm,
          }));
      }

      if (type === "OTHER_COST") {
        const otherCostOptions = (await getServiceSetupOtherCosts(country.code)).map((otherCost) => ({
          value: String(otherCost.id),
          label: otherCost.name,
        }));

        // Add "No Cost" option to the dropdown
        return [{ value: "no_cost", label: "No Cost" }, ...otherCostOptions];
      }

      return [];
    },
  });

  const methods = useForm<CriteriasFormData>({
    resolver: zodResolver(criteriasFormSchema),
    defaultValues: {
      criterias,
    },
  });

  useEffect(() => {
    methods.reset({ criterias });

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [criterias]);

  const { fields, append, remove, update } = useFieldArray({
    control: methods.control,
    name: "criterias",
    keyName: "key",
  });

  function handleAddCriteria(mode: CriteriaMode) {
    append({
      mode,
      type,
      title: null,
      help_text: null,
      input_type: mode === "COMMITMENT" ? "YES_NO" : null,
      calculator_type: mode === "CALCULATOR" ? "LICENSE_FEES" : null,
      country_id: country.id,
      packaging_service_id: packagingServiceId || null,
      required_information_id: requiredInformationId || null,
      options:
        mode === "COMMITMENT"
          ? [
              { option_value: "YES", option_to_value: null, value: optionValues?.[0]?.value || "" },
              { option_value: "NO", option_to_value: null, value: optionValues?.[1]?.value || "" },
            ]
          : [],
    });
  }

  function handleUpdateCriteria(index: number, data: Partial<FormCriteria>) {
    const criteria = methods.getValues(`criterias.${index}`);

    update(index, { ...criteria, ...data });
  }

  function handleRemoveCriteria(index: number) {
    remove(index);
    enqueueSnackbar("Criteria removed successfully", { variant: "success" });
  }

  async function handleFormSubmit(data: CriteriasFormData) {
    try {
      if (!data.criterias.length) return;

      const promises = data.criterias.map(async (criteria) => {
        if (!criteria.id) {
          return createCriteria(criteria);
        }

        return updateCriteria(criteria.id, criteria);
      });

      const responses = await Promise.allSettled(promises);

      responses.forEach((response, index) => {
        if (response.status !== "fulfilled") return;

        update(index, { ...response.value, id: response.value.id });
        methods.setValue(`criterias.${index}.id`, response.value.id);
      });

      enqueueSnackbar("Form saved successfully", { variant: "success" });
      queryClient.invalidateQueries({ queryKey: ["criterias", country.code, type, packagingServiceId] });
      queryClient.invalidateQueries({
        queryKey: ["optionValues", type, country.code, packagingServiceId, requiredInformationId],
      });
      queryClient.invalidateQueries({ queryKey: ["service-setup-commitment", country.code] });

      invalidateSubQueries(type, country.code);
    } catch {
      enqueueSnackbar("Error saving form. Please try again.", { variant: "error" });
    }
  }
  return (
    <FormProvider {...methods}>
      <form className="space-y-10" onSubmit={methods.handleSubmit(handleFormSubmit)}>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <h3 className="text-primary text-xl">{criteriaType?.label}</h3>
            <h2 className="text-primary text-3xl font-bold">{criteriaType?.title}</h2>
            {service?.name ? (
              <h2 className="text-tonal-dark-cream-40 text-xl">EPR Compliance Packaging - {service?.name}</h2>
            ) : null}
          </div>
          <PreviewCommitmentDialog countryCode={country.code}>
            <div className="flex flex-row gap-1 items-center text-primary">
              Preview:
              <Button
                type="button"
                variant="text"
                color="light-blue"
                size="small"
                className="underline underline-offset-2"
              >
                Additional assessment
              </Button>
            </div>
          </PreviewCommitmentDialog>
        </div>
        <div className="flex items-center gap-6 my-10">
          {/* TODO: uncomment to add calculator criteria */}
          {/* {criteriaType.hasCalculatorCriteria && (
            <Button
              disabled={isFetching}
              type="button"
              variant="filled"
              color="dark-blue"
              size="small"
              onClick={() => handleAddCriteria("CALCULATOR")}
            >
              Add criteria on Calculator
            </Button>
          )} */}
          {criteriaType.hasCommitmentCriteria && (
            <Button
              disabled={isFetching}
              type="button"
              variant="outlined"
              color="dark-blue"
              size="small"
              leadingIcon={<Add />}
              onClick={() => handleAddCriteria("COMMITMENT")}
            >
              Add new additional assessment criteria
            </Button>
          )}
        </div>
        {!isFetching &&
          optionValues &&
          fields.map((field, fieldIndex) => (
            <CriteriaCard
              key={field.key}
              criteriaIndex={fieldIndex}
              criteria={field}
              optionValues={optionValues}
              onRemove={handleRemoveCriteria}
              onUpdate={handleUpdateCriteria}
            />
          ))}
        {isFetching && <CriteriaCardSkeleton />}
        <div className="flex items-center justify-end">
          {!!fields.length && (
            <Button
              variant="filled"
              color={!!Object.keys(methods.formState.errors).length ? "red" : "yellow"}
              size="medium"
              disabled={methods.formState.isSubmitting || isFetching}
            >
              {methods.formState.isSubmitting ? "Saving..." : "Save criteria"}
            </Button>
          )}
        </div>
      </form>
    </FormProvider>
  );
}

export async function invalidateSubQueries(criteriaType: CriteriaType, countryCode: string) {
  switch (criteriaType) {
    case "PACKAGING_SERVICE":
      queryClient.invalidateQueries({ queryKey: ["service-setup-packaging-services", countryCode] });
      break;
    case "REPORT_SET":
      queryClient.invalidateQueries({ queryKey: ["service-setup-packaging-services", countryCode] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-report-sets", countryCode] });
      break;
    case "REPORT_FREQUENCY":
      queryClient.invalidateQueries({ queryKey: ["service-setup-packaging-services", countryCode] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-report-frequencies", countryCode] });
      break;

    case "OTHER_COST":
      queryClient.invalidateQueries({ queryKey: ["service-setup-other-costs", countryCode] });
      break;
    case "REQUIRED_INFORMATION":
      queryClient.invalidateQueries({ queryKey: ["service-setup-required-informations", countryCode] });
      break;
  }

  queryClient.invalidateQueries({ queryKey: ["service-setup-status", countryCode] });
  queryClient.invalidateQueries({ queryKey: ["service-setup-commitment", countryCode] });
}
