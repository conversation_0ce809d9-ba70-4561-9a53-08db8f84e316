"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { deletePackagingService } from "@/lib/api/packaging-services";
import { getServiceSetupPackagingServices } from "@/lib/api/service-setups";
import { queryClient } from "@/lib/react-query";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, CheckCircle, EditCircle } from "@interzero/oneepr-react-ui/Icon";
import { useMutation, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { useForm } from "react-hook-form";
import { CornerDownRight } from "lucide-react";
import { AddServiceDialog } from "./add-service-dialog";
import { EditServiceDialog } from "./edit-service-dialog";

interface ServiceSetupPackagingServicesProps {}

export function ServiceSetupPackagingServices({}: ServiceSetupPackagingServicesProps) {
  const { paramValues, changeParam, deleteAllParams } = useQueryFilter(["step"]);

  const isSelected = paramValues.step === "packaging-services";

  function handleOpenStep() {
    changeParam("step", "packaging-services");
  }

  function handleCloseStep() {
    deleteAllParams();
  }

  const { country } = useServiceSetup();

  const { data: packagingServices, isFetching } = useQuery({
    queryKey: ["service-setup-packaging-services", country.code],
    queryFn: () => getServiceSetupPackagingServices(country.code),
  });

  const isComplete = packagingServices && !!packagingServices.length;

  if (isFetching) {
    return (
      <div className="bg-background rounded-[20px] p-8 space-y-2" id="packaging-services-step">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    );
  }

  return (
    <div
      data-selected={isSelected}
      data-complete={isComplete}
      className="group bg-background rounded-[20px] p-8 cursor-pointer data-[selected=true]:cursor-default"
      onClick={() => !isSelected && handleOpenStep()}
      id="packaging-services-step"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-primary text-2xl font-bold">1. Service details</h3>
        {!isSelected && isComplete && <CheckCircle className="size-6 fill-success transition-all duration-300" />}
      </div>
      {isSelected && (
        <PackagingServicesForm packagingServices={packagingServices || []} onCloseStep={handleCloseStep} />
      )}
    </div>
  );
}

interface PackagingService {
  id: number;
  name: string;
  description: string;
}

interface PackagingServicesFormProps {
  packagingServices: PackagingService[];
  onCloseStep: () => void;
}

function PackagingServicesForm({ packagingServices, onCloseStep }: PackagingServicesFormProps) {
  const { country } = useServiceSetup();

  const {
    formState: { isSubmitting },
  } = useForm({
    defaultValues: {
      packagingServices,
    },
  });

  const { mutate: deleteService, isPending: isDeletingPackagingService } = useMutation({
    mutationFn: (packagingServiceId: number) => deletePackagingService(packagingServiceId),
  });

  async function handleDeletePackagingService(serviceId: number) {
    try {
      deleteService(serviceId, {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["service-setup-packaging-services", country.code] });
          queryClient.invalidateQueries({ queryKey: ["service-setup-report-sets", country.code] });
          queryClient.invalidateQueries({ queryKey: ["service-setup-report-frequencies", country.code] });
          queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
          enqueueSnackbar("Packaging service deleted successfully", { variant: "success" });
        },
        onError: () => {
          enqueueSnackbar("Failed to delete packaging service", { variant: "error" });
        },
      });
    } catch {
      enqueueSnackbar("Failed to delete packaging service", { variant: "error" });
    }
  }

  function handleSaveServiceDetails() {
    try {
      queryClient.invalidateQueries({ queryKey: ["service-setup-packaging-services", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-report-sets", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-report-frequencies", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
      enqueueSnackbar("Service details saved successfully", { variant: "success" });
      onCloseStep();
    } catch {
      enqueueSnackbar("Failed to save service details", { variant: "error" });
    }
  }

  return (
    <div className="mt-6 space-y-6">
      <p className="text-primary text-base">
        Specify the Authorized Representative (AR) settings, select and edit the services available for this country,
        and set the obligation criteria
      </p>

      <div className="space-y-4">
        {/* Always show EPR Compliance section */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="text-lg font-bold text-primary mb-2">EPR Compliance Packaging</div>
            <div className="text-base text-primary/80">
              Ensure your packaging meets all EU EPR and PPWR regulations. We handle registration, reporting, and
              compliance—so you can focus on your business.
            </div>
          </div>
          <AddServiceDialog>
            <Button
              type="button"
              variant="text"
              color="light-blue"
              size="small"
              leadingIcon={<Add />}
              className="text-support-blue font-medium ml-4"
            >
              Add service type
            </Button>
          </AddServiceDialog>
        </div>

        {/* Divider below EPR section */}
        <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />

        {/* Show actual services below EPR Compliance */}
        {(packagingServices || []).map((service) => (
          <div key={service.id}>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 mt-1 flex-shrink-0">
                  <CornerDownRight className="stroke-tonal-dark-cream-50" />
                </div>
                <div className="flex-1">
                  <div className="text-lg font-bold text-primary mb-2">{service.name}</div>
                  <div className="text-base text-primary/80">{service.description}</div>
                </div>
              </div>
              <div className="flex items-center gap-2 ml-4">
                <EditServiceDialog service={service}>
                  <Button
                    type="button"
                    variant="text"
                    color="light-blue"
                    size="small"
                    leadingIcon={<EditCircle />}
                    className="text-support-blue font-medium"
                  >
                    Edit
                  </Button>
                </EditServiceDialog>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button type="button" variant="text" color="red" size="small" className="text-error font-medium">
                      Delete
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete service type?</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete this service type? Other parts of the service setup may be
                        affected.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel disabled={isDeletingPackagingService}>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={(e) => {
                          e.preventDefault();
                          handleDeletePackagingService(service.id);
                        }}
                        disabled={isDeletingPackagingService}
                      >
                        {isDeletingPackagingService ? "Deleting..." : "Delete"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="space-y-6">
        <div className="flex items-center justify-end gap-10">
          <Button type="button" onClick={() => onCloseStep()} variant="text" color="dark-blue" size="medium">
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSaveServiceDetails}
            variant="filled"
            color="yellow"
            size="medium"
            className="w-60"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : "Save service details"}
          </Button>
        </div>
      </div>
    </div>
  );
}
