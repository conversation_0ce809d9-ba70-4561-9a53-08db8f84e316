"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { createPackagingService } from "@/lib/api/packaging-services";
import { queryClient } from "@/lib/react-query";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { ReactNode, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const addServiceFormSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field"),
  description: z
    .string()
    .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field")
    .optional()
    .or(z.literal("")),
});

export type AddServiceFormData = z.infer<typeof addServiceFormSchema>;

interface AddServiceDialogProps {
  children: ReactNode;
}

export function AddServiceDialog({ children }: AddServiceDialogProps) {
  const { country } = useServiceSetup();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const {
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<AddServiceFormData>({
    resolver: zodResolver(addServiceFormSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  const { mutate: createService, isPending } = useMutation({
    mutationFn: (data: { name: string; description: string; country_id: number }) => createPackagingService(data),
  });

  async function handleFormSubmit(data: AddServiceFormData) {
    try {
      createService(
        {
          name: data.name,
          description: data.description || "",
          country_id: country.id,
        },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["service-setup-packaging-services", country.code] });
            queryClient.invalidateQueries({ queryKey: ["service-setup-report-sets", country.code] });
            queryClient.invalidateQueries({ queryKey: ["service-setup-report-frequencies", country.code] });
            queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
            enqueueSnackbar("Service created successfully", { variant: "success" });
            setIsDialogOpen(false);
            reset();
          },
          onError: () => {
            enqueueSnackbar("Failed to create service", { variant: "error" });
          },
        }
      );
    } catch {
      enqueueSnackbar("Failed to create service", { variant: "error" });
    }
  }

  function handleDialogOpenChange(open: boolean) {
    if (!open) reset();
    setIsDialogOpen(open);
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <div onClick={(e) => e.stopPropagation()}>{children}</div>
      </DialogTrigger>
      <DialogContent className="py-8">
        <DialogHeader>
          <DialogTitle>Add service type</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-6 space-y-6">
          <div className="[&_input]:text-primary [&_input]:opacity-80">
            <Input
              label="Service type name *"
              placeholder="Enter service type name"
              {...register("name")}
              variant={errors.name ? "error" : "default"}
              errorMessage={errors.name?.message}
            />
          </div>
          <Textarea
            label="Description"
            placeholder="Enter description"
            rows={4}
            className="text-base text-primary/80 resize-none"
            {...register("description")}
            errorMessage={errors.description?.message}
          />
          <div className="flex items-center justify-end gap-4">
            <Button
              type="button"
              variant="outlined"
              color="dark-blue"
              size="medium"
              className="rounded-full"
              onClick={() => setIsDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="filled"
              color="yellow"
              size="medium"
              className="rounded-full"
              disabled={isSubmitting || isPending}
            >
              {isSubmitting || isPending ? "Saving..." : "Add service type"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
