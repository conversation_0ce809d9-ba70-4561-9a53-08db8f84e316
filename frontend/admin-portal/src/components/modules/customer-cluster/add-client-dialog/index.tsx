import { useEffect, useRef, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getCustomers } from "@/lib/api/customer";
import { ListCustomer } from "@/lib/api/customer/types";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Modal } from "@interzero/oneepr-react-ui/Modal";
import { Skeleton } from "@/components/ui/skeleton";
import { Delete } from "@interzero/oneepr-react-ui/Icon";

interface AddClientModalProps {
  isOpen: boolean;
  setOpen: (open: boolean) => void;
  onAddClient: (clients: ListCustomer[]) => void;
}

export function AddClientDialog({ isOpen, setOpen, onAddClient }: AddClientModalProps) {
  const [searchInputValue, setSearchInputValue] = useState("");
  const [selectedClients, setSelectedClients] = useState<ListCustomer[]>([]);
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

  // Fetch customers dynamically
  const {
    data: customers,
    refetch,
    isFetching,
  } = useQuery({
    queryKey: ["customers", searchInputValue],
    queryFn: async () => {
      if (searchInputValue.length < 3) return { customers: [] };
      const response = await getCustomers({ search: searchInputValue });

      return {
        customers: response.customers.filter(
          (client) =>
            client.first_name.toLowerCase().includes(searchInputValue.toLowerCase()) ||
            client.last_name.toLowerCase().includes(searchInputValue.toLowerCase()) ||
            client.email.toLowerCase().includes(searchInputValue.toLowerCase())
        ),
      };
    },
    enabled: false,
  });

  useEffect(() => {
    if (debounceTimeout.current) clearTimeout(debounceTimeout.current);
    debounceTimeout.current = setTimeout(() => {
      if (searchInputValue.length > 2) refetch();
    }, 300);

    return () => {
      if (debounceTimeout.current) clearTimeout(debounceTimeout.current);
    };

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchInputValue]);

  function handleSelectClient(client: ListCustomer) {
    if (!selectedClients.some((c) => c.id === client.id)) {
      setSelectedClients((prev) => [...prev, client]);
    }
    setSearchInputValue("");
  }

  function handleRemoveClient(clientId: number) {
    setSelectedClients((prev) => prev.filter((client) => client.id !== clientId));
  }

  function handleSave() {
    if (selectedClients.length > 0) {
      onAddClient(selectedClients);
      setOpen(false);
    }
  }

  return (
    <Modal
      open={isOpen}
      onOpenChange={setOpen}
      className="z-50 w-full"
      style={{
        maxWidth: "600px",
        borderRadius: "12px",
        maxHeight: "100vh",
        backgroundColor: "#F0F0EF",
      }}
    >
      <div className="py-6 max-w-2xl">
        <h2 className="font-bold text-lg text-primary">Add Client to Cluster</h2>
        <p className="text-tonal-dark-cream-20 font-normal">Search and select a client to add</p>

        {/* Search Input */}
        <div className="mt-4">
          <label htmlFor="clientSearch" className="text-primary text-base">
            Search for a client
          </label>
          <Input
            id="clientSearch"
            placeholder="Search by name or email"
            value={searchInputValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchInputValue(e.target.value)}
            className="mt-2"
          />
        </div>

        {/* Loading State */}
        {isFetching && (
          <div className="mt-3 space-y-2">
            <Skeleton className="h-3 w-1/4" />
            <Skeleton className="h-14 w-full" />
          </div>
        )}

        {/* Clients List */}
        {(customers?.customers?.length ?? 0) > 0 && (
          <ul className="mt-3 max-h-60 overflow-y-auto border border-gray-300 rounded-lg p-2 bg-white">
            {customers?.customers.map((client) => (
              <li
                key={client.id}
                onClick={() => handleSelectClient(client)}
                className="py-2 px-3 flex justify-between cursor-pointer rounded-lg transition-colors bg-white hover:bg-[#F7F5F2]"
              >
                <div>
                  <p className="text-primary text-base">
                    {client.first_name} {client.last_name}
                  </p>
                  <span className="text-tonal-dark-cream-40 text-sm">{client.email}</span>
                </div>
              </li>
            ))}
          </ul>
        )}

        {/* Selected Clients */}
        {selectedClients.length > 0 && (
          <ul className="mt-4 space-y-2">
            {selectedClients.map((client) => (
              <li key={client.id} className="flex justify-between items-center p-3 bg-white rounded-lg shadow-md">
                <div>
                  <p className="text-primary text-base">
                    {client.first_name} {client.last_name}
                  </p>
                  <span className="text-tonal-dark-cream-40 text-sm">{client.email}</span>
                </div>
                <button onClick={() => handleRemoveClient(client.id)} className="text-red-500">
                  <Delete className="size-6 fill-primary" />
                </button>
              </li>
            ))}
          </ul>
        )}

        <div className="mt-6 flex justify-end gap-4">
          <Button
            type="button"
            variant="filled"
            color="yellow"
            size="medium"
            onClick={handleSave}
            disabled={selectedClients.length === 0}
          >
            Save changes*
          </Button>
        </div>
      </div>
    </Modal>
  );
}
