"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ChevronDownIcon, ChevronLeftIcon } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { enqueueSnackbar } from "notistack";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckboxInput } from "@/components/ui/checkbox";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getCountries } from "@/lib/api/countries";
import { queryClient } from "@/lib/react-query";
import { dateManager } from "@/utils/date-manager";
import { formatCurrency } from "@/utils/format-currency";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";

import {
  CreateCustomerCluster,
  Customer,
  CustomerClusterCustomer,
  getCustomersClusterById,
  updateCustomerCluster,
} from "@/lib/api/customer-cluster";
import { ListCustomer } from "@/lib/api/customer/types";
import { cn } from "@/utils/cn";
import { useEffect, useState } from "react";
import { AddClientDialog } from "../add-client-dialog";
import { ClientsClusterTable } from "../clients-cluster-table";

export enum ServiceType {
  SALES_PACKAGING = "license_service_sales_packaging",
  B2B_PACKAGING = "license_service_b2b_packaging",
  ACTION_GUIDE = "action_guide",
  DIRECT_LICENSE = "direct_license",
}

const SERVICE_TYPES = [
  { label: "License Service Sales Packaging", value: ServiceType.SALES_PACKAGING },
  { label: "License Service B2B Packaging", value: ServiceType.B2B_PACKAGING },
  { label: "Action Guide", value: ServiceType.ACTION_GUIDE },
  { label: "Direct License", value: ServiceType.DIRECT_LICENSE },
];

const STATUS_TYPES = [
  { label: "Active", value: "ACTIVE" },
  { label: "Inactive", value: "INACTIVE" },
];

const updateCustomerClusterFormSchema = z
  .object({
    name: z.string().min(1, "Required"),
    registration_start_date: z.string().refine((date) => !isNaN(Date.parse(date)), {
      message: "Invalid date format",
    }),
    registration_end_date: z.string().refine((date) => !isNaN(Date.parse(date)), {
      message: "Invalid date format",
    }),
    status: z.enum(["ACTIVE", "INACTIVE"]),
    min_household_packaging: z.coerce.number().min(0, "Invalid number").max(999999, "Invalid number"),
    max_household_packaging: z.coerce.number().min(0, "Invalid number").max(999999, "Invalid number"),
    type_of_services: z.nativeEnum(ServiceType).array().optional(),
    participating_countries: z.array(z.string()).optional(),
    customers: z.array(z.string()).optional(),
  })
  .refine((data) => new Date(data.registration_end_date) > new Date(data.registration_start_date), {
    message: "Registration end date must be after registration start date",
    path: ["registration_end_date"],
  });

type UpdateCustomerClusterFormSchemaData = z.infer<typeof updateCustomerClusterFormSchema>;

interface UpdateCustomerClusterFormProps {
  customerClusterId: string;
}

export function UpdateCustomerClusterForm({ customerClusterId }: UpdateCustomerClusterFormProps) {
  const router = useRouter();
  const [selectedClients, setSelectedClients] = useState<ListCustomer[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { data: countries, status: countriesQueryStatus } = useQuery({
    queryKey: ["countries"],
    queryFn: () => getCountries(),
  });

  const { data: customerCluster, refetch } = useQuery({
    queryKey: ["customer-cluster", customerClusterId],
    queryFn: () => {
      return getCustomersClusterById(Number(customerClusterId));
    },
    enabled: !!customerClusterId,
  });

  function mapCustomerToListCustomer(customer: Customer): ListCustomer {
    return {
      id: customer.id,
      first_name: customer.first_name,
      last_name: customer.last_name,
      salutation: "",
      email: customer.email,
      user_id: 0,
      is_active: true,
      id_stripe: null,
      updated_at: "",
      created_at: customer.created_at,
      companies: [],
      phones: [],
      contracts: customer.contracts,
    };
  }

  useEffect(() => {
    if (customerCluster && customerCluster.customers) {
      const customers = (customerCluster.customers as unknown as CustomerClusterCustomer[]).map((item) =>
        mapCustomerToListCustomer(item.customer)
      );
      setSelectedClients(customers);
    }
  }, [customerCluster]);

  const defaultValues: Partial<UpdateCustomerClusterFormSchemaData> = {
    name: customerCluster?.name,
    registration_start_date: dateManager(customerCluster?.registration_start_date).format("YYYY-MM-DD"),
    registration_end_date: dateManager(customerCluster?.registration_end_date).format("YYYY-MM-DD"),
    status: customerCluster?.status,
    min_household_packaging: customerCluster?.min_household_packaging,
    max_household_packaging: customerCluster?.max_household_packaging,
    type_of_services: Object.keys(customerCluster?.type_of_services || {}).filter(
      (key) => customerCluster?.type_of_services[key]
    ) as ServiceType[],
    participating_countries: customerCluster?.participating_countries || [],
    customers: [],
  };

  const { formState, ...form } = useForm<UpdateCustomerClusterFormSchemaData>({
    resolver: zodResolver(updateCustomerClusterFormSchema),
    defaultValues,
  });

  useEffect(() => {
    if (customerClusterId) refetch();

    if (!customerCluster) return;

    form.reset(defaultValues);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customerClusterId, customerCluster]);

  const { mutate, isPending: isDuplicatingCustomerCluster } = useMutation({
    mutationFn: (data: UpdateCustomerClusterFormSchemaData) => {
      const typeOfServicesObject = {
        [ServiceType.SALES_PACKAGING]: data.type_of_services?.includes(ServiceType.SALES_PACKAGING) || false,
        [ServiceType.B2B_PACKAGING]: data.type_of_services?.includes(ServiceType.B2B_PACKAGING) || false,
        [ServiceType.ACTION_GUIDE]: data.type_of_services?.includes(ServiceType.ACTION_GUIDE) || false,
        [ServiceType.DIRECT_LICENSE]: data.type_of_services?.includes(ServiceType.DIRECT_LICENSE) || false,
      };
      const payload: CreateCustomerCluster = {
        name: data.name,
        registration_start_date: new Date(data.registration_start_date).toISOString(),
        registration_end_date: new Date(data.registration_end_date).toISOString(),
        status: data.status,
        min_household_packaging: data.min_household_packaging,
        max_household_packaging: data.max_household_packaging,
        type_of_services: typeOfServicesObject,
        participating_countries: data.participating_countries || [],
        customers: selectedClients.map((client) => Number(client.id.toString())),
      };

      return updateCustomerCluster(Number(customerClusterId), payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customer-clusters"] });
      enqueueSnackbar("Cluster updated successfully", { variant: "success" });
      router.push("/customer-clusters");
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  function handleFormSubmit(data: UpdateCustomerClusterFormSchemaData) {
    mutate(data);
  }

  function handleBack() {
    router.back();
  }

  function handleAddClient(clients: ListCustomer[]) {
    setSelectedClients((prev) => {
      const uniqueClients = clients.filter((client) => !prev.some((existing) => existing.id === client.id));
      return [...prev, ...uniqueClients];
    });
  }

  function handleRemoveClient(clientId: string) {
    setSelectedClients((prev) => prev.filter((client) => client.id !== Number(clientId)));
  }

  const isLoading = formState.isSubmitting || isDuplicatingCustomerCluster;

  return (
    <form onSubmit={form.handleSubmit(handleFormSubmit)} className="w-full lg:max-w-[912px] mx-auto px-4">
      <Button
        type="button"
        variant="text"
        color="light-blue"
        size="small"
        leadingIcon={<ChevronLeftIcon className="size-5" />}
        onClick={handleBack}
        className="pl-0 hover:bg-[none] hover:opacity-75"
      >
        Back
      </Button>

      <h3 className="mt-10 text-primary text-[2rem] font-bold">Cluster</h3>

      {/* Cluster parameters */}
      <section className="mt-10">
        <Card>
          <CardHeader>
            <CardTitle>Cluster parameters</CardTitle>
          </CardHeader>
          <CardContent className="w-full md:max-w-[338px]">
            <Input
              label="Cluster name"
              placeholder="Cluster name"
              variant={formState.errors.name ? "error" : "default"}
              errorMessage={formState.errors.name?.message}
              {...form.register("name")}
            />
          </CardContent>
          <div className="w-full grid md:grid-cols-5">
            <CardContent className="space-y-2.5 col-span-3">
              <label htmlFor="registrationDate">Registration date</label>
              <div className="flex flex-shrink-0 items-center gap-4">
                <Input
                  type="date"
                  variant={formState.errors.registration_start_date ? "error" : "default"}
                  errorMessage={formState.errors.registration_start_date?.message}
                  {...form.register("registration_start_date")}
                />
                <span className="text-center text-[#8C8A87]">to</span>
                <Input
                  type="date"
                  variant={formState.errors.registration_end_date ? "error" : "default"}
                  errorMessage={formState.errors.registration_end_date?.message}
                  {...form.register("registration_end_date")}
                />
              </div>
            </CardContent>
            <CardContent className="space-y-4 col-span-2">
              <label htmlFor="status">Status</label>
              <Controller
                control={form.control}
                name="status"
                render={({ field }) => (
                  <Select value={field.value} onValueChange={(value) => value && field.onChange(value)}>
                    <SelectTrigger id="status" className="w-full disabled:bg-[#BEBDBB61] disabled:text-[#8C8A87]">
                      <SelectValue placeholder="Select a status" />
                    </SelectTrigger>
                    <SelectContent>
                      {STATUS_TYPES.map(({ value, label }) => (
                        <SelectItem key={value} value={value} className="hover:!bg-support-blue/10 transition-colors">
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </CardContent>
          </div>
          <CardContent className="space-y-4">
            <label>Quantities for packages of household packaging</label>
            <div className="flex flex-wrap items-center gap-2.5">
              <Controller
                control={form.control}
                name="min_household_packaging"
                render={({ field }) => (
                  <Dropdown
                    trigger={
                      <button className="flex items-center text-primary text-base border-b border-b-[transparent] hover:border-b-primary">
                        <span className="ml-1 mr-2 mt-1 text-left">
                          {field.value <= 0 ? "00,00 €" : formatCurrency(field.value)}
                        </span>
                        <ChevronDownIcon width={20} height={20} />
                      </button>
                    }
                  >
                    {[0, 150, 0, 0].map((value, idx) => (
                      <DropdownItem
                        key={idx}
                        onClick={() => field.onChange(value)}
                        className="text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer"
                      >
                        {value <= 0 ? "00,00 €" : formatCurrency(value)}
                      </DropdownItem>
                    ))}
                  </Dropdown>
                )}
              />
              <span className="text-center text-[#8C8A87]">to</span>
              <Controller
                control={form.control}
                name="max_household_packaging"
                render={({ field }) => (
                  <Dropdown
                    trigger={
                      <button className="flex items-center text-primary text-base border-b border-b-[transparent] hover:border-b-primary">
                        <span className="ml-1 mr-2 mt-1 text-left">
                          {field.value <= 0 ? "00,00 €" : formatCurrency(field.value)}
                        </span>
                        <ChevronDownIcon width={20} height={20} />
                      </button>
                    }
                  >
                    {[0, 150, 0, 0].map((value, idx) => (
                      <DropdownItem
                        key={idx}
                        onClick={() => field.onChange(value)}
                        className="text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer"
                      >
                        {value <= 0 ? "00,00 €" : formatCurrency(value)}
                      </DropdownItem>
                    ))}
                  </Dropdown>
                )}
              />
            </div>
          </CardContent>
          <CardContent className="space-y-4 md:max-w-2xl">
            <label htmlFor="service-Type" className="text-tonal-dark-cream-10">
              Type of services
            </label>
            <Controller
              control={form.control}
              name="type_of_services"
              render={({ field }) => (
                <div className="flex items-center flex-wrap gap-4">
                  {SERVICE_TYPES.map((serviceType) => {
                    const values = field.value;
                    const isChecked = values?.includes(serviceType.value);
                    return (
                      <CheckboxInput
                        key={serviceType.value}
                        label={serviceType.label}
                        checked={isChecked}
                        value={serviceType.value}
                        onChange={() => {
                          if (values && isChecked) field.onChange(values.filter((v) => v !== serviceType.value));
                          else if (values) field.onChange([...values, serviceType.value]);
                        }}
                      />
                    );
                  })}
                </div>
              )}
            />
          </CardContent>
        </Card>
      </section>

      {/* Participating countries */}
      <section className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Participating countries</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center flex-wrap gap-3">
            {countriesQueryStatus === "success" && (
              <Controller
                control={form.control}
                name="participating_countries"
                render={({ field }) => (
                  <div className="flex items-center flex-wrap gap-3">
                    {countries.map((country) => {
                      const values = field.value;
                      const isChecked = values?.includes(country.code);
                      return (
                        <div key={country.id} className="flex items-center gap-1.5">
                          <CheckboxInput
                            id={country.code}
                            checked={isChecked}
                            value={country.code}
                            onChange={() => {
                              if (values && isChecked) {
                                field.onChange(values.filter((v) => v !== country.code));
                              } else if (values) field.onChange([...values, country.code]);
                            }}
                          />
                          <label htmlFor={country.code} className={cn("flex items-center gap-1 cursor-pointer")}>
                            <Image
                              src={country.flag_url}
                              alt={country.code}
                              width={16}
                              height={16}
                              className="size-4 rounded-full object-cover cursor-pointer"
                            />
                            {country.code}
                          </label>
                        </div>
                      );
                    })}
                  </div>
                )}
              />
            )}
          </CardContent>
        </Card>
      </section>

      {/* Clients on this cluster */}
      <section className="mt-10">
        <Card>
          <CardHeader className="sm:flex-row sm:items-center space-y-0 gap-6">
            <CardTitle className="flex-1">Clients on this cluster</CardTitle>
            <Button
              type="button"
              variant="filled"
              color="dark-blue"
              size="medium"
              onClick={() => setIsModalOpen(true)}
              leadingIcon={<Add className="size-4 mt-1" />}
            >
              Add clients
            </Button>
            <AddClientDialog isOpen={isModalOpen} setOpen={() => setIsModalOpen(false)} onAddClient={handleAddClient} />
          </CardHeader>
          <CardContent className="pt-0">
            <ClientsClusterTable clients={selectedClients} onRemoveClient={handleRemoveClient} />
          </CardContent>
        </Card>
      </section>

      <div className="mt-[60px] grid md:grid-cols-3 items-center justify-end gap-4 md:gap-10">
        <Button
          type="button"
          variant="outlined"
          color="dark-blue"
          size="medium"
          className="col-start-2"
          onClick={handleBack}
        >
          Cancel
        </Button>
        <Button variant="filled" color="yellow" size="medium" className="col-start-3" disabled={isLoading}>
          {isLoading ? "Saving..." : "Save"}
        </Button>
      </div>
    </form>
  );
}
