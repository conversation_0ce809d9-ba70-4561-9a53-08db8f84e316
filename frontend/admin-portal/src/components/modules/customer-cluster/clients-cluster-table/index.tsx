"use client";

import { useMemo, useState } from "react";
// import { useRouter } from "next/navigation";
import { createColumnHelper } from "@tanstack/react-table";
import { MoreVerticalIcon } from "lucide-react";

import { Datatable } from "@/components/common/datatable";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { dateManager } from "@/utils/date-manager";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { CalendarToday, Delete, FilterAlt, KeyboardArrowDown, SortDown } from "@interzero/oneepr-react-ui/Icon";

import { ListCustomer } from "@/lib/api/customer/types";
import { DeleteCustomerClusterDialog } from "../delete-customer-cluster-dialog";

const MONTHS = [
  { label: "January", value: "01" },
  { label: "February", value: "02" },
  { label: "March", value: "03" },
  { label: "April", value: "04" },
  { label: "May", value: "05" },
  { label: "June", value: "06" },
  { label: "July", value: "07" },
  { label: "August", value: "08" },
  { label: "September", value: "09" },
  { label: "October", value: "10" },
  { label: "November", value: "11" },
  { label: "December", value: "12" },
];

const YEARS = Array.from({ length: 10 }, (_, i) => {
  const year = 2020 + i;
  return { label: year.toString(), value: year.toString() };
});

const STATUS_FILTERS = [
  { label: "All Status", value: "ALL" },
  { label: "Active", value: "ACTIVE" },
  { label: "Expired", value: "EXPIRED" },
];

interface ClientsClusterTableProps {
  clients: ListCustomer[];
  onRemoveClient: (id: string) => void;
}

// const mockData: ClientsClusterData[] = [
//   {
//     id: "1",
//     client_name: "Taylor Lautner",
//     client_email: "<EMAIL>",
//     register_date: "2021.01.01",
//     payed: 30_000 * 100,
//     status: "EXPIRED",
//     created_at: "2021.01.01",
//   },
//   {
//     id: "2",
//     client_name: "Tom Cruise",
//     client_email: "<EMAIL>",
//     register_date: "2025.01.01",
//     payed: 30_000 * 100,
//     status: "ACTIVE",
//     created_at: "2025.01.01",
//   },
//   {
//     id: "3",
//     client_name: "Taylor Lautner",
//     client_email: "<EMAIL>",
//     register_date: "2025.01.01",
//     payed: 30_000 * 100,
//     status: "ACTIVE",
//     created_at: "2025.01.01",
//   },
//   {
//     id: "4",
//     client_name: "Taylor Lautner",
//     client_email: "<EMAIL>",
//     register_date: "2025.01.01",
//     payed: 30_000 * 100,
//     status: "ACTIVE",
//     created_at: "2025.01.01",
//   },
//   {
//     id: "5",
//     client_name: "Taylor Lautner",
//     client_email: "<EMAIL>",
//     register_date: "2025.01.01",
//     payed: 30_000 * 100,
//     status: "ACTIVE",
//     created_at: "2025.01.01",
//   },
// ];

const columnHelper = createColumnHelper<ListCustomer>();

export function ClientsClusterTable({ clients, onRemoveClient }: ClientsClusterTableProps) {
  // const router = useRouter();

  const [selectedMonth, setSelectedMonth] = useState(MONTHS[0]);
  const [selectedYear, setSelectedYear] = useState(YEARS[5]);
  const [selectedStatus, setSelectedStatus] = useState(STATUS_FILTERS[0]);

  // const [data] = useState<ListCustomer[]>(clients);
  // const [filteredData, setFilteredData] = useState<ListCustomer[]>(data);
  const [searchTerm, setSearchTerm] = useState("");

  // Calculate end date (1 month after start date)
  const endDate = useMemo(() => {
    const monthIndex = parseInt(selectedMonth.value) - 1;
    const year = parseInt(selectedYear.value);

    // const startDate = new Date(year, monthIndex);
    const endDate = new Date(year, monthIndex + 1);

    return {
      month: MONTHS[endDate.getMonth()],
      year: YEARS.find((y) => y.value === endDate.getFullYear().toString()) || selectedYear,
    };
  }, [selectedMonth, selectedYear]);

  // Get available months based on selected year
  const availableMonths = useMemo(() => {
    const currentDate = new Date("2025-01-16");
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();

    const selectedYearValue = parseInt(selectedYear.value);

    if (selectedYearValue > currentYear) {
      return [];
    }
    if (selectedYearValue === currentYear) {
      return MONTHS.filter((_, index) => index <= currentMonth);
    }
    return MONTHS;
  }, [selectedYear]);

  // useEffect(() => {
  //   let filtered = [...data];

  //   // Filter by search term
  //   if (searchTerm.trim()) {
  //     const term = searchTerm.toLowerCase().trim();
  //     filtered = filtered.filter((item) => item.first_name.toLowerCase().includes(term));
  //   }

  //   // Filter by date range
  //   filtered = filtered.filter((item) => {
  //     // Convert DD.MM.YYYY to Date object
  //     const [day, month, year] = item.created_at.split(".");
  //     const itemDate = dateManager(`${year}-${month}-${day}`);

  //     const startFilterDate = dateManager()
  //       .setYear(parseInt(selectedYear.value))
  //       .setMonth(parseInt(selectedMonth.value) - 1)
  //       .startOf("month");
  //     const endFilterDate = dateManager()
  //       .setYear(parseInt(endDate.year.value))
  //       .setMonth(parseInt(endDate.month.value) - 1)
  //       .endOf("month");

  //     return itemDate.toDate() >= startFilterDate.toDate() && itemDate.toDate() <= endFilterDate.toDate();
  //   });

  //   setFilteredData(filtered);
  // }, [data, selectedStatus, selectedMonth, selectedYear, searchTerm]);

  const columns = [
    columnHelper.accessor("first_name", {
      header: ({ column }) => {
        return (
          <Button
            type="button"
            variant="text"
            size="small"
            color="gray"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Name <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </Button>
        );
      },
      cell: (info) => {
        const clientEmail = info.row.original.email;
        return (
          <div className="pl-2 flex flex-col gap-1">
            <p className="text-base text-[#002652]">{info.getValue()}</p>
            <span className="text-xs text-[#656773] font-medium">{clientEmail}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("created_at", {
      header: "Register Date",
      cell: (info) => (
        <span className="pl-2 text-sm text-primary">{dateManager(info.getValue()).format("DD.MM.YYYY")}</span>
      ),
    }),
    // columnHelper.accessor("payed", {
    //   header: "Payed",
    //   cell: (info) => {
    //     const amount = info.getValue();
    //     return <span className="text-sm text-primary">{amount <= 0 ? "00,00 €" : formatCurrency(amount / 100)}</span>;
    //   },
    // }),
    columnHelper.accessor("contracts", {
      header: "Status",
      cell: (info) => {
        const status = info?.getValue()?.map((contract) => contract.status);
        const isActive = status?.includes("ACTIVE");
        return (
          <div data-status={info.getValue()} className="group flex items-center gap-2">
            <div className={`w-1.5 h-1.5 rounded-full ${isActive ? "bg-tonal-green-40" : "bg-primary"}`} />
            <span className={`font-bold text-sm capitalize ${isActive ? "text-tonal-green-40" : "text-primary"}`}>
              {status ?? "Inactive"}
            </span>
          </div>
        );
      },
    }),
    columnHelper.accessor("id", {
      header: "",
      cell: (info) => {
        const clientClusterId = info.getValue();
        return (
          <Dropdown
            trigger={
              <button className="flex items-center justify-center rounded-full size-6 text-primary hover:bg-tonal-dark-cream-70 transition-colors">
                <MoreVerticalIcon className="size-4" />
              </button>
            }
          >
            <DropdownItem asChild>
              <DeleteCustomerClusterDialog customerClusterId={clientClusterId}>
                <button
                  onClick={() => onRemoveClient(info.getValue().toString())}
                  className="flex flex-shrink-0 items-center gap-4 w-full text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer"
                >
                  <Delete className="size-4 fill-primary" /> Delete
                </button>
              </DeleteCustomerClusterDialog>
            </DropdownItem>
          </Dropdown>
        );
      },
    }),
  ];

  return (
    <div className="flex flex-col gap-6 rounded-3xl">
      <div className="flex items-center justify-between gap-8">
        <div className="relative flex-1 max-w-[320px]">
          <DatatableSearch onSearch={setSearchTerm} defaultValue={searchTerm} placeholder="Search by name" />
        </div>
        <div className="flex items-center gap-6">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <FilterAlt width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedStatus.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {STATUS_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => setSelectedStatus(filter)}
                className="flex items-center gap-2 text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.value !== "ALL" && (
                  <div
                    data-status={filter.value === "ACTIVE"}
                    className="w-3 h-3 rounded-full mr-2 data-[status='true']:bg-tonal-green-40 data-[status='false']:bg-tonal-cream-20"
                  />
                )}
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <div className="h-4 border w-px border-tonal-dark-cream-60" />
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <Dropdown
                trigger={
                  <button className="flex items-center text-support-blue font-bold text-base">
                    <CalendarToday className="w-5 h-5 fill-support-blue" />
                    <span className="ml-1 mr-2 mt-1 text-left">
                      {selectedMonth.value}/{selectedYear.value}
                    </span>
                    <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
                  </button>
                }
              >
                <div className="p-2">
                  <Dropdown
                    trigger={
                      <button className="w-full flex items-center justify-between text-support-blue font-bold text-base p-2 hover:bg-surface-01 rounded">
                        <span>{selectedYear.label}</span>
                        <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
                      </button>
                    }
                  >
                    {YEARS.map((year, idx) => (
                      <DropdownItem
                        key={idx}
                        onClick={() => setSelectedYear(year)}
                        className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
                      >
                        {year.label}
                      </DropdownItem>
                    ))}
                  </Dropdown>
                  {availableMonths.length > 0 && (
                    <div className="grid grid-cols-3 gap-1 mt-2">
                      {availableMonths.map((month, idx) => (
                        <button
                          key={idx}
                          onClick={() => setSelectedMonth(month)}
                          className={`p-2 text-base rounded hover:bg-surface-01 ${
                            month.value === selectedMonth.value
                              ? "bg-surface-01 text-primary"
                              : "text-tonal-dark-cream-10"
                          }`}
                        >
                          {month.label.slice(0, 3)}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </Dropdown>
              <span className="flex items-center text-support-blue font-bold text-base">to</span>
              <span className="flex items-center text-support-blue font-bold text-base">
                {endDate.month.value}/{endDate.year.value}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="rounded-xl overflow-hidden text-surface-01">
        <Datatable columns={columns} data={clients ?? []} />
      </div>
    </div>
  );
}
