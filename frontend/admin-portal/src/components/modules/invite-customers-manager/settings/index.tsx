"use client";

import { Divider } from "@/components/common/divider";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { DragFile } from "@/components/ui/drag-file";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@interzero/oneepr-react-ui/Button";
import {
  Controller<PERSON><PERSON><PERSON>,
  Handshake,
  KeyboardArrowLeft,
  MenuBook,
  RocketLaunch,
} from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import Link from "next/link";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { getSettings, upsertSettings } from "@/lib/api/setttings";
import { z } from "zod";
import { Loader2 } from "lucide-react";
import { enqueueSnackbar } from "notistack";

const SERVICE_TYPES = [
  { label: "month", value: "month" },
  { label: "year", value: "year" },
  { label: "weeks", value: "weeks" },
] as const;

const DISCOUNT_TYPES = [
  { label: "Discount X", value: "x" },
  { label: "Discount Y", value: "y" },
  { label: "Discount Z", value: "z" },
] as const;

const inviteCustomersSettingsSchema = z.object({
  isLinkEnabled: z.coerce.boolean().default(false),
  linkPattern: z.string().optional(),
  isVoucherEnabled: z.coerce.boolean().default(false),
  couponPrefix: z.string().optional(),
  minimumOrderValue: z.coerce.number({ message: "Minimum order value is required" }),
  validity: z
    .object({
      value: z.coerce.number().optional(),
      type: z.enum(["month", "year", "weeks"]).optional(),
    })
    .default({ value: 1, type: "month" }),
  maximumReferrals: z.coerce
    .number({ message: "Maximum referrals is required" })
    .min(1, "Maximum referrals must be at least 1"),
  minimumOrderPeriod: z
    .object({
      value: z.coerce.number().optional(),
      type: z.enum(["month", "year", "weeks"]).optional(),
    })
    .default({ value: 1, type: "month" }),
  euLicenseDiscountType: z.string({ message: "EU license discount type is required" }),
  directLicenseDiscountType: z.string({ message: "Direct license discount type is required" }),
  actionGuideDiscountType: z.string({ message: "Action guide discount type is required" }),
  carbonOffset: z
    .object({
      percentage: z.object({
        value: z.coerce.number().min(0).max(100).optional(),
        serviceType: z.enum(["month", "year", "weeks"]).optional(),
        amount: z.coerce.number({ message: "Amount is required" }).min(0).optional(),
      }),
      absoluteValue: z.object({
        value: z.coerce.number({ message: "Absolute value is required" }).min(0).optional(),
        serviceType: z.enum(["month", "year", "weeks"]).optional(),
        amount: z.coerce.number({ message: "Amount is required" }).min(0).optional(),
      }),
    })
    .optional(),
  termsAndConditionsFile: z
    .any()
    .refine((file) => {
      if (!file) return false;
      return true;
    }, "Please upload terms and conditions file")
    .refine((file) => {
      if (!file) return false;
      return ["image/png", "image/jpeg", "image/jpg", "application/pdf"].includes(file.type);
    }, "File must be PNG, JPEG, JPG or PDF"),
});

type InviteCustomersSettingsFormData = z.infer<typeof inviteCustomersSettingsSchema>;

export default function InviteCustomersSettingsPage() {
  useQuery({
    queryKey: ["invite-customers-settings"],
    queryFn: async () => {
      const settings = await getSettings("INVITE_CUSTOMERS_MANAGER");
      if (!settings?.value) return {};
      const parsedSettings = JSON.parse(settings.value) as InviteCustomersSettingsFormData;

      form.reset({
        ...parsedSettings,
        termsAndConditionsFile: settings.file
          ? new File([], settings.file.original_name, {
              type: settings.file.extension,
            })
          : undefined,
      });

      return parsedSettings;
    },
  });

  const form = useForm<InviteCustomersSettingsFormData>({
    resolver: zodResolver(inviteCustomersSettingsSchema),
    defaultValues: {
      isLinkEnabled: false,
      isVoucherEnabled: false,
      validity: {
        value: 1,
        type: "month",
      },
      minimumOrderPeriod: {
        value: 1,
        type: "month",
      },
      carbonOffset: {
        percentage: {
          value: 0,
          serviceType: "month",
          amount: 0,
        },
        absoluteValue: {
          value: 0,
          serviceType: "month",
          amount: 0,
        },
      },
    },
  });

  const { mutateAsync, isPending } = useMutation({
    mutationFn: async (data: InviteCustomersSettingsFormData) =>
      upsertSettings({
        key: "INVITE_CUSTOMERS_MANAGER",
        value: JSON.stringify(data),
        file: data.termsAndConditionsFile,
      }),
    onSuccess: () => {
      enqueueSnackbar("Settings saved successfully", { variant: "success" });
    },
    onError: () => {
      enqueueSnackbar("Error saving settings", { variant: "error" });
    },
  });

  function handleAddFile(file: File) {
    form.setValue("termsAndConditionsFile", file);
    form.clearErrors("termsAndConditionsFile");
  }

  async function handleSave(data: InviteCustomersSettingsFormData) {
    if (!data.isLinkEnabled && !data.isVoucherEnabled) {
      form.setError("linkPattern", { message: "Enable link or voucher", type: "manual" });
      form.setError("couponPrefix", { message: "Enable link or voucher", type: "manual" });
      return;
    }

    if (data.isLinkEnabled && data.isVoucherEnabled) {
      form.setError("linkPattern", { message: "Enable only one of the options", type: "manual" });
      return;
    }

    if (data.isLinkEnabled && !data.linkPattern) {
      form.setError("linkPattern", { message: "Link pattern is required", type: "manual" });
      return;
    }

    if (data.isVoucherEnabled && !data.couponPrefix) {
      form.setError("couponPrefix", { message: "Coupon prefix is required", type: "manual" });
      return;
    }

    await mutateAsync(data);
  }

  return (
    <>
      <ModuleContent containerClassName="bg-white">
        <div className="flex items-center gap-2 mb-4">
          <Handshake className="size-6 fill-primary" />
          <p className="text-primary text-paragraph-regular underline">Invite Customers Manager</p>
        </div>
        <ModuleTitle
          icon={ControllerPainel}
          title="Settings"
          description="See all the information about the Invite Customers"
        />
      </ModuleContent>
      <ModuleContent containerClassName="bg-[#F7F5F2]">
        <form onSubmit={form.handleSubmit(handleSave)}>
          <div className="flex flex-col gap-10">
            <Link href="/invite-customers-manager">
              <div className="flex items-center gap-2">
                <KeyboardArrowLeft className="size-6 fill-support-blue" />
                <p className="text-support-blue font-bold cursor-pointer hover:underline">Back</p>
              </div>
            </Link>
            <div>
              <p className="text-title-1 text-primary font-bold">Invite Customers Manager</p>
            </div>
            <div className="bg-white w-[800px] h-auto min-h-[655px] rounded-xl p-6">
              <div>
                <div className="mb-8">
                  <p className="text-title-3 text-primary font-bold">General Settings</p>
                </div>
                <div className="flex flex-col gap-4">
                  <div className="flex items-center gap-4">
                    <Controller
                      control={form.control}
                      name="isLinkEnabled"
                      render={({ field }) => <Switch checked={field.value} onCheckedChange={field.onChange} />}
                    />
                    <p className="text-paragraph-regular text-tonal-dark-cream-10">Link</p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex flex-1 flex-col gap-1">
                      <p className="text-paragraph-regular text-primary">Links pattern</p>
                      <p className="text-small-paragraph-regular text-tonal-dark-cream-40">
                        Example: lizenzero.com/loyalty-program-8298398
                      </p>
                    </div>
                    <div className="flex-1">
                      <Controller
                        control={form.control}
                        name="linkPattern"
                        render={({ field }) => (
                          <Input
                            {...field}
                            type="text"
                            placeholder=""
                            variant={form.formState.errors.linkPattern ? "error" : "default"}
                          />
                        )}
                      />
                      {form.formState.errors.linkPattern && (
                        <p className="text-error text-small-paragraph-regular">
                          {form.formState.errors.linkPattern.message}
                        </p>
                      )}
                    </div>
                  </div>
                  <Divider initialMarginDisabled className="mt-2" />
                </div>
                <div className="flex flex-col gap-4 mt-4">
                  <div className="flex items-center gap-4">
                    <Controller
                      control={form.control}
                      name="isVoucherEnabled"
                      render={({ field }) => <Switch checked={field.value} onCheckedChange={field.onChange} />}
                    />
                    <p className="text-paragraph-regular text-tonal-dark-cream-10">Voucher</p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex flex-1 flex-col gap-1">
                      <p className="text-paragraph-regular text-primary">Coupons prefix</p>
                      <p className="text-small-paragraph-regular text-tonal-dark-cream-40">Example: LIZEN12898</p>
                    </div>
                    <div className="flex-1">
                      <Controller
                        control={form.control}
                        name="couponPrefix"
                        render={({ field }) => (
                          <Input
                            {...field}
                            type="text"
                            placeholder=""
                            variant={form.formState.errors.couponPrefix ? "error" : "default"}
                          />
                        )}
                      />
                      {form.formState.errors.couponPrefix && (
                        <p className="text-error text-small-paragraph-regular">
                          {form.formState.errors.couponPrefix.message}
                        </p>
                      )}
                    </div>
                  </div>
                  <Divider initialMarginDisabled className="mt-2" />
                </div>
                <div className="flex flex-col gap-4 mt-4">
                  <div className="flex items-center gap-4">
                    <div className="flex flex-1 flex-col gap-1">
                      <p className="text-paragraph-regular text-tonal-dark-cream-10">Minimum order value</p>
                      <p className="text-small-paragraph-regular text-tonal-dark-cream-40">
                        Minimum order value for incentivising a new customer
                      </p>
                    </div>
                    <div className="flex-1">
                      <Controller
                        control={form.control}
                        name="minimumOrderValue"
                        render={({ field }) => (
                          <Input
                            {...field}
                            type="number"
                            placeholder=""
                            variant={form.formState.errors.minimumOrderValue ? "error" : "default"}
                          />
                        )}
                      />
                      {form.formState.errors.minimumOrderValue && (
                        <p className="text-error text-small-paragraph-regular">
                          {form.formState.errors.minimumOrderValue.message}
                        </p>
                      )}
                    </div>
                  </div>
                  <Divider initialMarginDisabled className="mt-2" />
                </div>
                <div className="flex flex-col gap-4 mt-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2 flex-1">
                      <p className="text-paragraph-regular text-tonal-dark-cream-10">After</p>
                      <div className="w-[100px]">
                        <Controller
                          control={form.control}
                          name="validity.value"
                          render={({ field }) => <Input {...field} placeholder="" type="number" />}
                        />
                      </div>
                      <div className="mt-1 w-full">
                        <Controller
                          control={form.control}
                          name="validity.type"
                          render={({ field }) => (
                            <Select onValueChange={field.onChange} value={field.value}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {SERVICE_TYPES.map((serviceType) => (
                                  <SelectItem key={serviceType.value} value={serviceType.value}>
                                    {serviceType.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                        />
                      </div>
                    </div>
                  </div>
                  <Divider initialMarginDisabled className="mt-2" />
                </div>
                <div className="flex flex-col gap-4 mt-4">
                  <div className="flex items-center gap-4">
                    <div className="flex flex-1 flex-col gap-1">
                      <p className="text-paragraph-regular text-tonal-dark-cream-10">Maximum of successful referrals</p>
                      <p className="text-small-paragraph-regular text-tonal-dark-cream-40">
                        Maximum number of successful referrals per year
                      </p>
                    </div>
                    <div className="flex-1">
                      <Controller
                        control={form.control}
                        name="maximumReferrals"
                        render={({ field }) => (
                          <Input
                            {...field}
                            type="number"
                            placeholder=""
                            variant={form.formState.errors.maximumReferrals ? "error" : "default"}
                          />
                        )}
                      />
                      {form.formState.errors.maximumReferrals && (
                        <p className="text-error text-small-paragraph-regular">
                          {form.formState.errors.maximumReferrals.message}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Successful referrals */}
            <div className="bg-white w-[800px] h-[203px] rounded-xl p-6">
              <div className="mb-8">
                <p className="text-title-3 text-primary font-bold">Successful referrals</p>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex-1 flex flex-col gap-2">
                  <p className="text-tonal-dark-cream-10 text-paragraph-regular">Minimum order period</p>
                  <p className="text-tonal-dark-cream-40 text-small-paragraph-regular">
                    The incentive amount will only be credited to the customer if the associated order of the recruited
                    person has not been cancelled and paid in full after 12 weeks
                  </p>
                </div>
                <div className="flex items-center gap-2 flex-1">
                  <p className="text-paragraph-regular text-tonal-dark-cream-10">After</p>
                  <div className="w-[100px]">
                    <Controller
                      control={form.control}
                      name="minimumOrderPeriod.value"
                      render={({ field }) => <Input type="number" placeholder="" {...field} />}
                    />
                    {form.formState.errors.minimumOrderPeriod && (
                      <p className="text-error text-small-paragraph-regular">
                        {form.formState.errors.minimumOrderPeriod.message}
                      </p>
                    )}
                  </div>
                  <div className="mt-1 w-full">
                    <Controller
                      control={form.control}
                      name="minimumOrderPeriod.type"
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {SERVICE_TYPES.map((serviceType) => (
                              <SelectItem key={serviceType.value} value={serviceType.value}>
                                {serviceType.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {form.formState.errors.minimumOrderPeriod && (
                      <p className="text-error text-small-paragraph-regular">
                        {form.formState.errors.minimumOrderPeriod.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {/* Incentive level/discount */}
            <div className="bg-white w-[800px] h-auto min-h-[467px] rounded-xl p-6">
              <div className="mb-8">
                <p className="text-title-3 text-primary font-bold">Incentive level/discount</p>
              </div>
              <div className="flex items-center">
                <div className="flex flex-1 items-center mt-3 gap-2">
                  <RocketLaunch className="fill-tonal-dark-cream-10 size-4" />
                  <p className="text-tonal-dark-cream-10 text-paragraph-regular">EU License</p>
                </div>
                <div className="flex-1">
                  <div className="space-y-2">
                    <label htmlFor="serviceType" className="text-primary text-base font-centra">
                      Discount type
                    </label>
                    <Controller
                      control={form.control}
                      name="euLicenseDiscountType"
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {DISCOUNT_TYPES.map((discountType) => (
                              <SelectItem key={discountType.value} value={discountType.value}>
                                {discountType.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {form.formState.errors.euLicenseDiscountType && (
                      <p className="text-error text-small-paragraph-regular">
                        {form.formState.errors.euLicenseDiscountType.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
              <Divider initialMarginDisabled className="mt-6" />
              <div className="flex items-center mt-4">
                <div className="flex flex-1 items-center mt-3 gap-2">
                  <RocketLaunch className="fill-tonal-dark-cream-10 size-4" />
                  <p className="text-tonal-dark-cream-10 text-paragraph-regular">Direct License</p>
                </div>
                <div className="flex-1">
                  <div className="space-y-2">
                    <label htmlFor="serviceType" className="text-primary text-base font-centra">
                      Discount type
                    </label>
                    <Controller
                      control={form.control}
                      name="directLicenseDiscountType"
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {DISCOUNT_TYPES.map((serviceType) => (
                              <SelectItem key={serviceType.value} value={serviceType.value}>
                                {serviceType.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {form.formState.errors.directLicenseDiscountType && (
                      <p className="text-error text-small-paragraph-regular">
                        {form.formState.errors.directLicenseDiscountType.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
              <Divider initialMarginDisabled className="mt-6" />
              <div className="flex items-center mt-4">
                <div className="flex flex-1 items-center mt-3 gap-2">
                  <MenuBook className="fill-tonal-dark-cream-10 size-4" />
                  <p className="text-tonal-dark-cream-10 text-paragraph-regular">Action guide</p>
                </div>
                <div className="flex-1">
                  <div className="space-y-2">
                    <label htmlFor="serviceType" className="text-primary text-base font-centra">
                      Discount type
                    </label>
                    <Controller
                      control={form.control}
                      name="actionGuideDiscountType"
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {DISCOUNT_TYPES.map((serviceType) => (
                              <SelectItem key={serviceType.value} value={serviceType.value}>
                                {serviceType.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {form.formState.errors.actionGuideDiscountType && (
                      <p className="text-error text-small-paragraph-regular">
                        {form.formState.errors.actionGuideDiscountType.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {/* Calculation for the equivalent value of carbon offset */}
            <div className="bg-white w-[800px] h-[348px] rounded-xl p-6">
              <div className="mb-8">
                <p className="text-title-3 text-primary font-bold">
                  Calculation for the equivalent value of carbon offset
                </p>
              </div>
              <div className="flex flex-col justify-between h-[250px] mb-10">
                <div className="flex items-center w-full justify-between">
                  <div className="w-[206px]">
                    <label className="text-primary text-base font-centra">Percentage</label>
                    <Controller
                      control={form.control}
                      name="carbonOffset.percentage.value"
                      render={({ field }) => <Input {...field} placeholder="" type="number" />}
                    />
                  </div>
                  <p className="w-[60px] text-error text-paragraph-regular mt-6">Equals</p>
                  <div className="flex gap-4 items-center">
                    <div className="space-y-2 w-[333px]">
                      <label className="text-primary text-base font-centra">Service Type</label>
                      <Controller
                        control={form.control}
                        name="carbonOffset.percentage.serviceType"
                        render={({ field }) => (
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {SERVICE_TYPES.map((serviceType) => (
                                <SelectItem key={serviceType.value} value={serviceType.value}>
                                  {serviceType.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                    <div className="w-[100px] mt-4">
                      <Controller
                        control={form.control}
                        name="carbonOffset.percentage.amount"
                        render={({ field }) => <Input {...field} placeholder="" type="number" />}
                      />
                    </div>
                  </div>
                </div>
                <p className="text-support-blue text-paragraph-regular">and/or</p>
                <div className="flex items-center w-full justify-between">
                  <div className="w-[206px]">
                    <label className="text-primary text-base font-centra">Absolute value</label>
                    <Controller
                      control={form.control}
                      name="carbonOffset.absoluteValue.value"
                      render={({ field }) => <Input {...field} placeholder="" type="number" />}
                    />
                  </div>
                  <p className="w-[60px] text-error text-paragraph-regular mt-6">Equals</p>
                  <div className="flex gap-4 items-center">
                    <div className="space-y-2 w-[333px]">
                      <label className="text-primary text-base font-centra">Service Type</label>
                      <Controller
                        control={form.control}
                        name="carbonOffset.absoluteValue.serviceType"
                        render={({ field }) => (
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {SERVICE_TYPES.map((serviceType) => (
                                <SelectItem key={serviceType.value} value={serviceType.value}>
                                  {serviceType.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                    <div className="w-[100px] mt-4">
                      <Controller
                        control={form.control}
                        name="carbonOffset.absoluteValue.amount"
                        render={({ field }) => <Input {...field} placeholder="" type="number" />}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white w-[800px] h-[208px] rounded-xl p-6">
              <div className="mb-8">
                <p className="text-title-3 text-primary font-bold">Upload terms and conditions</p>
              </div>
              <div className="flex items-center w-full gap-2">
                <DragFile
                  title="Choose a file"
                  description="or drag it here"
                  selectedFile={form.watch("termsAndConditionsFile")}
                  onFile={handleAddFile}
                  errorMessage={form.formState.errors.termsAndConditionsFile?.message as string}
                  accept="image/png,image/jpeg,image/jpg,application/pdf"
                  hideDownloadButton
                />
              </div>
            </div>
            <div className="w-full flex justify-end gap-4 mb-8 pr-28">
              <Button variant="outlined" color="red" size="medium" className="w-[269px]" type="button">
                Deactivate program
              </Button>
              <Button
                type="submit"
                variant="filled"
                color="yellow"
                size="medium"
                className="min-w-[269px]"
                disabled={form.formState.isSubmitting || isPending}
                leadingIcon={
                  form.formState.isSubmitting || isPending ? (
                    <Loader2 className="size-6 -mt-[2px] animate-spin" />
                  ) : undefined
                }
              >
                Save
              </Button>
            </div>
          </div>
        </form>
      </ModuleContent>
    </>
  );
}
