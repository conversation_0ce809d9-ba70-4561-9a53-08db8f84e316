"use client";

import { NEAR_YEARS, PRICE_LIST_TYPES, PriceListFormData } from "./price-list-form-provider";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { FractionInput } from "@/components/ui/fraction-input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import { PriceListFormFractions } from "./price-list-form-fractions";
import { useRouter } from "next/navigation";
import { useMutationState } from "@tanstack/react-query";

export function PriceListForm() {
  const router = useRouter();

  const {
    register,
    control,
    formState: { errors },
  } = useFormContext<PriceListFormData>();

  const formType = useWatch({ control, name: "form_type" });
  const priceListType = useWatch({ control, name: "type" });

  const createMutation = useMutationState({
    filters: {
      mutationKey: ["create-price-list"],
      status: "pending",
    },
  });

  const updateMutation = useMutationState({
    filters: {
      mutationKey: ["update-price-list"],
      status: "pending",
    },
  });

  const isSubmitting = !!createMutation.length || !!updateMutation.length;

  return (
    <>
      <div className="bg-white rounded-3xl py-9 px-8">
        <h3 className="text-primary text-2xl font-bold mb-6">
          {formType === "CREATE" ? "New Price List" : "Price List Details"}
        </h3>
        <fieldset className="w-full space-y-6" disabled={isSubmitting}>
          <div className="w-full grid grid-cols-1">
            <Input
              label="Name of the price list *"
              placeholder="Name for the price list"
              {...register("name")}
              variant={errors.name ? "error" : "default"}
              errorMessage={errors.name?.message}
            />
          </div>
          <div className="w-full grid grid-cols-1">
            <div className="space-y-2">
              <label htmlFor="serviceType" className="text-primary text-base">
                Service Type *
              </label>
              <Controller
                control={control}
                name="type"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Select key={value} onValueChange={onChange} value={String(value)} defaultValue={String(value)}>
                    <SelectTrigger onBlur={onBlur}>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {PRICE_LIST_TYPES.map((priceListType) => (
                        <SelectItem key={priceListType.value} value={priceListType.value}>
                          {priceListType.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
          </div>
          <div className="w-full grid grid-cols-1">
            <label htmlFor="description" className="text-primary text-base mb-2">
              Description *
            </label>
            <Textarea
              id="description"
              placeholder="Set a description"
              rows={5}
              className="resize-none"
              {...register("description")}
              errorMessage={errors.description?.message}
            />
          </div>
          <div className="w-full grid grid-cols-1 md:grid-cols-6 gap-6">
            <div className="col-span-2">
              <div className="space-y-2">
                <label htmlFor="serviceType" className="text-primary text-base">
                  License Year *
                </label>
                <Controller
                  control={control}
                  name="license_year"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <Select key={value} onValueChange={onChange} value={String(value)} defaultValue={String(value)}>
                      <SelectTrigger onBlur={onBlur}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {NEAR_YEARS.map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
            <div className="col-span-2">
              <Input
                label="Starting Date *"
                placeholder="Start Date"
                type="date"
                {...register("start_date")}
                variant={errors.start_date ? "error" : "default"}
                errorMessage={errors.start_date?.message}
              />
            </div>
            <div className="col-span-2">
              <Input
                label="Ending Date *"
                placeholder="Ending Date"
                type="date"
                {...register("end_date")}
                variant={errors.end_date ? "error" : "default"}
                errorMessage={errors.end_date?.message}
              />
            </div>
          </div>
          <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
          {priceListType === "EU_LICENSE" && (
            <>
              <div className="w-full grid grid-cols-1 md:grid-cols-6 gap-6">
                <div className="col-span-2">
                  <Controller
                    name={"registration_fee"}
                    control={control}
                    render={({ field }) => (
                      <FractionInput
                        label="Registration Fee (€) *"
                        {...field}
                        type="currency"
                        error={errors.registration_fee?.message}
                      />
                    )}
                  />
                </div>
                <div className="col-span-2">
                  <Controller
                    name={"handling_fee"}
                    control={control}
                    render={({ field }) => (
                      <FractionInput
                        label="Handling Fee (€) *"
                        {...field}
                        type="currency"
                        error={errors.handling_fee?.message}
                      />
                    )}
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    {...register("variable_handling_fee", {
                      min: 0,
                      max: 100,
                      setValueAs: (value) => Number(value),
                    })}
                    label="Variable Handling Fee (%) *"
                    placeholder="10%"
                    type="number"
                    variant={errors.variable_handling_fee ? "error" : "default"}
                    errorMessage={errors.variable_handling_fee?.message}
                  />
                  <span className="text-tonal-dark-cream-40 text-xs block mt-2">
                    % of third party costs that is added to price lists
                  </span>
                </div>
              </div>
            </>
          )}
          {priceListType === "DIRECT_LICENSE" && (
            <>
              <div className="w-full grid grid-cols-1 md:grid-cols-5 gap-6">
                <div className="col-span-2">
                  <Controller
                    name={"basic_price"}
                    control={control}
                    render={({ field }) => (
                      <FractionInput
                        label="Basic Price *"
                        {...field}
                        type="currency"
                        error={errors.basic_price?.message}
                      />
                    )}
                  />
                </div>
                <div className="col-span-2">
                  <Controller
                    name={"minimum_price"}
                    control={control}
                    render={({ field }) => (
                      <FractionInput
                        label="Minimum Price (€)*"
                        {...field}
                        type="currency"
                        error={errors.minimum_price?.message}
                      />
                    )}
                  />
                </div>
              </div>
            </>
          )}
          {(priceListType === "ACTION_GUIDE" || priceListType === "WORKSHOP") && (
            <div className="w-full grid grid-cols-1 md:grid-cols-5 gap-6">
              <div className="col-span-2">
                <Controller
                  name={"price"}
                  control={control}
                  render={({ field }) => (
                    <FractionInput label="Price (€) *" {...field} type="currency" error={errors.price?.message} />
                  )}
                />
              </div>
            </div>
          )}
        </fieldset>
      </div>
      {priceListType === "DIRECT_LICENSE" && <PriceListFormFractions />}
      <div className="flex items-center justify-end gap-10">
        <Button
          type="button"
          onClick={() => router.back()}
          variant="text"
          color="dark-blue"
          size="medium"
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              type="button"
              variant="filled"
              color={!!Object.keys(errors).length ? "red" : "dark-blue"}
              size="medium"
              className="w-60"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Save price list?</AlertDialogTitle>
              <AlertDialogDescription>By clicking on ”save” you are saving this price list.</AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Back</AlertDialogCancel>
              <AlertDialogAction form="price-list-form" type="submit" color="yellow">
                Confirm
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
}
