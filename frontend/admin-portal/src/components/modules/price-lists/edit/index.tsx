"use client";

import { ModuleTitle } from "@/components/common/module-title";
import { ModuleContent } from "@/components/common/module-content";
import { PriceListForm } from "../price-list-form";
import { PriceListFormProvider } from "../price-list-form/price-list-form-provider";

interface EditPriceListModuleProps {
  priceListId: number;
}

export function EditPriceListModule({ priceListId }: EditPriceListModuleProps) {
  return (
    <ModuleContent>
      <ModuleTitle title="Price Editor" description="Edit the prices for this price list." />
      <PriceListFormProvider type="UPDATE" priceListId={priceListId}>
        <PriceListForm />
      </PriceListFormProvider>
    </ModuleContent>
  );
}
