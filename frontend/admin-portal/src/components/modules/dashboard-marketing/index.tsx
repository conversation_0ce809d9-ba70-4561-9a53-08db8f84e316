import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { Dashboard } from "@interzero/oneepr-react-ui/Icon";
import { TotalCustomers } from "./total-customers";
import { PartnerHub } from "./partner-hub";
import { CustomerInviteCustomers } from "./customer-invite-customers";
import { LeadsGenerated } from "./leads-generated";
import { CouponsRevenue } from "./coupons-revenue";
import { LeadsGeneratedPieChart } from "./leads-generated/leads-generated-pie";
import { CouponsPerformance } from "./coupons-performance";
import { LatestDealsAdded } from "./latest-deals-added";
import { PartnersDealsUsed } from "./partner-deals-used";

export function DashboardMarketingModule() {
  return (
    <ModuleContent>
      <ModuleTitle
        icon={Dashboard}
        title="Dashboard"
        description="See all the information about marketing activities"
      />
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        <div className="col-span-5 space-y-6">
          <TotalCustomers />
        </div>

        <div className="col-span-3 space-y-6">
          <LeadsGenerated />
        </div>

        <div className="col-span-2 space-y-6">
          <LeadsGeneratedPieChart />
        </div>

        <div className="col-span-5 space-y-6">
          <CustomerInviteCustomers />
        </div>

        <div className="col-span-2 space-y-6">
          <PartnerHub />
        </div>

        <div className="col-span-3 space-y-6">
          <CouponsRevenue />
        </div>

        <div className="col-span-5 space-y-6">
          <CouponsPerformance />
        </div>

        <div className="col-span-3 space-y-6">
          <LatestDealsAdded />
        </div>

        <div className="col-span-2 space-y-6">
          <PartnersDealsUsed />
        </div>
      </div>
    </ModuleContent>
  );
}
