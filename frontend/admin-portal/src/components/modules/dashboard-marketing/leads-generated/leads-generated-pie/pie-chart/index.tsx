import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";

const data = [
  { name: "Progress 1", value: 80, fill: "#1D7151" }, // Green
  { name: "Progress 2", value: 60, fill: "#00AEEF" }, // Blue
];

export function CircularProgressChart() {
  return (
    <ResponsiveContainer width="100%" height="100%" className="relative">
      <>
        {/* Radial Bar Chart */}
        <RadialBarChart
          width={300}
          height={300}
          cx="50%"
          cy="50%"
          innerRadius="60%"
          outerRadius="100%"
          barSize={10}
          data={data}
          startAngle={90}
          endAngle={-270}
        >
          <PolarAngleAxis type="number" domain={[0, 100]} angleAxisId={0} tick={false} />
          <RadialBar background dataKey="value" />
        </RadialBarChart>

        {/* Centered Text */}
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            textAlign: "center",
          }}
        >
          <strong style={{ fontSize: "24px", color: "#0A2D55" }}>6 976 493</strong>
          <br />
          <span style={{ fontSize: "14px", color: "#4A4A4A" }}>Leads in total</span>
        </div>
      </>
    </ResponsiveContainer>
  );
}
