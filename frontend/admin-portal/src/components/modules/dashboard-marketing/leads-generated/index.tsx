"use client";

import { Divider } from "@/components/common/divider";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { Elipse, KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import StackedArea<PERSON>hart from "./stacked-area-chart";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

const TIME_RANGE_FILTERS = [
  { label: "QUARTER", value: "QUARTER" },
  { label: "SEMESTER", value: "SEMESTER" },
  { label: "MONTHLY", value: "MONTHLY" },
  { label: "YEARLY", value: "YEARLY" },
];

export function LeadsGenerated() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  const [selectedTimeRange, setSelectedTimeRange] = useState(TIME_RANGE_FILTERS[0]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedDateInterval, setSelectedDateInterval] = useState<DateRange | undefined>();

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  function handleChangeCouponRevenue(newOrder: (typeof TIME_RANGE_FILTERS)[number]) {
    setSelectedTimeRange(newOrder);
  }

  return (
    <div id="partner-hub" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col">
      <h3 className="text-primary text-2xl font-bold">Leads generated</h3>
      <div className="flex flex-row gap-4 items-center mt-2">
        <Dropdown
          trigger={
            <button className="flex items-center text-support-blue font-bold text-base">
              <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
              <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
            </button>
          }
        >
          {LICENSE_YEAR_FILTERS.map((filter, idx) => (
            <DropdownItem
              key={idx}
              onClick={() => handleChangeLicenseYear(filter)}
              className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
            >
              {filter.label}
            </DropdownItem>
          ))}
        </Dropdown>
        <Divider vertical initialMarginDisabled className="h-[20px]" />
        <MonthDatePickerWithRange onDateChange={setSelectedDateInterval} />
      </div>
      <div className="mt-2 flex items-center gap-2">
        <p className="text-[#808FA9] text-paragraph-regular">Filter by:</p>
        <Dropdown
          trigger={
            <button className="flex items-center text-support-blue font-bold text-base">
              <span className="ml-1 mr-2 mt-1 text-left">{selectedTimeRange.label}</span>
              <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
            </button>
          }
        >
          {TIME_RANGE_FILTERS.map((filter, idx) => (
            <DropdownItem
              key={idx}
              onClick={() => handleChangeCouponRevenue(filter)}
              className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
            >
              {filter.label}
            </DropdownItem>
          ))}
        </Dropdown>
        <Divider vertical initialMarginDisabled className="h-[20px]" />
        <Dropdown
          trigger={
            <button className="flex items-center text-support-blue font-bold text-base">
              <span className="ml-1 mr-2 mt-1 text-left">{selectedTimeRange.label}</span>
              <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
            </button>
          }
        >
          {TIME_RANGE_FILTERS.map((filter, idx) => (
            <DropdownItem
              key={idx}
              onClick={() => handleChangeCouponRevenue(filter)}
              className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
            >
              {filter.label}
            </DropdownItem>
          ))}
        </Dropdown>
      </div>
      <div className="flex items-center gap-3 mt-4">
        <div className="flex items-center gap-2">
          <Elipse className="fill-support-blue size-4" />
          <p className="text-[#000000] text-small-paragraph-regular">Customer invite customer</p>
        </div>
        <div className="flex items-center gap-2">
          <Elipse className="fill-[#FFCE00] size-4" />
          <p className="text-[#000000] text-small-paragraph-regular">Partner</p>
        </div>
      </div>
      <div className="w-full h-[280px] mt-4">
        <StackedAreaChart />
      </div>
    </div>
  );
}
