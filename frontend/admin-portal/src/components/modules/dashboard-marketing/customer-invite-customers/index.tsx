"use client";

import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { Elipse, KeyboardArrowDown, Launch } from "@interzero/oneepr-react-ui/Icon";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { PieGraph } from "./pie-graph";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

export function CustomerInviteCustomers() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedDateInterval, setSelectedDateInterval] = useState<DateRange | undefined>();

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  return (
    <div id="accounts-created" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row justify-between items-center">
        <div className="flex flex-row items-center gap-4">
          <h3 className="text-primary text-2xl font-bold">Customer invite customers</h3>
          <Launch className="fill-support-blue size-6 cursor-pointer" />
        </div>

        <div className="flex flex-row gap-6 items-center">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <MonthDatePickerWithRange onDateChange={setSelectedDateInterval} />
        </div>
      </div>
      <p className="text-[#808FA9] -mt-4">Values in thousands of euros</p>
      <div className="flex items-center h-[250px] w-full gap-3">
        <div className="flex-1 h-[250px] flex gap-3 justify-center">
          <div style={{ width: "100%", height: "100%" }}>
            <PieGraph />
          </div>
          <div className="flex flex-col gap-6 w-full justify-center">
            <div className="flex flex-col gap-1">
              <p className="text-title-3 text-primary font-bold">16 052</p>
              <div className="flex items-center gap-1">
                <Elipse className="fill-support-blue size-4" />
                <p className="text-small-paragraph-regular text-primary">Direct License</p>
              </div>
            </div>
            <div className="flex flex-col gap-1">
              <p className="text-title-3 text-primary font-bold">9 566</p>
              <div className="flex items-center gap-1">
                <Elipse className="fill-tonal-dark-green-30 size-4" />
                <p className="text-small-paragraph-regular text-primary">EU License</p>
              </div>
            </div>
            <div className="flex flex-col gap-1">
              <p className="text-title-3 text-primary font-bold">1 655</p>
              <div className="flex items-center gap-1">
                <Elipse className="fill-[#F6DA62] size-4" />
                <p className="text-small-paragraph-regular text-primary">Action Guide</p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex-1 h-[250px] flex flex-col gap-3 items-end justify-center">
          <div className="flex flex-col gap-4 rounded-2xl bg-surface-01 px-6 py-6 w-[300px]">
            <p className="text-title-1 text-primary font-bold">€ 23 659.36</p>
            <p className="text-paragraph-regular text-primary font-bold">Revenue earned</p>
          </div>
          <div className="flex flex-col gap-4 rounded-2xl bg-surface-01 px-6 py-6 w-[300px]">
            <p className="text-title-1 text-primary font-bold">€ 16 659.36</p>
            <p className="text-paragraph-regular text-primary font-bold">Earnings</p>
          </div>
        </div>
      </div>
    </div>
  );
}
