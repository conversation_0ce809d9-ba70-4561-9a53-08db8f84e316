"use client";

import { Divider } from "@/components/common/divider";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import AreaChartComponent from "./area-chart";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

const COUPONS_REVENUE_FILTERS = [{ label: "LIZENZERO", value: "LIZENZERO" }];

export function CouponsRevenue() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  const [selectedCouponRevenue, setSelectedCouponRevenue] = useState(COUPONS_REVENUE_FILTERS[0]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedDateInterval, setSelectedDateInterval] = useState<DateRange | undefined>();

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  function handleChangeCouponRevenue(newOrder: (typeof COUPONS_REVENUE_FILTERS)[number]) {
    setSelectedCouponRevenue(newOrder);
  }

  return (
    <div id="partner-hub" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col">
      <h3 className="text-primary text-2xl font-bold mb-2">Coupons revenue</h3>
      <p className="text-[#808FA9] mb-2">Values in thousands of euros</p>
      <div className="flex flex-row gap-4 items-center">
        <Dropdown
          trigger={
            <button className="flex items-center text-support-blue font-bold text-base">
              <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
              <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
            </button>
          }
        >
          {LICENSE_YEAR_FILTERS.map((filter, idx) => (
            <DropdownItem
              key={idx}
              onClick={() => handleChangeLicenseYear(filter)}
              className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
            >
              {filter.label}
            </DropdownItem>
          ))}
        </Dropdown>
        <Divider vertical initialMarginDisabled className="h-[20px]" />
        <MonthDatePickerWithRange onDateChange={setSelectedDateInterval} />
      </div>
      <div className="mt-2">
        <Dropdown
          trigger={
            <button className="flex items-center text-support-blue font-bold text-base">
              <span className="ml-1 mr-2 mt-1 text-left">{selectedCouponRevenue.label}</span>
              <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
            </button>
          }
        >
          {COUPONS_REVENUE_FILTERS.map((filter, idx) => (
            <DropdownItem
              key={idx}
              onClick={() => handleChangeCouponRevenue(filter)}
              className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
            >
              {filter.label}
            </DropdownItem>
          ))}
        </Dropdown>
      </div>
      <div className="w-full h-full mt-4">
        <AreaChartComponent />
      </div>
    </div>
  );
}
