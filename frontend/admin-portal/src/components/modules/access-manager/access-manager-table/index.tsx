import { Datatable } from "@/components/common/datatable";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { KeyboardArrowDown, FilterAlt } from "@interzero/oneepr-react-ui/Icon";
import { createColumnHelper } from "@tanstack/react-table";
import { forwardRef, useImperativeHandle, useMemo } from "react";
import { EllipsisVertical, Pencil } from "lucide-react";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { QueryObserverResult, RefetchOptions, useQuery } from "@tanstack/react-query";
import { AdminRole, getAdminUsers } from "@/lib/api/admin";
import { AdminUser } from "@/types/admin-user";
import { UpdateUserDialog } from "../update-user-dialog";
import { Role } from "@/utils/user";

const columnHelper = createColumnHelper<AdminUser>();

const useColumns = ({
  refetch,
}: {
  refetch?: (options?: RefetchOptions) => Promise<QueryObserverResult<AdminUser[], Error>>;
}) => {
  return useMemo(
    () => [
      columnHelper.accessor("name", {
        header: "Name",
        cell: (info) => (
          <div>
            <p className="text-sm max-w-44 overflow-hidden whitespace-nowrap text-ellipsis text-nowrap">
              {info.getValue()}
            </p>
            <span className="text-sm text-tonal-dark-cream-30">{info.row.original.email}</span>
          </div>
        ),
      }),
      columnHelper.accessor("role.name", {
        header: "Role",
        cell: (info) => <p className="text-sm text-nowrap">{info.row.original.role.display_name}</p>,
      }),
      columnHelper.display({
        id: "lastupdate",
        header: "Last update",
        cell: (info) => new Date(info.row.original.updated_at).toLocaleDateString(),
      }),
      // TODO: Clarify business logic for "Last Event" column - currently shows hardcoded values based on role
      columnHelper.display({
        id: "lastEventDate",
        header: "Last Event",
        cell: (info) => (info.row.original.role.name === "ADMIN" ? "Register clerk" : "License"),
      }),
      columnHelper.accessor("is_active", {
        header: "Status",
        cell: (info) => (
          <div data-status={info.getValue()} className="group flex items-center">
            <div className="w-2 h-2 rounded-full group-data-[status='true']:bg-tonal-green-40 group-data-[status='false']:bg-tonal-cream-20 mr-2" />
            <span className="font-bold  group-data-[status='true']:text-tonal-green-40 group-data-[status='false']:text-tonal-cream-20">
              {!!info.getValue() ? "Active" : "Inactive"}
            </span>
          </div>
        ),
      }),
      columnHelper.display({
        id: "actions",
        header: " ",
        cell: (info) => (
          <div className="w-4">
            <Dropdown
              trigger={
                <button className="rounded-full p-1 hover:bg-secondary/30" aria-label="Actions">
                  <EllipsisVertical className="size-4" />
                </button>
              }
            >
              <DropdownItem asChild className="w-full">
                <>
                  <UpdateUserDialog
                    key={info.row.original.updated_at}
                    userId={Number(info.row.original.id)}
                    onSubmitSuccessfully={refetch}
                  >
                    <div className="text-tonal-dark-cream-10 hover:bg-surface-01 flex items-center  w-full py-5 px-4 outline-none text-base hover:cursor-pointer">
                      <Pencil className="size-4 mr-5 stroke-primary" /> Edit
                    </div>
                  </UpdateUserDialog>
                </>
              </DropdownItem>
            </Dropdown>
          </div>
        ),
      }),
    ],
    [refetch]
  );
};

const ROLE_FILTERS = [
  { label: "All roles", value: "ALL" },
  { label: "Admin", value: Role.ADMIN },
  { label: "Clerk", value: Role.CLERK },
];

const STATUS_FILTERS = [
  { label: "All status", value: "ALL" },
  { label: "Active", value: "ACTIVE" },
  { label: "Inactive", value: "INACTIVE" },
];

const AccountManagerTable = forwardRef((props, ref) => {
  const { paramValues, changeParam } = useQueryFilter(["search", "role", "status"]);

  const searchParamValue = paramValues.search;
  const roleParamValue = paramValues.role as "ALL" | AdminRole | undefined;
  const statusParamValue = paramValues.status as "ALL" | "ACTIVE" | "INACTIVE" | undefined;

  const {
    data: adminUsers,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: ["admin-users", searchParamValue, roleParamValue, statusParamValue],
    queryFn: () =>
      getAdminUsers({
        search: searchParamValue || undefined,
        role: roleParamValue === "ALL" || !roleParamValue ? ["ADMIN", "SUPER_ADMIN", "CLERK"] : [roleParamValue],
        is_active: statusParamValue === "ACTIVE" ? true : statusParamValue === "INACTIVE" ? false : undefined,
      }),
  });

  useImperativeHandle(ref, () => ({
    refetch,
  }));

  const selectedRole = useMemo(() => {
    return ROLE_FILTERS.find((filter) => filter.value === roleParamValue) || ROLE_FILTERS[0];
  }, [roleParamValue]);

  function handleChangeRole(newOrder: (typeof ROLE_FILTERS)[number]) {
    changeParam("role", newOrder.value);
  }

  const selectedStatus = useMemo(() => {
    return STATUS_FILTERS.find((filter) => filter.value === statusParamValue) || STATUS_FILTERS[0];
  }, [statusParamValue]);

  function handleChangeStatus(newStatus: (typeof STATUS_FILTERS)[number]) {
    changeParam("status", newStatus.value);
  }

  function handleSearchName(search: string) {
    changeParam("search", search);
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-12">
        <DatatableSearch defaultValue={searchParamValue || ``} onSearch={handleSearchName} />
        <div className="flex items-center gap-4">
          <Dropdown
            trigger={
              <button
                className="flex items-center text-support-blue font-bold text-base gap-1"
                aria-label={`Filter by status: ${selectedStatus.label}`}
              >
                <FilterAlt width={20} height={20} className="fill-support-blue" />
                <span className="text-left">{selectedStatus.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {STATUS_FILTERS.map((filter) => (
              <DropdownItem
                key={filter.value}
                onClick={() => handleChangeStatus(filter)}
                className="flex items-center gap-2 text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 focus:outline-none focus-visible:ring-2 text-base hover:cursor-pointer"
              >
                {filter.value !== "ALL" && (
                  <div
                    data-status={filter.value === "ACTIVE"}
                    className="w-3 h-3 rounded-full mr-2 data-[status='true']:bg-tonal-green-40 data-[status='false']:bg-tonal-dark-cream-40"
                  />
                )}
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <Dropdown
            trigger={
              <button
                className="flex items-center text-support-blue font-bold text-base gap-1"
                aria-label={`Filter by role: ${selectedRole.label}`}
              >
                <FilterAlt width={20} height={20} className="fill-support-blue" />
                <span className="text-left">{selectedRole.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {ROLE_FILTERS.map((filter) => (
              <DropdownItem
                key={filter.value}
                onClick={() => handleChangeRole(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 focus:outline-none focus-visible:ring-2 text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
        </div>
      </div>
      <Datatable data={adminUsers || []} columns={useColumns({ refetch })} loading={isLoading} sortings={[0]} />
    </div>
  );
});

AccountManagerTable.displayName = "AccountManagerTable";

export default AccountManagerTable;
