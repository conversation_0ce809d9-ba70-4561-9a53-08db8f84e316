/* eslint-disable no-console */

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { GetAdminUsersParams } from "@/lib/api/admin";
import { createUser, getRoles } from "@/lib/api/user";
import { queryClient } from "@/lib/react-query";
import { Role } from "@/utils/user";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { PasswordVisibilityToggle } from "../../auth/common/PasswordVisibilityToggle";

const STATUS = [
  { label: "Active", value: "ACTIVE" },
  { label: "Inactive", value: "INACTIVE" },
];

const STATUS_VALUES = Object.values(STATUS).map((type) => type.value);

const createUserFormSchema = z.object({
  role: z.string({ message: "Role is required" }).min(1, { message: "Role is required" }),
  status: z.enum([STATUS_VALUES[0], ...STATUS_VALUES.slice(0)]),
  first_name: z.string({ message: "First name is required" }).min(1, { message: "First name is required" }),
  surname: z.string({ message: "Surname is required" }).min(1, { message: "Surname is required" }),
  email: z
    .string({ message: "Email is required" })
    .email({ message: "Email is required" })
    .min(1, { message: "Email is required" }),
  password: z.string({ message: "Password is required" }).min(1, { message: "Password is required" }),
});

type CreateUserFormData = z.infer<typeof createUserFormSchema>;

interface CreateUserDialogProps {
  children: React.ReactNode;
  onSubmitSuccessfully?: () => void;
}

export function CreateUserDialog({ children, onSubmitSuccessfully }: CreateUserDialogProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [roles, setRoles] = useState<{ label: string; value: string }[]>([]);
  const [seePassword, setSeePassword] = useState(false);

  const { paramValues } = useQueryFilter(["search", "role", "status"]);

  const searchParamValue = paramValues.search;
  const roleParamValue = paramValues.role as "ALL" | GetAdminUsersParams["role"] | undefined;
  const statusParamValue = paramValues.status as "ALL" | "ACTIVE" | "INACTIVE" | undefined;

  function invalidateAdminList() {
    queryClient.invalidateQueries({ queryKey: ["admin-users", searchParamValue, roleParamValue, statusParamValue] });
  }

  const defaultValues = {
    status: STATUS_VALUES[0],
    role: roles[0]?.value,
  };

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
    setError,
  } = useForm<CreateUserFormData>({
    defaultValues,
    resolver: zodResolver(createUserFormSchema),
  });

  async function handleFormSubmit(data: CreateUserFormData) {
    setIsLoading(true);
    try {
      await createUser({
        name: `${data.first_name} ${data.surname}`,
        email: data.email,
        password: data.password,
        role_id: Number(data.role),
        is_active: data.status === STATUS_VALUES[0],
      });

      enqueueSnackbar("User created successfully", { variant: "success" });
      onSubmitSuccessfully?.();
      handleDialogOpenChange(false);
      invalidateAdminList();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      // TODO: Remove, it could log sensitive data in production
      console.error(error);

      const status = error?.response?.status;

      if (status === 400 || status === 409) {
        enqueueSnackbar("Email already in use", { variant: "error" });
        return setError("email", { message: "Email already in use" });
      }

      enqueueSnackbar("Error creating user", { variant: "error" });
    } finally {
      setIsLoading(false);
    }
  }

  async function handleDialogOpenChange(open: boolean) {
    if (!open) reset(defaultValues);

    setIsDialogOpen(open);
  }

  useEffect(() => {
    reset(defaultValues);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDialogOpen]);

  useEffect(() => {
    (async () => {
      try {
        const roles = await getRoles();
        if (!roles || !roles.length) return;

        const allowedRoles = [Role.ADMIN, Role.CLERK];

        const filteredRoles = roles.filter((role) => allowedRoles.includes(role.name as Role));

        const mappedRoles = filteredRoles.map((role) => ({
          label: role.display_name,
          value: role.id.toString(),
        }));

        setRoles(mappedRoles);
      } catch (error) {
        // TODO: Remove, it could log sensitive data in production
        console.error(error);
      }
    })();
  }, []);

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-2xl z-[1001]">
        <DialogHeader>
          <DialogTitle>Add an account</DialogTitle>
          <DialogDescription>Create an account for an internal user</DialogDescription>
        </DialogHeader>
        <form className="w-full" onSubmit={handleSubmit(handleFormSubmit)}>
          <div className="w-full space-y-6">
            <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label htmlFor="role" className="text-primary text-base font-centra">
                  Role *
                </label>
                {!!roles.length ? (
                  <Select
                    defaultValue={roles[0]?.value}
                    onValueChange={(value) => value && setValue("role", value)}
                    {...register("role")}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Role" />
                    </SelectTrigger>
                    <SelectContent className="z-[1002]">
                      {roles.map((role) => (
                        <SelectItem key={role.value} value={role.value}>
                          {role.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <p className="text-sm text-primary">Loading info...</p>
                )}
              </div>
              <div className="space-y-2">
                <label htmlFor="status" className="text-primary text-base font-centra">
                  Status *
                </label>
                <Select
                  defaultValue={STATUS_VALUES[0]}
                  onValueChange={(value) => value && setValue("status", value)}
                  {...register("status")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent className="z-[1002]">
                    {STATUS.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
            <h3 className="text-xl text-primary font-bold">Credentials</h3>
            <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Input
                label="First Name"
                placeholder="First name"
                {...register("first_name")}
                variant={errors.first_name?.message ? "error" : "default"}
                errorMessage={errors.first_name?.message}
              />
              <Input
                label="Surname"
                placeholder="Surname"
                {...register("surname")}
                variant={errors.surname?.message ? "error" : "default"}
                errorMessage={errors.surname?.message}
                autoComplete="off"
              />
            </div>
            <div className="w-full grid grid-cols-1">
              <Input
                label="E-mail"
                placeholder="E-mail"
                type="email"
                {...register("email")}
                variant={errors.email?.message ? "error" : "default"}
                errorMessage={errors.email?.message}
              />
            </div>
            <div className="w-full grid grid-cols-1">
              <Input
                label="Password"
                placeholder="Password"
                type={seePassword ? "text" : "password"}
                {...register("password")}
                variant={errors.password?.message ? "error" : "default"}
                errorMessage={errors.password?.message}
                rightIcon={
                  <PasswordVisibilityToggle
                    errors={!!errors.password}
                    visibility={seePassword}
                    setVisibility={setSeePassword}
                  />
                }
              />
            </div>
          </div>
          <div className="flex flex-col mt-8">
            <div className="flex items-center justify-end">
              <Button type="submit" variant="filled" color="yellow" size="medium" disabled={isLoading}>
                {isLoading ? "Loading..." : "Create account*"}
              </Button>
            </div>
            <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full my-4" />
            <p className="text-sm text-tonal-dark-cream-20">
              *By saving this changes the user will be notified by e-mail. They can change the credentials later.
            </p>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
