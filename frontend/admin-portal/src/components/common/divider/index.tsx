import { CSSProperties } from "react";

interface IDividerProps {
  className?: string;
  style?: CSSProperties;
  initialMarginDisabled?: boolean;
  vertical?: boolean;
}

export function Divider({ className, style, initialMarginDisabled, vertical }: IDividerProps) {
  return (
    <div
      className={`border-t ${vertical ? "border-l h-full" : "w-full"} rounded border-tonal-dark-cream-80 ${
        !initialMarginDisabled && (vertical ? "mx-8 md:mx-10" : "my-8 md:my-10")
      } ${className}`}
      style={style}
    ></div>
  );
}
