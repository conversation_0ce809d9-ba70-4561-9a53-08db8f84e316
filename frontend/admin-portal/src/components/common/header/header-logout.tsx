"use client";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { Logout } from "@interzero/oneepr-react-ui/Icon";
import { signOut } from "next-auth/react";

export function HeaderLogout() {
  function handleLogout() {
    signOut({
      redirect: true,
      callbackUrl: "/en/auth/login",
    });
  }

  return (
    <Button
      variant="text"
      color="dark-blue"
      size="medium"
      trailingIcon={<Logout className="fill-primary" />}
      onClick={handleLogout}
    >
      Logout
    </Button>
  );
}
