import { Search } from "@interzero/oneepr-react-ui/Icon";
import { ChangeEvent, ComponentProps, useRef, useState } from "react";

interface DatatableSearchProps extends ComponentProps<"input"> {
  onSearch: (value: string) => void;
}

export function DatatableSearch({ onSearch, defaultValue, ...props }: DatatableSearchProps) {
  const [search, setSearch] = useState(defaultValue);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
      debounceRef.current = null;
    }

    debounceRef.current = setTimeout(() => {
      onSearch(e.target.value);
    }, 500);

    setSearch(e.target.value);
  }

  return (
    <div className="w-80 bg-white rounded-full py-3 px-4 flex items-center gap-4 border border-tonal-cream-90">
      <Search width={24} className="fill-primary flex-none" />
      <input
        value={search}
        onChange={handleChange}
        placeholder="Search by name"
        className="outline-none text-primary flex-1"
        {...props}
      />
    </div>
  );
}
