import { KeyboardArrowRight } from "@interzero/oneepr-react-ui/Icon";
import Link from "next/link";

interface BreadcrumbProps {
  paths: { href: string; label: string }[];
}

export function Breadcrumb({ paths }: BreadcrumbProps) {
  return (
    <nav aria-label="breadcrumb" className="z-50 w-full flex flex-row  justify-start">
      <ol className="flex items-center space-x-2">
        {paths.map((path, index) => (
          <li key={`${path.href}_${path.label}`} className="flex items-center space-x-1 text-sm">
            {index === paths.length - 1 ? (
              <span className="text-primary font-bold" aria-hidden>
                {path.label}
              </span>
            ) : (
              <Link href={path.href} className="flex items-center font-bold">
                <span className="text-tonal-dark-cream-50 hover:text-tonal-dark-cream-20 text-sm/none">
                  {path.label}
                </span>
                <KeyboardArrowRight className="ml-2 fill-tonal-dark-cream-50  text-tonal-dark-cream-50 flex-none w-5 h-5" />
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
