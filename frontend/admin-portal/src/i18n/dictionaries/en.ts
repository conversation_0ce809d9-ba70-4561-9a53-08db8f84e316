const dict = {
  welcome: {
    helloWorld: "Hello World",
    happyYear: "Happy {{ year }}!",
  },
  login: {
    title: "Log in with your credentials",
    subtitle: "Log in with your credentials",
    email: {
      label: "Email",
      placeholder: "Enter your email",
      error: "Please enter a valid email",
    },
    password: {
      label: "Password",
      placeholder: "Enter your password",
      error: "Please enter a valid password",
    },
    button: "Login",
    forgotPassword: "Forgot password?",
  },
  forgotPassword: {
    title: "Password recovery",
    subtitle: "Please enter the e-mail you’re using for your account",
    email: {
      label: "Email",
      placeholder: "Enter your email",
      error: "Please enter a valid email",
    },
    button: "Reset password",
    back: "Back to login",
    confirmation: {
      title: "Thank you!",
      subtitle:
        "We’ve sent password reset instructions to your e-mail address. If no e-mail is received within ten minutes, check if the submitted address is correct.",
      button: "Back to login",
    },
  },
  recoverPassword: {
    title: "Password recovery",
    subtitle: "Please enter the e-mail you’re using for your account",
    email: {
      label: "Email",
      placeholder: "Enter your email",
      error: "Please enter a valid email",
    },
    password: {
      label: "New Password",
      placeholder: "New Password",
      error: "Please enter a valid password",
    },
    passwordStrength: {
      weak: "Weak",
      medium: "Medium",
      strong: "Strong",
      lessThan6: "Less than 6 characters",
      specialChar: "At least one special character",
      number: "At least one number",
      label: "Password strength",
    },
    button: "Save",
    back: "Back to login",
    confirmation: {
      title: "Password changed successfully!",
      subtitle: "Email: {{ email }}",
      button: "Back to login",
    },
  },
};

export type DictMap = typeof dict;
export default dict;
