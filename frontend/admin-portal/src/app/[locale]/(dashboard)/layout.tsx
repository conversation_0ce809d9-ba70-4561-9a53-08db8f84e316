"use client";

import { SessionProvider } from "next-auth/react";

import { <PERSON><PERSON> } from "@/components/common/header";
import { Sidebar } from "@/components/common/sidebar";

import { SidebarProvider } from "@/hooks/use-sidebar";
import { ReactQueryProvider } from "@/lib/react-query";

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <ReactQueryProvider>
      <div className="h-full min-h-screen w-full flex items-stretch">
        <SessionProvider>
          <SidebarProvider>
            <Sidebar />
            <div className="flex flex-col flex-1 bg-surface-02">
              <Header />
              {children}
            </div>
          </SidebarProvider>
        </SessionProvider>
      </div>
    </ReactQueryProvider>
  );
}
