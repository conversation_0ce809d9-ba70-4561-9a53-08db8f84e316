import { Pin, Settings } from "@interzero/oneepr-react-ui/Icon";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { UpdateCustomerClusterForm } from "@/components/modules/customer-cluster/update-customer-cluster-form";

interface EditCustomerClusterPageProps {
  params: {
    customerClusterId: string;
  };
}

export default function EditCustomerClusterPage({ params }: EditCustomerClusterPageProps) {
  const { customerClusterId } = params;

  return (
    <div className="flex flex-col flex-1">
      <ModuleContent containerClassName="pb-12">
        <div className="mb-3 flex items-center gap-2">
          <Pin className="size-6 fill-primary" />
          <span className="text-[#183362]">Customer&apos;s Clusters</span>
        </div>
        <ModuleTitle
          icon={Settings}
          title="Settings"
          description="See all the clusters for your customers."
          className="mb-0"
        />
      </ModuleContent>
      <div className="bg-[#EDE9E4] w-full flex-1 py-[72px]">
        <UpdateCustomerClusterForm customerClusterId={customerClusterId} />
      </div>
    </div>
  );
}
