import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { BrokerUploadData } from "@/components/modules/brokers-data/broker-upload-data";
import { Upload } from "@interzero/oneepr-react-ui/Icon";

export default function UploadDataPage() {
  return (
    <ModuleContent>
      <div className="flex items-start justify-between gap-4 mb-5">
        <ModuleTitle
          icon={Upload}
          title="Upload Data"
          description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
        />
      </div>
      <BrokerUploadData />
    </ModuleContent>
  );
}
