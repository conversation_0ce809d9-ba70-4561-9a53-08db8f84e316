import { Layers } from "@interzero/oneepr-react-ui/Icon";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { UpdateDiscountCodeForm } from "@/components/modules/discount-codes/update-discount-code-form";

interface EditDiscountCodePageProps {
  params: {
    discountCodeId: string;
  };
}

export default function EditDiscountCodePage({ params }: EditDiscountCodePageProps) {
  const { discountCodeId } = params;

  return (
    <ModuleContent>
      <ModuleTitle
        icon={Layers}
        title="Discount Code Manage"
        description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
      />
      <div className="mt-[96px]">
        <UpdateDiscountCodeForm discountCodeId={parseInt(discountCodeId)} />
      </div>
    </ModuleContent>
  );
}
