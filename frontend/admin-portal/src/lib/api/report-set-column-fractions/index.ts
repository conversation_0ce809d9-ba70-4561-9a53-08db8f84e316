import { adminApi } from "@/lib/api";
import { ApiEndpoints } from "@/lib/api/endpoints";
import {
  CreateReportSetColumnFraction,
  ReportSetColumnFraction,
} from "@/types/service-setup/report-set-column-fraction";

export async function getReportSetColumnFractions(reportSetColumnId: number) {
  const response = await adminApi.get<ReportSetColumnFraction[]>(ApiEndpoints.reportSetColumns.findAll, {
    params: {
      reportSetColumnId,
    },
  });

  return response.data;
}

export async function createReportSetColumnFraction(reportSetColumnFraction: CreateReportSetColumnFraction) {
  const response = await adminApi.post<ReportSetColumnFraction>(
    ApiEndpoints.reportSetColumnFractions.create,
    reportSetColumnFraction
  );

  return response.data;
}

export async function deleteReportSetColumnFraction(reportSetColumnFractionId: number) {
  await adminApi.delete(ApiEndpoints.reportSetColumnFractions.delete(reportSetColumnFractionId));
}
