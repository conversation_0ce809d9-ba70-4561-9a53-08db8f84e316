export interface BrokerCompany {
  id: number;
  broker_id: number;
  name: string;
  register_number: string;
  vat?: string | null;
  tax?: string | null;
  country_code: string;
  address_number: string;
  address_street: string;
  city: string;
  contact_name: string;
  contact_email: string;
  phone_number: string;
  created_at: string;
  updated_at: string | null;
  deleted_at: string | null;
}

export interface GetBrokerCompaniesParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface GetBrokerCompaniesResponse {
  count: number;
  current_page: number;
  pages: number;
  companies: BrokerCompany[];
}
