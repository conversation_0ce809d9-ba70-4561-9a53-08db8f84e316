import { admin<PERSON>pi } from "..";
import { ApiEndpoints } from "../endpoints";
import { CheckFileBrokerResponse, CreateFileBrokerResponse } from "./types";

export async function checkFileBroker(file: File) {
  const formData = new FormData();

  formData.append("file", file);

  const response = await adminApi.post<CheckFileBrokerResponse>(ApiEndpoints.brokerFile.check, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });

  return response?.data;
}

export async function createFileBroker(user_id: number, body: CheckFileBrokerResponse, file: File) {
  const formData = new FormData();

  formData.append("file", file);
  formData.append("company", JSON.stringify(body.company));
  formData.append("order", JSON.stringify(body.order));
  formData.append("user_id", String(user_id));

  const response = await adminApi.post<CreateFileBrokerResponse>(ApiEndpoints.brokerFile.create, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });

  return response?.data;
}
