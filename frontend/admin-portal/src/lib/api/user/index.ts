import { adminApi } from "..";

export interface Roles {
  id: number;
  name: string;
  display_name: string;
}

export async function getRoles() {
  const response = await adminApi.get<Roles[]>(`/auth/user/all-roles`);

  return response?.data;
}

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  role_id: number;
  is_active: boolean;
}

export async function createUser(data: CreateUserData) {
  const response = await adminApi.post(`/auth/user`, data, {
    params: {
      create_by_admin: true,
    },
  });

  return response?.data;
}

export type UpdateUserData = Partial<CreateUserData>;

export async function updateUser(userId: string | number, data: UpdateUserData) {
  const response = await adminApi.patch(`/auth/user/${userId}`, data);

  return response?.data;
}
