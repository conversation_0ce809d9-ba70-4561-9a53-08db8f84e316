export const ApiEndpoints = {
  customers: {
    findAll: "/customer/customers",
    monthlySummary: "/customer/customers/summary/monthly",
    findById: (customerId: string) => `/customer/customers/${customerId}/details`,
  },
  clusters: {
    getAllPaginated: "/customer/cluster/list/paginated",
    create: "/customer/cluster",
    delete: (clusterId: number) => `/customer/cluster/${clusterId}`,
    findById: (clusterId: number) => `/customer/cluster/${clusterId}`,
    update: (clusterId: number) => `/customer/cluster/${clusterId}`,
  },
  countries: {
    findAll: "/admin/countries",
    findById: (countryId: number) => `/admin/countries/${countryId}`,
    findByCode: (countryCode: string) => `/admin/countries/code/${countryCode}`,
    update: (countryId: number) => `/admin/countries/${countryId}`,
  },
  coupon: {
    create: "/customer/coupon",
    delete: (couponId: number) => `/customer/coupon/${couponId}`,
    findById: (couponId: number) => `/customer/coupon/${couponId}`,
    getAllPaginated: "/customer/coupon/list/paginated",
    update: (couponId: number) => `/customer/coupon/${couponId}`,
  },
  adminUsers: {
    findAll: "/admin/admin",
    findById: (userId: number) => `/admin/admin/${userId}`,
    findByEmail: (email: string) => `/admin/admin/${email}`,
    update: (userId: number) => `/admin/admin/${userId}`,
  },
  priceLists: {
    findAll: "/admin/price-lists",
    findById: (priceListId: number) => `/admin/price-lists/${priceListId}`,
    create: "/admin/price-lists",
    update: (priceListId: number) => `/admin/price-lists/${priceListId}`,
    delete: (priceListId: number) => `/admin/price-lists/${priceListId}`,
  },
  marketingMaterials: {
    findAll: "/customer/marketing-materials",
    findById: (marketingMaterialId: string | number) => `/customer/marketing-materials/${marketingMaterialId}`,
    create: "/customer/marketing-materials",
    update: (marketingMaterialId: string | number) => `/customer/marketing-materials/${marketingMaterialId}`,
    delete: (marketingMaterialId: string | number) => `/customer/marketing-materials/${marketingMaterialId}`,
    deleteFile: (fileId: string) => `/customer/files/${fileId}`,
    downloadFile: (fileId: string) => `/customer/files/${fileId}`,
  },
  discountCode: {
    findById: (discountCodeId: string | number) => `/admin/discount-codes/${discountCodeId}`,
    create: "/admin/discount-codes",
    update: (discountCodeId: string | number) => `/admin/discount-codes/${discountCodeId}`,
    delete: (discountCodeId: string | number) => `/admin/discount-codes/${discountCodeId}`,
  },
  serviceSetups: {
    findByCountryCode: (countryCode: string) => `/admin/service-setups/${countryCode}`,
    findStatusByCountryCode: (countryCode: string) => `/admin/service-setups/${countryCode}/status`,
    findServiceSetupPackagingServices: (countryCode: string) =>
      `/admin/service-setups/${countryCode}/packaging-services`,
    findServiceSetupReportSets: (countryCode: string) => `/admin/service-setups/${countryCode}/report-sets`,
    findServiceSetupReportSet: (countryCode: string, reportSetId: number) =>
      `/admin/service-setups/${countryCode}/report-sets/${reportSetId}`,
    findServiceSetupReportFrequencies: (countryCode: string) =>
      `/admin/service-setups/${countryCode}/report-frequencies`,
    findServiceSetupRepresentativeTiers: (countryCode: string) =>
      `/admin/service-setups/${countryCode}/representative-tiers`,
    findServiceSetupOtherCosts: (countryCode: string) => `/admin/service-setups/${countryCode}/other-costs`,
    findServiceSetupPriceLists: (countryCode: string) => `/admin/service-setups/${countryCode}/price-lists`,
    findServiceSetupRequiredInformations: (countryCode: string) =>
      `/admin/service-setups/${countryCode}/required-informations`,
    findServiceSetupCriterias: (countryCode: string) => `/admin/service-setups/${countryCode}/criterias`,
    findServiceSetupCommitment: (countryCode: string) => `/admin/service-setups/${countryCode}/commitment`,
  },
  packagingServices: {
    findAll: "/admin/packaging-services",
    findById: (packagingServiceId: number) => `/admin/packaging-services/${packagingServiceId}`,
    create: "/admin/packaging-services",
    update: (packagingServiceId: number) => `/admin/packaging-services/${packagingServiceId}`,
    delete: (packagingServiceId: number) => `/admin/packaging-services/${packagingServiceId}`,
  },
  reportSets: {
    findAll: "/admin/report-sets",
    findById: (reportSetId: number) => `/admin/report-sets/${reportSetId}`,
    create: "/admin/report-sets",
    update: (reportSetId: number) => `/admin/report-sets/${reportSetId}`,
    delete: (reportSetId: number) => `/admin/report-sets/${reportSetId}`,
    duplicate: (reportSetId: number) => `/admin/report-sets/${reportSetId}/duplicate`,
  },
  reportSetFrequencies: {
    findAll: "/admin/report-set-frequencies",
    findById: (reportSetFrequencyId: number) => `/admin/report-set-frequencies/${reportSetFrequencyId}`,
    create: "/admin/report-set-frequencies",
    update: (reportSetFrequencyId: number) => `/admin/report-set-frequencies/${reportSetFrequencyId}`,
    delete: (reportSetFrequencyId: number) => `/admin/report-set-frequencies/${reportSetFrequencyId}`,
  },
  reportSetFractions: {
    findAll: "/admin/report-set-fractions",
    findById: (reportSetFractionId: number) => `/admin/report-set-fractions/${reportSetFractionId}`,
    create: "/admin/report-set-fractions",
    update: (reportSetFractionId: number) => `/admin/report-set-fractions/${reportSetFractionId}`,
    delete: (reportSetFractionId: number) => `/admin/report-set-fractions/${reportSetFractionId}`,
  },
  reportSetColumns: {
    findAll: "/admin/report-set-columns",
    findById: (reportSetColumnId: number) => `/admin/report-set-columns/${reportSetColumnId}`,
    create: "/admin/report-set-columns",
    update: (reportSetColumnId: number) => `/admin/report-set-columns/${reportSetColumnId}`,
    delete: (reportSetColumnId: number) => `/admin/report-set-columns/${reportSetColumnId}`,
  },
  reportSetColumnFractions: {
    findAll: "/admin/report-set-column-fractions",
    findById: (reportSetColumnFractionId: number) => `/admin/report-set-column-fractions/${reportSetColumnFractionId}`,
    create: "/admin/report-set-column-fractions",
    bulkCreate: "/admin/report-set-column-fractions/bulk",
    update: (reportSetColumnFractionId: number) => `/admin/report-set-column-fractions/${reportSetColumnFractionId}`,
    delete: (reportSetColumnFractionId: number) => `/admin/report-set-column-fractions/${reportSetColumnFractionId}`,
  },
  otherCosts: {
    findAll: "/admin/other-costs",
    findById: (otherCostId: number) => `/admin/other-costs/${otherCostId}`,
    create: "/admin/other-costs",
    update: (otherCostId: number) => `/admin/other-costs/${otherCostId}`,
    delete: (otherCostId: number) => `/admin/other-costs/${otherCostId}`,
  },
  representativeTiers: {
    findAll: "/admin/representative-tiers",
    findById: (representativeTierId: number) => `/admin/representative-tiers/${representativeTierId}`,
    create: "/admin/representative-tiers",
    update: (representativeTierId: number) => `/admin/representative-tiers/${representativeTierId}`,
    delete: (representativeTierId: number) => `/admin/representative-tiers/${representativeTierId}`,
  },
  countryPriceLists: {
    findAll: "/admin/country-price-lists",
    findById: (countryPriceListId: number) => `/admin/country-price-lists/${countryPriceListId}`,
    create: "/admin/country-price-lists",
    update: (countryPriceListId: number) => `/admin/country-price-lists/${countryPriceListId}`,
    delete: (countryPriceListId: number) => `/admin/country-price-lists/${countryPriceListId}`,
  },
  requiredInformations: {
    findAll: "/admin/required-informations",
    findById: (requiredInformationId: number) => `/admin/required-informations/${requiredInformationId}`,
    create: "/admin/required-informations",
    update: (requiredInformationId: number) => `/admin/required-informations/${requiredInformationId}`,
    delete: (requiredInformationId: number) => `/admin/required-informations/${requiredInformationId}`,
  },
  criterias: {
    findAll: "/admin/criterias",
    findById: (criteriumId: number) => `/admin/criterias/${criteriumId}`,
    create: "/admin/criterias",
    update: (criteriumId: number) => `/admin/criterias/${criteriumId}`,
    delete: (criteriumId: number) => `/admin/criterias/${criteriumId}`,
  },
  uploadFiles: {
    upload: "/admin/upload-files",
    download: (fileId: string) => `/admin/upload-files/${fileId}`,
  },
  fractionIcons: {
    findAll: "/admin/fraction-icons",
    create: "/admin/fraction-icons",
    delete: (fractionIconId: number) => `/admin/fraction-icons/${fractionIconId}`,
  },
  dashboard: {
    totalCustomers: "/customer/dashboard/total-customers",
    openBalance: "/customer/dashboard/open-balance",
    averageRevenue: "/customer/dashboard/average-revenue",
    revenueAndContracts: "/customer/dashboard/revenue-and-contracts",
    servicesCustomers: "/customer/dashboard/services-customers",
    topServices: "/customer/dashboard/top-services",
    servicesRevenueOvertime: "/customer/dashboard/services-revenue-overtime",
    accountsAndObligations: "/customer/dashboard/accounts-obligations",
    paymentMethods: "/customer/dashboard/payment-methods",
    terminations: "/customer/dashboard/terminations",
  },
  partner: {
    findAll: "/customer/partner",
    findAllPaginated: "/customer/partner/list/paginated",
    create: "/customer/partner",
    findOne: (partnerId: number) => `/customer/partner/${partnerId}`,
    update: (partnerId: number) => `/customer/partner/${partnerId}`,
    updateContract: (partnerId: number, contractId: number) => `/customer/partner/${partnerId}/contracts/${contractId}`,
    getPartnerCommissions: (partnerId: number) => `/customer/partner/${partnerId}/commissions`,
    getPartnerGlobalStatistics: (partnerId: number) => `/customer/partner-results/${partnerId}/statistics`,
    getRevenuePlusEarningsOfTopFivePartners: "/customer/partner-results/revenue-plus-earnings",
    getDiscountLinksUsage: "/customer/partner-results/discount-links-used",
    getPartnerCouponsPerformance: (partnerId: number) => `/customer/partner-results/${partnerId}/coupon-performance`,
    getPartnerLicensePurchases: (partnerId: number) => `/customer/partner-results/${partnerId}/license-purchases`,
  },
  settings: {
    upsert: (key: string) => `/admin/settings/${key}`,
    findByKey: (key: string) => `/admin/settings/${key}`,
  },
  broker: {
    findAll: "/admin/broker",
    create: "/admin/broker",
    update: (brokerId: number) => `/admin/broker/${brokerId}`,
    findById: (brokerId: number) => `/admin/broker/${brokerId}`,
    delete: (brokerId: number) => `/admin/broker/${brokerId}`,
  },
  brokerFile: {
    check: "/admin/broker-file/check",
    create: "/admin/broker-file",
  },
  brokerCompanies: {
    findAll: "/admin/broker-companies",
  },
};
