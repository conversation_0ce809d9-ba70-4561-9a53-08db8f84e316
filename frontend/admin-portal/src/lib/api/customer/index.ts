import { customerApi } from "..";
import { ApiEndpoints } from "../endpoints";
import { Company, Contract, ContractType, CustomerPhone, ListCustomer } from "./types";

interface GetCustomersParams {
  page?: number;
  limit?: number;
  search?: string;
  service_type?: ContractType;
  order?: "ASC" | "DESC" | "LAST_MODIFIED" | "FIRST_MODIFIED";
  status?: "ACTIVE" | "TERMINATED";
  country_code?: string;
}

interface GetCustomersResponse {
  count: number;
  current_page: number;
  pages: number;
  customers: ListCustomer[];
}

export async function getCustomers(params: GetCustomersParams) {
  const response = await customerApi.get<GetCustomersResponse>(ApiEndpoints.customers.findAll, { params });

  if (response.status !== 200 || !response.data) {
    throw new Error("Failed to fetch customers");
  }

  return response.data;
}

interface GetCustomersMonthlySummaryResponse {
  created: {
    current: number;
    growthRate: number;
  };
  active: {
    current: number;
    growthRate: number;
  };
  terminated: {
    current: number;
    growthRate: number;
  };
}

export async function getCustomersMonthlySummary() {
  const response = await customerApi.get<GetCustomersMonthlySummaryResponse>(ApiEndpoints.customers.monthlySummary);

  if (response.status !== 200 || !response.data) {
    throw new Error("Failed to fetch customers monthly summary");
  }

  return response.data;
}

interface GetCustomerResponse {
  id: number;
  first_name: string;
  last_name: string;
  salutation: string;
  email: string;
  user_id: number;
  is_active: boolean;
  id_stripe: number | null;
  updated_at: string;
  created_at: string;
  companies: Company[];
  phones: CustomerPhone[];
  contracts: Contract[];
}

export async function getCustomer(customerId: string) {
  const response = await customerApi.get<GetCustomerResponse>(ApiEndpoints.customers.findById(customerId));

  if (response.status !== 200 || !response.data) {
    throw new Error("Failed to fetch customer");
  }

  return response.data;
}
