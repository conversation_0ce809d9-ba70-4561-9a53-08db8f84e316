import { uploadFile } from "../upload-files";
import { Role } from "@/utils/user";
import { UploadedFile } from "@/types/file";
import { ApiEndpoints } from "../endpoints";
import { adminApi } from "..";

type UpsertSettingsParams = {
  key: string;
  value: string;
  file?: File;
};

type Settings = {
  id: number;
  key: string;
  value: string;
  term_or_condition_file_id?: string;
  file?: UploadedFile;
};

export async function upsertSettings({ key, value, file }: UpsertSettingsParams): Promise<Settings> {
  let fileId: string | undefined;

  if (file) {
    const fileResponse = await uploadFile({
      file: file!,
      user_id: 0,
      user_role: Role.ADMIN,
      document_type: "INVITE_CUSTOMER_MANAGER_TERMS",
    });
    fileId = fileResponse.id;
  }

  const response = await adminApi.put(ApiEndpoints.settings.upsert(key), {
    value,
    term_or_condition_file_id: fileId,
  });

  return response.data;
}

export async function getSettings(key: string): Promise<Settings> {
  const response = await adminApi.get(ApiEndpoints.settings.findByKey(key));
  return response.data;
}
