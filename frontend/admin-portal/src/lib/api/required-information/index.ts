import { adminApi } from "@/lib/api";
import { ApiEndpoints } from "@/lib/api/endpoints";
import {
  CreateRequiredInformation,
  RequiredInformation,
  UpdateRequiredInformation,
} from "@/types/service-setup/required-information";

export async function getRequiredInformations(countryId: number) {
  const response = await adminApi.get<RequiredInformation[]>(ApiEndpoints.requiredInformations.findAll, {
    params: {
      countryId,
    },
  });

  return response.data;
}

export async function createRequiredInformation(requiredInformation: CreateRequiredInformation) {
  const response = await adminApi.post(ApiEndpoints.requiredInformations.create, requiredInformation);

  return response.data;
}

export async function updateRequiredInformation(
  requiredInformationId: number,
  requiredInformation: UpdateRequiredInformation
) {
  const response = await adminApi.put(
    ApiEndpoints.requiredInformations.update(requiredInformationId),
    requiredInformation
  );

  return response.data;
}

export async function deleteRequiredInformation(requiredInformationId: number) {
  await adminApi.delete(ApiEndpoints.requiredInformations.delete(requiredInformationId));
}
