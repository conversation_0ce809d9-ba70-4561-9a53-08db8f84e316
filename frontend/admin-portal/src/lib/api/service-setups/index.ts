import { admin<PERSON>pi } from "@/lib/api";
import { ApiEndpoints } from "@/lib/api/endpoints";
import { FullReportSet, ServiceSetup, SetupPriceList } from "@/types/service-setup";
import { Criteria, CriteriaType } from "@/types/service-setup/criteria";
import { OtherCost } from "@/types/service-setup/other-cost";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { ReportSet } from "@/types/service-setup/report-set";
import { ReportSetFrequency } from "@/types/service-setup/report-set-frequency";
import { RepresentativeTier } from "@/types/service-setup/representative-tier";
import { RequiredInformation } from "@/types/service-setup/required-information";

export async function getServiceSetupByCountryCode(countryCode: string) {
  const response = await adminApi.get<ServiceSetup>(ApiEndpoints.serviceSetups.findByCountryCode(countryCode));

  return response.data;
}

export interface ServiceSetupStatus {
  completed: boolean;
  message?: string;
}

export async function getServiceSetupStatusByCountryCode(countryCode: string) {
  const response = await adminApi.get<ServiceSetupStatus>(
    ApiEndpoints.serviceSetups.findStatusByCountryCode(countryCode)
  );

  return response.data;
}

export async function getServiceSetupPackagingServices(countryCode: string) {
  const response = await adminApi.get<PackagingService[]>(
    ApiEndpoints.serviceSetups.findServiceSetupPackagingServices(countryCode)
  );

  return response.data;
}

export async function getServiceSetupReportSets(countryCode: string) {
  const response = await adminApi.get<ReportSet[]>(ApiEndpoints.serviceSetups.findServiceSetupReportSets(countryCode));

  return response.data;
}

export async function getServiceSetupReportSet(countryCode: string, reportSetId: number) {
  const response = await adminApi.get<FullReportSet>(
    ApiEndpoints.serviceSetups.findServiceSetupReportSet(countryCode, reportSetId)
  );

  return response.data;
}

export async function getServiceSetupReportFrequencies(countryCode: string) {
  const response = await adminApi.get<ReportSetFrequency[]>(
    ApiEndpoints.serviceSetups.findServiceSetupReportFrequencies(countryCode)
  );
  if (response.data && response.data.length) {
    // TODO: Wait for the backend to fix this.
    // This is a temporary fix.
    // The packaging_service_id is always null in the current response from the backend.
    // Fill in the correct value from the packaging_service field.
    response.data = response.data.map((item) => {
      return {
        ...item,
        packaging_service_id: item.packaging_service_id
          ? item.packaging_service_id
          : "packaging_service" in item && item?.packaging_service && typeof item?.packaging_service === "object"
            ? "id" in item?.packaging_service && typeof item?.packaging_service.id === "number"
              ? item?.packaging_service.id
              : -1
            : -1,
      };
    });
  }
  return response.data;
}

export async function getServiceSetupRepresentativeTiers(countryCode: string) {
  const response = await adminApi.get<RepresentativeTier[]>(
    ApiEndpoints.serviceSetups.findServiceSetupRepresentativeTiers(countryCode)
  );

  return response.data;
}

export async function getServiceSetupOtherCosts(countryCode: string) {
  const response = await adminApi.get<OtherCost[]>(ApiEndpoints.serviceSetups.findServiceSetupOtherCosts(countryCode));

  return response.data;
}

export async function getServiceSetupPriceLists(countryCode: string) {
  const response = await adminApi.get<SetupPriceList[]>(
    ApiEndpoints.serviceSetups.findServiceSetupPriceLists(countryCode)
  );

  return response.data;
}

export async function getServiceSetupRequiredInformations(countryCode: string) {
  const response = await adminApi.get<RequiredInformation[]>(
    ApiEndpoints.serviceSetups.findServiceSetupRequiredInformations(countryCode)
  );

  return response.data;
}

export async function getServiceSetupCriterias(
  countryCode: string,
  params?: {
    type: CriteriaType;
    packagingServiceId?: number;
    requiredInformationId?: number;
  }
) {
  const response = await adminApi.get<Criteria[]>(ApiEndpoints.serviceSetups.findServiceSetupCriterias(countryCode), {
    params: {
      type: params?.type,
      packaging_service_id: params?.packagingServiceId,
      required_information_id: params?.requiredInformationId,
    },
  });

  return response.data;
}

export async function getServiceSetupCommitment(countryCode: string) {
  const response = await adminApi.get<Criteria[]>(ApiEndpoints.serviceSetups.findServiceSetupCommitment(countryCode));

  return response.data;
}
