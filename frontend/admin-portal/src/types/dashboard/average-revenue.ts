interface CartTypeRevenueOutputDto {
  total: number;
  growth: number;
}

interface CartPerCustomerOutputDto {
  direct_licensing: CartTypeRevenueOutputDto;
  action_guide: CartTypeRevenueOutputDto;
  other_services: CartTypeRevenueOutputDto;
  eu_licensing: CartTypeRevenueOutputDto;
  overall: CartTypeRevenueOutputDto;
}

export interface AverageRevenue {
  cart_overall: number;
  cart_per_customer: CartPerCustomerOutputDto;
}
