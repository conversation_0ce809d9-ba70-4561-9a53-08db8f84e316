export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: {
    id: number;
    name: string;
    display_name: string;
  };
  type: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type CreateAdminUser = Omit<AdminUser, "id" | "created_at" | "updated_at" | "role"> & { role_id: number };

export type UpdateAdminUser = Omit<AdminUser, "id" | "created_at" | "updated_at" | "role"> & { role_id: number };
