services:
  oneepr-db:
    image: postgres:17.3
    restart: always
    environment:
      PGUSER: oneepr-local-user
      POSTGRES_USER: oneepr-local-user
      POSTGRES_PASSWORD: oneepr-local-password
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready"]
      interval: 1s
      timeout: 5s
      retries: 10
    ports:
      - 5432:5432

  oneepr-backend:
    build:
      context: ../backend
      dockerfile: ../docker/build/backend/Dockerfile
    platform: linux/arm64
    restart: unless-stopped
    depends_on:
      oneepr-db:
        condition: service_healthy
      oneepr-mailpit:
        condition: service_started
    env_file:
      - ./build/backend/.env
    ports:
      - 8080:8080

  oneepr-frontend-shop:
    build:
      context: ../frontend/shop-customer-portal
      dockerfile: ../../docker/build/frontend/Dockerfile
    platform: linux/arm64
    restart: unless-stopped
    env_file:
      - ./build/frontend/.env_shop
    ports:
      - 3000:3000

  oneepr-frontend-clerk-portal:
    build:
      context: ../frontend/clerk-portal
      dockerfile: ../../docker/build/frontend/Dockerfile
    platform: linux/arm64
    restart: unless-stopped
    env_file:
      - ./build/frontend/.env_clerk_portal
    ports:
      - 3001:3000

  oneepr-frontend-admin-portal:
    build:
      context: ../frontend/admin-portal
      dockerfile: ../../docker/build/frontend/Dockerfile
    platform: linux/arm64
    restart: unless-stopped
    env_file:
      - ./build/frontend/.env_admin_portal
    ports:
      - 3002:3000

  oneepr-mailpit:
    image: axllent/mailpit:v1.22
    restart: always
    ports:
      - 1025:1025
      - 8025:8025

volumes:
  pgdata:
