#!/usr/bin/env bash
source ./common

docker-compose up -d oneepr-frontend-shop oneepr-frontend-clerk-portal oneepr-frontend-admin-portal

check_error "=> Maybe it needs to be rebuilt, try ./rebuild-fe\n"

print_success "\n=> Shop frontend should be running at http://localhost:3000\n\n"
print_success "\n=> Clerk frontend should be running at http://localhost:3001\n\n"
print_success "\n=> Admin frontend should be running at http://localhost:3002\n\n"

exit 0
