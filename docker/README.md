# Docker dev stack for oneepr

This is a docker-compose setup prepared to facilitate the development in this project. It is assuming that you are either a frontend dev and need a running backend or a backend dev and need a running db and maybe the frontend.

The stack will always use what is there locally. When switching branches and there are changes in whatever you use, **remember to rebuild!**

The docker compose commands have been prepared in scripts that can be used, inspect the scripts to see what is going on.

## As a frontend developer

### Running the backend

Running the backend should be as simple as executing (from the docker folder)

```shell
./start-be
```

This will create a database and build the backend from the current state of the repo. If the backend changes (f.e. you switch branches), you need to **rebuild** the container. It will take some time for the first time running and be quicker after that.

The backend will be running on `localhost:8080`.

### Rebuilding the backend

In order to rebuild the backend, run

```shell
./rebuild-be-full
```

This will basically rebuild the backend from whatever is there currently.

#### Database rebuild

Sometimes, switching branches will break the database and the backend will not be able to come up properly. In this case, we need to throw away the database and let it rebuild.

**THIS WILL WIPE ALL DB CONTENTS AND START WITH A FRESH DB!**

```shell
./rebuild-be-db

# Or to just flush the db, without restarting the backend
./flush-db
```

Both will shutdown the stack, throw away volumes, and rebuild from scratch

### Emails

The backend will also be running with mailpit. You can see emails that the backend sends at http://localhost:8025.

## As a backend developer

### Running the frontend

You can build and run the frontend using

```shell
./start-fe
```

This will build and run the frontend which will be talking to your backend on `0.0.0.0:8080`. The frontend will be available at http://localhost:3000.

### Rebuilding the frontend

In order to rebuild the frontend, run

```shell
./rebuild-fe
```

This will basically rebuild the frontend from whatever is there currently.

### Postgres Database

The stack provides a preconfigured Postgres database. To launch it, run

```shell
./start-db
```

The database will be available at `localhost:5432`, which should be setup in the `application.properties` already.

#### Flushing the DB

In order to restart with a fresh db, throw away the volume and restart the server.

```shell
./flush-db
```

### Emails

The stack gives you a local instance of mailpit. In order to run it, run

```shell
./start-mailpit
```

This will open an SMTP server on `localhost:1025` to talk to and you can see the mails on http://localhost:8025.

# Issues on MacOS

### Docker-compose: command not found

If you encounter the error `docker-compose: command not found`, ensure that Docker's CLI tools are properly configured:

1. Open Docker Desktop.
2. Click on the Settings icon (gear) in the top right corner.
3. Navigate to the "Advanced" tab.
4. Check both options: "System (requires password)" and "Allow the default Docker to be used (requires password)".
5. Click on "Apply & Restart".

This should resolve the issue and make the `docker-compose` command available.

### Performance issues using x86_64/amd64 images on Apple Silicon

If you are using an Apple Silicon and experiencing performance issues when using x86_64/amd64 images, you can enable Rosetta emulation for these images:

1. Open Docker Desktop.
2. Click on the Settings icon (gear) in the top right corner.
3. Navigate to the "General" tab.
4. Check the option "Use Rosetta for x86_64/amd64 emulation on Apple Silicon".
5. Click on "Apply & Restart".
